﻿using Microsoft.EntityFrameworkCore;
using CQR.Domain.CQRHeaders;
using CQR.Domain.Enum;

namespace CQR.Persistence.Command.Repositories;

public class CQRHeaderRepository : EntityRepository<CQR_Header, int>, ICQRHeaderRepository
{
    public CQRHeaderRepository(CQRDbContext context) : base(context) { }

    public async Task<bool> ExistProjectNbr(int modifiedProjInt)
    {
        bool isValidNbr = _context.CQR_Header.Any(h => Convert.ToInt32(h.ProjectNbr) == modifiedProjInt);
        return isValidNbr;
    }
    public async Task<bool> ExistProjectNbrAndRevNbr(int modifiedProjInt, int modifiedRevInt)
    {
        bool isValidRevNbr = _context.CQR_Header.Any(h => Convert.ToInt32(h.ProjectNbr) == modifiedProjInt &&
              Convert.ToInt32(h.RevNbr) == modifiedRevInt);
        return isValidRevNbr;
    }
    private string GetUserId()
    {
        // Implement your user ID retrieval logic
        return "CurrentUser"; // Placeholder
    }
    private void SetElectronicProperty(CQR_Header entity, string propertyName, object value)
    {
        var property = typeof(CQR_Header).GetProperty(propertyName);
        if (property != null && property.CanWrite)
        {
            property.SetValue(entity, value);
        }
    }
    private string GetVersion()
    {
        // Return your application version
        return "1.0.0"; // Placeholder
    }


    public async Task<bool> UpdateCQRHeader(int queueKey, CQR_Header model, bool exit = false)
    {
        //throw new NotImplementedException();
        if (queueKey == 0) return false;


        //using var context = new YourDbContext(); // Replace with your actual DbContext

        var cqrHeader = await _context.CQR_Header.FirstOrDefaultAsync(x => x.QueueKey == queueKey);
        if (cqrHeader == null) return false;

        // Folder Fields
        //cqrHeader.ProjectNbr = model.ProjectNbr;
        //cqrHeader.RevNbr = model.RevNbr;
        //cqrHeader.NewModRepl = model.FranNewModRepl;
        //cqrHeader.QuoteType = model.QuoteType;
        //cqrHeader.Status = model.Status;
        //cqrHeader.StatusDate = ConvertDateScreenToCygnet(model.StatusDate);
        //cqrHeader.OriginatorId = model.Originator;
        //cqrHeader.OriginatorDate = ConvertDateScreenToCygnet(model.OriginationDate);
        //cqrHeader.FranIssueDate = ConvertDateScreenToCygnet(model.CQRIssueDate);

        // Description Tab
        //cqrHeader.FranDesc = model.CQRDesc;
        //cqrHeader.AwardQuarter = model.AwardQuarter;
        //cqrHeader.AwardYear = model.AwardYear;
        //cqrHeader.GateExit = model.GateExit;
        //cqrHeader.ProductDesc = model.ProductDescription;
        //cqrHeader.VehicleBuildId = model.VehicleBuildId;
        //cqrHeader.CustNbr = model.OEMGroup;
        //cqrHeader.Vehicle = model.Platform;
        //cqrHeader.Nameplate = model.Nameplate;
        //cqrHeader.VolumePerAnnum = model.VolumePerAnnum;
        //cqrHeader.RemProductLife = model.RemProductLife;
        //cqrHeader.Gateway = model.Gateway;

        // Comment handling for Volume Per Annum
        //var vpaCommentString = CommentHelper.CommentStamp(
        //    model.DbsCommentsVolumePerAnnum,
        //    model.NewCommentsVolumePerAnnum,
        //    model.OrgCommentsVolumePerAnnum,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.VPAComments = vpaCommentString;

        //cqrHeader.ApproxAnnualValue = model.ApproxAnnualValue;
        //cqrHeader.OpeningMeetingDate = ConvertDateScreenToCygnet(model.OpeningMeetingDate);
        //cqrHeader.CustomerJob1 = ConvertDateScreenToCygnet(model.CustomerSOP);
        //cqrHeader.RegionalQuotingTeam = ConvertDateScreenToCygnet(model.RegionalQuotingTeam);

        // Target Pricing Comments
        //var targetPricingCommentString = CommentHelper.CommentStamp(
        //    model.DbsCommentsTargetPricing,
        //    model.NewCommentsTargetPricing,
        //    model.OrgCommentsTargetPricing,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.IntPricComments = targetPricingCommentString;

        //cqrHeader.CustQuoteDueDate = ConvertDateScreenToCygnet(model.CustomerQuoteDueDate);
        //cqrHeader.CustQuoteDueDateOrig = ConvertDateScreenToCygnet(model.CustomerQuoteDueDateOrig);
        //cqrHeader.CustQuoteDueDateChangeComment = ConvertDateScreenToCygnet(model.CustomerQuoteDueDateChangeComment);
        //cqrHeader.ModelYear = model.ModelYear;

        //// Background Comments
        //var backgroundCommentString = CommentHelper.CommentStamp(
        //    model.Comment,
        //    model.NewComment,
        //    model.OrgComment,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.BkRndInfComments = backgroundCommentString;

        ////cqrHeader.CustBuyerName = model.CustomerBuyer;
        ////cqrHeader.CustEngineerName = model.CustomerEngineer;
        ////cqrHeader.ObsolescenceReqdInd = model.ObsolescenceRequiredInd;

        //// Customer Productivity Comments
        //var custProdCommentString = CommentHelper.CommentStamp(
        //    model.DbsCommentsCustomerProductivity,
        //    model.NewCommentsCustomerProductivity,
        //    model.OrgCommentsCustomerProductivity,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.CustProdComments = custProdCommentString;

        // Milestones
        //cqrHeader.AccountMgrId = model.AccountManager;
        //cqrHeader.CommercialManager = model.CommercialManager;
        //cqrHeader.CommercialBusinessManager = model.CommercialBusinessManager;
        //cqrHeader.SalesAcctDirectorId = model.SalesAccountDirector;
        //cqrHeader.CostEstimatorId = model.CostEstimator;
        //cqrHeader.PGMId = model.PGM_aka_CoC;
        //cqrHeader.PETMId = model.PDM_aka_ProgramManager;
        //cqrHeader.AMECoordinator = model.AMECoordinator;
        //cqrHeader.PurchasingCoordinator = model.PurchasingCoordinator;

        //cqrHeader.TDRDate = model.TDRDate;
        //cqrHeader.TDRNoInput = model.TDRNoInput;
        //cqrHeader.Communication = model.Communication;
        //cqrHeader.ASILLevel = model.ASILLevel;
        //cqrHeader.AUTOSAR = model.AUTOSAR;
        //cqrHeader.Cybersecurity = model.Cybersecurity;
        //cqrHeader.OtherRelevantFeatures = model.OtherRelevantFeatures;

        //cqrHeader.PAId = model.PA;
        //cqrHeader.PSMId = model.PSM;
        //cqrHeader.EngineeringManagerId = model.EngineeringManager;
        //cqrHeader.CalculationCurrency = model.CalculationCurrency;
        //cqrHeader.Customergroup = model.CustomerGroup;
        //cqrHeader.ProductCategory2 = model.ProductCategory;
        //cqrHeader.BusinessPlan = model.BusinessPlan;

        // Additional flags and indicators
        //cqrHeader.TimeFrameOkInd = model.TimeFrameOkInd;
        //cqrHeader.InformationOkInd = model.InformationOkInd;
        //cqrHeader.WorkProceedOkInd = model.WorkProceedOkInd;
        //cqrHeader.HealthInd = model.HealthInd;
        //cqrHeader.ModNeedsCostInd = model.ModNeedsCostInd;
        //cqrHeader.QS9000Considered = model.QS9000Considered;
        //cqrHeader.DueDateToBNE = ConvertDateScreenToCygnet(model.DueDateToBidsAndEstimating);
        //cqrHeader.DueDateFromEng = ConvertDateScreenToCygnet(model.DueDateFromEngineering);
        //cqrHeader.DateOfCSR = ConvertDateScreenToCygnet(model.DateOfCSR);
        //cqrHeader.PrdId = model.EngineerAssigned;
        //cqrHeader.LeadManufacturing = model.LeadManufacturing;
        //cqrHeader.LeadPurchasing = model.LeadPurchasing;
        //cqrHeader.LeadValidation = model.LeadValidation;
        //cqrHeader.LeadManufacturingNoCostImpact = model.LeadManufacturingNoCostImpact;
        //cqrHeader.LeadPurchasingNoCostImpact = model.LeadPurchasingNoCostImpact;
        //cqrHeader.LeadValidationNoCostImpact = model.LeadValidationNoCostImpact;

        // Electronic inputs loop (1-8)
        for (int prdLoop = 1; prdLoop <= 8; prdLoop++)
        {
            var suffix = prdLoop == 1 ? "" : prdLoop.ToString();

            // 反射讀取目前值（如需複製）
            var propertyName = $"ElecInputReqdInd{suffix}";
            var currentValue = cqrHeader.GetType().GetProperty(propertyName)?.GetValue(cqrHeader);

            // 再設定值（這段等同複製自己，其實沒意義，除非有特殊邏輯）
            cqrHeader.GetType().GetProperty(propertyName)?.SetValue(cqrHeader, currentValue);

            // 真正從 model 塞新值
            SetElectronicProperty(cqrHeader, $"ElecInputReqdInd{suffix}", cqrHeader.ElecInputReqdInd[prdLoop]);
            SetElectronicProperty(cqrHeader, $"ElecPETMId{suffix}", cqrHeader.ElecPETMId[prdLoop]);
            //SetElectronicProperty(cqrHeader, $"ElecPETMId{suffix}NoInput", cqrHeader.e[prdLoop]);
        }

        //for (int prdLoop = 1; prdLoop <= 8; prdLoop++)
        //{
        //    var suffix = prdLoop == 1 ? "" : prdLoop.ToString();

        //    var propertyName1 = $"ElecInputReqdInd{i}";
        //    var value1 = cqrHeader.GetType().GetProperty(propertyName1)?.GetValue(cqrHeader);
        //    cqrHeader.GetType().GetProperty($"ElecInputReqdInd{prdLoop}")?.SetValue(cqrHeader, value1);

        //    // Use reflection or switch statement to set properties dynamically
        //    SetElectronicProperty(cqrHeader, $"ElecPETMId{suffix}", model.ElecInputReqdInd[prdLoop]);
        //    SetElectronicProperty(cqrHeader, $"ElecPETMId{suffix}", model.ElecInputReqdInd[prdLoop]);
        //    SetElectronicProperty(cqrHeader, $"ElecInputReqdInd{suffix}", model.ElecInputReqdInd[prdLoop]);
        //    //todo

        //    //SetElectronicProperty(cqrHeader, $"ElecPETMId{suffix}", model.ElecPRD[prdLoop]);
        //    //SetElectronicProperty(cqrHeader, $"ElecPETMId{prdLoop}NoInput", model.ElecPRDNoInput[prdLoop]);


        //}

        cqrHeader.ManufacturingSite = model.ManufacturingSite?.Length >= 3 ?
            model.ManufacturingSite.Substring(model.ManufacturingSite.Length - 3) :
            model.ManufacturingSite;
        cqrHeader.EngineeringSite = model.EngineeringSite;
        cqrHeader.PIMSite = model.PIMSite;

        // Engineering Package Comments
        //var epCommentString = CommentStamp(
        //    model.CommentsEP,
        //    model.NewCommentsEP,
        //    model.OrgCommentsEP,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.EngPkgComments = epCommentString;

        //cqrHeader.NoRCTFolders = model.NoRCTFolders;
        //cqrHeader.GDPIMPhase1Required = model.GDPEPPhase1Required;
        //cqrHeader.GDPIMPhase2Required = model.GDPEPPhase2Required;

        // QR fields
        //cqrHeader.QRToolLeadTime = model.QRLeadTime;
        cqrHeader.QRFOB = model.QRFOB;

        //var qrMaterialDate = model.QRMaterialDate != "N/A" ? model.QRMaterialDate : "";
        //cqrHeader.QRMaterialDate = ConvertDateScreenToCygnet(qrMaterialDate);

        var qrLaborDate = model.QRLaborDate != "N/A" ? model.QRLaborDate : "";
        //cqrHeader.QRLaborDate = ConvertDateScreenToCygnet(qrLaborDate);

        //cqrHeader.QRToolCap = model.QRToolCap;

        // QR Tooling Comments
        //var qrToolingCommentString = CommentHelper.CommentStamp(
        //    model.DbsCommentsQRTooling,
        //    model.NewCommentsQRTooling,
        //    model.OrgCommentsQRTooling,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.QRToolingComments = qrToolingCommentString;

        // QAF Comments
        //var qafCommentString = CommentHelper.CommentStamp(
        //    model.DbsCommentsQAF,
        //    model.NewCommentsQAF,
        //    model.OrgCommentsQAF,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.QAFComments = qafCommentString;

        //cqrHeader.QRProgClass = model.QRProgClass;
        //cqrHeader.QuoteCompletedSent = model.QRCompletedAndSent;
        //cqrHeader.QuoteSendForReview = model.QRReviewAvailable;

        // QS fields
        //cqrHeader.QSQuoteStatus = model.QSQuoteStatus;
        //cqrHeader.QSAwardStatus = model.QSAwardStatus;
        //cqrHeader.QuoteDate = ConvertDateScreenToCygnet(model.QSQuoteDate);
        //cqrHeader.DateWon = ConvertDateScreenToCygnet(model.QSDateWon);
        //cqrHeader.SalesAdministrator = ConvertDateScreenToCygnet(model.QSSalesAdministrator);

        // QS Comments
        //var qsCommentString = CommentHelper.CommentStamp(
        //    model.QSComments,
        //    model.NewQSComments,
        //    model.OrgQSComments,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.QSComments = qsCommentString;

        // QS Proceed/Cancel Comments
        //var qsProceedCancelCommentString = CommentHelper.CommentStamp(
        //    model.QSCommentsProceedCancel,
        //    model.NewQSCommentsProceedCancel,
        //    model.OrgQSCommentsProceedCancel,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.QSCommentsProceedCancel = qsProceedCancelCommentString;

        // Action Comments
        //var actionCommentString = CommentHelper.CommentStamp(
        //    model.DbsCommentsAction,
        //    model.NewCommentsAction,
        //    model.OrgCommentsAction,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.ActionComments = actionCommentString;

        // GDPEP Rejection Comments
        //var gdpepRejectionCommentString = CommentHelper.CommentStamp(
        //    model.DbsCommentsGDPEPRejection,
        //    model.NewCommentsGDPEPRejection,
        //    model.OrgCommentsGDPEPRejection,
        //    true,
        //    GetUserId(),
        //    false);
        //cqrHeader.GDPIMRejectionComments = gdpepRejectionCommentString;

        // Handle exit condition
        if (exit)
        {
            cqrHeader.LockUserId = null;
            cqrHeader.LockTime = null;
        }

        // Update tracking fields
        //cqrHeader.LastUpdatedDate = ConvertDateScreenToCygnet(DateTime.Today.ToString());
        cqrHeader.LastUpdatedTime = DateTime.Now.ToString("HHmm");
        cqrHeader.LastUpdatedVersion = GetVersion(); // Replace with your VERSION constant
        cqrHeader.LastUpdatedBy = GetUserId();

        await _context.SaveChangesAsync();
        return true;
    }

    //public Task<CQR_Header> GetByQueueKeyAsync(int queueKey)
    //{
    //    throw new NotImplementedException();
    //}
    //public async Task<IEnumerable<CQR_GDPIMPhase>> GetByAllAsync()
    //{
    //    int count = 10;
    //    return await _context.Set<CQR_GDPIMPhase>()
    //        .Take(count).ToListAsync();
    //}

    //public async Task<CQR_GDPIMPhase> GetByQueueKeyAsync(int queueKey)
    //{
    //    return await _context.Set<CQR_GDPIMPhase>().FindAsync(queueKey);
    //}
    //public async Task<CQR_Header>> GetByQueueKeyAsync()
    //{
    //    var sql = @"SELECT CH.* FROM CQR_Header CH WHERE CH.QueueKey = 19633";
    //    var result = await _connection.QueryAsync<CQR_TRS_Header>(sql, new { });
    //    return result;
    //}



    public async Task<bool> CreateNewCQRAsync(string userId)
    {
        //throw new NotImplementedException();
        // 先刪除符合條件的舊資料
        var oldHeaders = _context.CQR_Header
            .Where(h => h.OriginatorId == userId && h.LastUpdatedBy == "Create");

        _context.CQR_Header.RemoveRange(oldHeaders);
        await _context.SaveChangesAsync();

        StatusUsfrEnum status = StatusUsfrEnum.status010100FR;
        string name = status.ToString(); // "status010100FR"

        string formattedDate = DateTime.Now.ToString("yyyyMMdd");

        // 1. 先用 DateTime.ParseExact 解析成 DateTime
        //DateTime dt = DateTime.ParseExact(DateTime.Now, "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture);

        // 2. 再用 ToString 輸出成指定格式字串
        //string output = dt.ToString("yyyyMMdd");


        // 新增一筆新的資料
        var newHeader = new CQR_Header
        {
            OriginatorId = userId,
            LastUpdatedBy = "Create",
            Status = StatusUsfrEnum.status010100FR.ToString(),     // 你要事先定義這個值
            StatusDate = formattedDate,
            OriginationDate = formattedDate
        };

        _context.CQR_Header.Add(newHeader);
        await _context.SaveChangesAsync();
        return true;
    }

    public Task<string> SearchsUsedByOtherCQR(int sProjectNum, int uniqueNumber)
    {
        // 假設 uniqueNumber 是你要查詢的 UniqueNumber (int)
        var projectNbr = (from header in _context.CQR_Header
                          join folder in _context.CQRAntaresFolder
                            on header.QueueKey equals folder.QueueKey
                          where (folder.Archived == null || folder.Archived == false)  // ISNULL(Archived,0)=0
                                && string.Compare(header.Status, "010100FR") > 0
                                && int.Parse(header.ProjectNbr) != sProjectNum
                                && folder.UniqueNumber == uniqueNumber
                          orderby header.ProjectNbr // 可加排序，確保 TOP 1 有定義
                          select header.ProjectNbr)
                         .FirstOrDefaultAsync();
        return projectNbr;
    }
}
