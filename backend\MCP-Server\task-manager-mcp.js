import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import fs from 'fs/promises';
import path from 'path';

// 任務管理與 Claude 整合的 MCP Server
class TaskManagerMCPServer {
  constructor() {
    this.tasksFile = './tasks.json';
    this.server = new Server(
      {
        name: 'task-manager-claude',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          prompts: {},
        },
      }
    );

    this.setupHandlers();
    this.initializeTasksFile();
  }

  async initializeTasksFile() {
    try {
      await fs.access(this.tasksFile);
    } catch {
      // 如果文件不存在，創建初始任務
      const initialTasks = {
        projects: {
          "cqr-system": {
            name: "CQR System Development",
            tasks: [
              {
                id: "1",
                title: "實作 ApplicationServices 整合",
                description: "將所有服務整合到 ApplicationServices 中",
                status: "completed",
                priority: "high",
                assignee: "developer",
                created: new Date().toISOString(),
                estimated_hours: 8,
                actual_hours: 6,
                tags: ["architecture", "services"]
              },
              {
                id: "2", 
                title: "建立單元測試",
                description: "為 ApplicationServices 和 CQRQueryService 建立完整測試",
                status: "completed",
                priority: "high",
                assignee: "developer",
                created: new Date().toISOString(),
                estimated_hours: 12,
                actual_hours: 10,
                tags: ["testing", "quality"]
              },
              {
                id: "3",
                title: "導入 MCP Server",
                description: "設置 MCP Server 以整合多個 AI 模型",
                status: "in_progress",
                priority: "medium",
                assignee: "developer", 
                created: new Date().toISOString(),
                estimated_hours: 16,
                actual_hours: 4,
                tags: ["mcp", "ai-integration"]
              }
            ]
          }
        }
      };
      await fs.writeFile(this.tasksFile, JSON.stringify(initialTasks, null, 2));
    }
  }

  setupHandlers() {
    // 列出所有工具
    this.server.setRequestHandler('tools/list', async () => ({
      tools: [
        {
          name: 'create_task',
          description: '創建新任務',
          inputSchema: {
            type: 'object',
            properties: {
              project: { type: 'string', description: '項目名稱' },
              title: { type: 'string', description: '任務標題' },
              description: { type: 'string', description: '任務描述' },
              priority: { type: 'string', enum: ['low', 'medium', 'high'], default: 'medium' },
              estimated_hours: { type: 'number', description: '預估工時' },
              tags: { type: 'array', items: { type: 'string' }, description: '標籤' }
            },
            required: ['project', 'title', 'description']
          }
        },
        {
          name: 'update_task_status',
          description: '更新任務狀態',
          inputSchema: {
            type: 'object', 
            properties: {
              project: { type: 'string' },
              task_id: { type: 'string' },
              status: { type: 'string', enum: ['todo', 'in_progress', 'completed', 'blocked'] },
              actual_hours: { type: 'number', description: '實際工時' }
            },
            required: ['project', 'task_id', 'status']
          }
        },
        {
          name: 'list_tasks',
          description: '列出任務',
          inputSchema: {
            type: 'object',
            properties: {
              project: { type: 'string', description: '項目名稱' },
              status: { type: 'string', enum: ['todo', 'in_progress', 'completed', 'blocked'] },
              priority: { type: 'string', enum: ['low', 'medium', 'high'] }
            }
          }
        },
        {
          name: 'analyze_project_progress',
          description: '分析項目進度',
          inputSchema: {
            type: 'object',
            properties: {
              project: { type: 'string', description: '項目名稱' }
            },
            required: ['project']
          }
        },
        {
          name: 'suggest_next_tasks',
          description: '基於當前項目狀態建議下一步任務',
          inputSchema: {
            type: 'object',
            properties: {
              project: { type: 'string', description: '項目名稱' },
              context: { type: 'string', description: '額外上下文' }
            },
            required: ['project']
          }
        },
        {
          name: 'generate_task_from_code',
          description: '從代碼分析生成任務建議',
          inputSchema: {
            type: 'object',
            properties: {
              project: { type: 'string' },
              code: { type: 'string', description: '要分析的代碼' },
              analysis_type: { 
                type: 'string', 
                enum: ['refactor', 'test', 'optimize', 'security'],
                description: '分析類型'
              }
            },
            required: ['project', 'code', 'analysis_type']
          }
        }
      ]
    }));

    // 執行工具
    this.server.setRequestHandler('tools/call', async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'create_task':
          return await this.createTask(args);
        case 'update_task_status':
          return await this.updateTaskStatus(args);
        case 'list_tasks':
          return await this.listTasks(args);
        case 'analyze_project_progress':
          return await this.analyzeProjectProgress(args);
        case 'suggest_next_tasks':
          return await this.suggestNextTasks(args);
        case 'generate_task_from_code':
          return await this.generateTaskFromCode(args);
        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });
  }

  async loadTasks() {
    const data = await fs.readFile(this.tasksFile, 'utf8');
    return JSON.parse(data);
  }

  async saveTasks(tasks) {
    await fs.writeFile(this.tasksFile, JSON.stringify(tasks, null, 2));
  }

  async createTask(args) {
    const tasks = await this.loadTasks();
    
    if (!tasks.projects[args.project]) {
      tasks.projects[args.project] = { name: args.project, tasks: [] };
    }

    const newTask = {
      id: Date.now().toString(),
      title: args.title,
      description: args.description,
      status: 'todo',
      priority: args.priority || 'medium',
      assignee: 'developer',
      created: new Date().toISOString(),
      estimated_hours: args.estimated_hours || 0,
      actual_hours: 0,
      tags: args.tags || []
    };

    tasks.projects[args.project].tasks.push(newTask);
    await this.saveTasks(tasks);

    return {
      content: [{
        type: 'text',
        text: `✅ 任務已創建:\n\n**${newTask.title}**\n${newTask.description}\n\n- ID: ${newTask.id}\n- 優先級: ${newTask.priority}\n- 預估工時: ${newTask.estimated_hours}h`
      }]
    };
  }

  async updateTaskStatus(args) {
    const tasks = await this.loadTasks();
    const project = tasks.projects[args.project];
    
    if (!project) {
      throw new Error(`Project ${args.project} not found`);
    }

    const task = project.tasks.find(t => t.id === args.task_id);
    if (!task) {
      throw new Error(`Task ${args.task_id} not found`);
    }

    const oldStatus = task.status;
    task.status = args.status;
    
    if (args.actual_hours !== undefined) {
      task.actual_hours = args.actual_hours;
    }

    if (args.status === 'completed') {
      task.completed = new Date().toISOString();
    }

    await this.saveTasks(tasks);

    return {
      content: [{
        type: 'text',
        text: `🔄 任務狀態已更新:\n\n**${task.title}**\n${oldStatus} → ${args.status}\n\n${args.actual_hours ? `實際工時: ${args.actual_hours}h` : ''}`
      }]
    };
  }

  async listTasks(args) {
    const tasks = await this.loadTasks();
    let allTasks = [];

    if (args.project) {
      const project = tasks.projects[args.project];
      if (project) {
        allTasks = project.tasks.map(t => ({ ...t, project: args.project }));
      }
    } else {
      // 列出所有項目的任務
      for (const [projectName, project] of Object.entries(tasks.projects)) {
        allTasks.push(...project.tasks.map(t => ({ ...t, project: projectName })));
      }
    }

    // 過濾
    if (args.status) {
      allTasks = allTasks.filter(t => t.status === args.status);
    }
    if (args.priority) {
      allTasks = allTasks.filter(t => t.priority === args.priority);
    }

    // 格式化輸出
    let output = '# 📋 任務列表\n\n';
    
    const groupedByStatus = allTasks.reduce((acc, task) => {
      if (!acc[task.status]) acc[task.status] = [];
      acc[task.status].push(task);
      return acc;
    }, {});

    for (const [status, statusTasks] of Object.entries(groupedByStatus)) {
      const statusEmoji = {
        'todo': '📝',
        'in_progress': '🔄', 
        'completed': '✅',
        'blocked': '🚫'
      };

      output += `## ${statusEmoji[status]} ${status.toUpperCase()}\n\n`;
      
      for (const task of statusTasks) {
        const priorityEmoji = { 'low': '🟢', 'medium': '🟡', 'high': '🔴' };
        output += `### ${priorityEmoji[task.priority]} ${task.title}\n`;
        output += `- **項目**: ${task.project}\n`;
        output += `- **描述**: ${task.description}\n`;
        output += `- **預估/實際工時**: ${task.estimated_hours}h / ${task.actual_hours}h\n`;
        if (task.tags?.length) {
          output += `- **標籤**: ${task.tags.map(tag => `\`${tag}\``).join(', ')}\n`;
        }
        output += `- **ID**: \`${task.id}\`\n\n`;
      }
    }

    return {
      content: [{
        type: 'text',
        text: output
      }]
    };
  }

  async analyzeProjectProgress(args) {
    const tasks = await this.loadTasks();
    const project = tasks.projects[args.project];
    
    if (!project) {
      throw new Error(`Project ${args.project} not found`);
    }

    const projectTasks = project.tasks;
    const total = projectTasks.length;
    const completed = projectTasks.filter(t => t.status === 'completed').length;
    const inProgress = projectTasks.filter(t => t.status === 'in_progress').length;
    const blocked = projectTasks.filter(t => t.status === 'blocked').length;
    
    const totalEstimated = projectTasks.reduce((sum, t) => sum + (t.estimated_hours || 0), 0);
    const totalActual = projectTasks.reduce((sum, t) => sum + (t.actual_hours || 0), 0);
    
    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;
    const efficiency = totalEstimated > 0 ? Math.round((totalEstimated / totalActual) * 100) : 0;

    let analysis = `# 📊 ${project.name} 項目進度分析\n\n`;
    analysis += `## 📈 總體進度\n`;
    analysis += `- **完成率**: ${completionRate}% (${completed}/${total})\n`;
    analysis += `- **進行中**: ${inProgress} 個任務\n`;
    analysis += `- **被阻塞**: ${blocked} 個任務\n\n`;
    
    analysis += `## ⏱️ 工時分析\n`;
    analysis += `- **預估總工時**: ${totalEstimated}h\n`;
    analysis += `- **實際工時**: ${totalActual}h\n`;
    analysis += `- **效率指標**: ${efficiency}%\n\n`;

    // 按優先級分析
    const highPriority = projectTasks.filter(t => t.priority === 'high');
    const highCompleted = highPriority.filter(t => t.status === 'completed').length;
    
    analysis += `## 🔴 高優先級任務\n`;
    analysis += `- **高優先級完成率**: ${highPriority.length > 0 ? Math.round((highCompleted / highPriority.length) * 100) : 0}%\n\n`;

    // 建議
    analysis += `## 💡 建議\n`;
    if (blocked > 0) {
      analysis += `- ⚠️ 有 ${blocked} 個任務被阻塞，需要優先解決\n`;
    }
    if (efficiency < 80) {
      analysis += `- 📊 工時預估準確度較低，建議檢討預估方法\n`;
    }
    if (completionRate > 80) {
      analysis += `- 🎉 項目進度良好，即將完成！\n`;
    }

    return {
      content: [{
        type: 'text',
        text: analysis
      }]
    };
  }

  async suggestNextTasks(args) {
    const tasks = await this.loadTasks();
    const project = tasks.projects[args.project];
    
    if (!project) {
      throw new Error(`Project ${args.project} not found`);
    }

    const completedTasks = project.tasks.filter(t => t.status === 'completed');
    const inProgressTasks = project.tasks.filter(t => t.status === 'in_progress');
    const todoTasks = project.tasks.filter(t => t.status === 'todo');

    // 基於已完成的任務分析下一步
    const suggestions = [];

    // 分析已完成的標籤，推薦相關任務
    const completedTags = new Set();
    completedTasks.forEach(task => {
      task.tags?.forEach(tag => completedTags.add(tag));
    });

    let suggestionText = `# 🎯 ${project.name} 下一步任務建議\n\n`;
    
    suggestionText += `## 📋 當前狀態\n`;
    suggestionText += `- ✅ 已完成: ${completedTasks.length} 個\n`;
    suggestionText += `- 🔄 進行中: ${inProgressTasks.length} 個\n`;
    suggestionText += `- 📝 待辦: ${todoTasks.length} 個\n\n`;

    suggestionText += `## 🔍 基於已完成工作的建議\n`;

    // 根據 CQR 項目的特殊邏輯
    if (completedTags.has('architecture') && completedTags.has('testing')) {
      suggestions.push({
        title: '性能優化',
        description: '架構和測試都完成了，建議進行性能測試和優化',
        priority: 'medium',
        estimated_hours: 12,
        tags: ['performance', 'optimization']
      });
      
      suggestions.push({
        title: '文檔完善',
        description: '為已完成的架構和測試編寫技術文檔',
        priority: 'low',
        estimated_hours: 8,
        tags: ['documentation']
      });
    }

    if (completedTags.has('mcp')) {
      suggestions.push({
        title: 'MCP 工具擴展',
        description: '基於基礎 MCP 設置，添加更多自定義工具',
        priority: 'medium', 
        estimated_hours: 16,
        tags: ['mcp', 'tooling']
      });
    }

    // 如果沒有特殊建議，提供通用建議
    if (suggestions.length === 0) {
      suggestions.push({
        title: '代碼審查',
        description: '對已完成的功能進行全面代碼審查',
        priority: 'high',
        estimated_hours: 6,
        tags: ['code-review', 'quality']
      });
    }

    suggestions.forEach((suggestion, index) => {
      suggestionText += `### 💡 建議 ${index + 1}: ${suggestion.title}\n`;
      suggestionText += `- **描述**: ${suggestion.description}\n`;
      suggestionText += `- **優先級**: ${suggestion.priority}\n`;
      suggestionText += `- **預估工時**: ${suggestion.estimated_hours}h\n`;
      suggestionText += `- **建議標籤**: ${suggestion.tags.map(tag => `\`${tag}\``).join(', ')}\n\n`;
    });

    // 高優先級待辦任務提醒
    const highPriorityTodo = todoTasks.filter(t => t.priority === 'high');
    if (highPriorityTodo.length > 0) {
      suggestionText += `## 🔴 高優先級待辦任務\n`;
      highPriorityTodo.forEach(task => {
        suggestionText += `- **${task.title}**: ${task.description}\n`;
      });
    }

    return {
      content: [{
        type: 'text',
        text: suggestionText
      }]
    };
  }

  async generateTaskFromCode(args) {
    const { project, code, analysis_type } = args;
    
    let analysisPrompt = '';
    let taskSuggestions = [];

    switch (analysis_type) {
      case 'refactor':
        analysisPrompt = '分析以下代碼，找出需要重構的部分：';
        taskSuggestions = [
          '提取重複代碼到共用方法',
          '簡化複雜的條件邏輯', 
          '改善方法命名和可讀性',
          '優化類的職責分離'
        ];
        break;
      
      case 'test':
        analysisPrompt = '分析以下代碼，建議需要添加的測試：';
        taskSuggestions = [
          '添加單元測試覆蓋邊界情況',
          '建立集成測試',
          '添加錯誤處理測試',
          '性能測試基準'
        ];
        break;
        
      case 'optimize':
        analysisPrompt = '分析以下代碼的性能優化機會：';
        taskSuggestions = [
          '數據庫查詢優化',
          '記憶體使用優化',
          '異步處理改進',
          '快取策略實施'
        ];
        break;
        
      case 'security':
        analysisPrompt = '檢查以下代碼的安全性問題：';
        taskSuggestions = [
          '輸入驗證加強',
          '權限檢查完善',
          '敏感資料保護',
          'SQL 注入防護'
        ];
        break;
    }

    // 簡化的代碼分析（實際應該調用 AI 模型）
    const codeLines = code.split('\n').length;
    const hasAsync = code.includes('async');
    const hasDatabase = code.includes('Repository') || code.includes('DbContext');
    const hasValidation = code.includes('Validate') || code.includes('IsValid');

    let taskText = `# 🔍 代碼分析結果 (${analysis_type})\n\n`;
    taskText += `## 📊 代碼概況\n`;
    taskText += `- 代碼行數: ${codeLines}\n`;
    taskText += `- 包含異步操作: ${hasAsync ? '是' : '否'}\n`;
    taskText += `- 涉及數據庫操作: ${hasDatabase ? '是' : '否'}\n`;
    taskText += `- 包含驗證邏輯: ${hasValidation ? '是' : '否'}\n\n`;

    taskText += `## 💡 建議任務\n`;
    
    // 根據分析類型和代碼特徵生成具體任務
    const specificTasks = [];
    
    if (analysis_type === 'test' && hasAsync) {
      specificTasks.push({
        title: '異步方法單元測試',
        description: '為代碼中的異步方法添加完整的單元測試，包括成功和失敗情況',
        priority: 'high',
        estimated_hours: 4
      });
    }
    
    if (analysis_type === 'optimize' && hasDatabase) {
      specificTasks.push({
        title: '數據庫查詢優化',
        description: '檢查並優化數據庫查詢性能，考慮添加索引或改進查詢邏輯',
        priority: 'medium',
        estimated_hours: 6
      });
    }

    if (analysis_type === 'security' && !hasValidation) {
      specificTasks.push({
        title: '輸入驗證實施',
        description: '為方法參數添加適當的驗證邏輯，防止無效輸入',
        priority: 'high',
        estimated_hours: 3
      });
    }

    // 如果沒有特定任務，使用通用建議
    if (specificTasks.length === 0) {
      specificTasks.push({
        title: `${analysis_type} 改進`,
        description: `對分析的代碼進行 ${analysis_type} 相關的改進`,
        priority: 'medium',
        estimated_hours: 8
      });
    }

    specificTasks.forEach((task, index) => {
      taskText += `### 🎯 任務 ${index + 1}: ${task.title}\n`;
      taskText += `- **描述**: ${task.description}\n`;
      taskText += `- **優先級**: ${task.priority}\n`;
      taskText += `- **預估工時**: ${task.estimated_hours}h\n`;
      taskText += `- **類型**: ${analysis_type}\n\n`;
    });

    return {
      content: [{
        type: 'text',
        text: taskText
      }]
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
  }
}

// 如果直接運行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new TaskManagerMCPServer();
  server.run().catch(console.error);
}

export default TaskManagerMCPServer;