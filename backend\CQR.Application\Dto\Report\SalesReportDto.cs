﻿namespace CQR.Application.Dto.Report;
public class SalesReportDto
{
    public string? QueueKey { get; set; }
    public string? CQRNumber { get; set; }
    public string? FolderStatus { get; set; }
    public string? QuoteType { get; set; }
    public string? QuoteDescription { get; set; }
    public string? CQRDescription { get; set; }
    public string? VehicleBuildID { get; set; }
    public string? ProductDescription { get; set; }
    public string? RegionalQuotingTeam { get; set; }
    public string? OEMGroup { get; set; }
    public string? Platform { get; set; }
    public decimal? VolumePerAnnum { get; set; }
    public decimal? ApproxAnnualValue { get; set; }
    public DateTime? ReleaseDate { get; set; }
    public DateTime? CustQuoteDueDate { get; set; }
    public DateTime? SOPDate { get; set; }
    public string? ManufacturingSite { get; set; }
    public string? BackgroundInformation { get; set; }
    public string? SalesAccountManager { get; set; }
    public string? BusinessPlan { get; set; }
    public DateTime? Gateway2ApprovalDate { get; set; }
    public string? WinLossStatus { get; set; }
    public string? WinLossComments { get; set; }
    public DateTime? DateWinLoss { get; set; }
}
