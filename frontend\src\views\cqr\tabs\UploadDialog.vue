<template>
  <el-dialog
    v-model="visible"
    title="上傳檔案（可多選）"
    width="600px"
    :before-close="handleClose"
  >
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      drag
      action=""
      multiple
      :http-request="onUploadRequest"
      :file-list="fileList"
      :auto-upload="false"
      :on-change="onFileChange"
      :limit="10"
      :on-exceed="onExceed"
      :show-file-list="true"
    >
      <i class="el-icon-upload" />
      <div class="el-upload__text">
        拖曳檔案至此或 <em>點擊選擇檔案</em>（最多 10 個）
      </div>
    </el-upload>

    <template #footer>
      <el-button @click="store.close()">取消</el-button>
      <el-button
        type="primary"
        :disabled="!fileList.length"
        @click="submitUpload"
      >
        確認上傳
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { useUploadDialogStore } from "@/store/modules/uploadDialog";
import { uploadAttachment } from "@/api/cqr";
const store = useUploadDialogStore();
import type {
  UploadUserFile,
  UploadRequestOptions,
  UploadProgressEvent
} from "element-plus";

const visible = ref(true);
const fileList = ref<UploadUserFile[]>([]);

const handleUpload = async (options: UploadRequestOptions) => {
  const formData = new FormData();
  formData.append("file", options.file);

  const xhr = new XMLHttpRequest();
  xhr.open("POST", "/api/upload", true);

  // 顯示進度條
  xhr.upload.onprogress = (event: ProgressEvent) => {
    if (event.lengthComputable) {
      const percent = Math.round((event.loaded * 100) / event.total);
      options.onProgress?.({ percent } as UploadProgressEvent);
    }
  };

  xhr.onload = () => {
    if (xhr.status >= 200 && xhr.status < 300) {
      options.onSuccess?.(xhr.response, options.file);
    } else {
      options.onError?.(new Error("Upload failed"));
    }
  };

  xhr.onerror = () => {
    options.onError?.(new Error("Upload failed"));
  };

  xhr.send(formData);
};

const onFileChange = (_file: UploadUserFile, files: UploadUserFile[]) => {
  fileList.value = files;
};

const onExceed = () => {
  ElMessage.warning("最多只能選擇 10 個檔案");
};

// const submitUpload = () => {
//   const uploader = document.querySelector(".upload-demo") as any;
//   uploader?.submit?.(); // 觸發 <el-upload> 上的所有檔案上傳
// };
const uploadRef = ref();

// 1️⃣ 自定義上傳請求：Element Plus 會呼叫它，傳入 UploadRequestOptions
const onUploadRequest = async (options: UploadRequestOptions) => {
  const file = options.file as File;
  try {
    let queueKey = 19625;
    const response = await uploadAttachment(queueKey, file);
    options.onSuccess?.(response, file);
  } catch (error) {
    options.onError?.(error as Error);
  }
};

const submitUpload = () => {
  const uploader = uploadRef.value;
  uploader?.submit();
};

const handleClose = () => {
  visible.value = false;
  fileList.value = [];
};
</script>

<style scoped>
.upload-demo {
  margin: 20px 0;
}
</style>
