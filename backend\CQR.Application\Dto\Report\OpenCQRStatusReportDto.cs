﻿namespace CQR.Application.Dto.Report;
public class OpenCQRStatusReportDto
{
    public string? QueueKey { get; set; }
    public string? CQRNumber { get; set; }
    public string? CBMName { get; set; }
    public string? SalesDirectorName { get; set; }
    public string? AccountManagerName { get; set; }
    public string? CustomerName { get; set; }
    public string? StatusName { get; set; }
    public string? FRANDesc { get; set; }
    public string? BkRndInfComments { get; set; }
    public string? ProductDesc { get; set; }
    public DateTime? OriginationDate { get; set; }
    public DateTime? CustQuoteDueDate { get; set; }
    public DateTime? DueDateToBnE { get; set; }
    public DateTime? DueDateFromEng { get; set; }
    public string? QuoteType { get; set; }
}
