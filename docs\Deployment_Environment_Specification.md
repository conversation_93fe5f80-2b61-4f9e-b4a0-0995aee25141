# CQR 系統部署與環境規格書

## 文檔資訊

| 項目 | 內容 |
|------|------|
| 文檔標題 | CQR 系統部署與環境規格書 |
| 版本 | 2.0 |
| 建立日期 | 2025-07-22 |
| 最後更新 | 2025-07-22 |
| 文檔狀態 | 草案 |
| 作者 | DevOps 團隊 |

## 目錄

1. [部署架構概述](#部署架構概述)
2. [環境規劃](#環境規劃)
3. [基礎設施要求](#基礎設施要求)
4. [Docker 容器化](#docker-容器化)
5. [CI/CD 流程](#cicd-流程)
6. [監控與日誌](#監控與日誌)
7. [安全配置](#安全配置)
8. [災難復原](#災難復原)
9. [運維管理](#運維管理)
10. [故障排除](#故障排除)

---

## 部署架構概述

### 1.1 整體架構

```
┌─────────────────────────────────────────────────────────────────┐
│                         CQR 生產環境架構                        │
├─────────────────────────────────────────────────────────────────┤
│                       負載均衡器 (Azure Load Balancer)          │
├─────────────────────────────────────────────────────────────────┤
│  Web 伺服器層 (nginx + 反向代理)                                │
│  ┌─────────────────────┐  ┌─────────────────────┐                │
│  │   Web Server 1      │  │   Web Server 2      │                │
│  │   - nginx           │  │   - nginx           │                │
│  │   - SSL 終端        │  │   - SSL 終端        │                │
│  │   - 靜態資源        │  │   - 靜態資源        │                │
│  └─────────────────────┘  └─────────────────────┘                │
├─────────────────────────────────────────────────────────────────┤
│  應用伺服器層 (ASP.NET Core API)                                 │
│  ┌─────────────────────┐  ┌─────────────────────┐                │
│  │   API Server 1      │  │   API Server 2      │                │
│  │   - ASP.NET Core    │  │   - ASP.NET Core    │                │
│  │   - Docker 容器     │  │   - Docker 容器     │                │
│  │   - 健康檢查        │  │   - 健康檢查        │                │
│  └─────────────────────┘  └─────────────────────┘                │
├─────────────────────────────────────────────────────────────────┤
│  資料層                                                          │
│  ┌─────────────────────┐  ┌─────────────────────┐                │
│  │   SQL Server        │  │   Redis Cache       │                │
│  │   - 主資料庫        │  │   - Session 儲存    │                │
│  │   - 讀寫分離        │  │   - 應用快取        │                │
│  │   - 自動備份        │  │   - 高可用性配置    │                │
│  └─────────────────────┘  └─────────────────────┘                │
├─────────────────────────────────────────────────────────────────┤
│  外部服務                                                        │
│  ┌─────────────────────┐  ┌─────────────────────┐                │
│  │   Azure AD          │  │   SAP RFC Server    │                │
│  │   - 身份認證        │  │   - ERP 整合        │                │
│  │   - 單點登入        │  │   - 資料同步        │                │
│  └─────────────────────┘  └─────────────────────┘                │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 部署模式

#### 雲端部署 (推薦)

**Azure 雲端架構**:
- **Azure App Service**: 應用程式託管
- **Azure SQL Database**: 資料庫服務
- **Azure Redis Cache**: 快取服務
- **Azure Storage**: 檔案存儲
- **Azure Load Balancer**: 負載均衡
- **Azure Application Gateway**: WAF + SSL
- **Azure DevOps**: CI/CD 流水線

#### 混合部署

**本地 + 雲端**:
- **本地**: SQL Server (主資料庫) + SAP 系統
- **雲端**: 應用程式 + 快取 + 檔案存儲

#### 完全本地部署

**內部資料中心**:
- **VMware/Hyper-V**: 虛擬化平台
- **Windows Server 2022**: 作業系統
- **IIS 10.0**: Web 伺服器
- **SQL Server 2019+**: 資料庫
- **Redis**: 快取服務

---

## 環境規劃

### 2.1 環境分離

#### 開發環境 (Development)

```yaml
環境目的: 開發人員日常開發測試
資源配置: 
  - 單一伺服器
  - 2 CPU cores, 4GB RAM
  - 100GB SSD 存儲
資料庫: SQL Server Express LocalDB
快取: 內建記憶體快取
域名: https://dev.cqr.company.com
```

#### 測試環境 (Testing/QA)

```yaml
環境目的: 功能測試、整合測試、用戶驗收測試
資源配置:
  - Web Server: 2 CPU cores, 4GB RAM
  - API Server: 4 CPU cores, 8GB RAM  
  - DB Server: 2 CPU cores, 8GB RAM
資料庫: SQL Server Standard (測試資料)
快取: Redis (單節點)
域名: https://test.cqr.company.com
```

#### 預發布環境 (Staging)

```yaml
環境目的: 生產前最後驗證，與生產環境配置相同
資源配置:
  - 與生產環境相同配置的縮小版
  - Web Server: 2 instances (2C/4G each)
  - API Server: 2 instances (4C/8G each)
  - DB Server: 4 CPU cores, 16GB RAM
資料庫: SQL Server Standard (生產資料的副本)
快取: Redis (高可用配置)
域名: https://staging.cqr.company.com
```

#### 生產環境 (Production)

```yaml
環境目的: 正式生產運行
資源配置:
  - Load Balancer: 2 instances (HA)
  - Web Server: 3 instances (4C/8G each)
  - API Server: 4 instances (8C/16G each)
  - DB Server: 8 CPU cores, 32GB RAM (HA)
  - Cache Server: Redis Cluster (3 master + 3 slave)
資料庫: SQL Server Enterprise (Always On)
域名: https://api.cqr.company.com
```

### 2.2 網路規劃

#### 網路分段

```
DMZ 區域 (對外服務):
├── Load Balancer (Public IP)
├── Web Servers (Private IP)
└── Application Gateway

應用區域 (內部服務):
├── API Servers
├── Application Servers
└── File Servers

資料區域 (資料服務):
├── SQL Server Cluster
├── Redis Cluster
└── Backup Servers

管理區域 (維運服務):
├── Jump Server
├── Monitoring Server
└── Log Server
```

#### 防火牆規則

| 來源 | 目標 | 埠號 | 協議 | 用途 |
|------|------|------|------|------|
| Internet | Load Balancer | 443 | HTTPS | Web 存取 |
| Load Balancer | Web Servers | 80 | HTTP | 內部轉發 |
| Web Servers | API Servers | 5000 | HTTP | API 調用 |
| API Servers | SQL Server | 1433 | TCP | 資料庫連線 |
| API Servers | Redis | 6379 | TCP | 快取存取 |
| API Servers | SAP Server | 3300 | RFC | SAP 整合 |

### 2.3 域名與 SSL 配置

#### 域名規劃

```
生產環境:
├── cqr.company.com (主站點)
├── api.cqr.company.com (API 服務)
└── files.cqr.company.com (檔案服務)

測試環境:
├── test.cqr.company.com
├── test-api.cqr.company.com
└── test-files.cqr.company.com
```

#### SSL 憑證

- **生產環境**: EV SSL 憑證 (Extended Validation)
- **測試環境**: DV SSL 憑證 (Domain Validation)
- **憑證管理**: 自動續訂 (Let's Encrypt 或 Azure Certificate)

---

## 基礎設施要求

### 3.1 硬體規格

#### 生產環境最低需求

**Web 伺服器**:
```yaml
CPU: Intel Xeon 8 cores @ 2.4GHz
RAM: 16GB DDR4
Storage: 500GB SSD (OS + App)
Network: 1Gbps
Redundancy: 負載均衡 (至少 2 台)
```

**API 伺服器**:
```yaml
CPU: Intel Xeon 16 cores @ 2.4GHz
RAM: 32GB DDR4
Storage: 1TB SSD (OS + App + Logs)
Network: 1Gbps
Redundancy: 負載均衡 (至少 3 台)
```

**資料庫伺服器**:
```yaml
CPU: Intel Xeon 24 cores @ 2.4GHz
RAM: 64GB DDR4
Storage: 
  - OS: 500GB SSD
  - Data: 2TB NVMe SSD (RAID 1)
  - Log: 1TB SSD (RAID 1)
  - TempDB: 500GB SSD
Network: 10Gbps
Redundancy: SQL Server Always On (主 + 副本)
```

**快取伺服器**:
```yaml
CPU: Intel Xeon 8 cores @ 2.4GHz
RAM: 32GB DDR4 (主要用於快取)
Storage: 250GB SSD
Network: 1Gbps
Redundancy: Redis Cluster (3 master + 3 replica)
```

### 3.2 軟體需求

#### 作業系統

**Windows Server**:
```yaml
版本: Windows Server 2022 Standard/Datacenter
更新: 最新累積更新
功能: 
  - IIS 10.0
  - .NET 8.0 Runtime
  - Windows Features
```

**Linux (替代選項)**:
```yaml
版本: Ubuntu Server 22.04 LTS 或 CentOS 8
容器: Docker 20.10+
編排: Kubernetes 1.25+ (可選)
```

#### 資料庫

**SQL Server**:
```yaml
版本: SQL Server 2019 Enterprise 或更新版本
功能:
  - Always On 可用性群組
  - 透明資料加密 (TDE)
  - 備份壓縮
  - 查詢存放區
```

#### Web 伺服器

**IIS (Windows)**:
```yaml
版本: IIS 10.0
模組:
  - ASP.NET Core Module
  - URL Rewrite
  - Application Request Routing
  - HTTP Compression
```

**nginx (Linux)**:
```yaml
版本: nginx 1.20+
模組:
  - HTTP SSL module
  - HTTP V2 module
  - HTTP RealIP module
  - HTTP Gzip module
```

### 3.3 監控和管理工具

#### 系統監控

```yaml
Application Performance:
  - Application Insights (Azure)
  - 或 New Relic / DataDog

Infrastructure Monitoring:
  - Azure Monitor
  - 或 Prometheus + Grafana

Log Management:
  - Azure Log Analytics
  - 或 ELK Stack (Elasticsearch, Logstash, Kibana)

Database Monitoring:
  - SQL Server Management Studio
  - Azure SQL Analytics
  - 或 SolarWinds Database Performance Analyzer
```

---

## Docker 容器化

### 4.1 容器架構

#### 微服務分解

```yaml
cqr-web:
  - nginx + Vue.js 前端應用
  - 靜態資源服務

cqr-api:
  - ASP.NET Core Web API
  - 業務邏輯處理

cqr-worker:
  - 背景服務處理
  - 排程任務執行

cqr-proxy:
  - nginx 反向代理
  - SSL 終端處理
```

### 4.2 Dockerfile 範例

#### 前端 Dockerfile

```dockerfile
# 前端建構階段
FROM node:18-alpine AS builder

WORKDIR /app
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ .
RUN npm run build

# 生產階段
FROM nginx:alpine

# 複製自定義 nginx 配置
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf /etc/nginx/conf.d/default.conf

# 複製建構的應用
COPY --from=builder /app/dist /usr/share/nginx/html

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### 後端 API Dockerfile

```dockerfile
# 建構階段
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS builder

WORKDIR /src

# 複製專案檔案並還原 NuGet 套件
COPY backend/*.sln ./
COPY backend/*/*.csproj ./
RUN for file in $(ls *.csproj); do mkdir -p ${file%.*}/ && mv $file ${file%.*}/; done
RUN dotnet restore

# 複製源碼並建構
COPY backend/ .
RUN dotnet publish CQR.API/CQR.API.csproj -c Release -o /app/publish

# 運行階段
FROM mcr.microsoft.com/dotnet/aspnet:8.0

# 安裝必要的系統套件
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 建立非 root 用戶
RUN groupadd -r cqrapp && useradd -r -g cqrapp cqrapp

WORKDIR /app

# 複製發布的應用程式
COPY --from=builder /app/publish .

# 設定權限
RUN chown -R cqrapp:cqrapp /app
USER cqrapp

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

EXPOSE 5000

ENTRYPOINT ["dotnet", "CQR.API.dll"]
```

### 4.3 Docker Compose 配置

#### 開發環境

```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  cqr-db:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=Dev@Password123
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - cqr-db-data:/var/opt/mssql
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P Dev@Password123 -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 3

  cqr-redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - cqr-redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  cqr-api:
    build:
      context: .
      dockerfile: backend/Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=cqr-db;Database=CQRDev;User Id=sa;Password=Dev@Password123;TrustServerCertificate=true
      - Redis__Configuration=cqr-redis:6379
    ports:
      - "5000:5000"
    depends_on:
      cqr-db:
        condition: service_healthy
      cqr-redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  cqr-web:
    build:
      context: .
      dockerfile: frontend/Dockerfile
      target: development
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:5000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm run dev

volumes:
  cqr-db-data:
  cqr-redis-data:
```

#### 生產環境

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  cqr-proxy:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - cqr-web
      - cqr-api
    restart: unless-stopped

  cqr-web:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  cqr-api:
    build:
      context: .
      dockerfile: backend/Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=${SQL_CONNECTION_STRING}
      - Redis__Configuration=${REDIS_CONNECTION_STRING}
      - Azure__KeyVault__Uri=${AZURE_KEYVAULT_URI}
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  cqr-worker:
    build:
      context: .
      dockerfile: backend/Dockerfile.worker
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=${SQL_CONNECTION_STRING}
      - Redis__Configuration=${REDIS_CONNECTION_STRING}
    restart: unless-stopped

networks:
  default:
    driver: bridge
```

### 4.4 Kubernetes 部署

#### Deployment 配置

```yaml
# k8s/cqr-api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cqr-api
  namespace: cqr-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cqr-api
  template:
    metadata:
      labels:
        app: cqr-api
    spec:
      containers:
      - name: cqr-api
        image: cqr/api:latest
        ports:
        - containerPort: 5000
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: cqr-secrets
              key: sql-connection-string
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: cqr-api-service
  namespace: cqr-system
spec:
  selector:
    app: cqr-api
  ports:
    - protocol: TCP
      port: 80
      targetPort: 5000
  type: ClusterIP
```

#### Ingress 配置

```yaml
# k8s/cqr-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cqr-ingress
  namespace: cqr-system
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.cqr.company.com
    - cqr.company.com
    secretName: cqr-tls-secret
  rules:
  - host: api.cqr.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cqr-api-service
            port:
              number: 80
  - host: cqr.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cqr-web-service
            port:
              number: 80
```

---

## CI/CD 流程

### 5.1 Azure DevOps Pipeline

#### Build Pipeline

```yaml
# azure-pipelines.yml
trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - backend/*
    - frontend/*

variables:
  - group: CQR-Variables
  - name: dockerRegistryServiceConnection
    value: 'cqr-acr-connection'
  - name: imageRepository
    value: 'cqr'
  - name: containerRegistry
    value: 'cqrregistry.azurecr.io'
  - name: dockerfilePath
    value: '$(Build.SourcesDirectory)/Dockerfile'
  - name: tag
    value: '$(Build.BuildId)'

stages:
- stage: Build
  displayName: Build and Test
  jobs:
  - job: BuildBackend
    displayName: Build Backend
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - task: UseDotNet@2
      displayName: 'Use .NET 8 SDK'
      inputs:
        packageType: 'sdk'
        version: '8.0.x'

    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet packages'
      inputs:
        command: 'restore'
        projects: 'backend/**/*.csproj'

    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: 'backend/**/*.csproj'
        arguments: '--configuration Release --no-restore'

    - task: DotNetCoreCLI@2
      displayName: 'Run unit tests'
      inputs:
        command: 'test'
        projects: 'backend/**/*Tests.csproj'
        arguments: '--configuration Release --no-build --collect:"XPlat Code Coverage"'

    - task: PublishCodeCoverageResults@1
      displayName: 'Publish code coverage'
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Agent.TempDirectory)/*/coverage.cobertura.xml'

  - job: BuildFrontend
    displayName: Build Frontend
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - task: NodeTool@0
      displayName: 'Use Node.js 18'
      inputs:
        versionSpec: '18.x'

    - task: Npm@1
      displayName: 'npm install'
      inputs:
        command: 'install'
        workingDir: 'frontend'

    - task: Npm@1
      displayName: 'npm run lint'
      inputs:
        command: 'custom'
        customCommand: 'run lint'
        workingDir: 'frontend'

    - task: Npm@1
      displayName: 'npm run test'
      inputs:
        command: 'custom'
        customCommand: 'run test:unit'
        workingDir: 'frontend'

    - task: PublishTestResults@2
      displayName: 'Publish test results'
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: 'frontend/tests/unit/junit.xml'

    - task: Npm@1
      displayName: 'npm run build'
      inputs:
        command: 'custom'
        customCommand: 'run build'
        workingDir: 'frontend'

    - task: PublishBuildArtifacts@1
      displayName: 'Publish frontend artifacts'
      inputs:
        pathToPublish: 'frontend/dist'
        artifactName: 'frontend-dist'

- stage: BuildImages
  displayName: Build Docker Images
  dependsOn: Build
  jobs:
  - job: BuildAPIImage
    displayName: Build API Docker Image
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - task: Docker@2
      displayName: 'Build and push API image'
      inputs:
        command: 'buildAndPush'
        repository: '$(imageRepository)/api'
        dockerfile: 'backend/Dockerfile'
        containerRegistry: '$(dockerRegistryServiceConnection)'
        tags: |
          $(tag)
          latest

  - job: BuildWebImage
    displayName: Build Web Docker Image
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - task: Docker@2
      displayName: 'Build and push Web image'
      inputs:
        command: 'buildAndPush'
        repository: '$(imageRepository)/web'
        dockerfile: 'frontend/Dockerfile'
        containerRegistry: '$(dockerRegistryServiceConnection)'
        tags: |
          $(tag)
          latest
```

#### Release Pipeline

```yaml
# azure-pipelines-release.yml
trigger: none

resources:
- pipeline: BuildPipeline
  source: CQR-Build
  trigger:
    branches:
      include:
      - main

variables:
- group: CQR-Production-Variables

stages:
- stage: DeployToStaging
  displayName: Deploy to Staging
  jobs:
  - deployment: DeployStaging
    displayName: Deploy to Staging Environment
    environment: 'CQR-Staging'
    pool:
      vmImage: 'ubuntu-latest'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebAppContainer@1
            displayName: 'Deploy API to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appName: 'cqr-api-staging'
              containers: '$(containerRegistry)/cqr/api:$(resources.pipeline.BuildPipeline.runID)'

          - task: AzureWebAppContainer@1
            displayName: 'Deploy Web to Azure App Service'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appName: 'cqr-web-staging'
              containers: '$(containerRegistry)/cqr/web:$(resources.pipeline.BuildPipeline.runID)'

          - task: AzureCLI@2
            displayName: 'Run database migrations'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az webapp config connection-string set \
                  --resource-group $(resourceGroup) \
                  --name cqr-api-staging \
                  --connection-string-type SQLAzure \
                  --settings DefaultConnection="$(stagingConnectionString)"

- stage: DeployToProduction
  displayName: Deploy to Production
  dependsOn: DeployToStaging
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployProduction
    displayName: Deploy to Production Environment
    environment: 'CQR-Production'
    pool:
      vmImage: 'ubuntu-latest'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureKeyVault@2
            displayName: 'Get secrets from Key Vault'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              keyVaultName: 'cqr-keyvault-prod'
              secretsFilter: '*'

          - task: AzureWebAppContainer@1
            displayName: 'Deploy API to Production'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appName: 'cqr-api-prod'
              containers: '$(containerRegistry)/cqr/api:$(resources.pipeline.BuildPipeline.runID)'

          - task: AzureWebAppContainer@1
            displayName: 'Deploy Web to Production'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appName: 'cqr-web-prod'
              containers: '$(containerRegistry)/cqr/web:$(resources.pipeline.BuildPipeline.runID)'
```

### 5.2 GitHub Actions (替代方案)

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      sql-server:
        image: mcr.microsoft.com/mssql/server:2019-latest
        env:
          ACCEPT_EULA: Y
          SA_PASSWORD: Test@Password123
        options: >-
          --health-cmd "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P Test@Password123 -Q 'SELECT 1'"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore backend/
    
    - name: Build
      run: dotnet build backend/ --no-restore
    
    - name: Test
      run: dotnet test backend/ --no-build --verbosity normal --collect:"XPlat Code Coverage"
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        files: ./coverage.xml

  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: npm ci
      working-directory: frontend
    
    - name: Lint
      run: npm run lint
      working-directory: frontend
    
    - name: Test
      run: npm run test:unit
      working-directory: frontend
    
    - name: Build
      run: npm run build
      working-directory: frontend

  build-and-push:
    needs: [test-backend, test-frontend]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha
    
    - name: Build and push API image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: backend/Dockerfile
        push: true
        tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/api:${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
    
    - name: Build and push Web image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: frontend/Dockerfile
        push: true
        tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/web:${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # 部署邏輯

  deploy-production:
    needs: deploy-staging
    runs-on: ubuntu-latest
    environment: production
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # 生產部署邏輯
```

### 5.3 部署腳本

#### PowerShell 部署腳本

```powershell
# deploy.ps1
param(
    [Parameter(Mandatory=$true)]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$Version,
    
    [string]$ResourceGroup = "CQR-RG",
    [string]$SubscriptionId
)

# 設定錯誤處理
$ErrorActionPreference = "Stop"

# 登入 Azure (如果需要)
if (-not (Get-AzContext)) {
    Connect-AzAccount -SubscriptionId $SubscriptionId
}

# 設定變數
$webAppName = "cqr-web-$Environment"
$apiAppName = "cqr-api-$Environment"
$containerRegistry = "cqrregistry.azurecr.io"

Write-Host "開始部署到 $Environment 環境..." -ForegroundColor Green

try {
    # 部署 API
    Write-Host "部署 API 應用程式..." -ForegroundColor Yellow
    Set-AzWebApp -ResourceGroupName $ResourceGroup -Name $apiAppName -DockerImageName "$containerRegistry/cqr/api:$Version"
    
    # 等待 API 啟動
    Write-Host "等待 API 啟動..." -ForegroundColor Yellow
    do {
        Start-Sleep -Seconds 10
        $apiStatus = Invoke-RestMethod -Uri "https://$apiAppName.azurewebsites.net/health" -Method GET -TimeoutSec 30
        Write-Host "API 狀態: $($apiStatus.status)" -ForegroundColor Cyan
    } while ($apiStatus.status -ne "healthy")
    
    # 部署 Web
    Write-Host "部署 Web 應用程式..." -ForegroundColor Yellow
    Set-AzWebApp -ResourceGroupName $ResourceGroup -Name $webAppName -DockerImageName "$containerRegistry/cqr/web:$Version"
    
    # 驗證部署
    Write-Host "驗證部署..." -ForegroundColor Yellow
    $webStatus = Invoke-RestMethod -Uri "https://$webAppName.azurewebsites.net/health" -Method GET -TimeoutSec 30
    
    if ($webStatus.status -eq "healthy") {
        Write-Host "部署成功完成！" -ForegroundColor Green
        Write-Host "Web App: https://$webAppName.azurewebsites.net" -ForegroundColor Cyan
        Write-Host "API App: https://$apiAppName.azurewebsites.net" -ForegroundColor Cyan
    } else {
        throw "部署驗證失敗"
    }
}
catch {
    Write-Error "部署失敗: $($_.Exception.Message)"
    exit 1
}
```

#### Bash 部署腳本

```bash
#!/bin/bash
# deploy.sh

set -e

ENVIRONMENT=$1
VERSION=$2
RESOURCE_GROUP="CQR-RG"

if [[ -z "$ENVIRONMENT" || -z "$VERSION" ]]; then
    echo "Usage: $0 <environment> <version>"
    echo "Example: $0 staging v1.0.0"
    exit 1
fi

echo "🚀 開始部署到 $ENVIRONMENT 環境 (版本: $VERSION)"

# 設定變數
WEB_APP_NAME="cqr-web-$ENVIRONMENT"
API_APP_NAME="cqr-api-$ENVIRONMENT"
CONTAINER_REGISTRY="cqrregistry.azurecr.io"

# 函數：檢查健康狀態
check_health() {
    local url=$1
    local max_attempts=30
    local attempt=1
    
    echo "⏳ 檢查 $url 健康狀態..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url/health" > /dev/null; then
            echo "✅ 服務健康檢查通過"
            return 0
        fi
        
        echo "🔄 嘗試 $attempt/$max_attempts - 等待服務啟動..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    echo "❌ 健康檢查失敗"
    return 1
}

# 部署 API
echo "📦 部署 API 應用程式..."
az webapp config container set \
    --resource-group $RESOURCE_GROUP \
    --name $API_APP_NAME \
    --docker-custom-image-name "$CONTAINER_REGISTRY/cqr/api:$VERSION"

# 檢查 API 健康狀態
check_health "https://$API_APP_NAME.azurewebsites.net"

# 部署 Web
echo "🌐 部署 Web 應用程式..."
az webapp config container set \
    --resource-group $RESOURCE_GROUP \
    --name $WEB_APP_NAME \
    --docker-custom-image-name "$CONTAINER_REGISTRY/cqr/web:$VERSION"

# 檢查 Web 健康狀態
check_health "https://$WEB_APP_NAME.azurewebsites.net"

echo "🎉 部署成功完成！"
echo "🔗 Web App: https://$WEB_APP_NAME.azurewebsites.net"
echo "🔗 API App: https://$API_APP_NAME.azurewebsites.net"
```

---

## 監控與日誌

### 6.1 應用程式監控

#### Application Insights 配置

```csharp
// Program.cs
builder.Services.AddApplicationInsightsTelemetry(options =>
{
    options.ConnectionString = builder.Configuration.GetConnectionString("ApplicationInsights");
    options.EnableAdaptiveSampling = true;
    options.EnableQuickPulseMetricStream = true;
});

// 自定義遙測初始化器
builder.Services.AddSingleton<ITelemetryInitializer, CustomTelemetryInitializer>();
```

```csharp
// CustomTelemetryInitializer.cs
public class CustomTelemetryInitializer : ITelemetryInitializer
{
    public void Initialize(ITelemetry telemetry)
    {
        if (telemetry is RequestTelemetry requestTelemetry)
        {
            // 添加自定義屬性
            requestTelemetry.Properties["Environment"] = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            requestTelemetry.Properties["Version"] = Assembly.GetExecutingAssembly().GetName().Version?.ToString();
        }
    }
}
```

#### 健康檢查配置

```csharp
// Program.cs
builder.Services.AddHealthChecks()
    .AddCheck("self", () => HealthCheckResult.Healthy())
    .AddSqlServer(
        connectionString: builder.Configuration.GetConnectionString("DefaultConnection"),
        name: "sql-server",
        tags: new[] { "db", "sql", "sqlserver" })
    .AddRedis(
        connectionString: builder.Configuration.GetConnectionString("Redis"),
        name: "redis",
        tags: new[] { "cache", "redis" })
    .AddCheck<SapHealthCheck>("sap-rfc", tags: new[] { "external", "sap" })
    .AddApplicationInsightsPublisher();

app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.MapHealthChecks("/health/ready", new HealthCheckOptions
{
    Predicate = check => check.Tags.Contains("ready")
});

app.MapHealthChecks("/health/live", new HealthCheckOptions
{
    Predicate = _ => false
});
```

### 6.2 結構化日誌

#### Serilog 配置

```csharp
// Program.cs
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information)
    .Enrich.FromLogContext()
    .Enrich.WithProperty("Environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"))
    .Enrich.WithProperty("Application", "CQR.API")
    .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
    .WriteTo.ApplicationInsights(TelemetryConfiguration.CreateDefault(), TelemetryConverter.Traces)
    .WriteTo.File(
        path: "logs/cqr-.txt",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30,
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
    .CreateLogger();

builder.Host.UseSerilog();
```

#### 日誌中介軟體

```csharp
// LoggingMiddleware.cs
public class LoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<LoggingMiddleware> _logger;

    public LoggingMiddleware(RequestDelegate next, ILogger<LoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var correlationId = Guid.NewGuid().ToString();
        context.Items["CorrelationId"] = correlationId;

        using (_logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = correlationId,
            ["UserId"] = context.User.Identity?.Name ?? "Anonymous",
            ["UserAgent"] = context.Request.Headers["User-Agent"].ToString(),
            ["IPAddress"] = context.Connection.RemoteIpAddress?.ToString()
        }))
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("開始處理請求 {Method} {Path}",
                    context.Request.Method,
                    context.Request.Path);

                await _next(context);

                stopwatch.Stop();

                _logger.LogInformation("完成請求處理 {Method} {Path} - 狀態碼: {StatusCode}, 耗時: {ElapsedMs}ms",
                    context.Request.Method,
                    context.Request.Path,
                    context.Response.StatusCode,
                    stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                _logger.LogError(ex, "請求處理異常 {Method} {Path} - 耗時: {ElapsedMs}ms",
                    context.Request.Method,
                    context.Request.Path,
                    stopwatch.ElapsedMilliseconds);

                throw;
            }
        }
    }
}
```

### 6.3 監控儀表板

#### Grafana Dashboard 配置

```json
{
  "dashboard": {
    "id": null,
    "title": "CQR System Monitoring",
    "tags": ["cqr", "production"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
            "legendFormat": "95th Percentile"
          },
          {
            "expr": "histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
            "legendFormat": "50th Percentile"
          }
        ]
      },
      {
        "id": 2,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(http_requests_total[5m]))",
            "legendFormat": "Total Requests/sec"
          }
        ]
      },
      {
        "id": 3,
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) / sum(rate(http_requests_total[5m]))",
            "legendFormat": "5xx Error Rate"
          }
        ]
      },
      {
        "id": 4,
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "sql_server_connections_active",
            "legendFormat": "Active Connections"
          }
        ]
      }
    ]
  }
}
```

### 6.4 告警規則

#### Azure Monitor 告警

```json
{
  "name": "CQR API High Error Rate",
  "description": "API 錯誤率超過 5%",
  "severity": 1,
  "enabled": true,
  "condition": {
    "allOf": [
      {
        "metricName": "requests/failed",
        "metricNamespace": "microsoft.insights/components",
        "operator": "GreaterThan",
        "threshold": 0.05,
        "timeAggregation": "Average"
      }
    ]
  },
  "actions": [
    {
      "actionGroupId": "/subscriptions/xxx/resourceGroups/CQR-RG/providers/microsoft.insights/actionGroups/CQR-Alerts"
    }
  ],
  "windowSize": "PT5M",
  "evaluationFrequency": "PT1M"
}
```

#### Prometheus 告警規則

```yaml
# alerts.yml
groups:
- name: cqr-system
  rules:
  - alert: HighErrorRate
    expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) > 0.05
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "CQR API 錯誤率過高"
      description: "錯誤率 {{ $value | humanizePercentage }} 超過 5% 閾值"

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le)) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "CQR API 響應時間過長"
      description: "95th 百分位響應時間 {{ $value }}s 超過 2s 閾值"

  - alert: DatabaseConnectionsHigh
    expr: sql_server_connections_active > 80
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "資料庫連接數過高"
      description: "活動連接數 {{ $value }} 超過 80 個"
```

---

## 安全配置

### 7.1 網路安全

#### 防火牆規則

```bash
# Azure 網路安全群組規則
az network nsg rule create \
  --resource-group CQR-RG \
  --nsg-name CQR-NSG \
  --name Allow-HTTPS \
  --protocol Tcp \
  --priority 100 \
  --destination-port-range 443 \
  --source-address-prefixes Internet \
  --destination-address-prefixes VirtualNetwork \
  --access Allow

az network nsg rule create \
  --resource-group CQR-RG \
  --nsg-name CQR-NSG \
  --name Allow-HTTP-Redirect \
  --protocol Tcp \
  --priority 110 \
  --destination-port-range 80 \
  --source-address-prefixes Internet \
  --destination-address-prefixes VirtualNetwork \
  --access Allow

az network nsg rule create \
  --resource-group CQR-RG \
  --nsg-name CQR-NSG \
  --name Deny-All-Inbound \
  --protocol '*' \
  --priority 4000 \
  --source-address-prefixes '*' \
  --destination-address-prefixes '*' \
  --access Deny
```

#### WAF 規則

```json
{
  "customRules": [
    {
      "name": "RateLimitRule",
      "priority": 1,
      "ruleType": "RateLimitRule",
      "rateLimitThreshold": 100,
      "rateLimitDurationInMinutes": 1,
      "matchConditions": [
        {
          "matchVariables": [
            {
              "variableName": "RemoteAddr"
            }
          ],
          "operator": "IPMatch",
          "matchValues": ["*"]
        }
      ],
      "action": "Block"
    },
    {
      "name": "SQLInjectionRule",
      "priority": 2,
      "ruleType": "MatchRule",
      "matchConditions": [
        {
          "matchVariables": [
            {
              "variableName": "QueryString"
            }
          ],
          "operator": "Contains",
          "matchValues": ["union select", "' or 1=1", "'; drop table"]
        }
      ],
      "action": "Block"
    }
  ]
}
```

### 7.2 應用程式安全

#### HTTPS 強制重定向

```csharp
// Program.cs
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
    app.UseHsts();
}

app.UseSecurityHeaders(policies =>
    policies
        .AddFrameOptionsDeny()
        .AddContentTypeOptionsNoSniff()
        .AddReferrerPolicyStrictOriginWhenCrossOrigin()
        .AddCrossSiteScriptingProtection()
        .AddContentSecurityPolicy(builder =>
        {
            builder.AddObjectSrc().None();
            builder.AddFormAction().Self();
            builder.AddFrameAncestors().None();
            builder.AddScriptSrc().Self().UnsafeInline();
            builder.AddStyleSrc().Self().UnsafeInline();
        }));
```

#### API 金鑰管理

```csharp
// Startup.cs
builder.Services.AddAuthentication("ApiKey")
    .AddScheme<ApiKeyAuthenticationSchemeOptions, ApiKeyAuthenticationHandler>("ApiKey", options =>
    {
        options.ApiKeyHeaderName = "X-API-Key";
    });

// API Key 驗證處理器
public class ApiKeyAuthenticationHandler : AuthenticationHandler<ApiKeyAuthenticationSchemeOptions>
{
    private readonly IConfiguration _configuration;

    public ApiKeyAuthenticationHandler(IOptionsMonitor<ApiKeyAuthenticationSchemeOptions> options,
        ILoggerFactory logger, UrlEncoder encoder, ISystemClock clock, IConfiguration configuration)
        : base(options, logger, encoder, clock)
    {
        _configuration = configuration;
    }

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        if (!Request.Headers.TryGetValue(Options.ApiKeyHeaderName, out var apiKeyHeaderValues))
        {
            return Task.FromResult(AuthenticateResult.NoResult());
        }

        var providedApiKey = apiKeyHeaderValues.FirstOrDefault();
        if (string.IsNullOrWhiteSpace(providedApiKey))
        {
            return Task.FromResult(AuthenticateResult.NoResult());
        }

        var validApiKeys = _configuration.GetSection("ApiKeys").Get<string[]>();
        if (validApiKeys?.Contains(providedApiKey) == true)
        {
            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, "api-user"),
                new Claim(ClaimTypes.Name, "API User")
            };

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return Task.FromResult(AuthenticateResult.Success(ticket));
        }

        return Task.FromResult(AuthenticateResult.Fail("Invalid API Key"));
    }
}
```

### 7.3 資料保護

#### 敏感資料加密

```csharp
// 資料加密服務
public interface IDataProtectionService
{
    string Encrypt(string plaintext);
    string Decrypt(string ciphertext);
}

public class DataProtectionService : IDataProtectionService
{
    private readonly IDataProtector _protector;

    public DataProtectionService(IDataProtectionProvider provider)
    {
        _protector = provider.CreateProtector("CQR.DataProtection.v1");
    }

    public string Encrypt(string plaintext)
    {
        return _protector.Protect(plaintext);
    }

    public string Decrypt(string ciphertext)
    {
        return _protector.Unprotect(ciphertext);
    }
}
```

#### Key Vault 整合

```csharp
// Program.cs
if (!builder.Environment.IsDevelopment())
{
    var keyVaultUrl = builder.Configuration["Azure:KeyVault:Uri"];
    if (!string.IsNullOrEmpty(keyVaultUrl))
    {
        builder.Configuration.AddAzureKeyVault(
            new Uri(keyVaultUrl),
            new DefaultAzureCredential());
    }
}
```

### 7.4 稽核日誌

```csharp
// AuditLogMiddleware.cs
public class AuditLogMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IAuditLogger _auditLogger;

    public async Task InvokeAsync(HttpContext context)
    {
        var auditLog = new AuditLog
        {
            UserId = context.User.Identity?.Name,
            Action = $"{context.Request.Method} {context.Request.Path}",
            IPAddress = context.Connection.RemoteIpAddress?.ToString(),
            UserAgent = context.Request.Headers["User-Agent"],
            Timestamp = DateTime.UtcNow
        };

        try
        {
            await _next(context);
            auditLog.Success = context.Response.StatusCode < 400;
        }
        catch (Exception ex)
        {
            auditLog.Success = false;
            auditLog.Error = ex.Message;
            throw;
        }
        finally
        {
            await _auditLogger.LogAsync(auditLog);
        }
    }
}
```

---

## 災難復原

### 8.1 備份策略

#### 資料庫備份

```sql
-- 完整備份腳本
BACKUP DATABASE [CQRProduction] 
TO DISK = 'D:\Backup\CQRProduction_Full_$(Date).bak'
WITH 
    COMPRESSION,
    CHECKSUM,
    INIT,
    STATS = 10;

-- 差異備份腳本  
BACKUP DATABASE [CQRProduction] 
TO DISK = 'D:\Backup\CQRProduction_Diff_$(Date)_$(Time).bak'
WITH 
    DIFFERENTIAL,
    COMPRESSION,
    CHECKSUM,
    INIT,
    STATS = 10;

-- 交易記錄備份腳本
BACKUP LOG [CQRProduction] 
TO DISK = 'D:\Backup\CQRProduction_Log_$(Date)_$(Time).trn'
WITH 
    COMPRESSION,
    CHECKSUM,
    INIT;
```

#### 自動備份排程

```powershell
# BackupScript.ps1
param(
    [Parameter(Mandatory=$true)]
    [string]$DatabaseName,
    [string]$BackupPath = "D:\Backup",
    [string]$RetentionDays = "30"
)

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupFile = "$BackupPath\${DatabaseName}_Full_$timestamp.bak"

# 執行備份
$query = @"
BACKUP DATABASE [$DatabaseName] 
TO DISK = '$backupFile'
WITH COMPRESSION, CHECKSUM, INIT, STATS = 10;
"@

Invoke-Sqlcmd -Query $query -ServerInstance $env:COMPUTERNAME

# 清理舊備份
Get-ChildItem -Path $BackupPath -Name "*.bak" | 
Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-$RetentionDays) } |
Remove-Item -Force

Write-Host "備份完成: $backupFile" -ForegroundColor Green
```

### 8.2 高可用性配置

#### SQL Server Always On

```sql
-- 創建可用性群組
CREATE AVAILABILITY GROUP [CQR-AG]
WITH (
    AUTOMATED_BACKUP_PREFERENCE = SECONDARY,
    FAILURE_CONDITION_LEVEL = 3,
    HEALTH_CHECK_TIMEOUT = 30000
)
FOR DATABASE [CQRProduction]
REPLICA ON 
'SQL-PRIMARY' WITH (
    ENDPOINT_URL = 'TCP://sql-primary.company.com:5022',
    AVAILABILITY_MODE = SYNCHRONOUS_COMMIT,
    FAILOVER_MODE = AUTOMATIC,
    BACKUP_PRIORITY = 30,
    SECONDARY_ROLE(ALLOW_CONNECTIONS = NO)
),
'SQL-SECONDARY' WITH (
    ENDPOINT_URL = 'TCP://sql-secondary.company.com:5022', 
    AVAILABILITY_MODE = ASYNCHRONOUS_COMMIT,
    FAILOVER_MODE = MANUAL,
    BACKUP_PRIORITY = 90,
    SECONDARY_ROLE(ALLOW_CONNECTIONS = READ_ONLY)
);
```

#### Redis 高可用性

```conf
# redis-master.conf
port 6379
bind 0.0.0.0
protected-mode yes
requirepass "YourStrongPassword123"
appendonly yes
appendfsync everysec

# Redis Sentinel 配置
# sentinel.conf
port 26379
sentinel monitor cqr-master 192.168.1.100 6379 2
sentinel auth-pass cqr-master "YourStrongPassword123"
sentinel down-after-milliseconds cqr-master 5000
sentinel failover-timeout cqr-master 10000
sentinel parallel-syncs cqr-master 1
```

### 8.3 災難復原計劃

#### RTO/RPO 目標

| 服務層級 | RTO (復原時間目標) | RPO (復原點目標) | 備份頻率 |
|---------|-------------------|------------------|----------|
| 關鍵業務 | < 1 小時 | < 15 分鐘 | 實時同步 |
| 重要業務 | < 4 小時 | < 1 小時 | 每小時 |
| 一般業務 | < 24 小時 | < 4 小時 | 每日 |

#### 災難復原步驟

```powershell
# DisasterRecovery.ps1
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Database", "Application", "Full")]
    [string]$RecoveryType,
    
    [string]$BackupPath = "\\backup-server\CQR\",
    [string]$RecoveryPoint = "Latest"
)

Write-Host "開始災難復原程序..." -ForegroundColor Yellow

switch ($RecoveryType) {
    "Database" {
        Write-Host "復原資料庫..." -ForegroundColor Green
        
        # 1. 停止應用服務
        Stop-Service -Name "CQR-API" -Force
        
        # 2. 復原資料庫
        $latestBackup = Get-ChildItem -Path $BackupPath -Filter "*.bak" | 
                       Sort-Object LastWriteTime -Descending | 
                       Select-Object -First 1
        
        $restoreQuery = @"
        RESTORE DATABASE [CQRProduction] 
        FROM DISK = '$($latestBackup.FullName)'
        WITH REPLACE, STATS = 10;
        "@
        
        Invoke-Sqlcmd -Query $restoreQuery
        
        # 3. 重啟應用服務
        Start-Service -Name "CQR-API"
        
        Write-Host "資料庫復原完成" -ForegroundColor Green
    }
    
    "Application" {
        Write-Host "復原應用程式..." -ForegroundColor Green
        
        # 1. 部署最新的應用程式版本
        & "$PSScriptRoot\deploy.ps1" -Environment "Production" -Version "Latest"
        
        Write-Host "應用程式復原完成" -ForegroundColor Green
    }
    
    "Full" {
        Write-Host "執行完整災難復原..." -ForegroundColor Green
        
        # 按順序執行資料庫和應用程式復原
        & $MyInvocation.MyCommand.Path -RecoveryType "Database"
        & $MyInvocation.MyCommand.Path -RecoveryType "Application"
        
        Write-Host "完整災難復原完成" -ForegroundColor Green
    }
}

# 驗證服務狀態
Write-Host "驗證服務狀態..." -ForegroundColor Yellow

$healthCheck = Invoke-RestMethod -Uri "https://api.cqr.company.com/health" -Method GET -TimeoutSec 30
if ($healthCheck.status -eq "healthy") {
    Write-Host "✅ 系統復原成功，所有服務正常運行" -ForegroundColor Green
} else {
    Write-Host "❌ 系統復原完成，但健康檢查失敗" -ForegroundColor Red
}
```

---

## 運維管理

### 9.1 運維自動化

#### 系統監控腳本

```powershell
# SystemMonitor.ps1
param(
    [string]$ConfigFile = "monitor-config.json"
)

# 讀取配置
$config = Get-Content $ConfigFile | ConvertFrom-Json

# 監控指標
$metrics = @{
    "CPU使用率" = (Get-Counter "\Processor(_Total)\% Processor Time").CounterSamples.CookedValue
    "記憶體使用率" = [math]::Round((Get-Counter "\Memory\% Committed Bytes In Use").CounterSamples.CookedValue, 2)
    "磁碟使用率" = [math]::Round((Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'").Size / 1GB, 2)
}

# 檢查服務狀態
$services = @("CQR-API", "CQR-Web", "MSSQLSERVER", "Redis")
$serviceStatus = @{}

foreach ($service in $services) {
    $status = Get-Service -Name $service -ErrorAction SilentlyContinue
    $serviceStatus[$service] = if ($status) { $status.Status } else { "Not Found" }
}

# 檢查網站可用性
$websites = $config.websites
$websiteStatus = @{}

foreach ($site in $websites) {
    try {
        $response = Invoke-WebRequest -Uri $site.url -TimeoutSec 10 -UseBasicParsing
        $websiteStatus[$site.name] = @{
            "Status" = "Online"
            "ResponseTime" = $response.Headers["Response-Time"]
            "StatusCode" = $response.StatusCode
        }
    } catch {
        $websiteStatus[$site.name] = @{
            "Status" = "Offline"
            "Error" = $_.Exception.Message
        }
    }
}

# 生成報告
$report = @{
    "Timestamp" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "SystemMetrics" = $metrics
    "ServiceStatus" = $serviceStatus
    "WebsiteStatus" = $websiteStatus
}

# 輸出報告
$report | ConvertTo-Json -Depth 3 | Out-File -FilePath "monitor-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"

# 發送告警（如果需要）
foreach ($metric in $metrics.GetEnumerator()) {
    $threshold = $config.thresholds.($metric.Key)
    if ($threshold -and $metric.Value -gt $threshold) {
        Send-Alert -Type "SystemMetric" -Message "$($metric.Key) 超過閾值: $($metric.Value)% (閾值: $threshold%)"
    }
}
```

#### 自動擴展腳本

```bash
#!/bin/bash
# auto-scale.sh

RESOURCE_GROUP="CQR-RG"
APP_SERVICE_PLAN="CQR-Plan"
SCALE_UP_THRESHOLD=80
SCALE_DOWN_THRESHOLD=30
MIN_INSTANCES=2
MAX_INSTANCES=10

# 獲取當前指標
get_cpu_usage() {
    az monitor metrics list \
        --resource "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/serverfarms/$APP_SERVICE_PLAN" \
        --metric "CpuPercentage" \
        --interval PT5M \
        --query "value[0].timeseries[0].data[-1].average" \
        --output tsv
}

get_current_instances() {
    az appservice plan show \
        --resource-group $RESOURCE_GROUP \
        --name $APP_SERVICE_PLAN \
        --query "sku.capacity" \
        --output tsv
}

# 擴展邏輯
current_cpu=$(get_cpu_usage)
current_instances=$(get_current_instances)

echo "當前 CPU 使用率: ${current_cpu}%"
echo "當前實例數: $current_instances"

if (( $(echo "$current_cpu > $SCALE_UP_THRESHOLD" | bc -l) )) && (( current_instances < MAX_INSTANCES )); then
    new_instances=$((current_instances + 1))
    echo "擴展到 $new_instances 個實例"
    
    az appservice plan update \
        --resource-group $RESOURCE_GROUP \
        --name $APP_SERVICE_PLAN \
        --number-of-workers $new_instances
        
elif (( $(echo "$current_cpu < $SCALE_DOWN_THRESHOLD" | bc -l) )) && (( current_instances > MIN_INSTANCES )); then
    new_instances=$((current_instances - 1))
    echo "縮減到 $new_instances 個實例"
    
    az appservice plan update \
        --resource-group $RESOURCE_GROUP \
        --name $APP_SERVICE_PLAN \
        --number-of-workers $new_instances
else
    echo "不需要擴展"
fi
```

### 9.2 維護任務

#### 資料庫維護

```sql
-- DatabaseMaintenance.sql
-- 重建索引
DECLARE @TableName NVARCHAR(255)
DECLARE @SQL NVARCHAR(MAX)
DECLARE table_cursor CURSOR FOR
SELECT name FROM sys.tables WHERE type = 'U'

OPEN table_cursor
FETCH NEXT FROM table_cursor INTO @TableName

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @SQL = 'ALTER INDEX ALL ON [' + @TableName + '] REBUILD'
    EXEC sp_executesql @SQL
    PRINT '重建索引: ' + @TableName
    
    FETCH NEXT FROM table_cursor INTO @TableName
END

CLOSE table_cursor
DEALLOCATE table_cursor

-- 更新統計資料
EXEC sp_updatestats

-- 清理查詢計劃快取
DBCC FREEPROCCACHE

-- 檢查資料庫一致性
DBCC CHECKDB('CQRProduction') WITH NO_INFOMSGS

PRINT '資料庫維護完成: ' + CONVERT(VARCHAR, GETDATE(), 120)
```

#### 日誌清理

```powershell
# LogCleanup.ps1
param(
    [string]$LogPath = "C:\Logs\CQR",
    [int]$RetentionDays = 30,
    [string]$ArchivePath = "\\archive-server\CQR-Logs"
)

$cutoffDate = (Get-Date).AddDays(-$RetentionDays)
$oldLogs = Get-ChildItem -Path $LogPath -Recurse -File | Where-Object { $_.LastWriteTime -lt $cutoffDate }

Write-Host "找到 $($oldLogs.Count) 個過期日誌文件" -ForegroundColor Yellow

# 壓縮並歸檔
if ($oldLogs.Count -gt 0) {
    $archiveFileName = "CQR-Logs-$(Get-Date -Format 'yyyyMMdd').zip"
    $archiveFullPath = Join-Path $ArchivePath $archiveFileName
    
    # 創建歸檔目錄（如果不存在）
    if (!(Test-Path $ArchivePath)) {
        New-Item -Path $ArchivePath -ItemType Directory -Force
    }
    
    # 壓縮舊日誌
    Compress-Archive -Path $oldLogs.FullName -DestinationPath $archiveFullPath -CompressionLevel Optimal
    Write-Host "日誌已歸檔到: $archiveFullPath" -ForegroundColor Green
    
    # 刪除原始文件
    $oldLogs | Remove-Item -Force
    Write-Host "已刪除 $($oldLogs.Count) 個舊日誌文件" -ForegroundColor Green
}

# 清理 IIS 日誌
$iisLogPath = "C:\inetpub\logs\LogFiles"
if (Test-Path $iisLogPath) {
    $oldIISLogs = Get-ChildItem -Path $iisLogPath -Recurse -File -Filter "*.log" | 
                  Where-Object { $_.LastWriteTime -lt $cutoffDate }
    
    if ($oldIISLogs.Count -gt 0) {
        $oldIISLogs | Remove-Item -Force
        Write-Host "已清理 $($oldIISLogs.Count) 個 IIS 日誌文件" -ForegroundColor Green
    }
}

Write-Host "日誌清理完成" -ForegroundColor Green
```

### 9.3 性能調優

#### SQL Server 調優

```sql
-- Performance Tuning Scripts

-- 1. 找出耗時查詢
SELECT TOP 10
    qs.execution_count,
    qs.total_elapsed_time / 1000 as total_elapsed_time_ms,
    qs.total_elapsed_time / qs.execution_count / 1000 as avg_elapsed_time_ms,
    qs.total_worker_time / 1000 as total_worker_time_ms,
    qs.total_worker_time / qs.execution_count / 1000 as avg_worker_time_ms,
    SUBSTRING(qt.text, (qs.statement_start_offset/2)+1,
        ((CASE qs.statement_end_offset
            WHEN -1 THEN DATALENGTH(qt.text)
            ELSE qs.statement_end_offset
        END - qs.statement_start_offset)/2)+1) as statement_text
FROM sys.dm_exec_query_stats qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) qt
ORDER BY qs.total_elapsed_time DESC

-- 2. 找出缺失的索引
SELECT 
    migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) AS improvement_measure,
    'CREATE INDEX [IX_' + OBJECT_NAME(mid.object_id, mid.database_id) + '_' 
    + REPLACE(REPLACE(REPLACE(ISNULL(mid.equality_columns,''), ', ', '_'), '[', ''), ']', '') 
    + CASE WHEN mid.inequality_columns IS NOT NULL 
        THEN '_' + REPLACE(REPLACE(REPLACE(mid.inequality_columns, ', ', '_'), '[', ''), ']', '') 
        ELSE '' END + ']'
    + ' ON ' + mid.statement + ' (' + ISNULL(mid.equality_columns,'')
    + CASE WHEN mid.equality_columns IS NOT NULL 
        AND mid.inequality_columns IS NOT NULL THEN ',' ELSE '' END
    + ISNULL(mid.inequality_columns, '') + ')'
    + ISNULL(' INCLUDE (' + mid.included_columns + ')', '') AS create_index_statement
FROM sys.dm_db_missing_index_groups mig
INNER JOIN sys.dm_db_missing_index_group_stats migs ON migs.group_handle = mig.index_group_handle
INNER JOIN sys.dm_db_missing_index_details mid ON mig.index_handle = mid.index_handle
WHERE migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) > 10
ORDER BY migs.avg_total_user_cost * migs.avg_user_impact * (migs.user_seeks + migs.user_scans) DESC

-- 3. 資料庫配置優化
-- 設定最大記憶體 (留 2GB 給作業系統)
DECLARE @MaxMemoryMB INT = (SELECT (physical_memory_kb / 1024) - 2048 FROM sys.dm_os_sys_info)
EXEC sp_configure 'max server memory (MB)', @MaxMemoryMB
RECONFIGURE

-- 啟用查詢存放區
ALTER DATABASE CURRENT SET QUERY_STORE = ON 
    (OPERATION_MODE = READ_WRITE, 
     DATA_FLUSH_INTERVAL_SECONDS = 900,
     INTERVAL_LENGTH_MINUTES = 60,
     MAX_STORAGE_SIZE_MB = 1000,
     QUERY_CAPTURE_MODE = AUTO)
```

---

## 故障排除

### 10.1 常見問題診斷

#### 應用程式無法啟動

```powershell
# TroubleshootStartup.ps1
Write-Host "=== CQR 系統啟動故障診斷 ===" -ForegroundColor Yellow

# 1. 檢查服務狀態
Write-Host "檢查 Windows 服務狀態..." -ForegroundColor Cyan
$services = @("CQR-API", "MSSQLSERVER", "Redis")
foreach ($service in $services) {
    $status = Get-Service -Name $service -ErrorAction SilentlyContinue
    if ($status) {
        Write-Host "$service`: $($status.Status)" -ForegroundColor $(if($status.Status -eq 'Running'){'Green'}else{'Red'})
    } else {
        Write-Host "$service`: 服務不存在" -ForegroundColor Red
    }
}

# 2. 檢查連接埠占用
Write-Host "`n檢查連接埠占用..." -ForegroundColor Cyan
$ports = @(80, 443, 5000, 1433, 6379)
foreach ($port in $ports) {
    $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($connection) {
        $process = Get-Process -Id $connection.OwningProcess -ErrorAction SilentlyContinue
        Write-Host "Port $port`: 被程序 $($process.ProcessName) (PID: $($process.Id)) 占用" -ForegroundColor Yellow
    } else {
        Write-Host "Port $port`: 可用" -ForegroundColor Green
    }
}

# 3. 檢查資料庫連線
Write-Host "`n檢查資料庫連線..." -ForegroundColor Cyan
try {
    $connectionString = "Server=localhost;Database=CQRProduction;Integrated Security=true;TrustServerCertificate=true"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    Write-Host "資料庫連線: 成功" -ForegroundColor Green
    $connection.Close()
} catch {
    Write-Host "資料庫連線: 失敗 - $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 檢查磁碟空間
Write-Host "`n檢查磁碟空間..." -ForegroundColor Cyan
$drives = Get-WmiObject -Class Win32_LogicalDisk -Filter "DriveType=3"
foreach ($drive in $drives) {
    $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
    $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
    $freePercent = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)
    
    $color = if ($freePercent -lt 10) { 'Red' } elseif ($freePercent -lt 20) { 'Yellow' } else { 'Green' }
    Write-Host "$($drive.DeviceID) $freeSpaceGB GB / $totalSpaceGB GB 可用 ($freePercent%)" -ForegroundColor $color
}

# 5. 檢查事件日誌
Write-Host "`n檢查事件日誌 (最近 1 小時的錯誤)..." -ForegroundColor Cyan
$startTime = (Get-Date).AddHours(-1)
$errorEvents = Get-WinEvent -FilterHashtable @{LogName='Application'; Level=2; StartTime=$startTime} -MaxEvents 10 -ErrorAction SilentlyContinue

if ($errorEvents) {
    foreach ($event in $errorEvents) {
        Write-Host "$($event.TimeCreated): $($event.LevelDisplayName) - $($event.Message.Substring(0, [Math]::Min(100, $event.Message.Length)))..." -ForegroundColor Red
    }
} else {
    Write-Host "沒有發現錯誤事件" -ForegroundColor Green
}

Write-Host "`n=== 診斷完成 ===" -ForegroundColor Yellow
```

#### 性能問題診斷

```sql
-- PerformanceDiagnosis.sql
-- 檢查當前運行的查詢
SELECT 
    s.session_id,
    r.status,
    r.command,
    s.login_name,
    s.host_name,
    r.start_time,
    r.cpu_time,
    r.total_elapsed_time / 1000 as elapsed_seconds,
    r.reads,
    r.writes,
    r.logical_reads,
    t.text AS query_text
FROM sys.dm_exec_requests r
INNER JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
WHERE s.is_user_process = 1
  AND r.status NOT IN ('sleeping', 'background')
ORDER BY r.total_elapsed_time DESC

-- 檢查阻塞情況
SELECT 
    blocking.session_id AS blocking_session,
    blocked.session_id AS blocked_session,
    blocking_sql.text AS blocking_query,
    blocked_sql.text AS blocked_query,
    blocked.wait_type,
    blocked.wait_time,
    blocked.wait_resource
FROM sys.dm_exec_requests blocked
INNER JOIN sys.dm_exec_requests blocking ON blocked.blocking_session_id = blocking.session_id
CROSS APPLY sys.dm_exec_sql_text(blocking.sql_handle) blocking_sql
CROSS APPLY sys.dm_exec_sql_text(blocked.sql_handle) blocked_sql

-- 記憶體使用狀況
SELECT 
    object_name,
    counter_name,
    cntr_value
FROM sys.dm_os_performance_counters 
WHERE counter_name IN ('Buffer cache hit ratio', 'Page life expectancy', 'Memory Grants Pending')

-- I/O 統計
SELECT 
    db_name(database_id) as database_name,
    file_id,
    num_of_reads,
    num_of_writes,
    io_stall_read_ms,
    io_stall_write_ms,
    io_stall / (num_of_reads + num_of_writes) as avg_io_stall_ms
FROM sys.dm_io_virtual_file_stats(null, null)
WHERE num_of_reads > 0 OR num_of_writes > 0
ORDER BY io_stall DESC
```

### 10.2 故障恢復流程

#### 緊急恢復流程

```mermaid
graph TD
    A[發現故障] --> B{服務類型}
    B -->|Web應用| C[檢查IIS/nginx]
    B -->|API服務| D[檢查應用程式池]
    B -->|資料庫| E[檢查SQL Server]
    B -->|網路| F[檢查網路連線]
    
    C --> G[重啟Web服務]
    D --> H[重啟應用程式池]
    E --> I[檢查資料庫狀態]
    F --> J[檢查防火牆規則]
    
    G --> K{服務恢復?}
    H --> K
    I --> K
    J --> K
    
    K -->|是| L[監控服務狀態]
    K -->|否| M[執行災難復原]
    
    M --> N[從備份復原]
    N --> O[驗證系統功能]
    O --> L
    
    L --> P[記錄故障報告]
```

#### 故障響應檢查清單

```markdown
# 故障響應檢查清單

## 立即響應 (0-15 分鐘)
- [ ] 確認故障範圍和影響
- [ ] 通知相關人員和用戶
- [ ] 檢查系統監控儀表板
- [ ] 執行基本診斷步驟
- [ ] 記錄故障開始時間

## 初步診斷 (15-30 分鐘)
- [ ] 檢查服務和程序狀態
- [ ] 查看系統日誌和錯誤訊息
- [ ] 檢查資源使用情況 (CPU、記憶體、磁碟)
- [ ] 檢查網路連線和DNS解析
- [ ] 檢查資料庫連線和狀態

## 問題解決 (30-60 分鐘)
- [ ] 根據診斷結果執行修復措施
- [ ] 如需要，執行服務重啟
- [ ] 如問題嚴重，考慮執行災難復原
- [ ] 持續監控系統狀態

## 恢復後驗證 (60-90 分鐘)
- [ ] 驗證所有核心功能正常
- [ ] 檢查資料完整性
- [ ] 確認用戶可以正常存取
- [ ] 更新監控儀表板狀態

## 後續跟進 (90 分鐘後)
- [ ] 編寫故障報告
- [ ] 分析根本原因
- [ ] 制定預防措施
- [ ] 更新運維文檔和程序
```

### 10.3 故障預防

#### 健康檢查和監控

```csharp
// HealthCheckExtensions.cs
public static class HealthCheckExtensions
{
    public static IServiceCollection AddCQRHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHealthChecks()
            .AddCheck("self", () => HealthCheckResult.Healthy())
            .AddSqlServer(
                connectionString: configuration.GetConnectionString("DefaultConnection"),
                healthQuery: "SELECT 1",
                name: "sql-server",
                failureStatus: HealthStatus.Unhealthy,
                tags: new[] { "db", "sql", "sqlserver" })
            .AddRedis(
                connectionString: configuration.GetConnectionString("Redis"),
                name: "redis",
                failureStatus: HealthStatus.Unhealthy,
                tags: new[] { "cache", "redis" })
            .AddCheck<ApiEndpointHealthCheck>("api-endpoints", tags: new[] { "api" })
            .AddCheck<DiskSpaceHealthCheck>("disk-space", tags: new[] { "storage" })
            .AddCheck<SapConnectionHealthCheck>("sap-connection", tags: new[] { "external" });

        return services;
    }
}

// 自定義健康檢查
public class DiskSpaceHealthCheck : IHealthCheck
{
    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var drive = new DriveInfo("C:");
        var freeSpacePercent = (double)drive.AvailableFreeSpace / drive.TotalSize * 100;

        if (freeSpacePercent < 5)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy($"磁碟空間不足: {freeSpacePercent:F1}%"));
        }
        else if (freeSpacePercent < 15)
        {
            return Task.FromResult(HealthCheckResult.Degraded($"磁碟空間偏低: {freeSpacePercent:F1}%"));
        }

        return Task.FromResult(HealthCheckResult.Healthy($"磁碟空間充足: {freeSpacePercent:F1}%"));
    }
}
```

這份完整的 CQR 系統部署與環境規格書涵蓋了從基礎架構規劃到故障排除的各個方面。它為系統的成功部署、運行和維護提供了詳細的指導方針和最佳實踐。

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"content": "\u5206\u6790\u73fe\u6709\u7cfb\u7d71\u67b6\u69cb\u548c\u4ee3\u78bc\u7d50\u69cb", "status": "completed", "priority": "high", "id": "1"}, {"content": "\u5275\u5efa\u7cfb\u7d71\u67b6\u69cb\u898f\u683c\u66f8", "status": "completed", "priority": "high", "id": "2"}, {"content": "\u7de8\u5beb\u5f8c\u7aef ASP.NET Core API \u898f\u683c", "status": "completed", "priority": "high", "id": "3"}, {"content": "\u7de8\u5beb\u524d\u7aef Vue3 + Pinia \u898f\u683c", "status": "completed", "priority": "high", "id": "4"}, {"content": "\u5275\u5efa\u8cc7\u6599\u5eab\u8a2d\u8a08\u898f\u683c", "status": "completed", "priority": "high", "id": "5"}, {"content": "\u7de8\u5beb API \u63a5\u53e3\u898f\u683c", "status": "completed", "priority": "high", "id": "6"}, {"content": "\u5275\u5efa\u90e8\u7f72\u548c\u74b0\u5883\u898f\u683c", "status": "completed", "priority": "high", "id": "7"}]