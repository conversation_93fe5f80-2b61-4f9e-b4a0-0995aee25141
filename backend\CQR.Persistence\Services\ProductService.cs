//using CQRLIB.Dtos;
//using CQRLIB.Exceptions;
//using CQRLIB.Models;
//using CQRLIB.Persistence;
//using CQRLIB.Services.Interface;
//using Microsoft.EntityFrameworkCore;

//namespace CQRLIB.Services.Implementations
//{
//    public class ProductService : IProductService
//    {
//        private readonly IProductRepository _productRepository;
//        private readonly IProductDetailRepository _productDetailRepository;
//        private readonly CQrDbContext _context;
//        public ProductService(IProductRepository productRepository,
//                                    IProductDetailRepository productDetailRepository,
//                                    CQrDbContext context)
//        {
//            _productRepository = productRepository;
//            _productDetailRepository = productDetailRepository;
//            _context = context;
//        }

//        public async Task AddProductAsync(Product product)
//        {
//            await _productRepository.AddAsync(product);   // 添加产品
//            await _productRepository.SaveChangesAsync();  // 自动提交事务
//        }

//        public async Task CreateProductWithDetails(ProductWithDetailsDto productWithDetailsDto)
//        {
//            using (var transaction = await _context.Database.BeginTransactionAsync())
//            {
//                try
//                {
//                    await _productRepository.AddAsync(productWithDetailsDto.Product);
//                    await _productRepository.SaveChangesAsync();

//                    productWithDetailsDto.ProductDetail.ProductId = productWithDetailsDto.Product.Id;
//                    await _productDetailRepository.AddAsync(productWithDetailsDto.ProductDetail);
//                    await _productDetailRepository.SaveChangesAsync();

//                    await transaction.CommitAsync(); // 提交事务
//                }
//                catch (Exception ex)
//                {
//                    await transaction.RollbackAsync(); // 回滚事务
//                    throw; // 重新抛出异常
//                }
//            }
//        }

//        public async Task DeleteProductAsync(int productId)
//        {
//            Product entity = await GetProductByIdAsync(productId);
//            _productRepository.Delete(entity);
//        }

//        public async Task<Product> GetProductByIdAsync(int productId)
//        {
//            return await _productRepository.GetByIdAsync(productId);
//        }

//        public async Task<int> GetProductCountAsync()
//        {
//            return await _productRepository.CountAsync();
//        }

//        public async Task<IEnumerable<Product>> GetProductsAsync()
//        {
//            return await _productRepository.GetAllAsync();
//        }

//        public async Task RemoveProductWithDetails(int productId)
//        {
//            using (var transaction = await _context.Database.BeginTransactionAsync())
//            {
//                try
//                {
//                    var product = await _productRepository.GetByIdAsync(productId);
//                    if (product == null) throw new NotFoundException($"Product is empty: {productId}");

//                    if (product == null) throw new("Product ");
//                    var productDetail = await _productDetailRepository.GetByIdAsync(productId);

//                    _productRepository.Delete(product);
//                    _productDetailRepository.Delete(productDetail);

//                    await _productRepository.SaveChangesAsync();
//                    await _productDetailRepository.SaveChangesAsync();

//                    await transaction.CommitAsync(); // 提交事务
//                }
//                catch (Exception ex)
//                {
//                    await transaction.RollbackAsync(); // 回滚事务
//                    throw; // 重新抛出异常
//                }
//            }
//        }

//        public async Task UpdateProductAsync(Product product)
//        {
//            _productRepository.Update(product);
//        }
//    }
//}