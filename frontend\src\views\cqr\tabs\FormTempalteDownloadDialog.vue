<template>
  <el-dialog v-model="fileStore.isDialogVisible" title="Add Form" width="400px">
    <span>Please select a form:</span>

    <el-select
      v-model="fileStore.selectedFile"
      placeholder="Select file"
      style="width: 100%; margin-top: 10px"
    >
      <el-option
        v-for="file in fileStore.files"
        :key="file"
        :label="file"
        :value="file"
      />
    </el-select>

    <template #footer>
      <el-button @click="fileStore.closeDialog">Cancel</el-button>
      <el-button
        type="primary"
        :disabled="!fileStore.selectedFile"
        @click="downloadFile"
      >
        Download
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useFileStore } from "@/store/modules/formFileTemplateDownloadDialog";

const visible = ref(true);
const selectedFile = ref("");
const fileStore = useFileStore();

const close = () => {
  visible.value = false;
};

// const downloadFile = () => {
//   if (!selectedFile.value) return;
//   const fileName = selectedFile.value;
//   const downloadUrl = `/api/files/download?filename=${encodeURIComponent(fileName)}`;
//   window.open(downloadUrl, "_blank");
//   visible.value = false;
// };
const downloadFile = () => {
  const fileName = fileStore.selectedFile;
  const downloadUrl = `/api/files/download?filename=${encodeURIComponent(fileName)}`;
  window.open(downloadUrl, "_blank");
  fileStore.closeDialog();
};
</script>
