﻿

using Microsoft.AspNetCore.Hosting;

public class ATT_DATA
{
    private readonly IWebHostEnvironment _env;

    public ATT_DATA(IWebHostEnvironment env)
    {
        _env = env;
    }
    public string sDisplay;
    public string sQueueKey;
    public string sQueueKeyForCompare;
    public bool bCreateOnSave;
    public bool bDeleteOnSave;
    public bool bRequiresProcessing;
    public string sAttachType;
    public string sAttachTitle;
    public string sRevLevel;
    public string sExtension;
    public string sSrcFile;
    public string sDstFile;
    public string sUserLoc;
    public string sCreator;
    public string sCreateDate;
    public string sCreateTime;
    public string sCheckoutDate;
    public string sCheckoutUser;
    public string sCheckoutUserDbs;
    public string sCustomData;
    public string sCustomDataDbs;

    public void Copy(ATT_DATA src, string copyExtension = "")
    {
        sDisplay = src.sDisplay;
        sQueueKey = src.sQueueKey;
        sQueueKeyForCompare = src.sQueueKeyForCompare;
        bCreateOnSave = src.bCreateOnSave;
        bDeleteOnSave = src.bDeleteOnSave;
        bRequiresProcessing = src.bRequiresProcessing;
        sAttachType = src.sAttachType;
        sAttachTitle = src.sAttachTitle;
        sRevLevel = src.sRevLevel;
        sExtension = src.sExtension;

        if (src.bCreateOnSave)
        {
            string rootPath = _env.ContentRootPath;  // 對應 ASP.NET 傳統的 APPL_PHYSICAL_PATH
            string zipPath = Path.Combine(rootPath, "Zip");
            //string zipPath = HttpContext.Current.Request.ServerVariables["APPL_PHYSICAL_PATH"]
            //+ "Zip\\";
            int dotIndex = src.sSrcFile.LastIndexOf('.');

            string baseName = (dotIndex >= 0) ? src.sSrcFile.Substring(0, dotIndex) : src.sSrcFile;
            string extension = (dotIndex >= 0) ? src.sSrcFile.Substring(dotIndex) : "";

            string newFileName = $"{baseName}_{copyExtension}{extension}";

            try
            {
                File.Delete(Path.Combine(zipPath, newFileName));
            }
            catch (Exception ex)
            {
                // 可以 log ex.Message，如果需要的話
            }

            File.Copy(Path.Combine(zipPath, src.sSrcFile), Path.Combine(zipPath, newFileName));

            sSrcFile = newFileName;
            sDstFile = newFileName;
        }
        else
        {
            sSrcFile = src.sSrcFile;
            sDstFile = src.sDstFile;
        }

        sUserLoc = src.sUserLoc;
        sCreator = src.sCreator;
        sCreateDate = src.sCreateDate;
        sCreateTime = src.sCreateTime;
        sCheckoutDate = src.sCheckoutDate;
        sCheckoutUser = src.sCheckoutUser;
        sCheckoutUserDbs = src.sCheckoutUserDbs;
        sCustomData = src.sCustomData;
        sCustomDataDbs = src.sCustomDataDbs;
    }
}