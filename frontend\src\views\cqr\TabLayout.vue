<template>
  <el-tabs v-model="activeTab" type="card" :stretch="true">
    <!-- <el-tab-pane label="TabA" name="tabA" /> -->
    <!-- <el-tab-pane label="TabB" name="tabB" /> -->
    <el-tab-pane label="TabA" name="tabA">
      <TabA :queueKey="route.params.queueKey" />
    </el-tab-pane>
    <el-tab-pane label="TabB" name="tabB">
      <TabB :queueKey="route.params.queueKey" />
    </el-tab-pane>
  </el-tabs>

  <router-view />
</template>

<script setup>
import { useRouter, useRoute } from "vue-router";
import { ref, watch, onMounted } from "vue";

import TabA from "@/views/cqr/elTabs/TabA.vue";
import TabB from "@/views/cqr/elTabs/TabB.vue";

const router = useRouter();
const route = useRoute();

// const activeTab = ref(route.path.split("/").pop());

const tabNames = ["tabA", "tabB"];
// const activeTab = ref(route.name?.toLowerCase() || "tabA");

const activeTab = ref("tabA");

// onMounted(() => {
//   if (
//     route.name === "tabLayout" &&
//     !route.path.endsWith("/tabA") && // 沒有在 tabA
//     !route.path.endsWith("/tabB") // 也不是 tabB
//   ) {
//     router.replace({
//       name: "tabA",
//       params: { queueKey: route.params.queueKey }
//     });
//   }
// });

// onMounted(() => {
//   // 如果是剛進來的 tabLayout 路由，且 path 是沒有子路由的，就跳轉到 tabA
//   if (route.name === "tabLayout") {
//     router.replace({
//       name: "tabA",
//       params: { queueKey: route.params.queueKey }
//     });
//   }
// });

// 當 route.name 變動時，更新 activeTab
// watch(
//   () => route.name,
//   newName => {
//     if (newName && tabNames.includes(newName.toLowerCase())) {
//       activeTab.value = newName.toLowerCase();
//     }
//   },
//   { immediate: true }
// );

// // 使用者切換 Tab 時觸發，並導航到對應子路由
// function onTabChange(tabName) {
//   const queueKey = route.params.queueKey;
//   const targetPath = `/TabLayout/${queueKey}/${tabName}`;
//   if (route.path !== targetPath) {
//     router.push(targetPath);
//   }
// }

// onMounted(() => {
//   if (route.name === "tabLayout") {
//     router.replace({
//       name: "tabA",
//       params: { queueKey: route.params.queueKey }
//     });
//   }
// });

// watch(
//   () => route.name,
//   newName => {
//     if (tabNames.includes(newName)) {
//       activeTab.value = newName;
//     }
//   }
// );

// function onTabClick(tab) {
//   const paneName = tab.props?.name; // Element Plus 內部提供的 tab pane name
//   const queueKey = route.params.queueKey;
//   const targetPath = `/TabLayout/${queueKey}/${paneName}`;

//   // router.push({ name: "TabAK" });

//   // ✅ 如果目標路徑跟當前 path 一樣，就不要跳轉
//   if (route.path !== targetPath) {
//     router.push(targetPath);
//   }
// }
</script>
