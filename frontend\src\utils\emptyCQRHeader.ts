import type { CQRIHSFolder } from "@/store/types";

export const emptyCQRIHSFolder: CQRIHSFolder = {
  id: 0,
  queueKey: 0,
  uniqueNumber: -1,
  archived: null,
  archivedByQueueKey: null,
  archivedByUser: null,
  archivedDate: null,
  dateAdded: null,
  dateUpdated: null,
  coreNameplatePlantMnemonic: "",
  region: "",
  country: "",
  platform: "",
  program: "",
  productionNameplate: "",
  startOfProduction: "",
  endOfProduction: "",
  oemGroup: "",
  oem: "",
  productDescription: "",
  productGrouping: null,
  soldFrom: "",
  finalAssembly: ""
  // isNew: true // 新增時的標記
};
