# 使用 .NET SDK 8 作為構建階段的基底映像
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build

# 設定工作目錄
WORKDIR /app

# 複製 .csproj 檔案並還原相依性
COPY *.csproj ./
RUN dotnet restore

# 複製所有檔案並構建應用程式
COPY . ./
RUN dotnet publish -c Release -o out

# 使用 .NET 8 執行環境作為運行階段的基底映像
FROM mcr.microsoft.com/dotnet/aspnet:8.0

# 設定工作目錄
WORKDIR /app

# 複製從建置階段產生的檔案
COPY --from=build /app/out ./

# 開放應用程式的埠號
EXPOSE 80

# 設定執行命令
ENTRYPOINT ["dotnet", "CQRAPI.dll"]
