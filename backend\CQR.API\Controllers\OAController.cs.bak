using CQRLIB.DTO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CQRAPI.Controllers
{

    [ApiController]
    //[AllowAnonymous]
    [Route("EflowService")]
    public class OAController : ControllerBase
    {
        private User user;
        private readonly IHttpContextAccessor _httpContextAccessor;

        private readonly IHttpContextAccessor _contextAccessor;
        // private IBusServicesColleciton busServicesColleciton;

        // public OAController(
        //       IBusServicesColleciton _BusServicesColleciton
        //     , IHttpContextAccessor httpContextAccessor
        //     )
        // {
        //     busServicesColleciton = _BusServicesColleciton;
        //     _httpContextAccessor = httpContextAccessor;
        //     user = HelperUtil.GetHttpContextUserInfo(_httpContextAccessor.HttpContext);
        // }
        //user/OA/plan/for…
        // [AllowAnonymous]
        // [HttpGet]
        // [Route("user/OA/plan/{searchWord}")]
        // public IActionResult SearchUserListByCriterias(string searchWord)
        // {
        //     var result = busServicesColleciton.repositoryCollection.stUserDao.SearchOAUserByCriterias(searchWord);
        //     return Ok(result);
        // }


    }
}
