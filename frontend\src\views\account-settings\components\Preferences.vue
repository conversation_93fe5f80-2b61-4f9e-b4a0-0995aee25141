<script setup lang="ts">
import { computed } from "vue";
import { message } from "@/utils/message";
import { deviceDetection } from "@pureadmin/utils";
import { useUserPreferences } from "@/composables/useUserPreferences";

defineOptions({
  name: "Preferences"
});

const { preferences, updatePreference } = useUserPreferences();

// 計算屬性來簡化偏好設定的存取
const searchPrefs = computed(() => preferences.value.search);
const notificationPrefs = computed(() => preferences.value.notifications);

// 搜尋相關設定
const searchSettings = [
  {
    key: 'showEmptySearchConfirm',
    title: "空搜尋確認",
    illustrate: "當沒有輸入搜尋條件時，是否顯示確認彈窗",
    checked: searchPrefs.value.showEmptySearchConfirm
  }
];

// 通知相關設定
const notificationSettings = [
  {
    key: 'accountPassword',
    title: "帳戶密碼",
    illustrate: "其他用戶的消息將以站內信的形式通知",
    checked: notificationPrefs.value.accountPassword
  },
  {
    key: 'systemMessages',
    title: "系統消息",
    illustrate: "系統消息將以站內信的形式通知",
    checked: notificationPrefs.value.systemMessages
  },
  {
    key: 'todoTasks',
    title: "待辦任務",
    illustrate: "待辦任務將以站內信的形式通知",
    checked: notificationPrefs.value.todoTasks
  }
];

function onSearchSettingChange(val: boolean, key: string) {
  updatePreference('search', { [key]: val });
  message(`搜尋設定已更新`, { type: "success" });
}

function onNotificationChange(val: boolean, key: string) {
  updatePreference('notifications', { [key]: val });
  message(`通知設定已更新`, { type: "success" });
}
</script>

<template>
  <div
    :class="[
      'min-w-[180px]',
      deviceDetection() ? 'max-w-[100%]' : 'max-w-[70%]'
    ]"
  >
    <h3 class="my-8!">偏好設定</h3>
    
    <!-- 搜尋設定 -->
    <div class="mb-6">
      <h4 class="mb-4 text-base font-medium">搜尋設定</h4>
      <div v-for="(item, index) in searchSettings" :key="`search-${index}`">
        <div class="flex items-center">
          <div class="flex-1">
            <p>{{ item.title }}</p>
            <p class="wp-4">
              <el-text class="mx-1" type="info">
                {{ item.illustrate }}
              </el-text>
            </p>
          </div>
          <el-switch
            :model-value="searchPrefs[item.key]"
            inline-prompt
            active-text="是"
            inactive-text="否"
            @change="val => onSearchSettingChange(val, item.key)"
          />
        </div>
        <el-divider />
      </div>
    </div>

    <!-- 通知設定 -->
    <div class="mb-6">
      <h4 class="mb-4 text-base font-medium">通知設定</h4>
      <div v-for="(item, index) in notificationSettings" :key="`notification-${index}`">
        <div class="flex items-center">
          <div class="flex-1">
            <p>{{ item.title }}</p>
            <p class="wp-4">
              <el-text class="mx-1" type="info">
                {{ item.illustrate }}
              </el-text>
            </p>
          </div>
          <el-switch
            :model-value="notificationPrefs[item.key]"
            inline-prompt
            active-text="是"
            inactive-text="否"
            @change="val => onNotificationChange(val, item.key)"
          />
        </div>
        <el-divider />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.el-divider--horizontal {
  border-top: 0.1px var(--el-border-color) var(--el-border-style);
}
</style>
