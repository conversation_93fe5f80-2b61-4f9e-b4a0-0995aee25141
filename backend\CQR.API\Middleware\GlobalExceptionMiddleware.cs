using CQR.Infrastructure.Configurations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Data;
using System.Net;
using System.Net.Mail;
using System.Text.Json;

namespace CQR.API.Middleware;

public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;
    private readonly IWebHostEnvironment _environment;
    private IOptions<SmtpSettings> smtpSettings;
    
    public GlobalExceptionMiddleware(
        RequestDelegate next, 
        ILogger<GlobalExceptionMiddleware> logger, 
        IWebHostEnvironment environment,
        IOptions<SmtpSettings> smtpSettings)
    {
        _next = next;
        _logger = logger;
        _environment = environment;
        this.smtpSettings = smtpSettings;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (DataException ex)
        {
            _logger.LogError(ex, "資料庫資料異常: {RequestPath} - {Message}", context.Request.Path, ex.Message);
            await HandleExceptionAsync(context, HttpStatusCode.BadRequest, "資料庫資料異常", ex);
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "資料庫更新錯誤: {RequestPath} - {Message}", context.Request.Path, ex.Message);
            await HandleExceptionAsync(context, HttpStatusCode.BadRequest, "資料庫更新錯誤", ex);
        }
        catch (ArgumentException ex)
        {
            _logger.LogError(ex, "參數錯誤: {RequestPath} - {Message}", context.Request.Path, ex.Message);
            await HandleExceptionAsync(context, HttpStatusCode.BadRequest, "參數錯誤", ex);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogError(ex, "未授權存取: {RequestPath} - {Message}", context.Request.Path, ex.Message);
            await HandleExceptionAsync(context, HttpStatusCode.Unauthorized, "未授權存取", ex);
        }
        catch (TimeoutException ex)
        {
            _logger.LogError(ex, "請求超時: {RequestPath} - {Message}", context.Request.Path, ex.Message);
            await HandleExceptionAsync(context, HttpStatusCode.RequestTimeout, "請求超時", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "系統發生未預期錯誤: {RequestPath} - {Message}", context.Request.Path, ex.Message);
            await HandleExceptionAsync(context, HttpStatusCode.InternalServerError, "系統發生未預期錯誤", ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, HttpStatusCode statusCode, string message, Exception ex)
    {
        // 詳細記錄錯誤資訊
        var errorDetails = new
        {
            RequestId = context.TraceIdentifier,
            RequestPath = context.Request.Path.Value,
            RequestMethod = context.Request.Method,
            QueryString = context.Request.QueryString.Value,
            Timestamp = DateTime.UtcNow,
            ErrorType = ex.GetType().Name,
            ErrorMessage = ex.Message,
            StackTrace = ex.StackTrace,
            InnerException = ex.InnerException?.Message
        };

        _logger.LogError("詳細錯誤資訊: {@ErrorDetails}", errorDetails);

        // 發送錯誤通知郵件 (如果有配置)
        try
        {
            await SendErrorNotificationAsync(errorDetails, ex);
        }
        catch (Exception emailEx)
        {
            _logger.LogError(emailEx, "發送錯誤通知郵件失敗");
        }

        // 準備回應資料
        var response = new
        {
            Success = false,
            StatusCode = (int)statusCode,
            Message = message,
            RequestId = context.TraceIdentifier,
            Timestamp = DateTime.UtcNow,
            // 只在開發環境顯示詳細錯誤
            Details = _environment.IsDevelopment() ? new
            {
                ExceptionType = ex.GetType().Name,
                ExceptionMessage = ex.Message,
                StackTrace = ex.StackTrace,
                InnerException = ex.InnerException?.Message
            } : null
        };

        context.Response.ContentType = "application/json";
        context.Response.StatusCode = (int)statusCode;
        
        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });

        await context.Response.WriteAsync(jsonResponse);
    }

    private async Task SendErrorNotificationAsync(object errorDetails, Exception ex)
    {
        if (smtpSettings?.Value == null) return;

        var mailMessage = new MailMessage
        {
            From = new MailAddress("<EMAIL>"),
            Subject = $"CQR API 系統錯誤通知 - {ex.GetType().Name}",
            Body = $@"
系統發生錯誤:

錯誤類型: {ex.GetType().Name}
錯誤訊息: {ex.Message}
發生時間: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC

詳細資訊:
{JsonSerializer.Serialize(errorDetails, new JsonSerializerOptions { WriteIndented = true })}

堆疊追蹤:
{ex.StackTrace}
",
            IsBodyHtml = false
        };

        // TODO: 實作 SmtpHelper.SendEmail(mailMessage, smtpSettings);
        // 暫時註解避免寄信錯誤
        // await SmtpHelper.SendEmailAsync(mailMessage, smtpSettings);
    }
}