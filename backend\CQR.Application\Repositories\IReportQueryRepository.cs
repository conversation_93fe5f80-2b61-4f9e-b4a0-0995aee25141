﻿using CQR.Application.Dto.Report;

namespace CQR.Application.Repositories;

public interface IReportQueryRepository
{
    Task<IEnumerable<WinLossReportDto>> GetSQLReportWinLoss();
    Task<IEnumerable<DueDateStatusReportDto>> GetSQLDueDateStatusReport();

    Task<IEnumerable<SalesReportDto>> GetSQLReportSalesReport();
    Task<IEnumerable<PipelineReportDto>> GetSQLReportPipeline();
    Task<IEnumerable<OpenCQRStatusReportDto>> GetSQLReportOpenCQRStatus();

}
