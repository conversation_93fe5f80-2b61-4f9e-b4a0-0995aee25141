﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CQR.API", "CQR.API\CQR.API.csproj", "{E9A5D37C-B963-45E6-8429-67D781D86AF2}"
	ProjectSection(ProjectDependencies) = postProject
		{F6D0C86E-7D3F-4CEC-9943-17527BE1DE6B} = {F6D0C86E-7D3F-4CEC-9943-17527BE1DE6B}
		{FB15BF42-487F-41B7-90AC-6A331754F1FF} = {FB15BF42-487F-41B7-90AC-6A331754F1FF}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CQR.Domain", "CQR.Domain\CQR.Domain.csproj", "{5DAF5D24-2EB6-4E8F-A7F0-DFBC11804AF7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CQR.Test", "CQR.Tests\CQR.Test.csproj", "{199D59CE-C152-458A-9E8D-C29F8E4719AB}"
	ProjectSection(ProjectDependencies) = postProject
		{FB15BF42-487F-41B7-90AC-6A331754F1FF} = {FB15BF42-487F-41B7-90AC-6A331754F1FF}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CQR.Scheduler", "CQR.Scheduler\CQR.Scheduler.csproj", "{F6D0C86E-7D3F-4CEC-9943-17527BE1DE6B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CQR.Infrastructure", "CQR.Infrastructure\CQR.Infrastructure.csproj", "{FB15BF42-487F-41B7-90AC-6A331754F1FF}"
	ProjectSection(ProjectDependencies) = postProject
		{5DAF5D24-2EB6-4E8F-A7F0-DFBC11804AF7} = {5DAF5D24-2EB6-4E8F-A7F0-DFBC11804AF7}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CQR.Application", "CQR.Application\CQR.Application.csproj", "{ABD3CB55-DBF0-46BA-8AA4-EB0FD181CE89}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CQR.Persistence.Command", "CQR.Persistence\CQR.Persistence.Command.csproj", "{1875E0FB-2DEB-44A0-AA63-C99BBDB5321B}"
	ProjectSection(ProjectDependencies) = postProject
		{5DAF5D24-2EB6-4E8F-A7F0-DFBC11804AF7} = {5DAF5D24-2EB6-4E8F-A7F0-DFBC11804AF7}
		{ABD3CB55-DBF0-46BA-8AA4-EB0FD181CE89} = {ABD3CB55-DBF0-46BA-8AA4-EB0FD181CE89}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CQR.Persistence.Query", "CQR.Persistence.Query\CQR.Persistence.Query.csproj", "{2028CD6A-ABF1-48E6-8DE7-86C259F7540C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{9B03A0D1-55EC-4744-9E0A-1AD785EF4BC4}"
	ProjectSection(SolutionItems) = preProject
		ReadMe.md = ReadMe.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E9A5D37C-B963-45E6-8429-67D781D86AF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9A5D37C-B963-45E6-8429-67D781D86AF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9A5D37C-B963-45E6-8429-67D781D86AF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9A5D37C-B963-45E6-8429-67D781D86AF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{5DAF5D24-2EB6-4E8F-A7F0-DFBC11804AF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5DAF5D24-2EB6-4E8F-A7F0-DFBC11804AF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5DAF5D24-2EB6-4E8F-A7F0-DFBC11804AF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5DAF5D24-2EB6-4E8F-A7F0-DFBC11804AF7}.Release|Any CPU.Build.0 = Release|Any CPU
		{199D59CE-C152-458A-9E8D-C29F8E4719AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{199D59CE-C152-458A-9E8D-C29F8E4719AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{199D59CE-C152-458A-9E8D-C29F8E4719AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{199D59CE-C152-458A-9E8D-C29F8E4719AB}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6D0C86E-7D3F-4CEC-9943-17527BE1DE6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6D0C86E-7D3F-4CEC-9943-17527BE1DE6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6D0C86E-7D3F-4CEC-9943-17527BE1DE6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6D0C86E-7D3F-4CEC-9943-17527BE1DE6B}.Release|Any CPU.Build.0 = Release|Any CPU
		{FB15BF42-487F-41B7-90AC-6A331754F1FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FB15BF42-487F-41B7-90AC-6A331754F1FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FB15BF42-487F-41B7-90AC-6A331754F1FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FB15BF42-487F-41B7-90AC-6A331754F1FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{ABD3CB55-DBF0-46BA-8AA4-EB0FD181CE89}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ABD3CB55-DBF0-46BA-8AA4-EB0FD181CE89}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ABD3CB55-DBF0-46BA-8AA4-EB0FD181CE89}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ABD3CB55-DBF0-46BA-8AA4-EB0FD181CE89}.Release|Any CPU.Build.0 = Release|Any CPU
		{1875E0FB-2DEB-44A0-AA63-C99BBDB5321B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1875E0FB-2DEB-44A0-AA63-C99BBDB5321B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1875E0FB-2DEB-44A0-AA63-C99BBDB5321B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1875E0FB-2DEB-44A0-AA63-C99BBDB5321B}.Release|Any CPU.Build.0 = Release|Any CPU
		{2028CD6A-ABF1-48E6-8DE7-86C259F7540C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2028CD6A-ABF1-48E6-8DE7-86C259F7540C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2028CD6A-ABF1-48E6-8DE7-86C259F7540C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2028CD6A-ABF1-48E6-8DE7-86C259F7540C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F823B58C-FD7F-43CC-AE6C-D06DA400DA34}
	EndGlobalSection
EndGlobal