﻿using CQR.Domain.Enum;

namespace CQR.Domain.Extensions;

// 擴展方法來轉換枚舉為字符串
public static class RoleEnumExtensions
{
    public static string ToRoleString(this RoleEnum role)
    {
        return role switch
        {
            RoleEnum.PETMId => "PETMId",
            RoleEnum.PGMId => "PGMId",
            RoleEnum.CostEstimatorId => "CostEstimatorId",
            RoleEnum.AccountMgrId => "AccountMgrId",
            RoleEnum.OriginatorId => "OriginatorId",
            _ => throw new ArgumentException($"未知的角色類型: {role}")
        };
    }

    public static RoleEnum FromString(string roleString)
    {
        return roleString switch
        {
            "OriginatorId" => RoleEnum.OriginatorId,
            "PETMId" => RoleEnum.PETMId,
            "PGMId" => RoleEnum.PGMId,
            "CostEstimatorId" => RoleEnum.CostEstimatorId,
            "AccountMgrId" => RoleEnum.AccountMgrId,
            _ => throw new ArgumentException($"無效的角色字符串: {roleString}")
        };
    }
}