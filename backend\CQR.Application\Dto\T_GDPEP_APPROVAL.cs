﻿using CQR.Domain.DecisionCore;

namespace CQR.Application.Dto;

public class GDPEP_APPROVAL
{
    public int iGDPEPPhaseId { get; set; }

    public string? sGDPIMCategory { get; set; }

    public string? sReleaseDate { get; set; }

    public string? sComments { get; set; }

    public string[] sVolume { get; set; }

    public string[] sSellPrice { get; set; }

    public string[] sApprovalUser { get; set; }

    public string[] sRequiredState { get; set; }

    public string[] sApprovalState { get; set; }

    public string[] sApprovalStateDbs { get; set; }

    public string[] sApprovalDate { get; set; }

    public string[] sApprovalRole { get; set; }

    public string? sReleaseGateway { get; set; }

    public clsAttachment tAttachment;

    public GDPEP_APPROVAL()
    {
        sVolume = new string[13];
        sSellPrice = new string[11];
        sApprovalUser = new string[13];
        sRequiredState = new string[13];
        sApprovalState = new string[13];
        sApprovalStateDbs = new string[13];
        sApprovalDate = new string[13];
        sApprovalRole = new string[15]
        {
            "ZERO", "ODIR", "MDIR", "FDIR", "ENG", "CDEV", "PDIR", "BDIR", "8", "9",
            "10", "11", "12", "13", "14"
        };
        sReleaseGateway = "";
        tAttachment = new clsAttachment();
    }
}

