// stores/fileStore.ts
import { defineStore } from "pinia";

export const useFileStore = defineStore("fileStore", {
  state: () => ({
    isDialogVisible: false,
    files: [
      "Consolidation_v2024.2.xlsm",
      "Consolidation_v2025.1.xlsm",
      "CQR_Logistic_v1.3.xlsx",
      "ECR Worksheet_v2024.2.xlsm",
      "Full Tasklist_Template_v2025.1.xlsb"
    ],
    selectedFile: ""
  }),
  actions: {
    openDialog() {
      this.isDialogVisible = true;
    },
    closeDialog() {
      this.isDialogVisible = false;
      this.selectedFile = "";
    }
  }
});
