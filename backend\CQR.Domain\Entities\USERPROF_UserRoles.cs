﻿using System.ComponentModel.DataAnnotations;

namespace CQR.Domain.Entities;

public class USERPROF_UserRoles
{
    [Required]
    [Range(0, int.MaxValue)]  // QueueKey as int, required field
    public int QueueKey { get; set; }

    [Range(0, int.MaxValue)]  // Level2Key as int, required field
    public int? Level2Key { get; set; }

    [StringLength(32)]  // RoleCode as varchar(32)
    public string RoleCode { get; set; }

    [StringLength(1)]  // PrimaryInd as varchar(1)
    public string PrimaryInd { get; set; } = string.Empty;

    public DateTime? LastUpdatedDate { get; set; }  // LastUpdatedDate as datetime, can be null

    [StringLength(25)]  // LastUpdatedBy as varchar(25)
    public string LastUpdatedBy { get; set; }

    [StringLength(1)]  // PrimaryTemp as varchar(1)
    public string PrimaryTemp { get; set; }
}