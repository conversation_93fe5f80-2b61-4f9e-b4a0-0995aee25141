<script setup lang="ts"></script>

<template>
  <el-card>
    <ApprovalAttachmentPanel
      :attachments="attachmentList"
      @add="handleAddAttachment"
      @delete="handleDeleteAttachment"
    />
    <el-form label-position="top" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="Business Type" />
        </el-col>

        <el-col :span="8">
          <el-form-item label="Date" />
        </el-col>

        <el-col :span="8">
          <el-form-item label="Sales Director (SDIR)" />
        </el-col>
      </el-row>

      <el-divider>1. CUSTOMER / PLATFORM DETAILS</el-divider>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="Product Description" />
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="Model Year" />
        </el-col>
        <el-col :span="8">
          <el-form-item label="Platform" />
        </el-col>
      </el-row>

      <el-form-item label="Comments">
        <el-input type="textarea" :rows="4" readonly disabled />
      </el-form-item>

      <el-divider>Objective</el-divider>
      <ul>
        <li>Confirm resources to quote</li>
        <li>Manufacturing strategy direction</li>
        <li>Component sourcing strategy direction</li>
        <li>
          Confirm commercial issues (Givebacks, Customer Funding, Target
          Pricing)
        </li>
      </ul>

      <el-divider>Approval</el-divider>
      <el-table :data="approvals" style="width: 100%">
        <el-table-column label="Role" prop="role" />
        <el-table-column label="Required" width="100">
          <template v-slot="scope" />
        </el-table-column>
        <el-table-column label="Approved" width="100">
          <template v-slot="scope" />
        </el-table-column>
        <el-table-column label="Date/Time" prop="date" width="160" />
        <el-table-column label="Comments" prop="comments" />
      </el-table>
    </el-form>

    <ApprovalAttachmentPanel
      :attachments="attachmentList"
      @add="handleAddAttachment"
      @delete="handleDeleteAttachment"
    />
  </el-card>
</template>

<script setup lang="ts">
import { useFormModelStore } from "@/store/modules/formModel";
import ApprovalAttachmentPanel from "@/views/cqr/tabs/ApprovalAttachmentPanel.vue";
import { onMounted, ref } from "vue";
const formStore = useFormModelStore();
onMounted(() => {
  if (formStore.modelCQRHeaderCollection) {
    // formData.value = { ...formStore.modelCQRHeaderCollection.iCQRHeader };
  }
});

const attachmentList = ref([
  { id: 1, filename: "Report_Q2_2024.pdf" },
  { id: 2, filename: "Design_Spec.xlsx" }
]);

function handleAddAttachment() {
  console.log("Add attachment clicked");
  // 開啟 dialog、呼叫 API、或其他處理
}

function handleDeleteAttachment(row) {
  attachmentList.value = attachmentList.value.filter(
    item => item.id !== row.id
  );
}
</script>

<style lang="scss" scoped></style>
