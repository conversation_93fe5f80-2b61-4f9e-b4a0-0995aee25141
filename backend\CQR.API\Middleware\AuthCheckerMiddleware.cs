using CQR.Infrastructure.Utilities;
using CQRLIB.DTOs;
using System.IdentityModel.Tokens.Jwt;

namespace CQRLIB.Middleware
{
    public class AuthCheckerMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IWebHostEnvironment _env;

        public AuthCheckerMiddleware(RequestDelegate next, IWebHostEnvironment env)
        {
            _next = next;
            _env = env;
        }

        public async Task Invoke(HttpContext context)
        {
            var token = context.Request.Headers["Authorization"].ToString();

            //if (string.IsNullOrEmpty(token))
            //{
            //    context.Response.StatusCode = 401;
            //    await context.Response.WriteAsync($"Access Denied-token is emtpy");
            //    return;
            //}

            try
            {
                if (string.IsNullOrWhiteSpace(token))
                {
                    await _next(context);
                    return;
                }


                var tokenValue = token.Split(" ")[1];
                if (string.IsNullOrEmpty(tokenValue)) throw new Exception("Token is required");

                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadToken(tokenValue) as JwtSecurityToken;

                if (jsonToken == null)
                {
                    throw new Exception("Invalid Token");
                }
                bool isFromSwagger = context.Request.Headers.ContainsKey("Swagger-Identifier");
                bool isFromSwagger2 = context.Request.Query.ContainsKey("swagger");

                /* todo restruct.
                */
                //step1 - normal  case
                var email = jsonToken?.Claims.FirstOrDefault(claim => claim.Type == "upn")?.Value;
                var strSubInfo = jsonToken.Claims.First(claim => claim.Type == "sub").Value;

                // 在需要检查过期的地方获取当前Token的过期时间
                var tokenExpiration = jsonToken?.Claims.FirstOrDefault(claim => claim.Type == "ValidTO")?.Value;// User.FindFirst("exp")?.Value;
                // 检查过期
                if (!string.IsNullOrEmpty(tokenExpiration) && DateTime.UtcNow >= DateTimeOffset.FromUnixTimeSeconds(long.Parse(tokenExpiration)).DateTime)
                {
                    context.Response.StatusCode = 401;
                    await context.Response.WriteAsync("Token has expired. Please reauthenticate.");
                    return;
                    // Token已过期，返回需要重新认证的响应
                    //return Un…authorized(/new { message = "Token has expired. Please reauthenticate." });
                }


                Dictionary<string, string> keyValuePairs = HelperUtil.ParseKeyValuePairs(strSubInfo);

                //JsonTokenSubject subInfo =  
                //    JsonSerializer.Deserialize<JsonTokenSubject>(strSubInfo);

                //step2 - special case for normal login
                //  if (name == null) name = keyValuePairs["name"]??""; 
                if (email == null) email = keyValuePairs["upn"] ?? "";
                var name = email.Split('@')[0];
                //if (userLogin == null) userLogin = keyValuePairs["userLogin"] ?? "";
                //if (IDUser == null) IDUser = keyValuePairs["IDUser"] ?? "";
                /*
                */
                context.Items["user"] = new UserDto()
                {
                    //userLogin = userLogin,//todo change to OA Account from front side.?
                    //userId = IDUser,
                    name = name,
                    email = email
                };

                // Add code to get user id from persistenceDb if needed
                // var userEmail = persistenceDb.GetUserEmail();
                // var oUserId = await persistenceDb.ModelGPLStUser.GetStUserIdByEmail(userEmail);

                await _next(context);
            }
            catch (Exception ex)
            {
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync($"Invalid Token: {ex.Message},StackTrace:${ex.StackTrace}");
            }
        }
    }
}
