﻿using System.Globalization;

namespace CQR.Infrastructure.Utilities
{
    public class DateUtil
    {
        //日期格式化：转换前20250416->转换后16-Apr-2025
        public static string FormateDate(string inputDate) 
        {
            if (DateTime.TryParseExact(inputDate, "yyyyMMdd", CultureInfo.InvariantCulture,
                                 DateTimeStyles.None, out DateTime result))
            {
                return result.ToString("dd-MMM-yyyy", CultureInfo.InvariantCulture);
            }
            else 
            {
                return inputDate;
            }
        }
    }
}
