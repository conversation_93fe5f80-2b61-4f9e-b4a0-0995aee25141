﻿namespace CQR.Application.Dto.Report;
public class WinLossReportDto
{
    public int QueueKey { get; set; }

    public string? CQRNumber { get; set; }

    public string? CBMName { get; set; }

    public string? SalesDirectorName { get; set; }

    public string? AccountManagerName { get; set; }

    public string? CustomerName { get; set; }

    public string? StatusName { get; set; }

    public string? FRANDesc { get; set; }

    public string? QSCommentsProceedCancel { get; set; }

    public string? BusinessType { get; set; }

    public string? ManufacturingSiteName { get; set; }

    public string? AwardStatus { get; set; }

    public string? BkRndInfComments { get; set; }

    public string? ProductDesc { get; set; }

    public string? OriginationDate { get; set; }

    public string? CustQuoteDueDate { get; set; }

    public string? DueDateToBnE { get; set; }

    public string? CustomData { get; set; }
}

