﻿using CQR.Domain.Enum;
using CQRLIB.DTOs;

namespace CQR.Application.Dto.RoleUser;

public class RoleUserCollectionDto
{
    /// <summary>
    /// 角色名稱 (例如: "PETMId", "PGMId")
    /// </summary>
    public string RoleName { get; set; }

    /// <summary>
    /// 角色ID (枚舉的數值)
    /// </summary>
    public int RoleId { get; set; }

    /// <summary>
    /// 該角色下的用戶列表
    /// </summary>
    public List<UserFullNameDto> Users { get; set; } = new List<UserFullNameDto>();

    /// <summary>
    /// 用戶總數
    /// </summary>
    public int UserCount { get; set; }

    /// <summary>
    /// 角色顯示名稱 (可選，用於前端顯示)
    /// </summary>
    public string DisplayName { get; set; }

    /// <summary>
    /// 建構函式
    /// </summary>
    public RoleUserCollectionDto()
    {
        Users = new List<UserFullNameDto>();
    }

    /// <summary>
    /// 帶參數的建構函式
    /// </summary>
    /// <param name="roleName">角色名稱</param>
    /// <param name="roleId">角色ID</param>
    public RoleUserCollectionDto(string roleName, int roleId)
    {
        RoleName = roleName;
        RoleId = roleId;
        DisplayName = roleName; // 預設顯示名稱與角色名稱相同
        Users = new List<UserFullNameDto>();
    }

    /// <summary>
    /// 從 RoleEnum 建立 RoleUserCollectionDto
    /// </summary>
    /// <param name="role">角色枚舉</param>
    /// <returns>RoleUserCollectionDto 實例</returns>
    public static RoleUserCollectionDto FromRoleEnum(RoleEnum role)
    {
        return new RoleUserCollectionDto
        {
            RoleName = role.ToString(),
            RoleId = (int)role,
            DisplayName = role.ToString(),
            Users = new List<UserFullNameDto>()
        };
    }

    /// <summary>
    /// 更新用戶數量
    /// </summary>
    public void UpdateUserCount()
    {
        UserCount = Users?.Count ?? 0;
    }

    /// <summary>
    /// 添加用戶到列表
    /// </summary>
    /// <param name="user">用戶資料</param>
    public void AddUser(UserFullNameDto user)
    {
        if (user != null)
        {
            Users ??= new List<UserFullNameDto>();
            Users.Add(user);
            UpdateUserCount();
        }
    }

    /// <summary>
    /// 批量添加用戶
    /// </summary>
    /// <param name="users">用戶列表</param>
    public void AddUsers(IEnumerable<UserFullNameDto> users)
    {
        if (users != null)
        {
            Users ??= new List<UserFullNameDto>();
            Users.AddRange(users);
            UpdateUserCount();
        }
    }
}