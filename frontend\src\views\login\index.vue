<script setup lang="ts">
import { useI18n } from "vue-i18n";
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import TypeIt from "@/components/ReTypeit";
import { debounce } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import { useEventListener } from "@vueuse/core";
import type { FormInstance } from "element-plus";
import { $t, transformI18n } from "@/plugins/i18n";
import { operates, thirdParty } from "./utils/enums";
import { useLayout } from "@/layout/hooks/useLayout";
import LoginPhone from "./components/LoginPhone.vue";
import LoginRegist from "./components/LoginRegist.vue";
import LoginUpdate from "./components/LoginUpdate.vue";
import LoginQrCode from "./components/LoginQrCode.vue";
import { useUserStoreHook } from "@/store/modules/user";
import { initRouter, getTopMenu } from "@/router/utils";
import { bg, avatar, illustration } from "./utils/static";
import { ReImageVerify } from "@/components/ReImageVerify";
import { ref, toRaw, reactive, watch, computed } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useTranslationLang } from "@/layout/hooks/useTranslationLang";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { usePermissionStoreHook } from "@/store/modules/permission";

import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import globalization from "@/assets/svg/globalization.svg?component";
import Lock from "~icons/ri/lock-fill";
import Check from "~icons/ep/check";
import User from "~icons/ri/user-3-fill";
import Info from "~icons/ri/information-line";
import Keyhole from "~icons/ri/shield-keyhole-line";
import { type DataInfo, setToken, removeToken, userKey } from "@/utils/auth";
import bgImg from "@/assets/images/BCS.jpg";
// import { msalInstance } from "@/utils/msal";

import { msalInstance } from "@/utils/auth/msal";
import { ascending } from "@/router/utils";

defineOptions({
  name: "Login"
});

const imgCode = ref("");
const loginDay = ref(7);
const router = useRouter();
const loading = ref(false);
const checked = ref(false);
const disabled = ref(false);
const ruleFormRef = ref<FormInstance>();
const currentPage = computed(() => {
  return useUserStoreHook().currentPage;
});

const { t } = useI18n();
const { initStorage } = useLayout();
initStorage();
const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title, getDropdownItemStyle, getDropdownItemClass } = useNav();
const { locale, translationCh, translationEn } = useTranslationLang();

const ruleForm = reactive({
  username: "admin",
  password: "admin123",
  verifyCode: ""
});

// const loginWithAzure = async () => {
//   try {
//     const response = await msalInstance.loginPopup({
//       scopes: ["User.Read"]
//     });
//     const account = response.account;
//     if (account) {
//       localStorage.setItem("azure_user", JSON.stringify(account));
//       router.push("/"); // 登入成功導入首頁
//     }
//   } catch (error) {
//     console.error("Azure 登入錯誤：", error);
//   }
// };

async function loginWithAzureAD() {
  loading.value = true;
  try {
    // 👇 這行是你缺少的關鍵
    await msalInstance.initialize();

    const result = await msalInstance.loginPopup({
      scopes: ["User.Read"]
    });

    const account = result.account;

    // 1. 儲存 access token 和使用者資訊
    // 存 token 和帳號資訊，可串接你的 API 做帳號同步
    localStorage.setItem("access_token", result.accessToken);
    localStorage.setItem("user_email", account?.username ?? "");

    // const account = result.account;
    const accessToken = result.accessToken;

    // 👉 Step 1: 儲存 token 到 store（也可存 storageLocal）
    const userStore = useUserStoreHook();
    userStore.SET_TOKEN(accessToken); // 如果你定義了這個方法

    // const userStore = useUserStoreHook();
    const permissionStore = usePermissionStoreHook();
    // 👉 Step 2: 建立 userInfo 物件
    const userInfo = {
      avatar: "https://www.gravatar.com/avatar/" + account?.username,
      username: account?.name ?? "Azure User",
      nickname: account?.name ?? "",
      roles: ["admin"], // ✅ 你可根據 email 調後端查真實角色
      permissions: ["*:*:*"], // 或空陣列，視你的權限系統
      email: account?.username
    };

    // 使用原始的 setToken 函數設定 token 和 userInfo
    setToken({
      // accessToken: accessToken,
      // refreshToken: result.idToken || "", // 如果有 refresh token
      // expires: new Date(Date.now() + 3600 * 1000), // 設定過期時間（1小時後）
      // userInfo: userInfo,
      username: userInfo.username,
      roles: ["admin"]
    });

    // 👉 Step 3: 存入 Pinia userStore
    // userStore.setUserInfo(userInfo);

    // 👉 Step 4: 同步存入 localStorage 的 `user-info`
    // storageLocal().setItem(userKey, userInfo);

    // 2. 設定 Pinia 的 token
    // userStore.SET_TOKEN(result.accessToken);
    // userStore.setToken(result.accessToken);
    // userStore.SET_TOKEN(result.accessToken);

    // 3. 可選：模擬 getInfo，寫入 userStore.userInfo
    // await userStore.setUserInfo({
    //   username: account?.name ?? "Azure User",
    //   roles: ["admin"] // 💡根據實際權限來源決定
    // });

    // 3️⃣ 初始化動態路由
    await initRouter(); // 👈 這裡一定要在使用 route 功能前完成

    // 4️⃣ 導向首頁（可用 getTopMenu()）
    const topMenu = getTopMenu(true);
    router.push(topMenu?.path || "/dashboard");

    // // 4. 初始化權限路由
    // // permissionStore.handleWholeMenus(routes);
    // // await permissionStore.setRoutes();
    // const routes = await ascending(); // 🔍 根據角色生成完整路由
    // // await permissionStore.setRoutes(await ascending());
    // permissionStore.handleWholeMenus(routes); // ✅ 注入權限模組

    // // 5. 找到導向的 top menu（可選：fallback 頁面）
    // const topMenu = getTopMenu(true);
    // const targetPath = topMenu?.path || "/dashboard";

    disabled.value = true;

    // await router.push(targetPath);

    // 6. 可選：加入多標籤頁
    // useMultiTagsStoreHook().handleTags("push", topMenu);

    ElMessage.success(`歡迎 ${account?.username}`);
    // router.push("/"); // 登入後導回首頁

    // return initRouter().then(() => {
    //   disabled.value = true;
    //   router
    //     .push(getTopMenu(true).path)
    //     .then(() => {
    //       message(t("login.pureLoginSuccess"), { type: "success" });
    //     })
    //     .finally(() => (disabled.value = false));
    // });
  } catch (err) {
    console.error("Azure AD 登入失敗", err);
  }
}

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      useUserStoreHook()
        .loginByUsername({
          username: ruleForm.username,
          password: ruleForm.password
        })
        .then(res => {
          if (res.success) {
            // 获取后端路由
            return initRouter().then(() => {
              disabled.value = true;
              router
                .push(getTopMenu(true).path)
                .then(() => {
                  message(t("login.pureLoginSuccess"), { type: "success" });
                })
                .finally(() => (disabled.value = false));
            });
          } else {
            message(t("login.pureLoginFail"), { type: "error" });
          }
        })
        .finally(() => (loading.value = false));
    }
  });
};

const immediateDebounce: any = debounce(
  formRef => onLogin(formRef),
  1000,
  true
);

useEventListener(document, "keydown", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value);
});

watch(imgCode, value => {
  useUserStoreHook().SET_VERIFYCODE(value);
});
watch(checked, bool => {
  useUserStoreHook().SET_ISREMEMBERED(bool);
});
watch(loginDay, value => {
  useUserStoreHook().SET_LOGINDAY(value);
});
</script>

<template>
  <div
    :style="{ backgroundImage: `url(${bgImg})` }"
    class="bg-cover bg-center min-h-screen"
  >
    <div class="absolute right-5 top-3 flex items-center space-x-2 z-10">
      <!-- 主题 -->
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
      <!-- 国际化 -->
      <el-dropdown trigger="click">
        <globalization
          class="hover:text-primary hover:bg-[transparent]! w-[20px] h-[20px] ml-1.5 cursor-pointer outline-hidden duration-300"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'zh')"
              :class="['dark:text-white!', getDropdownItemClass(locale, 'zh')]"
              @click="translationCh"
            >
              <IconifyIconOffline
                v-show="locale === 'zh'"
                class="check-zh"
                :icon="Check"
              />
              简体中文
            </el-dropdown-item>
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'en')"
              :class="['dark:text-white!', getDropdownItemClass(locale, 'en')]"
              @click="translationEn"
            >
              <span v-show="locale === 'en'" class="check-en">
                <IconifyIconOffline :icon="Check" />
              </span>
              English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="login-container">
      <!-- <div class="img">
        <component :is="toRaw(illustration)" />
      </div> -->
      <div class="login-box">
        <div class="login-form">
          <el-form class="w-full" size="large">
            <Motion :delay="100">
              <el-form-item>
                <el-button
                  class="w-full mt-4!"
                  size="default"
                  type="primary"
                  :loading="loading"
                  @click="loginWithAzureAD"
                >
                  使用 Azure AD 登入
                </el-button>
              </el-form-item>
            </Motion>
          </el-form>

          <!-- <el-form
            v-if="currentPage === 0"
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="loginRules"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: transformI18n($t('login.pureUsernameReg')),
                    trigger: 'blur'
                  }
                ]"
                prop="username"
              >
                <el-input
                  v-model="ruleForm.username"
                  clearable
                  :placeholder="t('login.pureUsername')"
                  :prefix-icon="useRenderIcon(User)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="password">
                <el-input
                  v-model="ruleForm.password"
                  clearable
                  show-password
                  :placeholder="t('login.purePassword')"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="200">
              <el-form-item prop="verifyCode">
                <el-input
                  v-model="ruleForm.verifyCode"
                  clearable
                  :placeholder="t('login.pureVerifyCode')"
                  :prefix-icon="useRenderIcon(Keyhole)"
                >
                  <template v-slot:append>
                    <ReImageVerify v-model:code="imgCode" />
                  </template>
                </el-input>
              </el-form-item>
            </Motion>

            <Motion :delay="250">
              <el-form-item>
                <div class="w-full h-[20px] flex justify-between items-center">
                  <el-checkbox v-model="checked">
                    <span class="flex">
                      <select
                        v-model="loginDay"
                        :style="{
                          width: loginDay < 10 ? '10px' : '16px',
                          outline: 'none',
                          background: 'none',
                          appearance: 'none',
                          border: 'none'
                        }"
                      >
                        <option value="1">1</option>
                        <option value="7">7</option>
                        <option value="30">30</option>
                      </select>
                      {{ t("login.pureRemember") }}
                      <IconifyIconOffline
                        v-tippy="{
                          content: t('login.pureRememberInfo'),
                          placement: 'top'
                        }"
                        :icon="Info"
                        class="ml-1"
                      />
                    </span>
                  </el-checkbox>
                  <el-button
                    link
                    type="primary"
                    @click="useUserStoreHook().SET_CURRENTPAGE(4)"
                  >
                    {{ t("login.pureForget") }}
                  </el-button>
                </div>
                <el-button
                  class="w-full mt-4!"
                  size="default"
                  type="primary"
                  :loading="loading"
                  :disabled="disabled"
                  @click="onLogin(ruleFormRef)"
                >
                  {{ t("login.pureLogin") }}
                </el-button>
              </el-form-item>
            </Motion>

            <Motion :delay="300">
              <el-form-item>
                <div class="w-full h-[20px] flex justify-between items-center">
                  <el-button
                    v-for="(item, index) in operates"
                    :key="index"
                    class="w-full mt-4!"
                    size="default"
                    @click="useUserStoreHook().SET_CURRENTPAGE(index + 1)"
                  >
                    {{ t(item.title) }}
                  </el-button>
                  <el-button
                    :loading="loading"
                    class="w-full mt-4!"
                    @click="loginWithAzureAD"
                  >
                    使用 Azure AD 登入
                  </el-button>
                </div>
              </el-form-item>
            </Motion>
          </el-form> -->

          <!-- <Motion v-if="currentPage === 0" :delay="350">
            <el-form-item>
              <el-divider>
                <p class="text-gray-500 text-xs">
                  {{ t("login.pureThirdLogin") }}
                </p>
              </el-divider>
              <div class="w-full flex justify-evenly">
                <span
                  v-for="(item, index) in thirdParty"
                  :key="index"
                  :title="t(item.title)"
                >
                  <IconifyIconOnline
                    :icon="`ri:${item.icon}-fill`"
                    width="20"
                    class="cursor-pointer text-gray-500 hover:text-blue-400"
                  />
                </span>
              </div>
            </el-form-item>
          </Motion> -->
          <!-- 手机号登录 -->
          <!-- <LoginPhone v-if="currentPage === 1" /> -->
          <!-- 二维码登录 -->
          <!-- <LoginQrCode v-if="currentPage === 2" /> -->
          <!-- 注册 -->
          <!-- <LoginRegist v-if="currentPage === 3" /> -->
          <!-- 忘记密码 -->
          <!-- <LoginUpdate v-if="currentPage === 4" /> -->
        </div>
      </div>
    </div>
    <div
      class="w-full flex-c absolute bottom-3 text-sm text-[rgba(0,0,0,0.6)] dark:text-[rgba(220,220,242,0.8)]"
    />
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}

.translation {
  ::v-deep(.el-dropdown-menu__item) {
    padding: 5px 40px;
  }

  .check-zh {
    position: absolute;
    left: 20px;
  }

  .check-en {
    position: absolute;
    left: 20px;
  }
}

.login-box {
  width: 100%;
  max-width: 420px;
  margin: auto;
  background-color: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

@media (max-width: 640px) {
  .login-box {
    padding: 1.5rem;
    border-radius: 0;
    box-shadow: none;
  }
}
.login-form {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平置中 */
  justify-content: center; /* 垂直置中 */
  text-align: center;
}

.login-form .el-form-item {
  width: 320px;
  margin-bottom: 1rem;
}
</style>
