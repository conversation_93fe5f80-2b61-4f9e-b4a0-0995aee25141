# CQR 系統各 Tab 頁面詳細功能分析

## 系統架構概述

CQR 系統採用分頁式設計，每個 Tab 頁面負責處理特定的業務功能。所有頁面繼承自 `corePage` 基類，確保統一的功能和安全模型。

## Tab 1: Description.aspx - 項目描述頁面

### 核心功能
項目基本資訊錄入，包含客戶需求描述、項目參數設定、人員配置和產品特性定義。

### 主要業務邏輯
1. **CQR 描述管理**: 項目摘要說明和識別資訊
2. **Antares/IHS 資料整合**: 透過 AJAX 動態載入和篩選汽車製造商資料
3. **項目時程規劃**: 模型年份、獎勵季度/年度設定
4. **財務資訊**: 年產量、產品生命週期、預估年度價值
5. **團隊配置**: 各角色責任人員指派

### 關鍵欄位分析

#### A. 基本資訊區塊
- **CQR Description** (`txtCQRDesc`): 項目描述，格式為 CQR-客戶-平台-銘牌-產品描述
- **Model Year** (`cmbModelYear`): 從 TRS 系統載入的車型年份
- **Volume Per Annum** (`txtVolPerAnnum`): 年產量數據，支援數字格式化顯示
- **Award Quarter/Year** (`cmbAwardQuarter`, `cmbAwardYear`): 獎勵時程，需傳送至 Antares 系統

#### B. Antares 資料整合區塊 (`trAntares`)
複雜的汽車製造商資料管理系統：
- **動態下拉選單**: OEM Group → OEM Customer → Platform → Nameplate 層級篩選
- **AJAX 整合**: 即時資料查詢和驗證
- **重複性檢查**: 防止 Unique ID 在其他 CQR 中重複使用
- **資料表格**: 顯示完整的 Antares 記錄，包含 SOP/EOP 日期、狀態等

#### C. IHS 資料整合區塊 (`trIHS`)
另一套汽車製造商資料系統：
- **Core Nameplate Plant Mnemonic**: 核心銘牌工廠代碼
- **Product Description/Sold From/Final Assembly**: 必填驗證欄位
- **區域化資料**: 支援多地區、多國家資料

#### D. 財務和時程區塊
- **Product Life** (`txtRemainingProdLife`): 剩餘產品生命週期
- **Approx Annual Value** (`txtApproxAnnualVal`): 預估年度價值，貨幣格式驗證
- **Gateway** (`optGatewayYES/NO`): Gateway 項目標記
- **TDR Date** (`txtTDRDate`, `chkTDRNoInput`): 技術設計審查日期，支援無輸入選項

#### E. 團隊責任配置
- **Commercial Manager** (`cmbCMGR`): 商務經理
- **Account Manager** (`cmbAMGR`): 客戶經理
- **Sales Account Director** (`cmbSDIR`): 銷售總監
- **Engineering** 系列: 工程相關負責人
- **Product Features**: 通訊、ASIL Level、AUTOSAR、網路安全等特性

### JavaScript 功能分析
1. **Antares/IHS 動態篩選**: 多層級連動下拉選單
2. **資料驗證**: 即時檢查重複和必填欄位
3. **UI 交互**: 日期選擇器、工具提示、動態顯示控制

### 權限控制邏輯
```vb
Private Sub SetSecurity(ByVal atCQR As clsCQR)
    ' 檢視模式直接退出
    If (atCQR.iOpenMode = OPEN_View) Then Exit Sub
    
    ' 創建者權限：可更新報價回應和客戶報價日期
    If (atCQR.bUserIsOrig) Then
        ' 根據狀態控制特定欄位
    End If
    
    ' 創建階段權限：完整編輯功能
    If (atCQR.sStatus = status010100FR Or atCQR.bSuperUser) Then
        ' 啟用所有基本欄位
    End If
    
    ' 工程包編輯權限
    If (atCQR.bSuperUser Or atCQR.CanEditEngineeringPackage()) Then
        ' 產品特性欄位
    End If
End Sub
```

---

## Tab 2: Background.aspx - 背景資訊頁面

### 核心功能
最簡化的資訊收集頁面，專注於項目背景和客戶聯絡資訊。

### 主要欄位
1. **Background Information** (`txtBgInfo`): 多行文字，項目背景描述
2. **Customer Contract** 區塊:
   - **Buyer** (`txtCustBuyer`): 客戶採購員，最大10字元
   - **Engineer** (`txtCustEngineer`): 客戶工程師，最大10字元
3. **Obsolescence Required** (`optObsolReq`): 是否需要淘汰評估
4. **Customer Productivity Targets** (`txtCustProdTargets`): 客戶生產力目標

### 業務邏輯
- 簡單的資料載入和儲存機制
- 所有欄位在非檢視模式下完全開放編輯
- 資料直接對應到 `clsCQR` 物件屬性

---

## Tab 3: Engineering.aspx - 工程評估頁面

### 核心功能
技術可行性評估、資源分配、設計輸入需求管理和 RCT/EPL 資料夾關聯。

### 主要業務區塊

#### A. 資源評估區塊
```javascript
// 自動邏輯控制
function disableFields() {
    if (CQR可達成 && 資訊充足) {
        自動設定工作可進行 = true
        // 灰化相關欄位
    }
}
```

關鍵評估問題：
- **Is the CQR achievable** (`optIsCQRAchievable`): 時程可行性
- **Is there sufficient information** (`optIsSuffInfo`): 資訊完整性
- **Can Work Proceed** (`optCanWorkProceed`): 工作進行許可
- **Health, Safety & Environmental** (`optIsSafetyConsidered`): 安全環境考量

#### B. 責任分派區塊
- **Due Date to Cost Estimating** (`txtDueDateBid`): 成本評估到期日
- **Due Date from Design Eng** (`txtDueDateDesign`): 設計工程到期日
- **PRD - Person Responsible for Design** (`cmbDesignResp`): 設計負責人

#### C. 設計輸入需求（8項）
每項包含：
- **Is Design Input Needed** (`optIsDesignNeeded1-8`): 是否需要設計輸入
- **Electrical PRD** (`cmbElecPRD1-8`): 電氣設計負責人
- **No Input** (`chkElecPRD1-8`): 無輸入核取方塊

動態控制邏輯：
```javascript
function enableElecPRD(index) {
    if (需要設計輸入) {
        啟用負責人選擇
        文字顏色 = 黑色
    } else {
        禁用並清空選擇
        文字顏色 = 灰色
    }
}
```

#### D. 團隊負責人配置
- **Manufacturing Lead** (`cmbLeadManufacturing`): 製造負責人
- **Purchasing Lead** (`cmbLeadPurchasing`): 採購負責人  
- **Validation Lead** (`cmbLeadValidation`): 驗證負責人
- 各負責人都有對應的 "No Cost Impact" 核取方塊

#### E. RCT/EPL 資料夾管理
- **RCT Count** (`txtRCTCount`): RCT 資料夾數量
- **EPL Count** (`txtEPLCount`): EPL 資料夾數量
- 動態顯示相關的資料夾清單和匯出功能

### 權限控制邏輯
```vb
Private Sub SetSecurity(ByVal atCQR As clsCQR)
    ' 工程資源和責任編輯權限
    If (atCQR.bSuperUser Or atCQR.CanEditEngResourcesAndResponsibilities()) Then
        EnableRadioButton("optIsCQRAchievable")
        ' ... 其他欄位
    End If
    
    ' 特定角色權限
    If (atCQR.tUserData.bIsPetmOnly) Then
        cmbPDM.Enabled = True  ' PETM 可選擇 PDM
    End If
End Sub
```

---

## Tab 4: QuoteResponse.aspx - 報價回應頁面

### 核心功能
財務協調員處理報價回應，支援 FCM（Full Cost Model）和 QR（Quote Response）兩種模式。

### 主要業務邏輯

#### A. 模式選擇
- **Quote Response** (`optQRQuoteResponse`): 快速報價模式
- **Full Cost Model** (`optQRFullCostModel`): 完整成本模型

#### B. Legacy 版本檢查
```vb
If (tCQR.isLegacy()) Then
    Response.Redirect("QuoteResponseLegacy.aspx?QueueKey=" & iQueueKey)
End If
```

#### C. 報價資訊管理
- **Tool Lead Time** (`txtToolLeadTime`): 工具交期
- **Tooling Capacity** (`txtToolingCapacity`): 工具產能
- **F.O.B.** (`cmbFOB`): 交貨條件
- **GDPEP Category** (`txtGDPIMCategory`): GDPEP 類別

#### D. 經濟水平日期管理
- **Materials** (`txtEconLvDateMaterial` + `chkEconLvDateMaterialNA`)
- **Labor** (`txtEconLvDateLabor` + `chkEconLvDateLaborNA`)
- 支援 "N/A" 選項的日期欄位

#### E. 狀態控制核取方塊
- **Quote Response Ready for Team Consensus Meeting** (`chkQuoteReviewAvailable`)
- **Quote Response ready for Gateway 2 Approval** (`chkQuoteCompleted`)

### Excel 整合功能
- 使用 GemBox.Spreadsheet 處理 Excel 檔案
- 動態載入專案清單資料
- 支援附件檢查機制

---

## Tab 5: Routing.aspx - 路由管理頁面

### 核心功能
工作流程狀態追蹤和管理，提供 CQR 流程的可視化監控。

### 主要功能區塊

#### A. 模擬使用者功能
```vb
If (Request("Imp") <> "") Then
    tCQR.UserId = Request("Imp")
    tCQR.bUserIsOrig = UCase(tCQR.sOriginator) = UCase(Request("Imp"))
    ' 重新設定所有相關屬性
End If
```

#### B. 路由資料顯示
- **Action Comments** (`txtActionComments`): 動作評論
- **Log Comments** (`txtLogComment`): 超級使用者專用日誌
- **Notifications** (`chkShowNotification`): 通知顯示控制
- **路由表格** (`tblResults_`): 顯示完整的工作流程狀態

#### C. JavaScript 交互功能
- 列選擇和高亮顯示
- 通知切換控制
- 動態內容重新整理

### 權限邏輯
基本的檢視控制，超級使用者可存取額外的日誌功能。

---

## Tab 6: Milestones.aspx - 里程碑頁面

### 核心功能
人員指派管理，功能相對簡化。

### 主要欄位
- **Sales Account Manager** (`cmbAMGR`): 銷售客戶經理
- **Sales Account Director** (`cmbSDIR`): 銷售客戶總監

### 業務邏輯
- 使用 `mUPPopulateControl` 填充人員清單
- 簡單的檢視/編輯權限控制
- 直接對應到 CQR 物件的人員屬性

---

## Tab 7: SalesCloseout.aspx - 銷售結案頁面

### 核心功能
報價狀態管理、獲獎狀態追蹤、P&L 資料處理和交接包裹管理。

### 主要業務區塊

#### A. 報價狀態管理
```vb
' 狀態設定邏輯
If tCQR.sQSQuoteStatus = "0" Then
    optQuoteStatusNotQuoted.Checked = True
ElseIf tCQR.sQSQuoteStatus = "1" Then
    optQuoteStatusVerbal.Checked = True
ElseIf tCQR.sQSQuoteStatus = "2" Then
    optQuoteStatusLetter.Checked = True
End If
```

狀態選項：
- **Not Quoted** (`optQuoteStatusNotQuoted`): 未報價
- **Verbal Quote** (`optQuoteStatusVerbal`): 口頭報價  
- **Quote Letter** (`optQuoteStatusLetter`): 正式報價函

#### B. 獲獎狀態追蹤
- **Won/Lost/Other** (`optAwardStatus`): 獲獎結果
- **Date Won** (`txtDateWon`): 獲獎日期
- **Sales Administrator** (`cmbSalesAdministrator`): 銷售管理員

#### C. P&L 資料表格
- 動態產生的盈虧資料網格 (`tblAwardStatus`)
- 支援複雜的財務資料計算和顯示
- Excel 整合的資料處理功能

#### D. JavaScript 控制邏輯
```javascript
function fnGoAwardStatus(bComments) {
    var objAwardW = document.getElementById("optAwardStatusWon");
    var objAwardO = document.getElementById("optAwardStatusOther");
    // 根據獲獎狀態動態控制界面元素
    if (objAwardO.checked && bComments) {
        checkReadOnly2('txtQSCommentsProceedCancel');
    }
}
```

### 權限控制
- 基於 `mHasCloseOutRole()` 的角色檢查
- 任務路由相依的權限控制
- 特殊的帳戶經理權限

---

## Tab 8: Attachment.aspx - 附件管理頁面

### 核心功能
通用附件管理和任務清單（Tasklist）的簽出/簽入管理。

### 主要業務邏輯

#### A. 附件權限管理
```vb
' 複雜的權限邏輯
Dim bIsAttachUser As Boolean = False
If tCQR.bSuperUser Then
    bIsAttachUser = True
Else
    ' 檢查是否為附件創建者
    For iLoop = 0 To tCQR.tAttachmentResponse.tTab.iAttCount - 1
        If tCQR.UserId() = tCQR.tAttachmentResponse.tTab.tAttInfo(iLoop).sCreator Then
            bIsAttachUser = True
            Exit For
        End If
    Next
End If
```

#### B. 任務清單管理
```vb
Private Sub TaskListToScreen(ByVal tCQR As clsCQR)
    Dim canAdd As Boolean = tCQR.CanAddTasklist()
    Dim canCheckout As Boolean = tCQR.CanCheckoutTasklist()
    ' 設定 AceOffix 模式和簽出功能
    If (tCQR.tTasklist.bAceOffixMode = False) Then
        tCQR.tTasklist.bShowCheckout = True
    End If
End Sub
```

#### C. AJAX 重新整理功能
```javascript
function refreshDocument() {
    var thisURL = 'Attachment.aspx?TasklistOnly=1&QueueKey=...';
    $.ajax({
        // 動態重新整理任務清單內容
    });
}
```

#### D. 進階附件功能
- **Forms Button** (`btnAdvancedAttachment`): 開啟進階附件表單
- 彈出視窗界面整合
- 檔案下載和開啟功能

### 安全機制
- 附件創建者權限檢查
- 檢視模式下的完全唯讀控制
- 任務清單的特殊簽出權限驗證

---

## 系統整合特性

### 1. 統一的資料流動
所有頁面透過 `clsCQR` 物件共享資料：
```vb
' 標準的資料載入和儲存流程
Call tCQR.ControlsFromPrevious(Me)  ' 從前一頁載入
If Page.IsPostBack Then
    Call tCQR.CheckSave(Me)  ' 自動儲存
End If
```

### 2. 權限控制架構
- 基於角色的存取控制（RBAC）
- 工作流程狀態相依的權限
- 超級使用者特殊權限
- 檔案鎖定機制

### 3. JavaScript 整合模式
- jQuery UI 元件整合
- AJAX 資料載入
- 動態界面控制
- 即時驗證和提示

### 4. 工作流程整合
- `pageContent.HighlightTab()` 導航管理
- `tCQR.SetSecurity()` 統一安全控制
- `SetHeaderInfo()` 頁面標頭資訊

### 5. 資料驗證機制
- 伺服器端驗證
- 客戶端即時檢查
- 資料格式化和清理
- 重複性檢查

這個分析展現了 CQR 系統完整而複雜的業務邏輯，每個 Tab 頁面都有其特定的功能定位，並透過統一的架構確保系統的一致性和可維護性。