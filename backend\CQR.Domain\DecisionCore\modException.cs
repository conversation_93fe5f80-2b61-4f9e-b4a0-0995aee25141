﻿using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.VisualBasic;
using System.Diagnostics;
using System.Net.Mail;
using System.Net;
using System.Text;

// decisionCore, Version=1.0.9056.27004, Culture=neutral, PublicKeyToken=null
// decisionCore.modException
using System.Runtime.CompilerServices;
using Microsoft.VisualBasic.CompilerServices;
using Microsoft.Extensions.Configuration;

namespace CQR.Domain.DecisionCore;
public sealed class modException
{
    private static int referenceCode = 100000;

    private static string lastException = string.Empty;

    private readonly IConfiguration _config;
    private readonly IHttpContextAccessor _http;


    public modException(IConfiguration config, IHttpContextAccessor http)
    {
        _config = config;
        _http = http;
    }

    public static SmtpClient GetSMTPClientAWS3()
    {
        string host = "email-smtp.us-east-1.amazonaws.com";
        int port = 587;
        string userName = "AKIAUH7YH37C7M4AYE2B";
        string password = "BNp5EhuZWvHhub8RcbJ7x3I0sMZF6POj9FHBYj0hUvTT";
        return new SmtpClient(host, port)
        {
            EnableSsl = true,
            UseDefaultCredentials = false,
            Credentials = new NetworkCredential(userName, password)
        };
    }

    public static SmtpClient GetSMTPClient()
    {
        string host = "SABSWI-AZSMTP01.bcs-abs.com";
        if (Operators.CompareString(Strings.UCase(Environment.MachineName), "SWINUS-AS04", TextCompare: false) == 0)
        {
            host = "smtp-amer1.ad.one-bcs.com";
        }
        if (Operators.CompareString(Strings.UCase(Environment.MachineName), "SAUBNY-TC", TextCompare: false) == 0)
        {
            host = "SABSWI-VMSMTP01.bcs-abs.com";
        }
        if (Operators.CompareString(Strings.UCase(Environment.MachineName), "ED10", TextCompare: false) == 0)
        {
            return GetSMTPClientAWS3();
        }
        return new SmtpClient(host, 25);
    }

    public void DumpExceptionHandler(Exception ex, string asOtherInfo, string asOtherInfoCaption, bool rethrowDumpException, SqlConnection dbConn,
        bool displayMessageAndEndResponse = true)
    {
        string text = string.Empty;
        string text2 = string.Empty;
        var httpContext = _http.HttpContext;
        //HttpSessionState httpSessionState = null;
        string text3 = "TotalConnect";
        try
        {
            if (httpContext != null)
            {
                if (httpContext.Request != null)
                {
                    text = httpContext?.Request?.Host.Host?.ToUpper() ?? "";
                    text2 = httpContext?.User?.Identity?.Name?.ToUpper() ?? "";

                    //text = Strings.UCase( HttpContext.Request.ServerVariables["SERVER_NAME"]);
                    //text2 = Strings.UCase(HttpContext.Current.Request.ServerVariables["REMOTE_USER"]);
                }
                //httpSessionState = HttpContext.Current.Session;
            }
            if (Operators.CompareString(text, "", TextCompare: false) == 0 && dbConn != null)
            {
                text = Strings.UCase(dbConn.WorkstationId);
            }
            text = Strings.LCase(text);
            //if (Strings.LCase(HttpContext.Current.Request.ServerVariables["PATH_INFO"]).Contains("staging"))
            //{
            //    text3 += "-Staging";
            //}
            if (string.IsNullOrEmpty(text2) && dbConn != null)
            {
                text2 = Strings.UCase(dbConn.WorkstationId);
            }
            if (string.IsNullOrEmpty(text2))
            {
                text2 = Strings.UCase(Environment.MachineName);
            }
        }
        catch (Exception ex2)
        {
            ProjectData.SetProjectError(ex2);
            Exception ex3 = ex2;
            ProjectData.ClearProjectError();
        }
        if (rethrowDumpException)
        {
            throw ex;
        }
        if (Operators.CompareString(ex.Message, "File does not exist.", TextCompare: false) == 0)
        {
            return;
        }
        bool flag = (Operators.CompareString(text2, "BCS-ABS\\BCSSERVERADMIN5", TextCompare: false) == 0) | (Operators.CompareString(Environment.MachineName, "ED10", TextCompare: false) == 0) | (Operators.CompareString(Environment.MachineName, "KURT-DDC", TextCompare: false) == 0) | Environment.MachineName.StartsWith("SCOTT") | modTools.isLocalhost_();
        StringBuilder stringBuilder = new StringBuilder();
        checked
        {
            referenceCode++;
            stringBuilder.AppendLine("<div style='font-family:Arial;font-size:10pt'>");
        }
        if (displayMessageAndEndResponse && !flag)
        {
            StringBuilder stringBuilder2 = new StringBuilder(stringBuilder.ToString());
            stringBuilder2.AppendLine("<br>TotalConnect has detected an error. We have logged the error details and will investigate its cause right away.  Please try again, if the error persists contact support.  ");
            //HttpContext.Current.Response.Write(stringBuilder2.ToString());
        }
        else
        {
            stringBuilder.AppendLine("<B>Note:</B> ERROR NOT SHOWN TO USER, RETRY TO FOLLOW<BR>");
        }
        stringBuilder.AppendLine("<B>Exception:</B> " + ex.GetType().ToString() + "<BR>");
        stringBuilder.AppendLine("<B>Message:</B> " + ex.Message.ToString() + "<BR>");
        if (!Information.IsNothing(ex.InnerException))
        {
            stringBuilder.AppendLine("<B>Inner Exception:</B> " + ex.InnerException.GetType().ToString() + "<BR>");
            stringBuilder.AppendLine("<B>Inner Message:</B> " + ex.InnerException.Message.ToString() + "<BR>");
        }
        try
        {
            SmtpClient sMTPClient = GetSMTPClient();
            MailMessage mailMessage = new MailMessage();
            mailMessage.To.Add("<EMAIL>");
            if (Operators.CompareString(Environment.MachineName, "ED10", TextCompare: false) != 0)
            {
                mailMessage.To.Add("<EMAIL>");
            }
            mailMessage.From = new MailAddress("<EMAIL>");
            mailMessage.Subject = Conversions.ToString(Operators.ConcatenateObject(text3 + " Error Detected - " + Conversions.ToString(referenceCode), Interaction.IIf(Operators.CompareString(text, "", TextCompare: false) == 0, "", " (" + text + ")")));
            stringBuilder.AppendLine("<B>Application Server:</B> " + text + "<BR>");
            if (dbConn != null)
            {
                stringBuilder.AppendLine("<B>Database Server:</B> " + dbConn.DataSource + "<BR>");
                stringBuilder.AppendLine("<B>Database:</B> " + dbConn.Database + "<BR>");
            }
            //if (HttpContext.Current != null && HttpContext.Current.Request != null)
            //{
            //    stringBuilder.AppendLine("<B>Application Page:</B> " + HttpContext.Current.Request.ServerVariables["URL"] + "<BR>");
            //    stringBuilder.AppendLine("<B>URL Parameters:</B> " + HttpContext.Current.Request.ServerVariables["QUERY_STRING"] + "<BR>");
            //}
            //stringBuilder.AppendLine(Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject("<B>User Id Impersonate:</B> ", HttpContext.Current.Session["UserImpersonate"]), "<BR>")));
            //stringBuilder.AppendLine("<B>User Impersonate:</B> " + modUserProf.UserNameFromId(Conversions.ToString(HttpContext.Current.Session["UserImpersonate"])) + "<BR>");
            //stringBuilder.AppendLine(Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject("<B>User Id Actual:</B> ", HttpContext.Current.Session["UserActual"]), "<BR>")));
            //stringBuilder.AppendLine("<B>User Actual:</B> " + modUserProf.UserNameFromId(Conversions.ToString(HttpContext.Current.Session["UserActual"])) + "<BR>");
            if (false)
            {
                //stringBuilder.AppendLine("<B>Context Unavailable</B><BR>");
                //if (Information.IsNothing(httpSessionState))
                //{
                //    stringBuilder.AppendLine("<B>Session Is Unavailable</B><BR>");
                //}
                //else
                //{
                //    stringBuilder.AppendLine("<B>Session Count:</B> " + Conversions.ToString(httpSessionState.Count) + "<BR>");
                //}
            }
            if (_http.HttpContext != null)
            {
                if (_http.HttpContext.Request != null)
                {
                    //HttpRequest request = HttpContext.Request;
                    stringBuilder.AppendLine("<BR><B><u>Request Data</u></B><BR>");
                    //stringBuilder.AppendLine("<B>Content Length:</B> " + Conversions.ToString(request.ContentLength) + "<BR>");
                    //stringBuilder.AppendLine("<B>Content Type:</B> " + request.ContentType + "<BR>");
                    //if (!Information.IsNothing(request.Files))
                    //{
                    //    stringBuilder.AppendLine("<br><B>Attached Files:</B><br>");
                    //    HttpFileCollection files = request.Files;
                    //    string[] allKeys = files.AllKeys;
                    //    foreach (string name in allKeys)
                    //    {
                    //        string text4 = ConfigurationManager.AppSettings["DocketTrakFiles"] + "\\ExceptionFiles\\";
                    //        text4 = text4 + "Exception__" + Conversions.ToString(Environment.TickCount) + ".xml";
                    //        stringBuilder.AppendLine("File: " + files[name].FileName + " Content Length: " + Conversions.ToString(files[name].ContentLength) + " Saved Error File:" + text4 + "<br>");
                    //        files[name].SaveAs(text4);
                    //    }
                    //}
                    stringBuilder.AppendLine("<br><br>");
                }
                else
                {
                    stringBuilder.AppendLine("<B>Request:</B> Not Present<BR>");
                }
            }
            else
            {
                stringBuilder.AppendLine("<B>Request:</B> Not Present<BR>");
            }
            stringBuilder.AppendLine("<BR>");
            if ((Operators.CompareString(asOtherInfoCaption, "", TextCompare: false) != 0) | (Operators.CompareString(asOtherInfo, "", TextCompare: false) != 0))
            {
                if (Operators.CompareString(asOtherInfoCaption, "SQL String", TextCompare: false) == 0 && flag)
                {
                    asOtherInfo = Strings.Replace(Strings.Replace(asOtherInfo, "\r\n", "<br>"), "UNCOMMITTED;", "UNCOMMITTED;<br><br>");
                }
                stringBuilder.AppendLine("<B>" + asOtherInfoCaption + ":</B><BR>" + asOtherInfo + "<BR><BR>");
            }
            stringBuilder.AppendLine("<B>Stack:</B><BR>" + Strings.Replace(ex.StackTrace, "\r\n", "<BR>") + "<BR>");
            object instance = new StackTrace(fNeedFileInfo: true);
            int num = Conversions.ToInteger(Operators.SubtractObject(NewLateBinding.LateGet(instance, null, "FrameCount", new object[0], null, null, null), 1));
            for (int j = 0; j <= num; j = checked(j + 1))
            {
                object[] array;
                bool[] array2;
                object obj = NewLateBinding.LateGet(instance, null, "GetFrame", array = new object[1] { j }, null, null, array2 = new bool[1] { true });
                if (array2[0])
                {
                    j = (int)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[0]), typeof(int));
                }
                StackFrame stackFrame = (StackFrame)obj;
                stringBuilder.AppendLine("<BR>");
                stringBuilder.AppendLine($"Method: {stackFrame.GetMethod()}<BR>");
                if (!string.IsNullOrEmpty(stackFrame.GetFileName()))
                {
                    stringBuilder.AppendLine($"&nbsp;&nbsp;&nbsp;File: {stackFrame.GetFileName()}<BR>");
                }
                if (stackFrame.GetFileLineNumber() != 0)
                {
                    stringBuilder.AppendLine($"&nbsp;&nbsp;&nbsp;Line Number: {stackFrame.GetFileLineNumber()}<BR>");
                }
            }
            for (Exception innerException = ex.InnerException; innerException != null; innerException = innerException.InnerException)
            {
                stringBuilder.AppendLine("<BR>");
                stringBuilder.AppendLine("<B>Inner Exception:</B> " + innerException.GetType().ToString() + "<BR>");
                stringBuilder.AppendLine("<B>Message:</B> " + innerException.Message.ToString() + "<BR>");
                if (!string.IsNullOrEmpty(innerException.StackTrace))
                {
                    stringBuilder.AppendLine("<B>Stack:</B><BR>" + Strings.Replace(innerException.StackTrace, "\r\n", "<BR>") + "<BR>");
                }
            }
            //if (HttpContext.Current != null && HttpContext.Current.Request != null)
            //{
            //    HttpBrowserCapabilities browser = HttpContext.Current.Request.Browser;
            //    stringBuilder.AppendLine("<br><B>Browser Capabilities:</B><br>");
            //    stringBuilder.AppendLine("Type = " + browser.Type + "<br>");
            //    stringBuilder.AppendLine("Name = " + browser.Browser + "<br>");
            //    stringBuilder.AppendLine("Version = " + browser.Version + "<br>");
            //    stringBuilder.AppendLine("Major Version = " + Conversions.ToString(browser.MajorVersion) + "<br>");
            //    stringBuilder.AppendLine("Minor Version = " + Conversions.ToString(browser.MinorVersion) + "<br>");
            //    stringBuilder.AppendLine("Platform = " + browser.Platform + "<br>");
            //    stringBuilder.AppendLine("Is Beta = " + Conversions.ToString(browser.Beta) + "<br>");
            //    stringBuilder.AppendLine("Is Crawler = " + Conversions.ToString(browser.Crawler) + "<br>");
            //    stringBuilder.AppendLine("Script Version = " + Conversions.ToString(browser.EcmaScriptVersion.Major) + "." + Conversions.ToString(browser.EcmaScriptVersion.Minor) + "<br>");
            //    stringBuilder.AppendLine("Supports Frames = " + Conversions.ToString(browser.Frames) + "<br>");
            //    stringBuilder.AppendLine("Supports Tables = " + Conversions.ToString(browser.Tables) + "<br>");
            //    stringBuilder.AppendLine("Supports Cookies = " + Conversions.ToString(browser.Cookies) + "<br>");
            //    stringBuilder.AppendLine("Supports VB Script = " + Conversions.ToString(browser.VBScript) + "<br>");
            //    stringBuilder.AppendLine("Supports ActiveX Controls = " + Conversions.ToString(browser.ActiveXControls) + "<br>");
            //    stringBuilder.AppendLine("<br><br>");
            //}
            stringBuilder.AppendLine("</div>");
            if (Operators.CompareString(lastException, stringBuilder.ToString(), TextCompare: false) != 0)
            {
                mailMessage.IsBodyHtml = true;
                mailMessage.Body = Strings.Replace(stringBuilder.ToString(), "%%REFERENCECODE%%", referenceCode.ToString());
                if (flag)
                {
                    //if (HttpContext.Current != null && HttpContext.Current.Request != null)
                    //{
                    //    HttpContext.Current.Response.Write(mailMessage.Body);
                    //}
                }
                else
                {
                    sMTPClient.Send(mailMessage);
                }
            }
            lastException = stringBuilder.ToString();
        }
        catch (Exception ex4)
        {
            ProjectData.SetProjectError(ex4);
            Exception ex5 = ex4;
            ProjectData.ClearProjectError();
        }
        //if (displayMessageAndEndResponse & (HttpContext.Current != null))
        //{
        //    HttpContext.Current.Response.Flush();
        //    HttpContext.Current.Response.End();
        //}
    }
}
