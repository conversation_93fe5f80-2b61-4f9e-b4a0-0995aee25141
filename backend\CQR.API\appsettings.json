{"ConnectionStrings": {"DefaultConnection": "Server=localhost\\MSSQLSERVER02;Database=TempRestore2;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=true;User ID=lyon;Password=******"}, "AzureAdB2C": {"TenentId": "dc94cd8a-025c-455a-9e35-c80612a79987", "Instance": "https://login.microsoftonline.com/", "Domain": "onebcsdev", "ClientId": "0201e396-d84d-4a3b-951d-b7877987bef9", "SignedOutCallbackPath": "https://login.microsoftonline.com/dc94cd8a-025c-455a-9e35-c80612a79987/saml2", "SignUpSignInPolicyId": "https://login.microsoftonline.com/dc94cd8a-025c-455a-9e35-c80612a79987/saml2"}, "SmtpSettings": {"SMTP_PORT": "25", "SMTP_HOST": "***********", "SMTP_SECURE": false, "SMTP_IGNORE_TLS": true, "SMPT_DESC": "bcs smtp", "SMTP_MAILER": "<EMAIL>"}, "SAP": {"AppServerHost": "your.sap.server.host", "SystemNumber": "00", "Client": "100", "User": "yourUsername", "Password": "yourPassword", "Language": "EN"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}