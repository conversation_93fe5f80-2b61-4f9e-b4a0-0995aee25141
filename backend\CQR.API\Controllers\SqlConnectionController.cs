using CQR.Domain.IServices;
using Microsoft.AspNetCore.Mvc;
// using SqlConnectionTest.Services;

namespace CQR.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SqlConnectionController : ControllerBase
    {
        private readonly ISqlConnectionService _sqlConnectionService;

        public SqlConnectionController(ISqlConnectionService sqlConnectionService)
        {
            _sqlConnectionService = sqlConnectionService;
        }

        [HttpGet("test-connection")]
        public async Task<IActionResult> TestConnection()
        {
            var result = await _sqlConnectionService.TestConnectionAsync();
            return Ok(result);
        }

        //[HttpGet("get-data")]
        //public async Task<IActionResult> GetData()
        //{
        //    var result = await _sqlConnectionService.GetDataFromDatabaseAsync();
        //    return Ok(result);
        //}
    }
}
