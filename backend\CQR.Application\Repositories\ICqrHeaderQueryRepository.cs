﻿using CQR.Application.Dto.RoutingTask;

namespace CQR.Application.Repositories;

public interface ICqrHeaderQueryRepository
{
    Task<long?> GetPreviousRevisionQueueKey(string projectNumber, string revision, int modifiedRevision);

    Task<IEnumerable< RoutingTaskDto>> GetRoutingTaskByQueuekey(int queueKey,string fodlerType);

    // 搜尋功能相關方法
    Task<T> ExecuteScalarAsync<T>(string sql, object parameters = null);
    Task<IEnumerable<T>> QueryAsync<T>(string sql, object parameters = null);

    //Task<List<UserDto>> GetUsersWithRolesAsync(string[]? roles = null, string[]? locationCodes = null);
}
