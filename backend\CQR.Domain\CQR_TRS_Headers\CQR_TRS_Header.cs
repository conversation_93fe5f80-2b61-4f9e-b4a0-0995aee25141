﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace CQR.Domain.CQR_TRS_Headers;

[Table("TRS_Header")]
public class CQR_TRS_Header //: BaseEntity<int>
{
    [Key]
    public int QueueKey { get; set; }
    public string? lock_userid { get; set; } = null!;
    public DateTime? lock_time { get; set; }
    public string? table_name { get; set; } = null!;
    public string? table_entry { get; set; } = null!;
    public string? _desc { get; set; }
    public string? child { get; set; }
    public string? format_data { get; set; }

    public string? inactive { get; set; }

    public string? restricted { get; set; }

    public DateTime? CreatedDate { get; set; }

    //public string? Created_By { get; set; }

    public DateTime? LastUpdatedDate { get; set; }

    public string? LastUpdatedBy { get; set; }
}

