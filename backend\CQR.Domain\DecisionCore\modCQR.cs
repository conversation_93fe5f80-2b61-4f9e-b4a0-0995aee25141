﻿using GemBox.Spreadsheet;
using Microsoft.VisualBasic;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;


// CQR, Version=1.0.9223.23847, Culture=neutral, PublicKeyToken=null
// CQR.modCQR
using System.Runtime.CompilerServices;
using Microsoft.VisualBasic.CompilerServices;

namespace CQR.Domain.DecisionCore;
[StandardModule]
public class modCQR
{
    public const int OPEN_Edit = 1;

    public const int OPEN_View = 2;

    public const string USER_AKOVACIC = "ASEIBERT";

    public const string BUSPLAN_TARGET = "00000001";

    public const string BUSPLAN_REPLACEMENTTARGET = "00000002";

    public const string BUSPLAN_OPPORTUNITY = "00000003";

    public const string BUSPLAN_OTHER = "00000004";

    public static void OptionalMessage(corePage atPage, string sMsg, string linkBack = "releaseOptionalLink")
    {
        StringBuilder str = new StringBuilder();
        str.Append("<script type=\"text/javascript\">\r\n");
        str.Append("   OptionalMessage('" + Strings.Replace(sMsg, "'", "`") + "','" + linkBack + "');");
        str.Append("</script>\r\n");
        //atPage.ClientScript.RegisterStartupScript(atPage.GetType(), "optionalMessage", str.ToString());
    }

    public static void AlertMessage(corePage atPage, string sMsg, string sCaption)
    {
        string script = "";
        script += "<script type='text/javascript'>";
        script = script + "   AlertMessage('" + Strings.Replace(sCaption, "'", "\\'") + "',\"" + sMsg + "\",'auto','auto',true);";
        script += "</script>";
        //atPage.ClientScript.RegisterClientScriptBlock(atPage.GetType(), "alertMessage", script);
    }

    public static object getSessionObject(object atPage, int aiQueueKey)
    {
        return getSessionCQR(RuntimeHelpers.GetObjectValue(atPage), aiQueueKey);
    }

    public static clsCQR getSessionCQR(object atPage, int aiQueueKey)
    {
        clsCQR getSessionCQR = (clsCQR)NewLateBinding.LateGet(atPage, null, "Session", new object[1] { "CQR." + Conversions.ToString(aiQueueKey) }, null, null, null);
        if (getSessionCQR == null)
        {
            //NewLateBinding.LateCall(NewLateBinding.LateGet(atPage, null, "Response", new object[0], null, null, null), null, "Redirect", new object[1] { getRedirectPage() }, null, null, null, IgnoreReturn: true);
        }
        if (getSessionCQR.bFolderWasLocked)
        {
            getSessionCQR.bFolderWasLocked = false;
            string sUserName = modUserProf.UserNameFromId(Conversions.ToString(NewLateBinding.LateGet(atPage, null, "Session", new object[1] { "CONCERN.LOCKEDBY." + Conversions.ToString(aiQueueKey) }, null, null, null)));
            if (Operators.CompareString(Strings.LCase(sUserName), "super", TextCompare: false) == 0)
            {
                sUserName = "an unknown user";
            }
            AlertMessage((corePage)atPage, "The requested folder was locked by '" + sUserName + "', it has been opened in View mode.", "Folder Locked");
            getSessionCQR.StoreInSession(RuntimeHelpers.GetObjectValue(atPage));
        }
        if (getSessionCQR.iOpenMode == 1 && atPage is corePage)
        {
            if (Operators.ConditionalCompareObjectEqual(NewLateBinding.LateGet(atPage, null, "cnExecuteForSingleValue", new object[1] { "SELECT lock_userid FROM CQR_Header WHERE QueueKey = " + Conversions.ToString(aiQueueKey) }, null, null, null), getSessionCQR.UserId, TextCompare: false))
            {
                clsCQR clsCQR2;
                object[] obj = new object[4]
                {
                    "CQR_Header",
                    "QueueKey",
                    aiQueueKey,
                    (clsCQR2 = getSessionCQR).UserId
                };
                object[] array = obj;
                bool[] obj2 = new bool[4] { false, false, true, true };
                bool[] array2 = obj2;
                NewLateBinding.LateCall(atPage, null, "FolderLock", obj, null, null, obj2, IgnoreReturn: true);
                if (array2[2])
                {
                    aiQueueKey = (int)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[2]), typeof(int));
                }
                if (array2[3])
                {
                    clsCQR2.UserId = (string)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[3]), typeof(string));
                }
            }
            else
            {
                getSessionCQR.iOpenMode = 2;
                getSessionCQR.StoreInSession(RuntimeHelpers.GetObjectValue(atPage));
            }
        }
        return getSessionCQR;
    }

    public static string mStripExt(string asPath)
    {
        string sTemp = asPath;
        int num = Strings.Len(asPath);
        checked
        {
            for (int iLoop = num; iLoop >= 1; iLoop += -1)
            {
                if (Operators.CompareString(Strings.Mid(asPath, iLoop, 1), ".", TextCompare: false) == 0)
                {
                    sTemp = Strings.Mid(asPath, 1, iLoop - 1);
                    break;
                }
                if ((Operators.CompareString(Strings.Mid(asPath, iLoop, 1), "\\", TextCompare: false) == 0) | (Operators.CompareString(Strings.Mid(asPath, iLoop, 1), "/", TextCompare: false) == 0) | (Operators.CompareString(Strings.Mid(asPath, iLoop, 1), ":", TextCompare: false) == 0))
                {
                    break;
                }
            }
            return sTemp;
        }
    }

    public static string mStripPath(string asPath)
    {
        string sTemp = asPath;
        int num = Strings.Len(asPath);
        checked
        {
            for (int iLoop = num; iLoop >= 1; iLoop += -1)
            {
                if ((Operators.CompareString(Strings.Mid(asPath, iLoop, 1), "\\", TextCompare: false) == 0) | (Operators.CompareString(Strings.Mid(asPath, iLoop, 1), "/", TextCompare: false) == 0) | (Operators.CompareString(Strings.Mid(asPath, iLoop, 1), ":", TextCompare: false) == 0))
                {
                    sTemp = Strings.Mid(asPath, iLoop + 1, Strings.Len(asPath) - iLoop);
                    break;
                }
            }
            return sTemp;
        }
    }

    //public static void SetHeaderInfo(corePage tPage, pageHeader pageHdr)
    //{
    //    if (!tPage.IsPostBack)
    //    {
    //        int iQueueKey = Conversions.ToInteger(tPage.Request["QueueKey"]);
    //        clsCQR tConcern = getSessionCQR(tPage, iQueueKey);
    //        tConcern.SetHeaderInfo(pageHdr);
    //    }
    //}

    public static string mRemoveChars(string asValue)
    {
        string sTemp = asValue;
        sTemp = Strings.Replace(sTemp, " ", "");
        return Strings.Replace(sTemp, "*", "%");
    }
    public static string mCommifyDollarAmount(string numberStr, int places = 0)
    {
        if (Operators.CompareString(numberStr, "", TextCompare: false) == 0)
        {
            return "";
        }
        return Strings.Format(Conversion.Val(numberStr), Conversions.ToString(Operators.AddObject("###,###,##0", Interaction.IIf(places == 0, "", RuntimeHelpers.GetObjectValue(Interaction.IIf(places == 2, ".00", ".0000"))))));
    }

    public static string mDecommifyDollarAmount(string numberStr, int places = 0)
    {
        return Strings.Replace(Strings.Replace(numberStr, ",", ""), "$", "");
    }

    public static string Get_AttachDirectory()
    {
        return "../Attachments";
    }

    //public static void setRedirectPage()
    //{
    //    HttpContext.Current.Session["CQR_OpenedFrom"] = HttpContext.Current.Request["AppMgr"];
    //}

    //public static string getRedirectPage()
    //{
    //    if (Operators.ConditionalCompareObjectEqual(HttpContext.Current.Session["CQR_OpenedFrom"], "1", TextCompare: false))
    //    {
    //        string sSuffix = "";
    //        if (Strings.InStr(Strings.LCase(HttpContext.Current.Request["PATH_INFO"]), "_staging") > 0)
    //        {
    //            sSuffix = "_staging";
    //        }
    //        return "/ApplicationManager" + sSuffix;
    //    }
    //    if (Operators.ConditionalCompareObjectEqual(HttpContext.Current.Session["LastSearch"], "REPORTS", TextCompare: false))
    //    {
    //        return "../Source/SearchView.aspx";
    //    }
    //    return "../Source/Search.aspx";
    //}

    //public static void TextAreaBG(TextBox textArea)
    //{
    //    if (Operators.CompareString(textArea.Attributes["OnClick"], "", TextCompare: false) == 0)
    //    {
    //        textArea.Style.Add("background-color", "silver");
    //    }
    //}

    public static void SetGemBoxLicense()
    {
        SpreadsheetInfo.SetLicense("E3JO-TSTN-883A-LM58");
    }

    public static string LatinToAscii(string inString)
    {
        StringBuilder newString = new StringBuilder();
        string charString = "";
        inString = Regex.Replace(inString, "[\\x00-\\x1f]", string.Empty);
        checked
        {
            int num = inString.Length - 1;
            for (int iNdx = 0; iNdx <= num; iNdx++)
            {
                charString = inString.Substring(iNdx, 1);
                charString = charString.Normalize(NormalizationForm.FormKD);
                if (charString.Length == 1)
                {
                    newString.Append(charString);
                    continue;
                }
                int charsCopied = 0;
                int num2 = charString.Length - 1;
                for (int jNdx = 0; jNdx <= num2; jNdx++)
                {
                    char ch = Conversions.ToChar(charString.ToCharArray().GetValue(jNdx));
                    UnicodeCategory ucChar = CharUnicodeInfo.GetUnicodeCategory(ch);
                    if (ucChar != UnicodeCategory.NonSpacingMark)
                    {
                        newString.Append(ch);
                        charsCopied++;
                    }
                }
                if (charsCopied == 0)
                {
                    newString.Append(inString.Substring(iNdx, 1));
                }
            }
            return newString.ToString();
        }
    }

    public static string GetCellValue(ref ExcelWorksheet acSheet, int aiRow, int aiColumn)
    {
        string GetCellValue;
        try
        {
            string holder = Strings.Trim(Conversions.ToString(acSheet.Cells[aiRow, aiColumn].Value));
            GetCellValue = LatinToAscii(Regex.Replace(holder, "\\xA0", " ").Trim());
        }
        catch (Exception ex2)
        {
            ProjectData.SetProjectError(ex2);
            Exception ex = ex2;
            GetCellValue = "";
            ProjectData.ClearProjectError();
        }
        return GetCellValue;
    }

    public static bool isABS()
    {
        //return Operators.CompareString(ConfigurationManager.AppSettings["isABS"], "1", TextCompare: false) == 0;
        //return Operators.CompareString(ConfigurationManager.AppSettings["isABS"], "1", TextCompare: false) == 0;
        return false;
    }
}

