﻿using CQR.Domain.Interfaces.Repositories;

namespace CQR.Domain.CQRHeaders;

public interface ICQRHeaderRepository : IEntityRepository<CQR_Header, int>
{
    //Task<CQR_Header> GetByQueueKeyAsync(int queueKey);
    //Task<IEnumerable<CQR_Header>> GetByAllAsync();

    Task<bool> CreateNewCQRAsync(string userId);

    Task<bool> ExistProjectNbr(int modifiedProjInt);
    Task<bool> ExistProjectNbrAndRevNbr(int modifiedProjNbr,int modifiedRevNbr);

    Task<bool> UpdateCQRHeader(int queueKey, CQR_Header model, bool exit = false);

    Task<string> SearchsUsedByOtherCQR(int sProjectNum, int sUniqueNumber);
        



}
