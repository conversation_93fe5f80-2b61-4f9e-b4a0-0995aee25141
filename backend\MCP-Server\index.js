import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import Anthropic from '@anthropic-ai/sdk';
import OpenAI from 'openai';
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

dotenv.config();

// 初始化不同的模型客戶端
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// 模型配置
const MODELS = {
  'claude-3-sonnet': {
    provider: 'anthropic',
    model: 'claude-3-sonnet-20240229',
    description: '適合複雜推理和代碼分析'
  },
  'claude-3-haiku': {
    provider: 'anthropic', 
    model: 'claude-3-haiku-20240307',
    description: '快速響應，適合簡單查詢'
  },
  'gpt-4': {
    provider: 'openai',
    model: 'gpt-4',
    description: '強大的通用能力'
  },
  'gpt-3.5-turbo': {
    provider: 'openai',
    model: 'gpt-3.5-turbo',
    description: '快速且經濟'
  }
};

// CQR 項目上下文
const CQR_PROJECT_CONTEXT = `
你正在協助開發一個 CQR (Customer Quote Request) 系統：

架構：
- Backend: C# .NET 8, ASP.NET Core Web API
- Database: SQL Server, SqlSugar ORM
- Architecture: DDD + CQRS + Repository Pattern
- Services: ApplicationServices 集中管理所有服務

主要組件：
- ApplicationServices: 統一服務入口
- CQRQueryService: 查詢服務
- CQRCommandHandler: 命令處理
- RepositoryCollection: 數據倉儲集合

編碼慣例：
- 使用 async/await 模式
- 方法命名：GetByIdAsync, SaveAsync
- 依賴注入：Interface + Implementation
- 錯誤處理：統一異常處理中間件
`;

class CQRMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'cqr-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          prompts: {},
        },
      }
    );

    this.setupHandlers();
  }

  setupHandlers() {
    // 工具處理器
    this.server.setRequestHandler('tools/list', async () => ({
      tools: [
        {
          name: 'ask_model',
          description: '向指定模型提問',
          inputSchema: {
            type: 'object',
            properties: {
              model: {
                type: 'string',
                enum: Object.keys(MODELS),
                description: '選擇要使用的模型'
              },
              question: {
                type: 'string',
                description: '要問的問題'
              },
              context: {
                type: 'string',
                description: '額外的上下文信息',
                default: ''
              }
            },
            required: ['model', 'question']
          }
        },
        {
          name: 'compare_models',
          description: '比較不同模型對同一問題的回答',
          inputSchema: {
            type: 'object',
            properties: {
              models: {
                type: 'array',
                items: { type: 'string', enum: Object.keys(MODELS) },
                description: '要比較的模型列表'
              },
              question: {
                type: 'string',
                description: '要問的問題'
              }
            },
            required: ['models', 'question']
          }
        },
        {
          name: 'get_project_context',
          description: '獲取 CQR 項目的上下文信息',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        }
      ]
    }));

    // 執行工具
    this.server.setRequestHandler('tools/call', async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'ask_model':
          return await this.askModel(args.model, args.question, args.context);
        
        case 'compare_models':
          return await this.compareModels(args.models, args.question);
        
        case 'get_project_context':
          return {
            content: [
              {
                type: 'text',
                text: CQR_PROJECT_CONTEXT
              }
            ]
          };
        
        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });

    // 提示詞處理器
    this.server.setRequestHandler('prompts/list', async () => ({
      prompts: [
        {
          name: 'cqr_code_review',
          description: 'CQR 項目代碼審查提示詞'
        },
        {
          name: 'cqr_architecture_analysis',
          description: 'CQR 架構分析提示詞'
        }
      ]
    }));

    this.server.setRequestHandler('prompts/get', async (request) => {
      const { name } = request.params;
      
      const prompts = {
        'cqr_code_review': `
作為 CQR 項目的高級開發者，請審查以下代碼：

審查重點：
1. 是否符合 DDD 架構原則
2. 是否正確使用 Repository Pattern
3. 異步方法的正確使用
4. 依賴注入的實現
5. 錯誤處理機制
6. 性能優化建議

項目上下文：
${CQR_PROJECT_CONTEXT}
        `,
        'cqr_architecture_analysis': `
作為系統架構師，請分析 CQR 項目的架構設計：

分析角度：
1. 分層架構的合理性
2. 服務間的耦合度
3. 可擴展性和維護性
4. 性能考量
5. 安全性設計
6. 改進建議

項目上下文：
${CQR_PROJECT_CONTEXT}
        `
      };

      return {
        description: `CQR 項目專用提示詞：${name}`,
        arguments: [
          {
            name: 'code_or_design',
            description: '要審查的代碼或設計',
            required: true
          }
        ],
        messages: [
          {
            role: 'user',
            content: {
              type: 'text',
              text: prompts[name]
            }
          }
        ]
      };
    });
  }

  async askModel(modelName, question, additionalContext = '') {
    const modelConfig = MODELS[modelName];
    if (!modelConfig) {
      throw new Error(`Model ${modelName} not found`);
    }

    const fullPrompt = `
${CQR_PROJECT_CONTEXT}

${additionalContext ? `Additional Context: ${additionalContext}` : ''}

Question: ${question}
    `;

    try {
      let response;
      
      if (modelConfig.provider === 'anthropic') {
        const message = await anthropic.messages.create({
          model: modelConfig.model,
          max_tokens: 4000,
          messages: [{ role: 'user', content: fullPrompt }]
        });
        response = message.content[0].text;
      } else if (modelConfig.provider === 'openai') {
        const completion = await openai.chat.completions.create({
          model: modelConfig.model,
          messages: [{ role: 'user', content: fullPrompt }],
          max_tokens: 4000
        });
        response = completion.choices[0].message.content;
      }

      return {
        content: [
          {
            type: 'text',
            text: `**${modelName}** (${modelConfig.description}):\n\n${response}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error calling ${modelName}: ${error.message}`
          }
        ]
      };
    }
  }

  async compareModels(models, question) {
    const results = await Promise.allSettled(
      models.map(model => this.askModel(model, question))
    );

    let comparison = `# 模型比較結果\n\n**問題:** ${question}\n\n---\n\n`;
    
    results.forEach((result, index) => {
      const modelName = models[index];
      if (result.status === 'fulfilled') {
        comparison += `${result.value.content[0].text}\n\n---\n\n`;
      } else {
        comparison += `**${modelName}**: Error - ${result.reason}\n\n---\n\n`;
      }
    });

    return {
      content: [
        {
          type: 'text',
          text: comparison
        }
      ]
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
  }
}

// HTTP API (可選)
const app = express();
app.use(cors());
app.use(express.json());

app.get('/models', (req, res) => {
  res.json(MODELS);
});

app.post('/ask', async (req, res) => {
  const { model, question, context } = req.body;
  
  try {
    const server = new CQRMCPServer();
    const result = await server.askModel(model, question, context);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`MCP Server HTTP API running on port ${PORT}`);
});

// 啟動 MCP Server
if (process.argv.includes('--mcp')) {
  const server = new CQRMCPServer();
  server.run().catch(console.error);
} else {
  console.log('Use --mcp flag to run as MCP server, or access HTTP API on port', PORT);
}