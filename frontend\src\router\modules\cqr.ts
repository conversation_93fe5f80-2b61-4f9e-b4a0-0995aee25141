import { cqr } from "@/router/enums";

export default {
  path: "/cqr",
  meta: {
    title: "CQR管理", // 标题可根据需要调整
    rank: cqr
  },
  children: [
    {
      path: "searchCQR", // ✅ 正确
      name: "cqr",
      component: () => import("@/views/CQRSearch/index.vue"),
      meta: {
        title: "CQR查找",
        showLink: true
      }
    },
    {
      path: "printCQR", // ✅ 正确
      name: "printCqr",
      component: () => import("@/views/cqr/printCQR.vue"),
      meta: {
        title: "Print CQR",
        showLink: true
      }
    },

    {
      path: "TabLayout/:queueKey", // ✅ 正确
      name: "tabLayout",
      component: () => import("@/views/cqr/TabLayout.vue"),

      meta: {
        title: "TabLayout",
        showLink: false,
        hiddenTag: true
      },

      children: [
        {
          path: "tabA",
          name: "tab<PERSON>",
          component: () => import("@/views/cqr/elTabs/TabA.vue"),
          meta: {
            title: "TabA",

            keepAlive: true
          }
        },
        {
          path: "tabB",
          name: "tabB",
          component: () => import("@/views/cqr/elTabs/TabB.vue"),
          meta: {
            title: "TabB",
            keepAlive: true,
            rank: 104
          }
        }
      ]
    },

    {
      path: "editCQR/:queueKey",
      name: "cqrEdit",
      component: () => import("@/views/cqr/EditCQR.vue"),
      meta: {
        title: "编辑 CQR",
        // showLink: false,
        // hiddenTag: true,
        // icon: "ep:edit",
        showParent: false,
        hidden: true, // 👈 加上這一行
        showLink: false,
        // hidden: true,       // ❗隱藏於側邊欄
        hiddenTag: true // ❗隱藏於頁籤
      },
      children: [
        {
          path: "description",
          name: "Description",
          component: () => import("@/views/cqr/tabs/DescriptionTab.vue"),
          props: true, // ✅ 启用将路由参数作为 props 传递
          meta: {
            title: "Description",
            hidden: true, // ✅ 隱藏在側邊欄
            hiddenTag: true // ✅ 隱藏在 tab 標籤
          }
        },
        {
          path: "engineering",
          name: "EngineeringTab",
          component: () => import("@/views/cqr/tabs/EngineerTab.vue"),
          meta: {
            title: "Engineering",
            hidden: true, // ✅ 隱藏在側邊欄
            hiddenTag: true // ✅ 隱藏在 tab 標籤
          }
        },
        {
          path: "quote-response",
          name: "QuoteResponseTab",
          component: () => import("@/views/cqr/tabs/QuoteResponseTab.vue"),
          meta: {
            title: "Quote Response",
            hidden: true, // ✅ 隱藏在側邊欄
            hiddenTag: true // ✅ 隱藏在 tab 標籤
          }
        },
        {
          path: "sales-closeout",
          name: "SalesCloseoutTab",
          component: () => import("@/views/cqr/tabs/SalesCloseoutTab.vue"),
          meta: {
            title: "Sales Closeout",
            hidden: true, // ✅ 隱藏在側邊欄
            hiddenTag: true // ✅ 隱藏在 tab 標籤
          }
        },
        {
          path: "gdpim-gateway/:gateway?",
          name: "GDPIMGatewayTab",
          component: () => import("@/views/cqr/tabs/GDPIMGatewayTab.vue"),
          meta: {
            title: "GDPIM Gateway"
          }
        },
        {
          path: "attachment",
          name: "AttachmentTab",
          component: () => import("@/views/cqr/tabs/AttachmentTab.vue"),
          meta: {
            title: "Attachment",
            hidden: true, // ✅ 隱藏在側邊欄
            hiddenTag: true // ✅ 隱藏在 tab 標籤
          }
        },
        {
          path: "routing",
          name: "RoutingTab",
          component: () => import("@/views/cqr/tabs/RoutingTab.vue"),
          meta: {
            title: "Routing",
            hidden: true, // ✅ 隱藏在側邊欄
            hiddenTag: true // ✅ 隱藏在 tab 標籤
          }
        }
        // {
        //   path: "", // This matches if no sub-path is provided
        //   redirect: { name: "Description" } // Redirect to the 'Description' tab by name
        // }
        // {
        //   path: "",
        //   redirect: "description"
        // }
      ]
      // children: [
      //   {
      //     path: "request", // 子路由不需要 :id，會自動繼承父路由的 id
      //     name: "RequestTab",
      //     component: () => import("@/views/cqr/tabs/RequestTab.vue"),
      //     meta: {
      //       title: "菜单1-1",
      //       hiddenTag: true,
      //       hidden: true, // ✅ 不在側邊欄顯示
      //       // 通过设置showParent为true，显示父级

      //       showParent: true
      //     }
      //   },
      //   {
      //     path: "engineer",
      //     name: "EngineerTab",
      //     component: () => import("@/views/cqr/tabs/EngineerTab.vue"),
      //     meta: {
      //       title: "菜单1-2",
      //       hiddenTag: true,
      //       hidden: true, // ✅ 不在側邊欄顯示
      //       // 通过设置showParent为true，显示父级
      //       showParent: true
      //     }
      //   },
      //   {
      //     path: "quote-response",
      //     name: "QuoteResponseTab",
      //     component: () => import("@/views/cqr/tabs/QuoteResponseTab.vue"),
      //     meta: {
      //       title: "菜单1-3",
      //       hiddenTag: true,
      //       hidden: true, // ✅ 不在側邊欄顯示
      //       // 通过设置showParent为true，显示父级
      //       showParent: true
      //     }
      //   }
      // ]
    }
  ] as Array<RouteConfigsTable>
} satisfies RouteConfigsTable;
