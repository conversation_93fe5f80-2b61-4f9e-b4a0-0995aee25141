﻿using CQR.Domain.CQR_GDPIMPhases;
using CQR.Domain.CQR_IHSs;
using CQR.Domain.CQRHeaders;
using CQR.Domain.CQRIHSFolder;
using CQR.Domain.ROUTING_RGHeaders;

namespace CQR.Domain.Interfaces.Repository;

public interface IUnitOfWork : IDisposable
{
    //ICustomerRepository Customers { get; }
    //IOrderRepository Orders { get; }
    //Task<int> SaveAsync(); // EF 的 SaveChangesAsync


    //ICQRIHSFolderRepository CQRIHSFolders { get; }

    ICQRIHSRepository CQRIHSRepository { get; }
    ICQRHeaderRepository CQRHeaderRepository { get; }
    ICQRIHSFolderRepository CQRIHSFolderRepository { get; }
    IRoutingRGHeaderRepository RoutingRGHeaderRepository { get; }
    //IAttachFileQueryRepository AttachFileQueryRepository { get; }
    ICQRGDPIMPhasesRepository CQRGDPIMPhasesRepository { get; }
    //ICqrHeaderQueryRepository CqrHeaderQueryRepository { get; }
    //IUserRoleQueryRepository UserRoleQueryRepository { get; }
    //ITRSHeaderQueryRespository TRSHeaderQueryRepository { get; }
    //ICQRAntaresQueryRepository CQRAntaresQueryRepository { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
