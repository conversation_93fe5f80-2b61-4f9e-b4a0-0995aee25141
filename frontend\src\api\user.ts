import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
export type CqrHeaderResult = {
  accountMgrId: string;
  actionComments: string;
  approxAnnualValue: string;
  bkRndInfComments: string;
  businessClassif: string | null;
  commissionPercent: string;
  commissionReqdInd: string;
  costEstimatorId: string;
  currentSupplier: string | null;
  custBuyerName: string;
  custEngineerName: string;
  custNbr: string;
  custProdComments: string | null;
  custQuoteDueDate: string;
  customerJob1: string | null;
  designPropReqdInd: string;
  dueDateFromEng: string;
  dueDateToBnE: string;
  elecInputReqdInd: string;
  elecInputReqdInd2: string | null;
  elecInputReqdInd3: string | null;
  elecInputReqdInd4: string | null;
  elecPETMId: string;
  elecPETMId2: string | null;
  elecPETMId3: string | null;
  elecPETMId4: string | null;
  engPkgComments: string | null;
  engineeringManagerId: string | null;
  engineeringSite: string | null;
  franClosedOut: string;
  franDesc: string;
  franIssueDate: string;
  gateway: string | null;
  healthAndSafetyInd: string;
  ifCheckedDoneInd: string;
  ifCheckedInd: string;
  inForecastInd: string;
  informationOKInd: string;
  intPricComments: string;
  lastUpdatedBy: string;
  lastUpdatedDate: string;
  lastUpdatedTime: string;
  lastUpdatedVersion: string;
  locationEngineer: string | null;
  locationSales: string | null;
  locationShipping: string | null;
  lockTime: string | null;
  lockUserId: string | null;
  manufacturingSite: string;
  mfgRepCompany: string;
  mfgRepIndividual: string;
  milestone1Date: string;
  milestone2Date: string;
  milestone3Date: string;
  milestone4Date: string;
  milestone5Date: string;
  milestone6Date: string;
  modNeedsCostInd: string;
  modelYear: string;
  newModRepl: string;
  noRctFolders: string;
  obsolescenceReqdInd: string | null;
  originalProductLife: string;
  originationDate: string;
  originatorId: string;
  otherInfo: string | null;
  paId: string;
  performanceRqmts: string | null;
  petmId: string;
  pgmId: string;
  piecePriceReqdInd: string;
  pimSite: string | null;
  prdId: string;
  productCategory: string | null;
  productDesc: string;
  productLine: string | null;
  productionReqdInd: string;
  projectNbr: string;
  prototypeQty: string;
  prototypeReqdInd: string;
  psmId: string;
  qafComments: string | null;
  qrLaborDate: string;
  qrMaterialDate: string;
  qrProgramClass: string;
  qrToolLeadTime: string;
  qrToolingCapacity: string;
  qrToolingComments: string;
  qrfob: string;
  qsAwardStatus: string | null;
  qsCommentId: string;
  qsComments: string | null;
  qsCustomerAuth: string;
  qsCustomerFran: string;
  qsCustomerPO: string;
  qsCustomerResp: string;
  qsQuoteDate: string;
  qsQuoteStatus: string;
  queueKey: number;
  quoteCompletedSent: string;
  quoteResponseDueDate: string;
  quoteResponseSent: string;
  quoteType: string;
  remainingProductLife: string;
  responseLevel: string | null;
  revNbr: string;
  rfqRcvdDate: string;
  rfqRefNbr: string;
  salesAcctDirectorId: string;
  status: string;
  statusDate: string;
  timeframeOKInd: string;
  timingPlanReqdInd: string;
  toolingReqdInd: string;
  unused1: string;
  unused2: string;
  unused_002: string;
  vehicle: string;
  volumePerAnnum: string;
  vpaComments: string | null;
  warrantyRqmts: string | null;
  workProceedOKInd: string;
};

export type UserResult = {
  success: boolean;
  data: {
    /** 头像 */
    avatar: string;
    /** 用户名 */
    username: string;
    /** 昵称 */
    nickname: string;
    /** 当前登录用户的角色 */
    roles: Array<string>;
    /** 按钮级别权限 */
    permissions: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type UserInfo = {
  /** 头像 */
  avatar: string;
  /** 用户名 */
  username: string;
  /** 昵称 */
  nickname: string;
  /** 邮箱 */
  email: string;
  /** 联系电话 */
  phone: string;
  /** 简介 */
  description: string;
};

export type UserInfoResult = {
  success: boolean;
  data: UserInfo;
};

type ResultTable = {
  success: boolean;
  data?: {
    /** 列表数据 */
    list: Array<any>;
    /** 总条目数 */
    total?: number;
    /** 每页显示条目个数 */
    pageSize?: number;
    /** 当前页数 */
    currentPage?: number;
  };
};

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<UserResult>("post", "/login", { data });
};

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refresh-token", { data });
};

/** 账户设置-个人信息 */
export const getMine = (data?: object) => {
  return http.request<UserInfoResult>("get", "/mine", { data });
};

/** 账户设置-个人安全日志 */
export const getMineLogs = (data?: object) => {
  return http.request<ResultTable>("get", "/mine-logs", { data });
};

export const getUserFullNameByRole = async (role: string) => {
  // role = "CQRHRCOST";
  return http.request<any>("get", baseUrlApi(`User/getUserRole/${role}`), {});
};
// export const getRoleUserCollection = async () => {
//   // role = "CQRHRCOST";
//   return http.request<HttpResponseResult>(
//     "get",
//     baseUrlApi(`User/getUserRole/${role}`),
//     {}
//   );
// };
