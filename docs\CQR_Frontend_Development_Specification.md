# CQR 系統前端開發規格書

## 📋 文件資訊

| 項目 | 內容 |
|------|------|
| 文件版本 | v1.0 |
| 創建日期 | 2024-01-21 |
| 系統名稱 | Customer Quote Request (CQR) System |
| 技術架構 | ASP.NET WebForms + jQuery + CSS |
| 目標 | 企業級報價管理系統前端開發規範 |

## 🎯 系統概述

CQR 系統前端負責提供直觀、高效的用戶界面，支援汽車零件報價管理的完整工作流程。系統採用分頁式設計，每個頁面專注於特定的業務功能，通過統一的導航和狀態管理確保用戶體驗的一致性。

### 核心設計原則
- **業務驅動**：界面設計完全服務於業務流程
- **用戶友好**：降低學習成本，提高操作效率
- **響應式設計**：支援不同螢幕尺寸和解析度
- **漸進增強**：基礎功能在所有瀏覽器可用，高級功能漸進提供

## 🏗️ 技術架構

### 前端技術棧

```
展示層 (Presentation Layer)
├── ASP.NET WebForms (.aspx/.aspx.vb)
├── HTML5 + CSS3
├── JavaScript (ES5 相容)
└── 第三方庫整合

樣式層 (Styling Layer)  
├── 960 Grid System (主要佈局)
├── Custom CSS (業務樣式)
├── jQuery UI Themes
└── 響應式 CSS

腳本層 (Scripting Layer)
├── jQuery 1.10.2+ (核心庫)
├── jQuery UI 1.11.4+ (UI 組件)
├── 自訂 JavaScript 模組
└── 第三方整合腳本

資源層 (Assets Layer)
├── 靜態圖片資源
├── 字型檔案
├── 圖示系統
└── 多媒體資源
```

### 檔案結構標準

```
/Source/              # 核心頁面目錄
├── *.aspx            # WebForms 頁面
├── *.aspx.vb         # 頁面後端邏輯
└── *.js              # 頁面專用腳本

/_elements/           # 可重用 UI 組件
├── pageHeader.ascx   # 頁面標頭
├── pageFooter.ascx   # 頁面頁尾  
├── pageContentStart.ascx  # 內容區開始
├── pageContentEnd.ascx    # 內容區結束
└── navColumn*.ascx   # 導航組件

/_coreForm/           # 共用表單組件
├── frmAttachment.*   # 附件表單
├── frmComment.*      # 評論表單
└── SearchDetails.*   # 搜索詳情

/css/                 # 樣式文件
├── 960.css          # 主要佈局系統
├── style.css        # 自訂樣式
├── jquery-ui.css    # jQuery UI 主題
└── custom-theme/    # 自訂主題

/js/                  # JavaScript 文件
├── jquery-*.js      # jQuery 相關庫
├── 業務邏輯模組.js    # 自訂業務腳本
└── ParamQuery/      # 表格組件

/images/              # 圖片資源
├── 系統圖示/         # UI 圖示
├── 背景圖片/         # 裝飾圖片
└── 業務圖片/         # 業務相關圖片
```

## 📱 核心頁面規格

### 1. 主頁面架構 (Master Page)

#### 頁面組件結構
```html
<!DOCTYPE HTML>
<html>
<head>
    <!-- Meta 資訊 -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- 樣式引用 -->
    <link href="../Styles.css" rel="stylesheet">
    <link href="../jquery/jquery-ui.css" rel="stylesheet">
    
    <!-- JavaScript 引用 -->
    <script src="../jquery/jquery-1.10.2.min.js"></script>
    <script src="../jquery/jquery-ui.min.js"></script>
</head>
<body>
    <!-- 統一頁面結構 -->
    <trw:pageHeader />
    <trw:pageContentStart />
    
    <!-- 頁面內容區 -->
    <main id="pageContent">
        <!-- 業務內容 -->
    </main>
    
    <trw:pageContentEnd />
    <trw:pageFooter />
</body>
</html>
```

#### 響應式設計要求
```css
/* 桌面優先設計 */
.container {
    max-width: 1200px;
    margin: 0 auto;
}

/* 平板適配 (768px - 1024px) */
@media (max-width: 1024px) {
    .container { max-width: 100%; padding: 0 20px; }
    .grid-column { flex: 1 1 100%; }
}

/* 手機適配 (320px - 767px) */
@media (max-width: 767px) {
    .navigation { display: none; }
    .mobile-nav { display: block; }
    .form-row { flex-direction: column; }
}
```

### 2. Description.aspx - 項目描述頁面

#### UI 組件規格

**A. 基本資訊區塊**
```html
<div class="form-section" id="basicInfo">
    <h3 class="section-title">基本資訊</h3>
    
    <!-- CQR 描述 -->
    <div class="form-row">
        <label for="txtCQRDesc" class="required">CQR 描述:</label>
        <div class="input-group">
            <input type="text" id="txtCQRDesc" class="form-control" 
                   placeholder="CQR-編號-客戶-平台-產品描述" maxlength="200">
            <span class="help-icon" data-tooltip="項目摘要說明格式">?</span>
        </div>
    </div>
    
    <!-- 車型年份和產量 -->
    <div class="form-row">
        <div class="col-md-6">
            <label for="cmbModelYear">車型年份:</label>
            <select id="cmbModelYear" class="form-control">
                <option value="">請選擇...</option>
                <!-- 動態載入選項 -->
            </select>
        </div>
        <div class="col-md-6">
            <label for="txtVolPerAnnum">年產量:</label>
            <input type="text" id="txtVolPerAnnum" class="form-control number-input"
                   placeholder="請輸入數字" data-format="number">
        </div>
    </div>
</div>
```

**B. Antares 整合區塊**
```html
<div class="form-section" id="antaresSection" style="display:none">
    <h3 class="section-title">
        Antares 資訊
        <span class="loading-indicator" id="antaresLoading" style="display:none">
            <i class="icon-spinner"></i> 載入中...
        </span>
    </h3>
    
    <!-- 篩選控制項 -->
    <div class="filter-panel">
        <div class="filter-row">
            <select id="cmbAntaresOEMGroup" class="filter-select" 
                    data-placeholder="選擇 OEM 集團" 
                    onchange="handleAntaresFilter('OEMGroup', this.value)">
            </select>
            
            <select id="cmbAntaresOEMCustomer" class="filter-select"
                    data-placeholder="選擇 OEM 客戶"
                    onchange="handleAntaresFilter('OEMCustomer', this.value)">
            </select>
            
            <select id="cmbAntaresPlatform" class="filter-select"
                    data-placeholder="選擇平台"
                    onchange="handleAntaresFilter('Platform', this.value)">
            </select>
        </div>
        
        <div class="action-row">
            <input type="text" id="txtAntaresUniqueId" 
                   placeholder="輸入 Unique ID" class="search-input">
            <button type="button" onclick="addAntaresRecord()" 
                    class="btn btn-primary">
                <i class="icon-plus"></i> 新增
            </button>
        </div>
    </div>
    
    <!-- 結果表格 -->
    <div class="data-table-container">
        <table id="tblAntares" class="data-table">
            <thead>
                <tr>
                    <th width="40">操作</th>
                    <th>Unique ID</th>
                    <th>OEM Group</th>
                    <th>OEM Customer</th>
                    <th>Platform</th>
                    <th>Program</th>
                    <th>Nameplate</th>
                    <th>SOP</th>
                    <th>EOP</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="antaresTableBody">
                <!-- 動態填充 -->
            </tbody>
        </table>
    </div>
    
    <!-- 警告訊息 -->
    <div id="antaresWarning" class="alert alert-warning" style="display:none">
        <i class="icon-warning"></i>
        <span id="antaresWarningText"></span>
    </div>
</div>
```

**C. 團隊配置區塊**
```html
<div class="form-section" id="teamAssignment">
    <h3 class="section-title">團隊配置</h3>
    
    <div class="team-grid">
        <!-- 銷售團隊 -->
        <div class="team-column">
            <h4>銷售團隊</h4>
            <div class="form-group">
                <label for="cmbAMGR">銷售經理 (AMGR):</label>
                <select id="cmbAMGR" class="form-control user-select" 
                        data-role="AMGR">
                    <option value="">請選擇...</option>
                </select>
            </div>
            <div class="form-group">
                <label for="cmbSDIR">銷售總監 (SDIR):</label>
                <select id="cmbSDIR" class="form-control user-select"
                        data-role="SDIR">
                    <option value="">請選擇...</option>
                </select>
            </div>
        </div>
        
        <!-- 工程團隊 -->
        <div class="team-column">
            <h4>工程團隊</h4>
            <div class="form-group">
                <label for="cmbPGM">工程總監:</label>
                <select id="cmbPGM" class="form-control user-select"
                        data-role="PGM">
                    <option value="">請選擇...</option>
                </select>
            </div>
            <div class="form-group">
                <label for="cmbEngineeringManager">工程協調員 (PETM):</label>
                <select id="cmbEngineeringManager" class="form-control user-select"
                        data-role="PETM">
                    <option value="">請選擇...</option>
                </select>
            </div>
        </div>
        
        <!-- 財務團隊 -->
        <div class="team-column">
            <h4>財務團隊</h4>
            <div class="form-group">
                <label for="cmbCMGR">商務經理:</label>
                <select id="cmbCMGR" class="form-control user-select"
                        data-role="CMGR">
                    <option value="">請選擇...</option>
                </select>
            </div>
            <div class="form-group">
                <label for="cmbCOST">財務協調員:</label>
                <select id="cmbCOST" class="form-control user-select"
                        data-role="COST">
                    <option value="">請選擇...</option>
                </select>
            </div>
        </div>
    </div>
</div>
```

#### JavaScript 功能規格

**A. Antares 資料整合**
```javascript
/**
 * Antares 系統整合管理器
 */
class AntaresManager {
    constructor() {
        this.filterCache = new Map();
        this.selectedRecords = [];
        this.isLoading = false;
    }
    
    /**
     * 初始化 Antares 區塊
     */
    init() {
        this.setupEventHandlers();
        this.loadInitialData();
    }
    
    /**
     * 處理篩選器變更
     */
    async handleFilterChange(filterType, value) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);
        
        try {
            // 構建篩選參數
            const filters = this.buildFilterObject();
            filters[filterType] = value;
            
            // 更新相依的下拉選單
            await this.updateDependentFilters(filterType, filters);
            
            // 快取結果
            this.filterCache.set(this.getFilterKey(filters), filters);
            
        } catch (error) {
            this.showError('載入 Antares 資料時發生錯誤: ' + error.message);
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }
    
    /**
     * 新增 Antares 記錄
     */
    async addRecord() {
        const uniqueId = $('#txtAntaresUniqueId').val().trim();
        if (!uniqueId) {
            this.showWarning('請輸入 Unique ID');
            return;
        }
        
        // 檢查重複
        if (this.isDuplicate(uniqueId)) {
            this.showWarning('此 Unique ID 已存在');
            return;
        }
        
        try {
            const result = await this.fetchAntaresRecord(uniqueId);
            if (result.success) {
                this.addToTable(result.data);
                this.clearInput();
                this.updateHiddenField();
            } else {
                this.showError(result.message);
            }
        } catch (error) {
            this.showError('新增記錄時發生錯誤: ' + error.message);
        }
    }
    
    /**
     * AJAX 資料獲取
     */
    async fetchAntaresRecord(uniqueId) {
        const filters = this.buildFilterObject();
        filters.UniqueId = uniqueId;
        filters.currentList = this.getExistingIds();
        
        return new Promise((resolve, reject) => {
            $.ajax({
                url: 'Description.aspx?Antares=1',
                type: 'POST',
                data: filters,
                dataType: 'html',
                timeout: 30000,
                success: function(result) {
                    if (result.startsWith('ERROR:')) {
                        resolve({ success: false, message: result });
                    } else if (result.trim() === '') {
                        resolve({ success: false, message: '找不到匹配的記錄' });
                    } else {
                        resolve({ success: true, data: result });
                    }
                },
                error: function(xhr, status, error) {
                    reject(new Error(`網路錯誤: ${error}`));
                }
            });
        });
    }
}

// 實例化管理器
const antaresManager = new AntaresManager();
```

**B. 表單驗證**
```javascript
/**
 * 表單驗證管理器
 */
class FormValidator {
    constructor() {
        this.rules = {};
        this.errors = [];
    }
    
    /**
     * 添加驗證規則
     */
    addRule(fieldId, validators) {
        this.rules[fieldId] = validators;
    }
    
    /**
     * 驗證所有欄位
     */
    validateAll() {
        this.errors = [];
        
        for (const [fieldId, validators] of Object.entries(this.rules)) {
            const field = document.getElementById(fieldId);
            if (!field) continue;
            
            const value = field.value.trim();
            
            for (const validator of validators) {
                const result = validator.validate(value, field);
                if (!result.isValid) {
                    this.errors.push({
                        fieldId: fieldId,
                        message: result.message,
                        severity: validator.severity || 'error'
                    });
                    this.markFieldInvalid(field, result.message);
                    break; // 停在第一個錯誤
                } else {
                    this.markFieldValid(field);
                }
            }
        }
        
        return this.errors.length === 0;
    }
    
    /**
     * 即時驗證
     */
    validateField(fieldId) {
        const field = document.getElementById(fieldId);
        const validators = this.rules[fieldId];
        
        if (!field || !validators) return true;
        
        const value = field.value.trim();
        
        for (const validator of validators) {
            const result = validator.validate(value, field);
            if (!result.isValid) {
                this.markFieldInvalid(field, result.message);
                return false;
            }
        }
        
        this.markFieldValid(field);
        return true;
    }
    
    /**
     * 標記欄位無效
     */
    markFieldInvalid(field, message) {
        field.classList.remove('valid');
        field.classList.add('invalid');
        
        // 顯示錯誤訊息
        let errorDiv = field.parentElement.querySelector('.error-message');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            field.parentElement.appendChild(errorDiv);
        }
        errorDiv.textContent = message;
    }
    
    /**
     * 標記欄位有效
     */
    markFieldValid(field) {
        field.classList.remove('invalid');
        field.classList.add('valid');
        
        // 移除錯誤訊息
        const errorDiv = field.parentElement.querySelector('.error-message');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
}

// 驗證器定義
const validators = {
    required: {
        validate: (value) => ({
            isValid: value.length > 0,
            message: '此欄位為必填'
        }),
        severity: 'error'
    },
    
    number: {
        validate: (value) => ({
            isValid: !value || /^\d+$/.test(value),
            message: '請輸入有效數字'
        }),
        severity: 'error'
    },
    
    currency: {
        validate: (value) => ({
            isValid: !value || /^\d+(\.\d{1,2})?$/.test(value),
            message: '請輸入有效金額格式'
        }),
        severity: 'error'
    },
    
    date: {
        validate: (value) => {
            if (!value) return { isValid: true };
            const date = new Date(value);
            return {
                isValid: !isNaN(date.getTime()),
                message: '請輸入有效日期格式'
            };
        },
        severity: 'error'
    }
};
```

### 3. Background.aspx - 背景資訊頁面

#### UI 組件規格
```html
<div class="form-section" id="backgroundInfo">
    <h3 class="section-title">背景資訊</h3>
    
    <!-- 背景說明 -->
    <div class="form-group">
        <label for="txtBgInfo" class="required">背景資訊:</label>
        <textarea id="txtBgInfo" class="form-control rich-editor" 
                  rows="6" maxlength="2000"
                  placeholder="請詳細描述項目背景、客戶需求和特殊要求...">
        </textarea>
        <div class="char-counter">
            <span id="bgInfoCount">0</span> / 2000 字元
        </div>
    </div>
    
    <!-- 客戶聯絡人 -->
    <div class="customer-contact-section">
        <h4>客戶聯絡人</h4>
        <div class="contact-grid">
            <div class="contact-item">
                <label for="txtCustBuyer">採購員:</label>
                <input type="text" id="txtCustBuyer" class="form-control"
                       maxlength="50" placeholder="客戶採購負責人">
            </div>
            <div class="contact-item">
                <label for="txtCustEngineer">工程師:</label>
                <input type="text" id="txtCustEngineer" class="form-control"
                       maxlength="50" placeholder="客戶技術負責人">
            </div>
        </div>
    </div>
    
    <!-- 淘汰評估 -->
    <div class="form-group">
        <label class="section-label">產品淘汰評估</label>
        <div class="radio-group">
            <label class="radio-label">
                <input type="radio" name="obsolescence" value="1" id="optObsolReqYES">
                <span class="radio-custom"></span>
                需要產品淘汰評估
            </label>
            <label class="radio-label">
                <input type="radio" name="obsolescence" value="0" id="optObsolReqNO">
                <span class="radio-custom"></span>
                不需要產品淘汰評估
            </label>
        </div>
        <div class="help-text">
            淘汰評估指現有產品或零件因新項目而需要停產的成本評估
        </div>
    </div>
    
    <!-- 客戶生產力目標 -->
    <div class="form-group">
        <label for="txtCustProdTargets">客戶生產力目標:</label>
        <textarea id="txtCustProdTargets" class="form-control" 
                  rows="4" maxlength="1000"
                  placeholder="客戶期望的成本節省、效率提升、品質改善等目標...">
        </textarea>
    </div>
</div>
```

### 4. Engineering.aspx - 工程評估頁面

#### UI 組件規格

**A. 可行性評估區塊**
```html
<div class="form-section" id="feasibilityAssessment">
    <h3 class="section-title">技術可行性評估</h3>
    
    <!-- 評估問題 -->
    <div class="assessment-grid">
        <div class="assessment-item">
            <div class="question-label">
                <i class="icon-clock"></i>
                在規定時間內可達成嗎？
            </div>
            <div class="answer-group">
                <label class="radio-label">
                    <input type="radio" name="achievable" value="1" 
                           onchange="updateFeasibilityLogic()">
                    <span class="radio-custom success"></span>
                    可以達成
                </label>
                <label class="radio-label">
                    <input type="radio" name="achievable" value="0"
                           onchange="updateFeasibilityLogic()">
                    <span class="radio-custom danger"></span>
                    無法達成
                </label>
            </div>
        </div>
        
        <div class="assessment-item">
            <div class="question-label">
                <i class="icon-info"></i>
                現有資訊是否充足？
            </div>
            <div class="answer-group">
                <label class="radio-label">
                    <input type="radio" name="sufficient" value="1"
                           onchange="updateFeasibilityLogic()">
                    <span class="radio-custom success"></span>
                    資訊充足
                </label>
                <label class="radio-label">
                    <input type="radio" name="sufficient" value="0"
                           onchange="updateFeasibilityLogic()">
                    <span class="radio-custom danger"></span>
                    資訊不足
                </label>
            </div>
        </div>
        
        <div class="assessment-item" id="workProceedItem">
            <div class="question-label">
                <i class="icon-play"></i>
                工作可以開始進行嗎？
            </div>
            <div class="answer-group">
                <label class="radio-label">
                    <input type="radio" name="proceed" value="1" id="optCanWorkProceedYES">
                    <span class="radio-custom success"></span>
                    可以進行
                </label>
                <label class="radio-label">
                    <input type="radio" name="proceed" value="0" id="optCanWorkProceedNO">
                    <span class="radio-custom danger"></span>
                    暫停等待
                </label>
            </div>
        </div>
    </div>
    
    <!-- 智能邏輯提示 -->
    <div id="feasibilityLogic" class="logic-indicator" style="display:none">
        <div class="logic-content">
            <i class="icon-lightbulb"></i>
            <span id="logicText"></span>
        </div>
    </div>
</div>
```

**B. 設計輸入需求矩陣**
```html
<div class="form-section" id="designInputMatrix">
    <h3 class="section-title">設計輸入需求評估</h3>
    
    <div class="matrix-table-container">
        <table class="design-matrix-table">
            <thead>
                <tr>
                    <th width="300">設計輸入項目</th>
                    <th width="120">需要/不需要</th>
                    <th width="200">負責人 (PRD)</th>
                    <th width="100">無輸入</th>
                </tr>
            </thead>
            <tbody>
                <!-- 動態生成 8 個設計輸入項目 -->
                <tr class="matrix-row" data-index="1">
                    <td class="input-description">
                        <div class="input-title">機械設計輸入</div>
                        <div class="input-detail">結構設計、材料規格、機械特性等</div>
                    </td>
                    <td class="need-assessment">
                        <div class="toggle-switch">
                            <input type="radio" name="designNeeded1" value="1" 
                                   id="optIsDesignNeeded1YES"
                                   onchange="togglePRDSelection(1, true)">
                            <label for="optIsDesignNeeded1YES">需要</label>
                            
                            <input type="radio" name="designNeeded1" value="0"
                                   id="optIsDesignNeeded1NO"
                                   onchange="togglePRDSelection(1, false)">
                            <label for="optIsDesignNeeded1NO">不需要</label>
                        </div>
                    </td>
                    <td class="prd-selection">
                        <select id="cmbElecPRD1" class="form-control prd-select" disabled>
                            <option value="">請選擇 PRD...</option>
                            <!-- 動態載入 PRD 清單 -->
                        </select>
                    </td>
                    <td class="no-input">
                        <label class="checkbox-label">
                            <input type="checkbox" id="chkElecPRD1">
                            <span class="checkbox-custom"></span>
                        </label>
                    </td>
                </tr>
                
                <!-- 重複類似結構 for 項目 2-8 -->
            </tbody>
        </table>
    </div>
    
    <!-- 批量操作 -->
    <div class="batch-actions">
        <button type="button" class="btn btn-secondary" onclick="selectAllDesignInputs()">
            全部需要
        </button>
        <button type="button" class="btn btn-secondary" onclick="clearAllDesignInputs()">
            全部清除
        </button>
        <button type="button" class="btn btn-secondary" onclick="copyFromTemplate()">
            從模板複製
        </button>
    </div>
</div>
```

#### JavaScript 功能規格

**A. 工程評估邏輯**
```javascript
/**
 * 工程評估智能邏輯控制
 */
function updateFeasibilityLogic() {
    const achievable = $('input[name="achievable"]:checked').val();
    const sufficient = $('input[name="sufficient"]:checked').val();
    
    const logicIndicator = $('#feasibilityLogic');
    const logicText = $('#logicText');
    const proceedItem = $('#workProceedItem');
    const proceedYes = $('#optCanWorkProceedYES');
    const proceedNo = $('#optCanWorkProceedNO');
    
    // 重置狀態
    proceedYes.prop('disabled', false);
    proceedNo.prop('disabled', false);
    proceedItem.removeClass('auto-determined');
    
    if (achievable === '1' && sufficient === '1') {
        // 可達成且資訊充足 -> 自動設定為可以進行
        proceedYes.prop('checked', true);
        proceedYes.prop('disabled', true);
        proceedNo.prop('disabled', true);
        
        proceedItem.addClass('auto-determined');
        logicText.html('<strong>系統建議：</strong>技術可行且資訊充足，建議開始進行工作');
        logicIndicator.show().removeClass('warning').addClass('success');
        
    } else if (achievable === '1' && sufficient === '0') {
        // 可達成但資訊不足 -> 提醒需要更多資訊
        logicText.html('<strong>注意：</strong>技術可行但資訊不足，建議先收集更多資訊再決定');
        logicIndicator.show().removeClass('success').addClass('warning');
        
    } else if (achievable === '0') {
        // 不可達成 -> 建議暫停或重新評估
        proceedNo.prop('checked', true);
        logicText.html('<strong>警告：</strong>技術上無法達成，建議暫停項目或重新評估需求');
        logicIndicator.show().removeClass('success').addClass('error');
        
    } else {
        // 未完成評估
        logicIndicator.hide();
    }
}

/**
 * 設計輸入需求控制
 */
function togglePRDSelection(index, needed) {
    const prdSelect = $(`#cmbElecPRD${index}`);
    const checkBox = $(`#chkElecPRD${index}`);
    const row = $(`.matrix-row[data-index="${index}"]`);
    
    if (needed) {
        // 需要設計輸入
        prdSelect.prop('disabled', false).focus();
        checkBox.prop('checked', false).prop('disabled', false);
        row.addClass('required').removeClass('not-required');
        
        // 動畫效果
        prdSelect.parent().addClass('highlight');
        setTimeout(() => prdSelect.parent().removeClass('highlight'), 1000);
        
    } else {
        // 不需要設計輸入
        prdSelect.prop('disabled', true).val('');
        checkBox.prop('checked', false).prop('disabled', true);
        row.addClass('not-required').removeClass('required');
    }
}

/**
 * RCT/EPL 資料夾管理
 */
class FolderManager {
    constructor() {
        this.rctFolders = [];
        this.eplFolders = [];
    }
    
    /**
     * 載入 RCT 資料夾
     */
    async loadRCTFolders() {
        try {
            const response = await this.fetchFolders('RCT');
            this.rctFolders = response.folders;
            this.renderRCTTable();
            this.updateFolderCounts();
        } catch (error) {
            this.showError('載入 RCT 資料夾失敗: ' + error.message);
        }
    }
    
    /**
     * 渲染 RCT 表格
     */
    renderRCTTable() {
        const tbody = $('#rctTableBody');
        tbody.empty();
        
        if (this.rctFolders.length === 0) {
            $('#noRCTMessage').show();
            $('#rctTable').hide();
            return;
        }
        
        $('#noRCTMessage').hide();
        $('#rctTable').show();
        
        this.rctFolders.forEach(folder => {
            const row = this.createFolderRow(folder, 'RCT');
            tbody.append(row);
        });
    }
    
    /**
     * 創建資料夾行
     */
    createFolderRow(folder, type) {
        return $(`
            <tr class="folder-row" data-folder-id="${folder.id}">
                <td class="folder-icon">
                    <i class="icon-folder"></i>
                </td>
                <td class="folder-name">
                    <a href="${folder.url}" target="_blank" class="folder-link">
                        ${folder.name}
                    </a>
                </td>
                <td class="folder-status">
                    <span class="status-badge status-${folder.status}">
                        ${folder.statusText}
                    </span>
                </td>
                <td class="folder-date">
                    ${this.formatDate(folder.lastModified)}
                </td>
                <td class="folder-actions">
                    <button class="btn btn-sm btn-secondary" 
                            onclick="openFolder('${folder.id}', '${type}')">
                        開啟
                    </button>
                </td>
            </tr>
        `);
    }
}
```

### 5. QuoteResponse.aspx - 報價回應頁面

#### UI 組件規格

**A. 報價模式選擇**
```html
<div class="form-section" id="quoteMode">
    <h3 class="section-title">報價模式選擇</h3>
    
    <div class="mode-selector">
        <div class="mode-option" data-mode="quick">
            <label class="mode-label">
                <input type="radio" name="quoteMode" value="quick" 
                       id="optQRQuoteResponse" onchange="switchQuoteMode('quick')">
                <div class="mode-card">
                    <div class="mode-icon">
                        <i class="icon-zap"></i>
                    </div>
                    <div class="mode-title">快速報價模式</div>
                    <div class="mode-description">
                        適用於簡單產品或初步報價<br>
                        預計完成時間：2-3 天
                    </div>
                    <div class="mode-features">
                        <span class="feature-tag">基本成本計算</span>
                        <span class="feature-tag">標準利潤率</span>
                        <span class="feature-tag">快速交期</span>
                    </div>
                </div>
            </label>
        </div>
        
        <div class="mode-option" data-mode="full">
            <label class="mode-label">
                <input type="radio" name="quoteMode" value="full" 
                       id="optQRFullCostModel" onchange="switchQuoteMode('full')">
                <div class="mode-card">
                    <div class="mode-icon">
                        <i class="icon-calculator"></i>
                    </div>
                    <div class="mode-title">完整成本模型</div>
                    <div class="mode-description">
                        適用於複雜產品或正式報價<br>
                        預計完成時間：7-10 天
                    </div>
                    <div class="mode-features">
                        <span class="feature-tag">詳細成本分析</span>
                        <span class="feature-tag">Excel 整合</span>
                        <span class="feature-tag">風險評估</span>
                    </div>
                </div>
            </label>
        </div>
    </div>
</div>
```

**B. Excel 整合區塊**
```html
<div class="form-section" id="excelIntegration" style="display:none">
    <h3 class="section-title">
        成本計算 Excel 整合
        <div class="excel-status" id="excelStatus">
            <span class="status-indicator"></span>
            <span class="status-text">準備就緒</span>
        </div>
    </h3>
    
    <!-- Excel 控制台 -->
    <div class="excel-control-panel">
        <div class="control-row">
            <button type="button" class="btn btn-primary" onclick="openExcelEditor()">
                <i class="icon-file-excel"></i>
                開啟 Excel 編輯器
            </button>
            
            <button type="button" class="btn btn-secondary" onclick="downloadTemplate()">
                <i class="icon-download"></i>
                下載模板
            </button>
            
            <button type="button" class="btn btn-secondary" onclick="uploadExistingFile()">
                <i class="icon-upload"></i>
                上傳現有檔案
            </button>
        </div>
        
        <div class="control-row">
            <div class="file-info">
                <span class="file-name" id="currentFileName">尚未開啟檔案</span>
                <span class="file-size" id="currentFileSize"></span>
                <span class="last-modified" id="lastModified"></span>
            </div>
        </div>
    </div>
    
    <!-- Excel 編輯器容器 -->
    <div class="excel-editor-container" id="excelEditorContainer" style="display:none">
        <div class="editor-toolbar">
            <div class="toolbar-group">
                <button class="toolbar-btn" onclick="saveExcelFile()" title="儲存">
                    <i class="icon-save"></i>
                </button>
                <button class="toolbar-btn" onclick="exportPDF()" title="匯出 PDF">
                    <i class="icon-file-pdf"></i>
                </button>
                <button class="toolbar-btn" onclick="emailReport()" title="Email 報告">
                    <i class="icon-mail"></i>
                </button>
            </div>
            
            <div class="toolbar-group">
                <button class="toolbar-btn" onclick="closeExcelEditor()" title="關閉">
                    <i class="icon-close"></i>
                </button>
            </div>
        </div>
        
        <!-- AceOfFix Excel 編輯器將嵌入此處 -->
        <iframe id="excelFrame" class="excel-frame" 
                src="about:blank" frameborder="0">
        </iframe>
    </div>
    
    <!-- 成本摘要顯示 -->
    <div class="cost-summary" id="costSummary" style="display:none">
        <h4>成本摘要</h4>
        <div class="summary-grid">
            <div class="summary-item">
                <label>材料成本:</label>
                <span class="cost-value" id="materialCost">$0.00</span>
            </div>
            <div class="summary-item">
                <label>人工成本:</label>
                <span class="cost-value" id="laborCost">$0.00</span>
            </div>
            <div class="summary-item">
                <label>間接成本:</label>
                <span class="cost-value" id="overheadCost">$0.00</span>
            </div>
            <div class="summary-item total">
                <label>總成本:</label>
                <span class="cost-value" id="totalCost">$0.00</span>
            </div>
            <div class="summary-item">
                <label>建議售價:</label>
                <span class="cost-value" id="suggestedPrice">$0.00</span>
            </div>
            <div class="summary-item">
                <label>利潤率:</label>
                <span class="cost-value" id="profitMargin">0%</span>
            </div>
        </div>
    </div>
</div>
```

**C. 報價資訊區塊**
```html
<div class="form-section" id="quoteInformation">
    <h3 class="section-title">報價資訊</h3>
    
    <div class="quote-grid">
        <div class="quote-column">
            <h4>工具資訊</h4>
            
            <div class="form-group">
                <label for="txtToolLeadTime">工具交期 (週):</label>
                <div class="input-group">
                    <input type="number" id="txtToolLeadTime" class="form-control"
                           min="1" max="52" placeholder="16">
                    <span class="input-group-text">週</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="txtToolingCapacity">工具產能 (件/月):</label>
                <div class="input-group">
                    <input type="number" id="txtToolingCapacity" class="form-control"
                           min="1" placeholder="10000">
                    <span class="input-group-text">件/月</span>
                </div>
            </div>
        </div>
        
        <div class="quote-column">
            <h4>交貨條件</h4>
            
            <div class="form-group">
                <label for="cmbFOB">F.O.B. 條件:</label>
                <select id="cmbFOB" class="form-control">
                    <option value="">請選擇...</option>
                    <option value="EXW">EXW - 工廠交貨</option>
                    <option value="FOB">FOB - 離岸價</option>
                    <option value="CIF">CIF - 到岸價</option>
                    <option value="DDP">DDP - 完稅交貨</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="txtGDPIMCategory">GDPEP 類別:</label>
                <input type="text" id="txtGDPIMCategory" class="form-control"
                       placeholder="自動從系統判斷">
            </div>
        </div>
        
        <div class="quote-column">
            <h4>經濟水平日期</h4>
            
            <div class="form-group">
                <label for="txtEconLvDateMaterial">材料經濟水平:</label>
                <div class="date-input-group">
                    <input type="date" id="txtEconLvDateMaterial" class="form-control">
                    <label class="checkbox-label">
                        <input type="checkbox" id="chkEconLvDateMaterialNA">
                        <span class="checkbox-custom"></span>
                        N/A
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="txtEconLvDateLabor">人工經濟水平:</label>
                <div class="date-input-group">
                    <input type="date" id="txtEconLvDateLabor" class="form-control">
                    <label class="checkbox-label">
                        <input type="checkbox" id="chkEconLvDateLaborNA">
                        <span class="checkbox-custom"></span>
                        N/A
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
```

#### JavaScript 功能規格

**A. Excel 整合管理**
```javascript
/**
 * Excel 整合管理器
 */
class ExcelManager {
    constructor() {
        this.isEditorOpen = false;
        this.currentFile = null;
        this.isDirty = false;
        this.autoSaveInterval = null;
    }
    
    /**
     * 開啟 Excel 編輯器
     */
    async openExcelEditor() {
        if (this.isEditorOpen) {
            this.focusEditor();
            return;
        }
        
        try {
            this.showLoading('正在初始化 Excel 編輯器...');
            
            // 檢查是否有現有檔案
            const existingFile = await this.checkExistingFile();
            
            let fileUrl;
            if (existingFile) {
                fileUrl = existingFile.url;
                this.currentFile = existingFile;
            } else {
                // 創建新檔案從模板
                fileUrl = await this.createFromTemplate();
            }
            
            // 載入 AceOfFix 編輯器
            await this.loadAceOfFix(fileUrl);
            
            this.isEditorOpen = true;
            this.showEditor();
            this.startAutoSave();
            this.updateStatus('編輯中', 'editing');
            
        } catch (error) {
            this.showError('開啟 Excel 編輯器失敗: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 載入 AceOfFix 編輯器
     */
    async loadAceOfFix(fileUrl) {
        return new Promise((resolve, reject) => {
            const frame = document.getElementById('excelFrame');
            const aceUrl = `../aceoffix/AceOfFix.aspx?file=${encodeURIComponent(fileUrl)}&mode=edit`;
            
            frame.onload = () => {
                try {
                    // 設定編輯器事件監聽
                    this.setupEditorEvents(frame);
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            
            frame.onerror = (error) => {
                reject(new Error('載入編輯器失敗'));
            };
            
            frame.src = aceUrl;
        });
    }
    
    /**
     * 設定編輯器事件
     */
    setupEditorEvents(frame) {
        try {
            const doc = frame.contentDocument || frame.contentWindow.document;
            
            // 監聽變更事件
            if (frame.contentWindow.AceOfFix) {
                frame.contentWindow.AceOfFix.OnChange = () => {
                    this.isDirty = true;
                    this.updateStatus('有未儲存的變更', 'dirty');
                };
                
                frame.contentWindow.AceOfFix.OnSave = () => {
                    this.isDirty = false;
                    this.updateStatus('已儲存', 'saved');
                    this.extractCostData();
                };
            }
        } catch (error) {
            console.warn('設定編輯器事件失敗:', error);
        }
    }
    
    /**
     * 自動儲存
     */
    startAutoSave() {
        this.autoSaveInterval = setInterval(() => {
            if (this.isDirty && this.isEditorOpen) {
                this.saveExcelFile(true); // 靜默儲存
            }
        }, 30000); // 每 30 秒自動儲存
    }
    
    /**
     * 儲存檔案
     */
    async saveExcelFile(silent = false) {
        if (!this.isEditorOpen) return;
        
        try {
            if (!silent) {
                this.showLoading('正在儲存...');
            }
            
            const frame = document.getElementById('excelFrame');
            if (frame.contentWindow.AceOfFix) {
                await frame.contentWindow.AceOfFix.Save();
                this.isDirty = false;
                this.updateStatus('已儲存', 'saved');
                
                if (!silent) {
                    this.showSuccess('檔案已成功儲存');
                }
                
                // 提取成本資料
                await this.extractCostData();
            }
            
        } catch (error) {
            this.showError('儲存失敗: ' + error.message);
        } finally {
            if (!silent) {
                this.hideLoading();
            }
        }
    }
    
    /**
     * 提取成本資料
     */
    async extractCostData() {
        try {
            const response = await $.ajax({
                url: 'QuoteResponse.aspx?extractCosts=1',
                type: 'POST',
                data: { fileId: this.currentFile?.id },
                dataType: 'json'
            });
            
            if (response.success) {
                this.updateCostSummary(response.costs);
            }
            
        } catch (error) {
            console.warn('提取成本資料失敗:', error);
        }
    }
    
    /**
     * 更新成本摘要
     */
    updateCostSummary(costs) {
        $('#materialCost').text(this.formatCurrency(costs.material));
        $('#laborCost').text(this.formatCurrency(costs.labor));
        $('#overheadCost').text(this.formatCurrency(costs.overhead));
        $('#totalCost').text(this.formatCurrency(costs.total));
        $('#suggestedPrice').text(this.formatCurrency(costs.suggestedPrice));
        $('#profitMargin').text(costs.profitMargin + '%');
        
        $('#costSummary').show();
    }
    
    /**
     * 關閉編輯器
     */
    closeExcelEditor() {
        if (!this.isEditorOpen) return;
        
        if (this.isDirty) {
            const save = confirm('有未儲存的變更，是否要儲存？');
            if (save) {
                this.saveExcelFile();
            }
        }
        
        this.isEditorOpen = false;
        this.currentFile = null;
        this.isDirty = false;
        
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
        
        $('#excelEditorContainer').hide();
        $('#excelFrame').attr('src', 'about:blank');
        this.updateStatus('準備就緒', 'ready');
    }
}

// 實例化 Excel 管理器
const excelManager = new ExcelManager();
```

### 6. Routing.aspx - 路由管理頁面

#### UI 組件規格
```html
<div class="routing-dashboard">
    <h3 class="section-title">工作流程路由狀態</h3>
    
    <!-- 進度概覽 -->
    <div class="progress-overview">
        <div class="progress-header">
            <h4>整體進度</h4>
            <span class="progress-percentage" id="overallProgress">65%</span>
        </div>
        
        <div class="progress-bar-container">
            <div class="progress-bar">
                <div class="progress-fill" style="width: 65%"></div>
            </div>
        </div>
        
        <div class="progress-stages">
            <div class="stage completed">Description</div>
            <div class="stage completed">Background</div>
            <div class="stage completed">Engineering</div>
            <div class="stage active">Quote Response</div>
            <div class="stage pending">Sales Closeout</div>
        </div>
    </div>
    
    <!-- 當前狀態卡片 -->
    <div class="current-status-card">
        <div class="status-header">
            <div class="status-icon">
                <i class="icon-user"></i>
            </div>
            <div class="status-info">
                <h5>目前處理者</h5>
                <p class="assignee-name">David Chen (財務協調員)</p>
            </div>
        </div>
        
        <div class="status-details">
            <div class="detail-item">
                <span class="label">當前階段:</span>
                <span class="value">Quote Response - 成本計算</span>
            </div>
            <div class="detail-item">
                <span class="label">開始時間:</span>
                <span class="value">2024-02-15 09:30</span>
            </div>
            <div class="detail-item">
                <span class="label">預計完成:</span>
                <span class="value">2024-02-20 17:00</span>
            </div>
            <div class="detail-item">
                <span class="label">剩餘時間:</span>
                <span class="value countdown" data-target="2024-02-20T17:00:00">3天 4小時</span>
            </div>
        </div>
    </div>
    
    <!-- 操作評論 -->
    <div class="action-comments-section">
        <label for="txtActionComments">操作評論:</label>
        <textarea id="txtActionComments" class="form-control" 
                  rows="3" placeholder="添加關於當前狀態的評論或說明..."></textarea>
        
        <div class="comment-actions">
            <button type="button" class="btn btn-primary" onclick="saveComments()">
                儲存評論
            </button>
            <button type="button" class="btn btn-secondary" onclick="addTimestamp()">
                添加時間戳記
            </button>
        </div>
    </div>
    
    <!-- 路由歷史表格 -->
    <div class="routing-history">
        <h4>路由歷史</h4>
        
        <div class="table-controls">
            <div class="control-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="chkShowNotification" 
                           onchange="toggleNotifications()">
                    <span class="checkbox-custom"></span>
                    顯示通知
                </label>
            </div>
            
            <div class="control-group">
                <button class="btn btn-secondary btn-sm" onclick="refreshRouting()">
                    <i class="icon-refresh"></i> 重新整理
                </button>
                <button class="btn btn-secondary btn-sm" onclick="exportRoutingHistory()">
                    <i class="icon-download"></i> 匯出
                </button>
            </div>
        </div>
        
        <div class="routing-table-container">
            <table class="routing-table" id="routingTable">
                <thead>
                    <tr>
                        <th width="120">階段</th>
                        <th width="150">任務</th>
                        <th width="150">分派對象</th>
                        <th width="100">狀態</th>
                        <th width="130">開始時間</th>
                        <th width="130">完成時間</th>
                        <th width="80">耗時</th>
                        <th>評論</th>
                    </tr>
                </thead>
                <tbody id="routingTableBody">
                    <!-- 動態填充路由記錄 -->
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 模擬使用者功能 (僅限管理員) -->
    <div class="admin-section" id="adminSection" style="display:none">
        <h4>管理員功能</h4>
        
        <div class="impersonation-panel">
            <label for="cmbImpersonateUser">模擬使用者:</label>
            <div class="input-group">
                <select id="cmbImpersonateUser" class="form-control">
                    <option value="">選擇要模擬的使用者...</option>
                    <!-- 動態載入使用者清單 -->
                </select>
                <button type="button" class="btn btn-warning" onclick="startImpersonation()">
                    開始模擬
                </button>
            </div>
            
            <div class="impersonation-warning" id="impersonationWarning" style="display:none">
                <i class="icon-warning"></i>
                目前正在模擬使用者: <strong id="currentImpersonatedUser"></strong>
                <button type="button" class="btn btn-sm btn-secondary" onclick="stopImpersonation()">
                    停止模擬
                </button>
            </div>
        </div>
    </div>
</div>
```

### 7. SalesCloseout.aspx - 銷售結案頁面

#### UI 組件規格

**A. 報價狀態管理**
```html
<div class="form-section" id="quoteStatus">
    <h3 class="section-title">報價狀態</h3>
    
    <div class="status-selection">
        <div class="status-options">
            <label class="status-option">
                <input type="radio" name="quoteStatus" value="0" id="optQuoteStatusNotQuoted">
                <div class="status-card">
                    <div class="status-icon not-quoted">
                        <i class="icon-clock"></i>
                    </div>
                    <div class="status-title">未報價</div>
                    <div class="status-description">尚未向客戶提供報價</div>
                </div>
            </label>
            
            <label class="status-option">
                <input type="radio" name="quoteStatus" value="1" id="optQuoteStatusVerbal">
                <div class="status-card">
                    <div class="status-icon verbal">
                        <i class="icon-phone"></i>
                    </div>
                    <div class="status-title">口頭報價</div>
                    <div class="status-description">已進行口頭報價溝通</div>
                </div>
            </label>
            
            <label class="status-option">
                <input type="radio" name="quoteStatus" value="2" id="optQuoteStatusLetter">
                <div class="status-card">
                    <div class="status-icon formal">
                        <i class="icon-file-text"></i>
                    </div>
                    <div class="status-title">正式報價函</div>
                    <div class="status-description">已發送正式報價文件</div>
                </div>
            </label>
        </div>
        
        <div class="status-details" id="quoteStatusDetails" style="display:none">
            <div class="form-group">
                <label for="txtQuoteDate">報價日期:</label>
                <input type="date" id="txtQuoteDate" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="txtQSComments">報價狀態備註:</label>
                <textarea id="txtQSComments" class="form-control" rows="3"
                          placeholder="報價相關的說明或補充資訊..."></textarea>
            </div>
        </div>
    </div>
</div>
```

**B. 獲獎狀態追蹤**
```html
<div class="form-section" id="awardStatus">
    <h3 class="section-title">獲獎狀態追蹤</h3>
    
    <div class="award-timeline">
        <div class="timeline-step" data-step="quote">
            <div class="step-indicator"></div>
            <div class="step-content">
                <h5>報價提交</h5>
                <p>已向客戶提供正式報價</p>
            </div>
        </div>
        
        <div class="timeline-step" data-step="evaluation">
            <div class="step-indicator"></div>
            <div class="step-content">
                <h5>客戶評估</h5>
                <p>等待客戶回覆和決定</p>
            </div>
        </div>
        
        <div class="timeline-step" data-step="result">
            <div class="step-indicator"></div>
            <div class="step-content">
                <h5>結果確認</h5>
                <p>獲得最終結果通知</p>
            </div>
        </div>
    </div>
    
    <div class="award-result-selection">
        <h4>獲獎結果</h4>
        
        <div class="result-options">
            <label class="result-option won">
                <input type="radio" name="awardResult" value="won" 
                       id="optAwardStatusWon" onchange="handleAwardStatusChange('won')">
                <div class="result-card">
                    <div class="result-icon">
                        <i class="icon-trophy"></i>
                    </div>
                    <div class="result-title">得標</div>
                </div>
            </label>
            
            <label class="result-option lost">
                <input type="radio" name="awardResult" value="lost" 
                       id="optAwardStatusLost" onchange="handleAwardStatusChange('lost')">
                <div class="result-card">
                    <div class="result-icon">
                        <i class="icon-x-circle"></i>
                    </div>
                    <div class="result-title">失標</div>
                </div>
            </label>
            
            <label class="result-option other">
                <input type="radio" name="awardResult" value="other" 
                       id="optAwardStatusOther" onchange="handleAwardStatusChange('other')">
                <div class="result-card">
                    <div class="result-icon">
                        <i class="icon-more-horizontal"></i>
                    </div>
                    <div class="result-title">其他</div>
                </div>
            </label>
        </div>
        
        <!-- 得標詳細資訊 -->
        <div class="award-details" id="wonDetails" style="display:none">
            <div class="details-grid">
                <div class="form-group">
                    <label for="txtDateWon">得標日期:</label>
                    <input type="date" id="txtDateWon" class="form-control">
                </div>
                
                <div class="form-group">
                    <label for="cmbSalesAdministrator">銷售管理員:</label>
                    <select id="cmbSalesAdministrator" class="form-control">
                        <option value="">請選擇...</option>
                        <!-- 動態載入選項 -->
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="txtContractValue">合約金額:</label>
                    <div class="input-group">
                        <span class="input-group-text">$</span>
                        <input type="text" id="txtContractValue" class="form-control currency-input"
                               placeholder="0.00">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="txtContractDuration">合約期間 (年):</label>
                    <input type="number" id="txtContractDuration" class="form-control"
                           min="1" max="10" step="0.5">
                </div>
            </div>
        </div>
        
        <!-- 失標/其他詳細資訊 -->
        <div class="award-details" id="lostDetails" style="display:none">
            <div class="form-group">
                <label for="txtLostReason">失標原因:</label>
                <select id="txtLostReason" class="form-control">
                    <option value="">請選擇主要原因...</option>
                    <option value="price">價格因素</option>
                    <option value="technical">技術規格</option>
                    <option value="delivery">交期要求</option>
                    <option value="relationship">客戶關係</option>
                    <option value="competitor">競爭對手優勢</option>
                    <option value="other">其他原因</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="txtQSCommentsProceedCancel">詳細說明:</label>
                <textarea id="txtQSCommentsProceedCancel" class="form-control" rows="4"
                          placeholder="請詳細說明失標原因或其他情況..."></textarea>
            </div>
        </div>
    </div>
</div>
```

**C. P&L 分析表格**
```html
<div class="form-section" id="plAnalysis">
    <h3 class="section-title">
        盈虧 (P&L) 分析
        <button type="button" class="btn btn-secondary btn-sm" onclick="refreshPLData()">
            <i class="icon-refresh"></i> 重新計算
        </button>
    </h3>
    
    <div class="pl-summary-cards">
        <div class="summary-card revenue">
            <div class="card-icon">
                <i class="icon-trending-up"></i>
            </div>
            <div class="card-content">
                <h5>預估營收</h5>
                <p class="card-value" id="estimatedRevenue">$0</p>
            </div>
        </div>
        
        <div class="summary-card cost">
            <div class="card-icon">
                <i class="icon-trending-down"></i>
            </div>
            <div class="card-content">
                <h5>總成本</h5>
                <p class="card-value" id="totalCosts">$0</p>
            </div>
        </div>
        
        <div class="summary-card profit">
            <div class="card-icon">
                <i class="icon-dollar-sign"></i>
            </div>
            <div class="card-content">
                <h5>預估利潤</h5>
                <p class="card-value" id="estimatedProfit">$0</p>
            </div>
        </div>
        
        <div class="summary-card margin">
            <div class="card-icon">
                <i class="icon-percent"></i>
            </div>
            <div class="card-content">
                <h5>利潤率</h5>
                <p class="card-value" id="profitMarginPercent">0%</p>
            </div>
        </div>
    </div>
    
    <div class="pl-table-container">
        <table class="pl-table" id="tblAwardStatus">
            <thead>
                <tr>
                    <th width="200">項目</th>
                    <th width="120">預估值</th>
                    <th width="120">實際值</th>
                    <th width="120">差異</th>
                    <th width="100">差異率</th>
                    <th>備註</th>
                </tr>
            </thead>
            <tbody id="plTableBody">
                <!-- 動態填充 P&L 資料 -->
            </tbody>
        </table>
    </div>
    
    <!-- P&L 趨勢圖 -->
    <div class="pl-chart-container" id="plChartContainer">
        <h4>盈虧趨勢分析</h4>
        <canvas id="plTrendChart" width="800" height="400"></canvas>
    </div>
</div>
```

### 8. Attachment.aspx - 附件管理頁面

#### UI 組件規格

**A. 附件管理區塊**
```html
<div class="attachment-section">
    <h3 class="section-title">
        附件管理
        <div class="attachment-stats">
            <span class="stat-item">
                <i class="icon-file"></i>
                <span id="totalFiles">0</span> 個檔案
            </span>
            <span class="stat-item">
                <i class="icon-database"></i>
                <span id="totalSize">0 MB</span>
            </span>
        </div>
    </h3>
    
    <!-- 上傳區域 -->
    <div class="upload-zone" id="uploadZone">
        <div class="upload-content">
            <div class="upload-icon">
                <i class="icon-upload-cloud"></i>
            </div>
            <h4>拖放檔案到此處或點擊上傳</h4>
            <p>支援格式: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, 圖片檔案</p>
            <button type="button" class="btn btn-primary" onclick="triggerFileSelect()">
                選擇檔案
            </button>
            <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.png,.jpg,.jpeg" style="display:none">
        </div>
        
        <!-- 上傳進度 -->
        <div class="upload-progress" id="uploadProgress" style="display:none">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text">
                <span id="progressText">上傳中...</span>
                <span id="progressPercent">0%</span>
            </div>
        </div>
    </div>
    
    <!-- 附件清單 -->
    <div class="attachment-list" id="attachmentList">
        <div class="list-header">
            <div class="view-controls">
                <button class="view-btn active" data-view="list" onclick="switchView('list')">
                    <i class="icon-list"></i>
                </button>
                <button class="view-btn" data-view="grid" onclick="switchView('grid')">
                    <i class="icon-grid"></i>
                </button>
            </div>
            
            <div class="sort-controls">
                <select id="sortBy" class="form-control form-control-sm" onchange="sortAttachments()">
                    <option value="name">名稱</option>
                    <option value="date">日期</option>
                    <option value="size">大小</option>
                    <option value="type">類型</option>
                </select>
                <button class="btn btn-sm btn-secondary" onclick="toggleSortOrder()">
                    <i class="icon-arrow-up" id="sortIcon"></i>
                </button>
            </div>
        </div>
        
        <div class="list-content" id="listContent">
            <!-- 動態填充附件項目 -->
        </div>
    </div>
</div>
```

**B. Tasklist 管理區塊**
```html
<div class="tasklist-section">
    <h3 class="section-title">
        任務清單 (Tasklist)
        <div class="tasklist-help">
            <span class="help-icon" data-tooltip="任務清單說明">
                <i class="icon-help-circle"></i>
            </span>
        </div>
    </h3>
    
    <!-- Tasklist 狀態指示器 -->
    <div class="tasklist-status" id="tasklistStatus">
        <div class="status-indicator available">
            <div class="indicator-icon">
                <i class="icon-check-circle"></i>
            </div>
            <div class="indicator-text">
                <h5>可用狀態</h5>
                <p>任務清單未被其他使用者使用</p>
            </div>
        </div>
    </div>
    
    <!-- Tasklist 操作面板 -->
    <div class="tasklist-controls">
        <div class="control-group">
            <button type="button" class="btn btn-primary" onclick="checkoutTasklist()" id="btnCheckout">
                <i class="icon-edit"></i>
                借出編輯
            </button>
            
            <button type="button" class="btn btn-success" onclick="checkinTasklist()" 
                    id="btnCheckin" style="display:none">
                <i class="icon-save"></i>
                歸還儲存
            </button>
            
            <button type="button" class="btn btn-secondary" onclick="viewTasklist()">
                <i class="icon-eye"></i>
                檢視
            </button>
        </div>
        
        <div class="control-group">
            <button type="button" class="btn btn-outline-secondary" onclick="downloadTasklist()">
                <i class="icon-download"></i>
                下載
            </button>
            
            <button type="button" class="btn btn-outline-secondary" onclick="emailTasklist()">
                <i class="icon-mail"></i>
                Email
            </button>
        </div>
    </div>
    
    <!-- Tasklist 檔案資訊 -->
    <div class="tasklist-info" id="tasklistInfo">
        <div class="info-grid">
            <div class="info-item">
                <label>檔案名稱:</label>
                <span id="tasklistFileName">-</span>
            </div>
            <div class="info-item">
                <label>檔案大小:</label>
                <span id="tasklistFileSize">-</span>
            </div>
            <div class="info-item">
                <label>最後修改:</label>
                <span id="tasklistLastModified">-</span>
            </div>
            <div class="info-item">
                <label>目前狀態:</label>
                <span id="tasklistCurrentStatus" class="status-badge">-</span>
            </div>
        </div>
    </div>
    
    <!-- Tasklist 編輯器 (當借出時顯示) -->
    <div class="tasklist-editor" id="tasklistEditor" style="display:none">
        <div class="editor-header">
            <h4>任務清單編輯器</h4>
            <div class="editor-controls">
                <button class="btn btn-sm btn-secondary" onclick="saveTasklist()">
                    <i class="icon-save"></i>
                    儲存
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="revertChanges()">
                    <i class="icon-rotate-ccw"></i>
                    復原
                </button>
            </div>
        </div>
        
        <div class="editor-content">
            <!-- Excel 編輯器將嵌入此處 -->
            <iframe id="tasklistFrame" class="tasklist-frame" 
                    src="about:blank" frameborder="0">
            </iframe>
        </div>
    </div>
</div>
```

## 🎨 CSS 樣式規範

### 設計系統
```css
/* CSS 變數定義 */
:root {
    /* 色彩系統 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --success-color: #16a34a;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    
    /* 中性色彩 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 字體系統 */
    --font-sans: 'Microsoft JhengHei', 'Segoe UI', sans-serif;
    --font-mono: 'Consolas', 'Monaco', monospace;
    
    /* 間距系統 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 陰影系統 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* 圓角系統 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
}

/* 基礎重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-sans);
    line-height: 1.5;
    color: var(--gray-800);
    background-color: var(--gray-50);
}

/* 佈局系統 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.form-section {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

/* 表單控制項 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control.invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-control.valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
}

/* 按鈕系統 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
    gap: var(--spacing-xs);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--gray-600);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--gray-700);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

/* 響應式網格 */
.grid-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(var(--spacing-sm) * -1);
}

.col-md-6 {
    flex: 0 0 50%;
    padding: 0 var(--spacing-sm);
}

.col-md-4 {
    flex: 0 0 33.333333%;
    padding: 0 var(--spacing-sm);
}

.col-md-3 {
    flex: 0 0 25%;
    padding: 0 var(--spacing-sm);
}

/* 平板響應式 */
@media (max-width: 1024px) {
    .col-md-6,
    .col-md-4,
    .col-md-3 {
        flex: 0 0 100%;
        margin-bottom: var(--spacing-md);
    }
}

/* 手機響應式 */
@media (max-width: 767px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .form-section {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}
```

## ⚡ JavaScript 架構規範

### 模組化結構
```javascript
/**
 * CQR 前端應用程式主模組
 */
const CQRApp = (function() {
    'use strict';
    
    // 私有變數
    let currentPage = '';
    let isDirty = false;
    let autoSaveInterval = null;
    
    // 模組初始化
    function init() {
        setupGlobalEventHandlers();
        detectCurrentPage();
        initializeCurrentPage();
        startAutoSave();
    }
    
    // 設定全域事件處理器
    function setupGlobalEventHandlers() {
        // 表單變更追蹤
        $(document).on('change input', '.form-control', function() {
            markDirty();
        });
        
        // 離開頁面確認
        window.addEventListener('beforeunload', function(e) {
            if (isDirty) {
                e.preventDefault();
                e.returnValue = '有未儲存的變更，確定要離開嗎？';
                return '有未儲存的變更，確定要離開嗎？';
            }
        });
        
        // AJAX 錯誤處理
        $(document).ajaxError(function(event, xhr, settings, error) {
            console.error('AJAX Error:', error);
            showNotification('網路錯誤，請稍後再試', 'error');
        });
        
        // 載入指示器
        $(document).ajaxStart(function() {
            showLoadingIndicator();
        }).ajaxStop(function() {
            hideLoadingIndicator();
        });
    }
    
    // 偵測當前頁面
    function detectCurrentPage() {
        const path = window.location.pathname;
        const fileName = path.split('/').pop().split('.')[0];
        currentPage = fileName.toLowerCase();
    }
    
    // 初始化當前頁面
    function initializeCurrentPage() {
        switch(currentPage) {
            case 'description':
                if (window.DescriptionPage) {
                    window.DescriptionPage.init();
                }
                break;
            case 'background':
                if (window.BackgroundPage) {
                    window.BackgroundPage.init();
                }
                break;
            case 'engineering':
                if (window.EngineeringPage) {
                    window.EngineeringPage.init();
                }
                break;
            case 'quoteresponse':
                if (window.QuoteResponsePage) {
                    window.QuoteResponsePage.init();
                }
                break;
            case 'routing':
                if (window.RoutingPage) {
                    window.RoutingPage.init();
                }
                break;
            case 'salescloseout':
                if (window.SalesCloseoutPage) {
                    window.SalesCloseoutPage.init();
                }
                break;
            case 'attachment':
                if (window.AttachmentPage) {
                    window.AttachmentPage.init();
                }
                break;
        }
    }
    
    // 公開 API
    return {
        init: init,
        getCurrentPage: function() { return currentPage; },
        isDirty: function() { return isDirty; },
        markClean: function() { isDirty = false; },
        markDirty: function() { isDirty = true; }
    };
})();

// DOM 準備就緒時初始化
$(document).ready(function() {
    CQRApp.init();
});
```

## 📊 效能優化規範

### 載入時間優化
- **首次內容繪製 (FCP)**: < 1.5秒
- **最大內容繪製 (LCP)**: < 2.5秒  
- **交互就緒時間 (TTI)**: < 3.5秒
- **累積版面偏移 (CLS)**: < 0.1

### 資源優化策略
```javascript
// 延遲載入非關鍵資源
function lazyLoadResources() {
    // 延遲載入圖片
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// 預載入關鍵資源
function preloadCriticalResources() {
    const criticalResources = [
        '/css/style.css',
        '/js/jquery-1.10.2.min.js',
        '/js/jquery-ui.min.js'
    ];
    
    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource;
        link.as = resource.endsWith('.css') ? 'style' : 'script';
        document.head.appendChild(link);
    });
}
```

## 🧪 測試規範

### 單元測試
```javascript
// 使用 QUnit 或 Jest 進行單元測試
QUnit.test("AntaresManager - Filter Validation", function(assert) {
    const manager = new AntaresManager();
    
    // 測試有效篩選參數
    const validFilters = {
        OEMGroup: 'BMW Group',
        OEMCustomer: 'BMW',
        Platform: 'CLAR'
    };
    
    assert.ok(manager.validateFilters(validFilters), "Valid filters should pass validation");
    
    // 測試無效篩選參數
    const invalidFilters = {
        OEMGroup: '',
        OEMCustomer: null
    };
    
    assert.notOk(manager.validateFilters(invalidFilters), "Invalid filters should fail validation");
});
```

### 整合測試
```javascript
// 頁面整合測試
QUnit.test("Description Page - Form Submission", function(assert) {
    const done = assert.async();
    
    // 填寫表單
    $('#txtCQRDesc').val('Test CQR Description');
    $('#cmbModelYear').val('2024');
    $('#txtVolPerAnnum').val('50000');
    
    // 模擬提交
    $('#submitButton').click();
    
    // 驗證結果
    setTimeout(function() {
        assert.ok($('.success-message').is(':visible'), "Success message should be visible");
        assert.equal($('#txtCQRDesc').val(), 'Test CQR Description', "Form data should be preserved");
        done();
    }, 1000);
});
```

### E2E 測試
```javascript
// 使用 Cypress 進行端到端測試
describe('CQR System - Complete Workflow', () => {
    it('Should complete full quote process', () => {
        // 登入系統
        cy.login('testuser', 'password');
        
        // 建立新 CQR
        cy.visit('/Source/CreateCQR.aspx');
        cy.get('#optCQRTypeNew').check();
        cy.get('#btnCreate').click();
        
        // 填寫 Description
        cy.get('#txtCQRDesc').type('Cypress Test CQR');
        cy.get('#cmbModelYear').select('2024');
        cy.get('#txtVolPerAnnum').type('50000');
        
        // 繼續到 Background
        cy.get('.nav-link[href*="Background"]').click();
        cy.get('#txtBgInfo').type('Test background information');
        
        // 驗證工作流程
        cy.get('.progress-indicator').should('contain', '進行中');
    });
});
```

## 🔒 安全性規範

### 輸入驗證
```javascript
// 客戶端輸入清理
function sanitizeInput(input) {
    return input
        .replace(/[<>\"']/g, '') // 移除潛在的 XSS 字符
        .trim()
        .substring(0, 1000); // 限制長度
}

// CSRF 保護
function addCSRFToken(data) {
    return {
        ...data,
        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
    };
}
```

### 資料傳輸安全
```javascript
// AJAX 請求加密
$.ajaxSetup({
    beforeSend: function(xhr, settings) {
        // 添加安全標頭
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        
        // 加密敏感資料
        if (settings.data && settings.type === 'POST') {
            settings.data = addCSRFToken(settings.data);
        }
    }
});
```

## 📋 驗收標準

### 功能驗收
- [ ] 所有8個頁面正常載入和導航
- [ ] 表單驗證正確運作
- [ ] AJAX 功能穩定運行
- [ ] Excel 整合功能正常
- [ ] 檔案上傳下載功能完整
- [ ] 權限控制正確實施

### 效能驗收
- [ ] 頁面載入時間 < 3秒
- [ ] AJAX 回應時間 < 2秒
- [ ] 記憶體使用量 < 100MB
- [ ] CPU 使用率 < 30%

### 相容性驗收
- [ ] Internet Explorer 11+
- [ ] Chrome 60+
- [ ] Firefox 55+
- [ ] Edge 16+
- [ ] 1024x768 以上解析度
- [ ] 平板和手機基本可用

---

**文件版本**: v1.0  
**最後更新**: 2024-01-21  
**審核狀態**: 待審核