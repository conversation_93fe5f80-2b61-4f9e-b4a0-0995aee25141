﻿namespace CQR.Domain.Enum;


public enum UserRoleCode
{
    SAFR,
    AMGR,
    CQRHRCOST,
    CQRHRPGM,
    CQRHRPM,
    CQRHRAME,
    CQRHRPUR,
    FRAN,
    FR00,
    MBnE,
    CQGWNFY,
    PETM,
    PRD,
    PSR,
    PSM,
    SDIR,
    BUM,
    FIN,
    PRES,
    UKGM,
    UKDM,
    CMGR,
    BEB,
    MEPL,
    AIME,
    AIPR,
    CTLC,
    PA
}
public static class UserRoleCodeExtensions
{
    // string → enum 對照（資料庫的 RoleCode 轉 enum）
    private static readonly Dictionary<string, UserRoleCode> _stringToEnum = new(StringComparer.OrdinalIgnoreCase)
    {
        { "SAFR", UserRoleCode.SAFR },
        { "AMGR", UserRoleCode.AMGR },
        { "CQRHRCOST", UserRoleCode.CQRHRCOST },
        { "CQRHRPGM", UserRoleCode.CQRHRPGM },
        { "CQRHRPM", UserRoleCode.CQRHRPM },
        { "CQRHRAME", UserRoleCode.CQRHRAME },
        { "CQRHRPUR", UserRoleCode.CQRHRPUR },
        { "FRAN", UserRoleCode.FRAN },
        { "FR00", UserRoleCode.FR00 },
        { "MB&E", UserRoleCode.MBnE },  // 特殊轉換
        { "CQGWNFY", UserRoleCode.CQGWNFY },
        { "PETM", UserRoleCode.PETM },
        { "PRD", UserRoleCode.PRD },
        { "PSR", UserRoleCode.PSR },
        { "PSM", UserRoleCode.PSM },
        { "SDIR", UserRoleCode.SDIR },
        { "BUM", UserRoleCode.BUM },
        { "FIN", UserRoleCode.FIN },
        { "PRES", UserRoleCode.PRES },
        { "UKGM", UserRoleCode.UKGM },
        { "UKDM", UserRoleCode.UKDM },
        { "CMGR", UserRoleCode.CMGR },
        { "BEB", UserRoleCode.BEB },
        { "MEPL", UserRoleCode.MEPL },
        { "AIME", UserRoleCode.AIME },
        { "AIPR", UserRoleCode.AIPR },
        { "CTLC", UserRoleCode.CTLC },
        { "PA", UserRoleCode.PA },
    };

    /// <summary>
    /// 將資料庫中的 RoleCode 字串轉換成對應的 Enum。
    /// </summary>
    public static bool TryParseRole(string input, out UserRoleCode role)
    {
        return _stringToEnum.TryGetValue(input?.Trim().ToUpper(), out role);
    }

    /// <summary>
    /// 將 Enum 轉換為資料庫原始 RoleCode 字串。
    /// </summary>
    public static string ToRoleString(this UserRoleCode role)
    {
        //return role switch
        //{
        //    UserRoleCode.MBnE => "MB&E",
        //    _ => role.ToString()
        //};
        return role.ToString();
    }
}