<!-- <script setup lang="ts"></script>

<template>
  <div>Gateway2Tab</div>
</template>

<style lang="scss" scoped></style> -->

<template>
  <el-card shadow="hover">
    <h2>GDPEP Gateway {{ phase }} Approval</h2>

    <el-form :model="form" label-width="160px" label-position="left">
      <el-form-item label="Release?">
        <el-checkbox v-model="form.release" disabled>Release?</el-checkbox>
      </el-form-item>

      <el-form-item label="Business Type">
        <el-input
          v-model="form.businessType"
          readonly
          style="background-color: silver"
        />
      </el-form-item>

      <el-form-item label="GDPEP Category">
        <el-select
          v-model="form.category"
          placeholder="Select Category"
          style="width: 150px"
          disabled
        >
          <el-option label="" value="" />
        </el-select>
      </el-form-item>

      <el-form-item label="Date">
        <el-date-picker
          v-model="form.date"
          type="date"
          disabled
          style="width: 175px"
        />
      </el-form-item>

      <el-form-item label="Sales Director (SDIR)">
        <el-input
          v-model="form.sdir"
          readonly
          style="background-color: silver"
        />
      </el-form-item>

      <h3>1. CUSTOMER / PLATFORM DETAILS</h3>

      <el-form-item label="Product Description">
        <el-input
          v-model="form.productDescription"
          readonly
          style="background-color: silver"
        />
      </el-form-item>

      <el-form-item label="Model Year">
        <el-input
          v-model="form.modelYear"
          readonly
          style="background-color: silver"
        />
      </el-form-item>

      <el-form-item label="Platform">
        <el-input
          v-model="form.platform"
          readonly
          style="background-color: silver"
        />
      </el-form-item>

      <el-form-item label="Comments">
        <el-input
          v-model="form.comments"
          type="textarea"
          rows="3"
          readonly
          style="background-color: silver"
        />
      </el-form-item>

      <h3>Objective</h3>
      <ul>
        <li>* Business Case</li>
        <li>* Review Financials and Sales Price</li>
        <li>* Define Quoting Strategy</li>
      </ul>

      <h3>Approval</h3>
      <el-table :data="approvers" border>
        <el-table-column prop="role" label="Role" width="120" />
        <el-table-column label="Required" width="100">
          <template #default="scope">
            <el-checkbox v-model="scope.row.required" disabled />
          </template>
        </el-table-column>
        <el-table-column label="Approved" width="100">
          <template #default="scope">
            <el-checkbox v-model="scope.row.approved" disabled />
          </template>
        </el-table-column>
        <el-table-column label="Date/Time" prop="date" width="160" />
        <el-table-column label="Comments">
          <template #default="scope">
            <el-input
              v-model="scope.row.comment"
              type="textarea"
              readonly
              autosize
              style="background-color: silver"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <ApprovalAttachmentPanel
      :attachments="attachmentList"
      @add="handleAddAttachment"
      @delete="handleDeleteAttachment"
    />
  </el-card>
</template>

<script setup lang="ts">
import { useFormModelStore } from "@/store/modules/formModel";
import ApprovalAttachmentPanel from "@/views/cqr/tabs/ApprovalAttachmentPanel.vue";
import { onMounted, ref } from "vue";
const formStore = useFormModelStore();
const fromCQRHeaderCollection = ref<any>({});

onMounted(() => {
  if (formStore.modelCQRHeaderCollection) {
    // formData.value = { ...formStore.modelCQRHeaderCollection.iCQRHeader };
  }
});
const phase = 2;

const form = ref({
  release: false,
  businessType: "",
  category: "",
  date: "",
  sdir: "Ratcliffe, Paul",
  productDescription: "",
  modelYear: "2024 1/2",
  platform: "",
  comments: ""
});

const approvers = ref([
  { role: "CEO", required: false, approved: false, date: "", comment: "" },
  { role: "CFO", required: false, approved: false, date: "", comment: "" },
  { role: "CTO", required: false, approved: false, date: "", comment: "" },
  { role: "CPO", required: false, approved: false, date: "", comment: "" }
]);

const attachmentList = ref([
  { id: 1, filename: "Report_Q2_2024.pdf" },
  { id: 2, filename: "Design_Spec.xlsx" }
]);

function handleAddAttachment() {
  console.log("Add attachment clicked");
  // 開啟 dialog、呼叫 API、或其他處理
}

function handleDeleteAttachment(row) {
  attachmentList.value = attachmentList.value.filter(
    item => item.id !== row.id
  );
}
</script>

<style scoped>
h2 {
  font-size: 20px;
  font-weight: bold;
}
h3 {
  font-size: 16px;
  font-weight: bold;
  border-top: 1px solid #000;
  padding-top: 10px;
}
</style>
