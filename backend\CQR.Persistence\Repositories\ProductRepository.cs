//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;
//using CQRLIB;
//using CQRLIB.Models;
//using CQRLIB.Persistence;
//using Microsoft.EntityFrameworkCore;

//public class ProductRepository : Repository<Product>, IProductRepository
//{
//    private readonly CQrDbContext _context;

//    public ProductRepository(CQrDbContext context) : base(context)
//    {
//        _context = context; // 保存 DbContext 实例
//    }
//    // 自定义方法
//    public async Task<IEnumerable<Product>> GetProductsInStockAsync()
//    {
//        return await _context.Product.Where(p => p.Stock > 0).ToListAsync();
//    }
//}
