using CQR.Application.Repositories;
using CQR.Application.Services;
using CQR.Application.UseCases.CQR_Headers.Commands.Handler;
using CQR.Application.Interfaces;
using CQR.Infrastructure.Database;
using CQR.Infrastructure.External.SAP;
using Moq;

namespace CQR.Application.Tests.TestBase;

public abstract class TestBase
{
    protected readonly Mock<IRepositoryCollection> MockRepositoryCollection;
    protected readonly Mock<ICQRCommandHandler> MockCommandHandler;
    protected readonly Mock<ICQRQueryService> MockQueryService;
    protected readonly Mock<ICQRValidationService> MockValidationService;
    protected readonly Mock<IAttachmentService> MockAttachmentService;
    protected readonly Mock<IProductService> MockProductService;
    protected readonly Mock<ICustomService> MockCustomService;
    protected readonly Mock<IExcelService> MockExcelService;
    protected readonly Mock<IDatabaseService> MockDatabaseService;
    protected readonly Mock<ISapRFCService> MockSapRFCService;
    protected readonly Mock<ISqlConnectionService> MockSqlConnectionService;
    protected readonly Mock<ISapConnectionService> MockSapConnectionService;
    protected readonly Mock<ICurrentUserService> MockCurrentUserService;

    // Repository Mocks
    protected readonly Mock<IUserRoleQueryRepository> MockUserRoleQueryRepository;
    protected readonly Mock<ICQRGDPIMPhasesRepository> MockCQRGDPIMPhasesRepository;
    protected readonly Mock<ICQRHeaderRepository> MockCQRHeaderRepository;
    protected readonly Mock<ICQRIHSFolderRepository> MockCQRIHSFolderRepository;
    protected readonly Mock<ICqrHeaderQueryRepository> MockCqrHeaderQueryRepository;

    protected TestBase()
    {
        // Initialize service mocks
        MockRepositoryCollection = new Mock<IRepositoryCollection>();
        MockCommandHandler = new Mock<ICQRCommandHandler>();
        MockQueryService = new Mock<ICQRQueryService>();
        MockValidationService = new Mock<ICQRValidationService>();
        MockAttachmentService = new Mock<IAttachmentService>();
        MockProductService = new Mock<IProductService>();
        MockCustomService = new Mock<ICustomService>();
        MockExcelService = new Mock<IExcelService>();
        MockDatabaseService = new Mock<IDatabaseService>();
        MockSapRFCService = new Mock<ISapRFCService>();
        MockSqlConnectionService = new Mock<ISqlConnectionService>();
        MockSapConnectionService = new Mock<ISapConnectionService>();
        MockCurrentUserService = new Mock<ICurrentUserService>();

        // Initialize repository mocks
        MockUserRoleQueryRepository = new Mock<IUserRoleQueryRepository>();
        MockCQRGDPIMPhasesRepository = new Mock<ICQRGDPIMPhasesRepository>();
        MockCQRHeaderRepository = new Mock<ICQRHeaderRepository>();
        MockCQRIHSFolderRepository = new Mock<ICQRIHSFolderRepository>();
        MockCqrHeaderQueryRepository = new Mock<ICqrHeaderQueryRepository>();

        SetupRepositoryCollection();
    }

    private void SetupRepositoryCollection()
    {
        MockRepositoryCollection.Setup(x => x.UserRoleQueryRepository)
            .Returns(MockUserRoleQueryRepository.Object);
        MockRepositoryCollection.Setup(x => x.CQRGDPIMPhasesRepository)
            .Returns(MockCQRGDPIMPhasesRepository.Object);
        MockRepositoryCollection.Setup(x => x.CQRHeaderRepository)
            .Returns(MockCQRHeaderRepository.Object);
        MockRepositoryCollection.Setup(x => x.CQRIHSFolderRepository)
            .Returns(MockCQRIHSFolderRepository.Object);
        MockRepositoryCollection.Setup(x => x.CqrHeaderQueryRepository)
            .Returns(MockCqrHeaderQueryRepository.Object);
    }

    protected IApplicationServices CreateApplicationServices()
    {
        return new ApplicationServices(
            MockCommandHandler.Object,
            MockQueryService.Object,
            MockValidationService.Object,
            MockAttachmentService.Object,
            MockProductService.Object,
            MockCustomService.Object,
            MockExcelService.Object,
            MockDatabaseService.Object,
            MockSapRFCService.Object,
            MockSqlConnectionService.Object,
            MockSapConnectionService.Object,
            MockCurrentUserService.Object,
            MockRepositoryCollection.Object
        );
    }

    protected ICQRQueryService CreateCQRQueryService()
    {
        return new CQRQueryService(MockRepositoryCollection.Object);
    }

    protected void VerifyAllMocks()
    {
        MockRepositoryCollection.VerifyAll();
        MockCommandHandler.VerifyAll();
        MockQueryService.VerifyAll();
        MockValidationService.VerifyAll();
        MockAttachmentService.VerifyAll();
        MockProductService.VerifyAll();
        MockCustomService.VerifyAll();
        MockExcelService.VerifyAll();
        MockDatabaseService.VerifyAll();
        MockSapRFCService.VerifyAll();
        MockSqlConnectionService.VerifyAll();
        MockSapConnectionService.VerifyAll();
        MockCurrentUserService.VerifyAll();
    }
}