import type { CQRHeader } from "./../types";
// src/store/modules/formModel.ts
import { defineStore } from "pinia";
import {
  getAttachFileByQueueKey,
  getCQRHeaderById,
  getCQRHeaderCollection,
  getIHSFolderByQueuekey,
  getRoutingHeaderByQueuekey,
  getAttachments,
  getIHSFolderCtierias,
  createNewCQRHeaderForm,
  getAwardModelYear,
  CreateNewCQRHeaderDraft,
  getDictCode,
  getUserRoleCollection,
  SaveCQRHeaderCollection
} from "@/api/cqr";
import type { AttachFileInfo, CQRHeader, CQRIHSFolder } from "../types";
import { CQRHeaderCollection } from "@/types/cqr/CQRHeaderCollection";

export const useFormModelStore = defineStore("formModel", {
  state: () => ({
    codes: [] as Array<any>, // 或用更明確型別
    modelCQRHeader: null as CQRHeader | null,
    modelIHSFolder: null as Record<string, any> | null,
    modelRoutingHeader: null as any[],
    modelAttachFile: null as AttachFileInfo[],
    modelCQRHeaderCollection: null as Record<string, any> | null,
    modelIHSFolderCriteria: null as Record<string, any> | null,
    modelAwardModelYear: null as Record<string, any> | null,
    modelUserRoleCollection: null as any,
    queueKey: null,
    loading: false
  }),
  actions: {
    async fetchUserRoleColleciton() {
      const userRoleCollection: any = await getUserRoleCollection();
      this.modelUserRoleCollection = userRoleCollection;
    },
    async fetchDictCodes() {
      // if (this.loaded) return; // 防止重複載入
      const res = await getDictCode();
      this.codes = res.data || [];
      // this.loaded = true;
    },
    getCodesByGroup(group: string) {
      return this.codes[group];
      // return this.codes.filter(code => code.group === group);
    },
    async fetchModelData(queueKey: number) {
      if (this.queueKey === queueKey && this.model !== null) {
        console.log("[formModel] 使用快取 model，不重新 request");
        return; // ✅ 已載入過，不重複 request
      }
      console.log("[formModel] 發出新 API 請求 for queueKey:", queueKey);
      this.loading = true;
      this.queueKey = queueKey;

      // 模擬一個 API 呼叫
      try {
        const { data } = await getCQRHeaderById(queueKey);
        const { data: dataIHSFolder } = await getIHSFolderByQueuekey(queueKey);
        this.model = data;
        this.modelIHSFolder = dataIHSFolder as CQRIHSFolder[];
      } catch (error) {
        console.error("❌ fetchModelData error:", error);
        this.model = null;
        this.modelIHSFolder = null;
      } finally {
        this.loading = false;
      }
      // const { data } = await getCQRHeaderById(queueKey);
      // const data = await response.json();
      // this.model = data;
    },
    async createNewCQRHeaderForm(CreateCQRHeaderInput: any) {
      try {
        const response = await createNewCQRHeaderForm(CreateCQRHeaderInput);
        this.model = response.data; // 假設 API 返回的數據結構符合 model 的類型
        return response;
      } catch (error) {
        console.error("Error creating new CQR Header:", error);
        throw error;
      }
    },
    async saveCQRHeaderCollection(dto: CQRHeaderCollection) {
      const response = await SaveCQRHeaderCollection(dto);
      return response;
    },
    async fetchModelAwardModelYear() {
      const data = await getAwardModelYear();
      this.modelAwardModelYear = data;
    },
    async fetchModelRoutingHeaderByQueuekey(queueKey: number) {
      const { data } = await getRoutingHeaderByQueuekey(queueKey);
      this.modelRoutingHeader = data;
    },
    async fetchModelAttachFileByQueuekey(queueKey: number) {
      const data = await getAttachFileByQueueKey(queueKey);
      this.modelAttachFile = data;
    },
    async fetchModelCQRHeaderCollectionByQueuekey(queueKey: number) {
      const data = await getCQRHeaderCollection(queueKey);
      this.modelCQRHeaderCollection = data;
    },
    async fetchModelIHSFolderCriteria() {
      const data = await getIHSFolderCtierias();
      this.modelIHSFolderCriteria = data;
    },
    async fetchAttachFiles(folderType: string, queueKey: number) {
      const data = await getAttachments(folderType, queueKey);
      this.modelAttachFile = data;
    },

    async refreshModel() {
      if (this.queueKey !== null) {
        await this.fetchModelData(this.queueKey);
      }
    },
    async createNewCQRHeaderDraft() {
      const _queuekey = await CreateNewCQRHeaderDraft();
      this.queueKey = _queuekey;
      return _queuekey;
    },

    setModel(data: Record<string, any>) {
      this.model = data;
    },
    clear() {
      this.model = null;
      this.modelIHSFolder = null;
      this.queueKey = null;
    }
  }
});
