// using Api.Entity;
// using Api.Service.Models;
using System.Data;

namespace CQR.Domain.IServices;

public interface ISapRFCService
{
    //PIR
    DataTable GetPIRData(string date, string time);

    // //Material Info
    // ApiResult GetMaterialData();

    // //HC Resource
    // DataTable GetEmployeeResource(string startDateTime, string endDateTime, string costCenter);

    // //BOM
    // ApiResult SendScheduleLineInfoToSapBom(List<SapBomModel> sapBomModels);

    // DataTable GetSapBomDataFromSap();

    // //MaterilaMapLine
    // DataTable GetMaterilaMapLine();

    // //ReleaseToSap
    // ApiResult ReleaseToSap(ReleaseToSapRFCModel releaseToSapRFCModel);

    // ApiResult GetSapInStorageData(SapInStorageInModel sapInStorage);
}
