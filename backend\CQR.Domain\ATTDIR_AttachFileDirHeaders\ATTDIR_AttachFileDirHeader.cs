﻿namespace CQR.Domain.ATTDIR_AttachFileDirHeaders;

public class ATTDIR_AttachFileDirHeader
{
    public int QueueKey { get; private set; }

    public string AttachTypeCode { get; private set; }
    public string MRPCompanyCode { get; private set; }
    public string PartNbr { get; private set; }
    public string RevLevel { get; private set; }
    public string AttachTitle { get; set; }
    public string FileLocation { get; private set; }
    public string ViewPgmCode { get; private set; }
    public string CreatedByName { get; private set; }
    public string CreatedDate { get; set; } // 可視需求轉成 DateOnly 或 DateTime
    public string CreatedTime { get; private set; }
    public string CreatedByLocCode { get; private set; }
    public string FolderTypeCode { get; private set; }
    public string FolderNbr { get; private set; }
    public string Comments { get; private set; }
    public string FolderStatus { get; private set; }
    public string CustomData { get; private set; }
    public string MimeType { get; private set; }

    public int? FolderNbr_int { get; private set; } // EF Core 會自動填入值，但你不能在程式中設定
}
