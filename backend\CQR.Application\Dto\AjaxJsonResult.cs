namespace CQRLIB.Dtos;
public class AjaxJsonResult
{
    public int StatusCode { get; set; }
    public Boolean SUCCESS { get; set; }
    public object DATA { get; set; }
    public string ERR_MESSAGE { get; set; }

    public AjaxJsonResult(int StatusCode, object _DATA, string _ERR_MESSAGE = "")
    {
        // this.StatusCode = StatusCode;
        this.SUCCESS = StatusCode == 200 ? true : false;
        this.DATA = _DATA;
        this.ERR_MESSAGE = _ERR_MESSAGE;
    }

    //public IActionResult Forbidden()
    //{
    //    var resultCollection = new
    //    {
    //        SUCCESS = false,
    //        ERR_MESSAGE = "Forbidden"
    //    };
    //    return StatusCodeResult
    //    //…return StatusCode BadRequest();// BadRequest(); //  return BadRequestResult(403, resultCollection);
    //}

    //public IActionResult Success(int statusCode, object oResult)
    //{
    //    statusCode = statusCode == 0 ? 200 : statusCode;

    //    var resultCollection = new
    //    {
    //        SUCCESS = true,
    //        DATA = oResult
    //    };
    //    return Ok(resultCollection);
    //    //return Ok(statusCode, resultCollection);
}

//public IActionResult Warning(int statusCode, object oResult, int oERRMessage = 500)
//{
//    var resultCollection = new
//    {
//        SUCCESS = false,
//        DATA = oResult,
//        ERR_MESSAGE = oERRMessage,
//        ERR_STACK_TRACE = (object)null
//    };

//    return StatusCode(statusCode, resultCollection);
//}

//public IActionResult Failure(object oResult, string oERRMessage = "")
//{
//    var resultCollection = new
//    {
//        SUCCESS = false,
//        DATA = oResult,
//        ERR_MESSAGE = oERRMessage,
//        ERR_STACK_TRACE = (object)null
//    };

//    //return StatusCode(500, resultCollection);
//}

//public IActionResult Exception(Exception e)
//{
//    var oErr = e is null ? string.Empty : e.Message;

//    var resultCollection = new
//    {
//        SUCCESS = false,
//        DATA = (object)null,
//        ERR_MESSAGE = oErr,
//        DETAIL_EXCEPTION = e
//    };

//    //return StatusCodes( StatusCode(500, resultCollection);
//}
//}
