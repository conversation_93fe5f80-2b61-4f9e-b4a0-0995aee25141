﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="MailKit" Version="4.8.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CQR.Application\CQR.Application.csproj" />
    <ProjectReference Include="..\CQR.Domain\CQR.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="sapnco">
      <HintPath>..\CQR.Domain\lib\sapnco.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="sapnco_utils">
      <HintPath>..\CQR.Domain\lib\sapnco_utils.dll</HintPath>
      <Private>true</Private>
    </Reference>
  </ItemGroup>

</Project>
