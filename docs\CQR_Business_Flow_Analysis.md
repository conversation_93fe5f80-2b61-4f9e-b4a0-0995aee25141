# CQR 系統業務流程分析

## 系統概述

CQR (Customer Quote Request) 系統是一個基於 ASP.NET WebForms 和 VB.NET 開發的企業級報價請求管理系統，用於管理客戶報價請求的完整生命週期。

## 核心架構

### 技術架構
- **前端**: ASP.NET WebForms with JavaScript/jQuery
- **後端**: VB.NET
- **數據庫**: SQL Server
- **文件處理**: Excel 集成 (GemBox.Spreadsheet, AceOfFix)
- **UI框架**: 自定義控件 + 960 Grid CSS

### 主要目錄結構
- `Source/` - 核心業務頁面和邏輯
- `_coreForm/` - 共用表單組件  
- `_elements/` - UI 元素和控件
- `Master/` - 模板和配置文件
- `aceoffix/` - Excel 在線編輯組件

## 核心業務流程

### 1. 系統入口和搜索流程

```mermaid
graph TD
    A[default.aspx] --> B[Search.aspx]
    B --> C[搜索條件設置]
    C --> D[執行搜索查詢]
    D --> E[顯示結果列表]
    E --> F[選擇特定 CQR]
    F --> G[進入 CQR 詳細頁面]
```

**流程說明**:
- 系統預設重定向到搜索頁面 (`default.aspx` → `Search.aspx`)
- 用戶可設定多種搜索條件篩選 CQR 記錄
- 搜索結果限制為前50筆以提升性能
- 支援高級搜索和報告模式

### 2. CQR 創建流程

```mermaid
graph TD
    A[CreateCQR.aspx] --> B{用戶權限檢查}
    B -->|AMGR/SDIR| C[允許創建新 CQR]
    B -->|其他用戶| D[權限受限]
    C --> E[選擇 CQR 類型]
    E --> F{類型判斷}
    F -->|新建| G[創建新 CQR 記錄]
    F -->|修改| H[驗證項目編號]
    H --> I[創建修訂版本]
    G --> J[初始化 CQR 對象]
    I --> J
    J --> K[存儲到 Session]
    K --> L[重定向到描述頁面]
```

**流程說明**:
- 僅 AMGR (Account Manager) 和 SDIR (Sales Director) 角色可創建 CQR
- 支援新建和修改兩種模式
- 修改模式需驗證原項目編號和版本號的有效性
- 創建後自動初始化 Session 對象進入編輯模式

### 3. CQR 詳細資訊管理流程

```mermaid
graph TD
    A[CQR 對象載入] --> B[Description.aspx - 項目描述]
    B --> C[Background.aspx - 背景資訊]
    C --> D[QuoteResponse.aspx - 報價回應]
    D --> E[Engineering.aspx - 工程資訊]
    E --> F[Routing.aspx - 路由審批]
    F --> G[Milestones.aspx - 里程碑管理]
    G --> H[SalesCloseout.aspx - 銷售結案]
    H --> I[Attachment.aspx - 附件管理]
```

**各階段功能**:

#### A. Description (項目描述)
- 輸入項目基本資訊
- 設定項目類型和優先級
- 客戶需求描述

#### B. Background (背景資訊)
- 項目背景說明
- 市場分析資訊
- 競爭對手情況

#### C. Quote Response (報價回應)
- 報價資訊輸入
- 成本分析
- 利潤計算
- 支援 Excel 模板整合

#### D. Engineering (工程資訊)
- 技術規格定義
- 工程可行性評估
- 資源需求分析

#### E. Routing (路由審批)
- 工作流程管理
- 審批路由設定
- 狀態跟踪
- 評論和操作記錄

#### F. Milestones (里程碑)
- 項目時程規劃
- 關鍵節點設定
- 進度追蹤

#### G. Sales Closeout (銷售結案)
- 最終報價確認
- 合約簽署狀態
- 專案移交

#### H. Attachments (附件管理)
- 文件上傳下載
- 版本控制
- 存取權限管理

### 4. 會話和安全管理流程

```mermaid
graph TD
    A[用戶登入] --> B[Session 初始化]
    B --> C[載入 CQR 對象到 Session]
    C --> D{編輯模式檢查}
    D -->|編輯模式| E[建立文件鎖定]
    D -->|檢視模式| F[唯讀存取]
    E --> G[定期刷新鎖定]
    G --> H[Session 維護]
    F --> H
    H --> I[Session 結束時清理資源]
```

**安全特性**:
- 基於角色的存取控制 (RBAC)
- 文件鎖定機制防止衝突編輯
- Session 自動逾時和資源清理
- 用戶權限細分 (AMGR, SDIR, Cost 用戶等)

### 5. Excel 整合流程

```mermaid
graph TD
    A[Excel 模板載入] --> B[AceOfFix 在線編輯器]
    B --> C[用戶編輯 Excel 文件]
    C --> D[自動儲存到伺服器]
    D --> E[資料解析和驗證]
    E --> F[更新 CQR 資料庫]
    F --> G[Session 結束時清理 Excel 程序]
```

**Excel 功能**:
- 支援多種 Excel 模板 (ECR worksheet, Full Tasklist 等)
- 在線編輯和協作功能
- 自動資料同步
- 程序資源管理和清理

### 6. 報告和匯出流程

```mermaid
graph TD
    A[報告需求] --> B[SearchView.aspx]
    B --> C[報告條件設定]
    C --> D[資料查詢和彙整]
    D --> E[格式化輸出]
    E --> F{匯出格式}
    F -->|Excel| G[Excel 報告生成]
    F -->|PDF| H[PDF 報告生成]
    F -->|Web| I[網頁報告顯示]
```

## 資料流向

### 主要資料表
- `CQR_Header` - CQR 主要資訊
- `CQR_*` 系列表 - 各模組詳細資料
- 用戶權限相關表 (`USERPROF_*`)

### 資料流程
1. **輸入** → 表單資料收集 → Session 暫存 → 資料驗證 → 資料庫儲存
2. **查詢** → 條件篩選 → SQL 查詢 → 結果快取 → 頁面顯示
3. **更新** → 鎖定檢查 → 資料變更 → 審計紀錄 → 通知相關用戶

## 系統特色功能

### 1. 工作流程管理
- 狀態驱動的業務流程
- 自動路由和通知
- 審批階層管理

### 2. 文件協作
- 在線 Excel 編輯
- 版本控制
- 多用戶協作

### 3. 權限管理
- 細粒度權限控制
- 角色繼承
- 動態權限檢查

### 4. 國際化支援
- 多語言字符處理 (LatinToAscii 函數)
- 區域化日期格式
- Unicode 正規化

## 潛在改進空間

1. **現代化技術升級**: 從 WebForms 遷移到現代 Web 框架
2. **API 化**: 建立 RESTful API 支援多端存取
3. **前端優化**: 改善用戶體驗和響應式設計
4. **雲端整合**: 支援雲端部署和儲存
5. **安全強化**: 實施現代安全標準和加密

## 結論

CQR 系統是一個功能完整的企業級報價管理系統，具備完善的工作流程控制、權限管理和文件協作功能。雖然技術架構相對傳統，但業務邏輯設計完善，具有良好的擴展性和維護性。