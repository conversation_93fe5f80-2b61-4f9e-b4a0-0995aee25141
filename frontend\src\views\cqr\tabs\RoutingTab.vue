<template>
  <el-card>
    <el-row style="margin-bottom: 6px">
      <el-col :span="24">
        <el-input
          v-model="fromCQRHeaderCollection.sTotalCommentsAction"
          type="textarea"
          :rows="14"
          style="width: 90%"
        />
      </el-col>
    </el-row>

    <el-row style="margin-bottom: 6px">
      <el-col :span="24">
        <el-checkbox v-model="showNotifications" @change="goShowNotifications">
          Show Notifications
        </el-checkbox>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-table :data="filteredRoutingTaskData" style="width: 100%" stripe>
          <el-table-column label="Task">
            <template #default="{ row }">
              {{ row.taskCode }}{{ row.routingTaskCode }}
            </template>
          </el-table-column>
          <el-table-column prop="assignedTo" label="Assigned To" />
          <el-table-column prop="result" label="Result" />
          <el-table-column prop="doneDate" label="Done" />
          <el-table-column prop="doneTm" label="Time" />
          <el-table-column prop="doneByName" label="Completed By" />
        </el-table>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";

// Define reactive variables
const actionComments = ref("");
const logComment = ref("");
// const showNotifications = ref(false);
const isSuperUser = ref(true); // This can be controlled by your logic

import { useFormModelStore } from "@/store/modules/formModel";
import { ROUTING_RGHeader } from "@/store/types";

const formStore = useFormModelStore();
const routingData = ref<ROUTING_RGHeader[]>([]);
const routingTaskData = ref<any[]>([]);
const cqrHeaderData = ref<any>(null);
const fromCQRHeaderCollection = ref<any>({});

const showNotifications = ref<boolean>(true);

onMounted(() => {
  if (formStore.modelCQRHeaderCollection) {
    // routingData.value = [...formStore.modelRoutingHeader];
    fromCQRHeaderCollection.value = { ...formStore.modelCQRHeaderCollection };
    routingTaskData.value = [
      ...formStore.modelCQRHeaderCollection.routingTasks
    ];
  }
});
watch(
  () => formStore.modelCQRHeaderCollection,
  newVal => {
    if (newVal) {
      fromCQRHeaderCollection.value = { ...newVal };
    }
  }
);

// watch(
//   () => formStore.modelCQRHeaderCollection,
//   newVal => {
//     if (newVal) {
//       cqrHeaderData.value = [...newVal];
//     }
//   }
// );

// Method stubs
const checkReadOnly = (id: string) => {
  console.log(`${id} clicked`);
};

const CommentPopUp = (id: string) => {
  console.log(`Pop-up for ${id}`);
};

const goShowNotifications = () => {
  console.log("Show Notifications toggled:", showNotifications.value);
};

// 用 computed 過濾資料

// 動態過濾資料：若 showNotifications 為 false，則排除 Notification 項目
const filteredRoutingTaskData = computed(() =>
  showNotifications.value
    ? routingTaskData.value
    : routingTaskData.value.filter(row => row.notificationInd == "1")
);
</script>

<style scoped>
/* Optional styling if needed */
.nowrap {
  white-space: nowrap;
}
</style>
