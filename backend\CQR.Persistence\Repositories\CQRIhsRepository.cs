﻿using CQR.Domain.CQR_GDPIMPhases;
using CQR.Domain.CQR_IHSs;
using Microsoft.EntityFrameworkCore;
using System.Data;

namespace CQR.Persistence.Command.Repositories;

public class CQRIHSRepository : EntityRepository<CQR_IHS, int>, ICQRIHSRepository
{
    public CQRIHSRepository(CQRDbContext context) : base(context: context) { }
    public async Task<int> countTotalAsync()
    {
        return await _context.Set<CQR_IHS>().CountAsync();
    }

    //public async Task<IEnumerable<SelectItem>> GetAntaresFieldAsync(string fieldName, string valueField, bool addBlank)
    //{
    //    //throw new NotImplementedException();
    //    valueField ??= fieldName;
    //    var sql = $@"
    //        SELECT DISTINCT 
    //            {fieldName} AS DisplayName, 
    //            {valueField} AS Value
    //        FROM CQR_Antares
    //        WHERE {fieldName} IS NOT NULL
    //        ORDER BY {fieldName}";

    //    //using var connection = CreateConnection();
    //    var items = (await _connection.QueryAsync<SelectItem>(sql)).ToList();

    //    if (addBlank)
    //    {
    //        items.Insert(0, new SelectItem { DisplayName = "", Value = "" });
    //    }

    //    return items;
    //}

    //public async Task<IEnumerable<SelectItem>> GetIHSFieldAsync(string fieldName, string valueField)
    //{
    //    valueField ??= fieldName;
    //    var sql = $@"
    //        SELECT DISTINCT 
    //            {fieldName} AS DisplayName, 
    //            {valueField} AS Value
    //        FROM CQR_IHS
    //        WHERE {fieldName} IS NOT NULL
    //        ORDER BY {fieldName}";

    //    var items = (await _connection.QueryAsync<SelectItem>(sql)).ToList();
    //    items.Insert(0, new SelectItem { DisplayName = "", Value = "" });
    //    return items;
    //}
    //throw new NotImplementedException();
    //public List<Dictionary<string, object>> GetAntaresData(AntaresQueryDto dto)
    //{
    //    var strSql = @"
    //            SELECT CA.UniqueNumber, CA.OEMGroup, CA.OEM, CA.Platform, CA.Program, CA.Nameplate, CA.Country, CA.Region, CA.NewBusinessCategory, CA.Status,
    //                   CA.ProductId + ': ' + CA.ProductDescription AS ProductFullDescription,
    //                   CA.ProductGrouping, CA.SOP, CA.EOP, CA.SoldFrom, CA.FinalAssembly,
    //                   CAST(CQR_Header.ProjectNbr AS INT) AS UsedBaseNumber
    //            FROM CQR_Antares CA
    //            OUTER APPLY (
    //                SELECT TOP 1 CQR_Header.ProjectNbr
    //                FROM CQR_AntaresFolder
    //                INNER JOIN CQR_Header ON CQR_Header.QueueKey = CQR_AntaresFolder.QueueKey
    //                                      AND CQR_Header.Status = 'XX_010100FR_XX'
    //                                      AND CAST(CQR_Header.ProjectNbr AS INT) <> @BaseProject
    //                WHERE ISNULL(CQR_AntaresFolder.Archived, 0) = 0
    //                  AND CQR_AntaresFolder.UniqueNumber = CA.UniqueNumber
    //            ) AS CQR_Header
    //            WHERE 1=1 ";

    //    var parameters = new DynamicParameters();
    //    parameters.Add("@BaseProject", dto.BaseProject);

    //    if (!string.IsNullOrEmpty(dto.CurrentList))
    //        strSql += $" AND CA.UniqueNumber NOT IN (0{dto.CurrentList})";

    //    if (dto.UniqueId.HasValue)
    //    {
    //        strSql += " AND CA.UniqueNumber = @UniqueId";
    //        parameters.Add("@UniqueId", dto.UniqueId);
    //    }

    //    if (!string.IsNullOrEmpty(dto.OEMGroup))
    //    {
    //        strSql += " AND CA.OEMGroup = @OEMGroup";
    //        parameters.Add("@OEMGroup", dto.OEMGroup);
    //    }

    //    if (!string.IsNullOrEmpty(dto.OEMCustomer))
    //    {
    //        strSql += " AND CA.OEM = @OEMCustomer";
    //        parameters.Add("@OEMCustomer", dto.OEMCustomer);
    //    }

    //    if (!string.IsNullOrEmpty(dto.Platform))
    //    {
    //        strSql += " AND CA.Platform = @Platform";
    //        parameters.Add("@Platform", dto.Platform);
    //    }

    //    if (!string.IsNullOrEmpty(dto.Nameplate))
    //    {
    //        strSql += " AND CA.Nameplate = @Nameplate";
    //        parameters.Add("@Nameplate", dto.Nameplate);
    //    }

    //    if (!string.IsNullOrEmpty(dto.ProductId))
    //    {
    //        strSql += " AND CA.ProductId = @ProductId";
    //        parameters.Add("@ProductId", dto.ProductId);
    //    }

    //    var result = _connection.Query(strSql, parameters)
    //                           .Select(row => (IDictionary<string, object>)row)
    //                           .Select(dict => dict.ToDictionary(kvp => kvp.Key, kvp => kvp.Value))
    //                           .ToList();

    //    return result;
    //}

    public Task<int> InsertAsync(CQR_GDPIMPhase cqrIhs)
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<CQR_GDPIMPhase>> GetByAllAsync()
    {
        throw new NotImplementedException();
    }

    public async Task<IEnumerable<string>> GetDisplayName(string fieldName)
    {
        // Validate fieldName to prevent SQL injection or runtime errors
        var validProperties = typeof(CQR_IHS).GetProperties()
            .Where(p => p.PropertyType == typeof(string))
            .Select(p => p.Name)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        if (!validProperties.Contains(fieldName))
            throw new ArgumentException($"Invalid field name: {fieldName}");

        // Build the query dynamically using LINQ and reflection
        var query = _context.Set<CQR_IHS>()
            .AsQueryable()
            .Select(e => EF.Property<string>(e, fieldName))
            .Where(v => v != null)
            .Distinct()
            .OrderBy(v => v);

        var result = await query.ToListAsync();
        result.Insert(0, ""); // Add blank item at the top, as in the original code
        return result;
        //throw new NotImplementedException();
    }

    //public Task<IEnumerable<CQR_IHS>> GetByQueuekey(int queuekey)
    //{
    //    return await _context.Set<CQR_IHS>().Where(s=>s.que();
    //    throw new NotImplementedException();
    //}

    //public Task<CQR_GDPIMPhase> GetByQueueKeyAsync(int queueKey)
    //{
    //    //throw new NotImplementedException();
    //    var result = _context.Set<CQR_IHS>().Where(x => x.QueueKey == queueKey).ToList();

    //    return result;
    //}

}
