using CQR.Application.Services;
using CQR.Domain.IServices;
using Microsoft.AspNetCore.Mvc;

namespace APS.Host.WebApi.Controllers;

public class SapController : ControllerBase
{
    private readonly ISapConnectionService _sapConnectionService;
    readonly ISapRFCService _sapRFCService;

    public SapController(ISapRFCService sapRFCService, ISapConnectionService sapConnectionService)
    {
        _sapRFCService = sapRFCService;
        _sapConnectionService = sapConnectionService;
    }

    [HttpGet("callrfc")]
    public IActionResult CallRfc()
    {
        try
        {
            var destination = _sapConnectionService.GetDestination();
            var repository = destination.Repository;
            var rfcFunction = repository.CreateFunction("RFC_FUNCTION_NAME");

            // 设置输入参数
            rfcFunction.SetValue("PARAM_NAME", "PARAM_VALUE");

            // 调用 RFC 函数
            rfcFunction.Invoke(destination);

            // 读取输出参数
            var result = rfcFunction.GetString("OUTPUT_PARAM_NAME");
            return Ok(result);
        }
        catch (RfcBaseException ex)
        {
            return StatusCode(500, $"SAP RFC 调用出错: {ex.Message}");
        }
    }

    // [HttpGet]
    // public ApiResult GetPIRInfo(string date, string time)
    // {
    //     ApiResult apiResult = new ApiResult();
    //     var _data = _sapRFCService.GetPIRData( date,  time);
    //     apiResult.IsSuccess = true;
    //     apiResult.Code = 200;
    //     apiResult.Message = "获取最新PIR成功.";
    //     apiResult.Data = _data;
    //     return apiResult;
    // }

    // [HttpGet]
    // public ApiResult GetMaterialInfo()
    // {
    //     return _sapRFCService.GetMaterialData();
    // }

    // [HttpGet]
    // public ApiResult GetHCResourceInfo(string startDateTime, string endDateTime, string costCenter)
    // {
    //     ApiResult apiResult = new ApiResult();
    //     var _data = _sapRFCService.GetEmployeeResource(startDateTime, endDateTime, costCenter);
    //     apiResult.IsSuccess = true;
    //     apiResult.Code = 200;
    //     apiResult.Message = "获取最新HC Resource成功.";
    //     apiResult.Data = _data;
    //     return apiResult;
    // }

    // [HttpPost]
    // public ApiResult GetPlanOrder(ReleaseToSapRFCModel releaseToSapRFCModel)
    // {
    //     return _sapRFCService.ReleaseToSap(releaseToSapRFCModel);
    // }

    // [HttpPost]
    // public ApiResult SendScheduleInfoToSap(List<SapBomModel> sapBomModels)
    // {
    //     ApiResult apiResult = new ApiResult();
    //     var _result = _sapRFCService.SendScheduleLineInfoToSapBom(sapBomModels);
    //     if (_result.IsSuccess)
    //     {
    //         apiResult.IsSuccess = true;
    //         apiResult.Code = 200;
    //         apiResult.Message = "将排程信息发送至SAP成功.";
    //     }
    //     else
    //     {
    //         apiResult.Code = 400;
    //         apiResult.Message = "将排程信息发送至SAP失败.";
    //         apiResult.IsSuccess = false;
    //         apiResult.Data = null;
    //         return apiResult;
    //     }
    //     return apiResult;
    // }

    // [HttpGet]
    // public ApiResult GetSapBomInfo()
    // {
    //     ApiResult apiResult = new ApiResult();
    //     var _data = _sapRFCService.GetSapBomDataFromSap();
    //     if (_data.Rows.Count == 0)
    //     {
    //         apiResult.Code = 400;
    //         apiResult.Message = "获取SAP BOM数据失败.";
    //         apiResult.IsSuccess = false;
    //         apiResult.Data = null;
    //         return apiResult;
    //     }

    //     apiResult.IsSuccess = true;
    //     apiResult.Code = 200;
    //     apiResult.Message = "获取SAP BOM数据成功.";
    //     apiResult.Data = _data;
    //     return apiResult;
    // }

    // [HttpPost]
    // public ApiResult GetSapInStorageInfo(SapInStorageInModel inStorage)
    // {
    //     return _sapRFCService.GetSapInStorageData(inStorage);
    // }
}
