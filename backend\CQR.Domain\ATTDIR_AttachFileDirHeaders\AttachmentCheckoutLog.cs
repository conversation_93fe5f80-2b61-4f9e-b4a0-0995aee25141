﻿namespace CQR.Domain.ATTDIR_AttachFileDirHeaders;

public class AttachmentCheckoutLog
{
    // 資料庫主鍵（Identity，自動產生）
    public int AttachmentCheckoutId { get; private set; }

    // 非主鍵，但可能與其它 Entity 關聯（ex: AttachFileDirHeader.QueueKey）
    public int? QueueKey { get; private set; }

    public DateTime? CheckoutDate { get; private set; }
    public DateTime? CheckinDate { get; private set; }
    public string CheckoutUser { get; private set; }
    public string FolderStatus { get; private set; }

    // EF Core 使用的空建構子
    private AttachmentCheckoutLog() { }

    // 建構用建構子
    public AttachmentCheckoutLog(
        int? queueKey,
        DateTime? checkoutDate,
        DateTime? checkinDate,
        string checkoutUser,
        string folderStatus
    )
    {
        QueueKey = queueKey;
        CheckoutDate = checkoutDate;
        CheckinDate = checkinDate;
        CheckoutUser = checkoutUser;
        FolderStatus = folderStatus;
    }

    // 可以新增 domain 行為，例如完成 Checkin 的方法
    public void Checkin(DateTime checkinTime)
    {
        CheckinDate = checkinTime;
    }

}