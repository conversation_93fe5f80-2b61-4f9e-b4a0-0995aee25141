using CQR.Application.Dto.CQRHead;
using CQR.Application.Services;
using CQR.Application.UseCases.CQR_Headers.Commands;
using CQRAPI.Controllers;
using CQRLIB.CQR_Headers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace CQR.Application.Tests.Controllers;

public class CQRHeaderControllerTests
{
    private readonly Mock<IApplicationServices> _mockApplicationServices;
    private readonly Mock<ICQRQueryService> _mockQueryService;
    private readonly Mock<IMediator> _mockMediator;
    private readonly CQRHeaderController _controller;

    public CQRHeaderControllerTests()
    {
        _mockApplicationServices = new Mock<IApplicationServices>();
        _mockQueryService = new Mock<ICQRQueryService>();
        _mockMediator = new Mock<IMediator>();

        _mockApplicationServices.Setup(x => x.QueryService).Returns(_mockQueryService.Object);

        _controller = new CQRHeaderController(_mockApplicationServices.Object, _mockMediator.Object);
    }

    [Fact]
    public async Task GetCQRCollection_ShouldReturnOkResult_WhenValidQueueKey()
    {
        // Arrange
        var queueKey = 12345;
        var expectedCollection = new CQRHeaderCollection
        {
            iCQRHeader = new CQR_Header { QueueKey = queueKey }
        };

        _mockQueryService.Setup(x => x.GetCollectionAsync(queueKey))
            .ReturnsAsync(expectedCollection);

        // Act
        var result = await _controller.getCQRCoillection(queueKey);

        // Assert
        var okResult = Assert.IsType<ActionResult<CQRHeaderCollection>>(result);
        var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
        var returnValue = Assert.IsType<CQRHeaderCollection>(okObjectResult.Value);
        
        Assert.Equal(expectedCollection, returnValue);
        Assert.Equal(queueKey, returnValue.iCQRHeader.QueueKey);

        _mockQueryService.Verify(x => x.GetCollectionAsync(queueKey), Times.Once);
    }

    [Fact]
    public async Task GetCQRCollection_ShouldReturnOkResult_WhenCollectionIsNull()
    {
        // Arrange
        var queueKey = 99999;

        _mockQueryService.Setup(x => x.GetCollectionAsync(queueKey))
            .ReturnsAsync((CQRHeaderCollection)null);

        // Act
        var result = await _controller.getCQRCoillection(queueKey);

        // Assert
        var okResult = Assert.IsType<ActionResult<CQRHeaderCollection>>(result);
        var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
        
        Assert.Null(okObjectResult.Value);
        _mockQueryService.Verify(x => x.GetCollectionAsync(queueKey), Times.Once);
    }

    [Fact]
    public async Task GetCQRHeaderById_ShouldReturnOkResult_WhenValidQueueKey()
    {
        // Arrange
        var queueKey = 12345;
        var expectedHeader = new CQR_Header 
        { 
            QueueKey = queueKey,
            CQRStatus = "Active"
        };

        _mockQueryService.Setup(x => x.GetHeaderByKeyAsync(queueKey))
            .ReturnsAsync(expectedHeader);

        // Act
        var result = await _controller.getCQRHeaderById(queueKey);

        // Assert
        var okResult = Assert.IsType<ActionResult<CQR_Header?>>(result);
        var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
        var returnValue = Assert.IsType<CQR_Header>(okObjectResult.Value);
        
        Assert.Equal(expectedHeader, returnValue);
        Assert.Equal(queueKey, returnValue.QueueKey);
        Assert.Equal("Active", returnValue.CQRStatus);

        _mockQueryService.Verify(x => x.GetHeaderByKeyAsync(queueKey), Times.Once);
    }

    [Fact]
    public async Task GetCQRHeaderById_ShouldReturnOkResult_WhenHeaderNotFound()
    {
        // Arrange
        var queueKey = 99999;

        _mockQueryService.Setup(x => x.GetHeaderByKeyAsync(queueKey))
            .ReturnsAsync((CQR_Header)null);

        // Act
        var result = await _controller.getCQRHeaderById(queueKey);

        // Assert
        var okResult = Assert.IsType<ActionResult<CQR_Header?>>(result);
        var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
        
        Assert.Null(okObjectResult.Value);
        _mockQueryService.Verify(x => x.GetHeaderByKeyAsync(queueKey), Times.Once);
    }

    [Fact]
    public async Task SaveCQRHeaderCollection_ShouldReturnOkResult_WhenValidData()
    {
        // Arrange
        var cqrHeaderDto = new CQRHeaderCollection
        {
            iCQRHeader = new CQR_Header { QueueKey = 12345 }
        };

        _mockMediator.Setup(x => x.Send(It.IsAny<SaveCQRHeaderCollectionCommand>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.SaveCQRHeaderCollection(cqrHeaderDto);

        // Assert
        var okResult = Assert.IsType<ActionResult<bool>>(result);
        var okObjectResult = Assert.IsType<OkObjectResult>(okResult.Result);
        var returnValue = Assert.IsType<bool>(okObjectResult.Value);
        
        Assert.True(returnValue);
        _mockMediator.Verify(x => x.Send(It.IsAny<SaveCQRHeaderCollectionCommand>(), default), Times.Once);
    }

    [Fact]
    public async Task SaveCQRHeaderCollection_ShouldReturnBadRequest_WhenDataIsNull()
    {
        // Act
        var result = await _controller.SaveCQRHeaderCollection(null);

        // Assert
        var badRequestResult = Assert.IsType<ActionResult<bool>>(result);
        var badRequestObjectResult = Assert.IsType<BadRequestObjectResult>(badRequestResult.Result);
        
        Assert.Equal("CQR Header data is required", badRequestObjectResult.Value);
        _mockMediator.Verify(x => x.Send(It.IsAny<SaveCQRHeaderCollectionCommand>(), default), Times.Never);
    }

    [Fact]
    public async Task SaveCQRHeaderCollection_ShouldReturnBadRequest_WhenModelStateIsInvalid()
    {
        // Arrange
        var cqrHeaderDto = new CQRHeaderCollection();
        _controller.ModelState.AddModelError("TestField", "Test error message");

        // Act
        var result = await _controller.SaveCQRHeaderCollection(cqrHeaderDto);

        // Assert
        var badRequestResult = Assert.IsType<ActionResult<bool>>(result);
        var badRequestObjectResult = Assert.IsType<BadRequestObjectResult>(badRequestResult.Result);
        
        Assert.IsType<SerializableError>(badRequestObjectResult.Value);
        _mockMediator.Verify(x => x.Send(It.IsAny<SaveCQRHeaderCollectionCommand>(), default), Times.Never);
    }

    [Theory]
    [InlineData(1)]
    [InlineData(12345)]
    [InlineData(99999)]
    public async Task GetCQRCollection_ShouldCallQueryService_WithCorrectQueueKey(int queueKey)
    {
        // Arrange
        var expectedCollection = new CQRHeaderCollection();
        _mockQueryService.Setup(x => x.GetCollectionAsync(queueKey))
            .ReturnsAsync(expectedCollection);

        // Act
        await _controller.getCQRCoillection(queueKey);

        // Assert
        _mockQueryService.Verify(x => x.GetCollectionAsync(queueKey), Times.Once);
    }

    [Theory]
    [InlineData(1)]
    [InlineData(12345)]
    [InlineData(99999)]
    public async Task GetCQRHeaderById_ShouldCallQueryService_WithCorrectQueueKey(int queueKey)
    {
        // Arrange
        var expectedHeader = new CQR_Header { QueueKey = queueKey };
        _mockQueryService.Setup(x => x.GetHeaderByKeyAsync(queueKey))
            .ReturnsAsync(expectedHeader);

        // Act
        await _controller.getCQRHeaderById(queueKey);

        // Assert
        _mockQueryService.Verify(x => x.GetHeaderByKeyAsync(queueKey), Times.Once);
    }
}