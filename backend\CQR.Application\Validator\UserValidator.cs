using CQRLIB.DTOs;
using FluentValidation;

namespace CQRLIB.Validators
{
    public class UserValidator : AbstractValidator<UserDto>
    {
        public UserValidator()
        {
            RuleFor(user => user.email);
            // .Empty().WithMessage("ID_User should not be provided during creation.")
            // .When(user => user.ID_User != null);  // Validate only if ID_User is explicitly provided

            // //  RuleFor(user => user.ID_User).NotEmpty().NotNull().WithMessage("ID_User is required.");
            // RuleFor(user => user.User_Email).NotEmpty().EmailAddress().WithMessage("Invalid email address.");
            // RuleFor(user => user.User_FirstName).NotEmpty().WithMessage("FirstName is required.");
            // RuleFor(user => user.User_LastName).NotEmpty().WithMessage("LastName is required.");

            // RuleFor(user => user.IS_Active).NotNull().NotEmpty().WithMessage("IS_Active is required.");

            // Add more rules as needed
        }
    }
}


