﻿namespace CQR.Application.Helpers;

using System;

public  class CommentHelper
{
    public static (string asOld, string asNew, string asOrg) SplitComments(string sComment, bool bSuperUser)
    {
        string asOld = "";
        string asNew = "";
        string asOrg = sComment;  // 永遠保留原始註解

        if (bSuperUser)
        {
            asNew = sComment;
            return (asOld, asNew, asOrg);
        }
        sComment = sComment ?? string.Empty;

        string tag = "[Comments Held]\r\n";
        int index = sComment.IndexOf(tag);

        if (index == -1)
        {
            asOld = sComment;
            asNew = "";

            if (!string.IsNullOrEmpty(asOld) &&
               (!asOld.EndsWith("\r\n") || !asOld.Contains("]")))
            {
                asOld += "\r\n[Unknown Date]   Unknown User\r\n\r\n";
            }
        }
        else
        {
            asOld = sComment.Substring(0, index);
            asNew = sComment.Substring(index + tag.Length);
        }
        return (asOld, asNew, asOrg);
        //return Tuple
    }

    public static string CommentStamp(string asOld, ref string asNew, ref string asOrg, bool abReleasing, string asUserId, bool bSuperUser)
    {
        string result;

        if (string.Compare(asUserId?.ToUpper(), "SUPER", StringComparison.OrdinalIgnoreCase) == 0 || bSuperUser)
        {
            // Get actual user from session if available
            //if (HttpContext.Current?.Session?["UserActual"] != null)
            //{
            //    asUserId = HttpContext.Current.Session["UserActual"].ToString();
            //}

            if (string.Compare(asNew, asOrg, StringComparison.OrdinalIgnoreCase) == 0)
            {
                result = asNew;
                return result;
            }
        }

        string text;
        if (bSuperUser)
        {
            text = asNew?.Trim() ?? "";
        }
        else
        {
            text = asOld?.Trim() ?? "";
            if (!string.IsNullOrEmpty(asNew?.Trim()))
            {
                if (!abReleasing)
                {
                    text += "[Comments Held]\r\n";
                }
                text += asNew.Trim();
                if (abReleasing)
                {
                    text += CommentStampText(asUserId);
                }
            }
        }

        result = text;
        asOrg = text;
        return result;
    }

    private static string CommentStampText(string userId)
    {
        // Implement your comment stamp text logic here
        // This typically adds timestamp and user info to comments
        return $" [{userId} - {DateTime.Now:MM/dd/yyyy HH:mm}]";
    }

}
