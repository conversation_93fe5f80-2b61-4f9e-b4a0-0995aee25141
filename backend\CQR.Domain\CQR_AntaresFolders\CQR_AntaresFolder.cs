﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace CQR.Domain.CQR_AntaresFolders;


[Table("CQR_AntaresFolder")]
public class CQRAntaresFolder
{
    [Key]
    [Column("CQRAntaresFolderId")]
    public int CQRAntaresFolderIdValue { get; set; }

    [Required]
    public int QueueKey { get; set; }

    public int? UniqueNumber { get; set; }

    [Required]
    public bool Archived { get; set; }

    public int? ArchivedByQueueKey { get; set; }

    [StringLength(50)]
    public string ArchivedByUser { get; set; }

    public DateTime? ArchivedDate { get; set; }

    [Required]
    public DateTime DateAdded { get; set; }

    public DateTime? DateUpdated { get; set; }

    public int? VB_ID { get; set; }

    [StringLength(100)]
    public string OEMGroup { get; set; }

    [StringLength(100)]
    public string OEM { get; set; }

    [StringLength(100)]
    public string Platform { get; set; }

    [StringLength(100)]
    public string Program { get; set; }

    [StringLength(100)]
    public string Nameplate { get; set; }

    [StringLength(100)]
    public string Country { get; set; }

    [StringLength(100)]
    public string Region { get; set; }

    [StringLength(50)]
    public string NewBusinessCategory { get; set; }

    [StringLength(50)]
    public string Status { get; set; }

    [StringLength(50)]
    public string ProductId { get; set; }

    [StringLength(500)]
    public string ProductDescription { get; set; }

    [StringLength(200)]
    public string ProductGrouping { get; set; }

    public int? SOP { get; set; }

    public int? EOP { get; set; }

    [StringLength(100)]
    public string SoldFrom { get; set; }

    [StringLength(100)]
    public string FinalAssembly { get; set; }

    [StringLength(50)]
    public string CQRNumber { get; set; }

    [StringLength(50)]
    public string AwardingQuarter { get; set; }

    // Navigation properties (if you have related entities)
    // Example: If QueueKey references CQRHeader
    // [ForeignKey("QueueKey")]
    // public virtual CQRHeader CQRHeader { get; set; }

    // Constructor
    public CQRAntaresFolder()
    {
        DateAdded = DateTime.Now;
        Archived = false;
    }
}
