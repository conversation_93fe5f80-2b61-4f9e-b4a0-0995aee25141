
using CQR.Domain.Entities;

namespace CQR.Domainore.Services;
public interface IProductService
{
    //Product
    //Task CreateProductWithDetails(ProductWithDetailsDto productWithDetailsDto);
    Task<int> GetProductCountAsync();
    Task AddProductAsync(Product product);
    Task UpdateProductAsync(Product product);
    Task<Product> GetProductByIdAsync(int productId);
    Task<IEnumerable<Product>> GetProductsAsync();

    Task DeleteProductAsync(int productId);

    //ProductDetial
    Task RemoveProductWithDetails(int productId);

}