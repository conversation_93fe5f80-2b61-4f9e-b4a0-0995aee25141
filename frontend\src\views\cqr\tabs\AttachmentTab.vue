<template>
  <el-container style="padding: 20px">
    <el-header>
      <h2>Attachment</h2>
    </el-header>

    <el-main>
      <div>
        <!-- Forms Button -->
        <el-button type="primary" @click="openAdvancedAttachment"
          >Forms</el-button
        >

        <!-- Attachments -->
        <div style="display: flex; margin-top: 20px">
          <h3>Attachments:</h3>
          <el-link
            type="primary"
            style="margin-left: 50px"
            @click="addAttachment"
            >add</el-link
          >
        </div>

        <el-table
          :data="AttachmentData"
          style="width: 100%; margin-top: 10px"
          border
        >
          <el-table-column prop="attachTitle" label="Source File" />
          <el-table-column prop="checkoutUser" label="Creator" />
          <el-table-column prop="createdDate" label="Last Updated" />
          <el-table-column label="View">
            <template #default="scope">
              <el-link @click="viewFile(scope.row)">view</el-link>
            </template>
          </el-table-column>
          <el-table-column label="Delete">
            <template #default="scope">
              <el-link @click="attachmentDelete(scope.row)">del</el-link>
            </template>
          </el-table-column>
        </el-table>

        <!-- Tasklist -->
        <h3 style="margin-top: 30px">Tasklist:</h3>

        <el-table
          :data="TasklistAttachFileData"
          style="width: 100%; margin-top: 10px"
          border
        >
          <el-table-column prop="attachTitle" label="Source File" />
          <el-table-column prop="checkoutUser" label="Creator" />
          <el-table-column prop="createdDate" label="Last Updated" />
          <el-table-column label="Comments">
            <template #default="scope">
              <el-button @click="toggleComment(scope.$index)"
                >Comments</el-button
              >
              <!-- <el-inputnt"
                v-model="scope.row.commen
                v-if="scope.row.showCommet"
                type="textarea"
                rows="2"
                style="margin-top: 5px"
              /> -->
            </template>
          </el-table-column>
          <el-table-column label="View">
            <template #default="scope">
              <el-link :href="scope.row.viewLink" target="_blank">view</el-link>
            </template>
          </el-table-column>
          <el-table-column label="Check Out">
            <template #default="scope">
              <el-link
                v-if="!scope.row.oldRevision"
                @click="checkout(scope.row)"
                >check out</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-main>
  </el-container>
  <el-dialog v-model="dialogVisible">
    <span>Please select a form:</span>
    <el-select
      v-model="selectedModel"
      placeholder="Select"
      style="width: 240px"
      @change="selectModel"
    >
      <el-option
        v-for="item in ModelList"
        :key="item"
        :label="item"
        :value="item"
      />
    </el-select>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="downloadModel"> OK </el-button>
        <el-button @click="dialogVisible = false">Cancel</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="dialogUploadVisible">
    <span>Attachment File:</span>
    <el-upload
      v-model:file-list="fileList"
      action="/Attachment/uploadAttachment"
      :auto-upload="false"
    >
      <el-button type="primary">Click to upload</el-button>
      <template #tip>
        <div class="el-upload__tip">
          jpg/png files with a size less than 500kb
        </div>
      </template>
    </el-upload>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="uploadAttachment"> OK </el-button>
        <el-button @click="dialogUploadVisible = false">Cancel</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import {
  ElMessageBox,
  ElMessage,
  UploadUserFile,
  UploadProps
} from "element-plus";
import { useFormModelStore } from "@/store/modules/formModel";
import { AttachFileInfo } from "../../../store/types";
import { getAttachments, getModelList } from "@/api/cqr";
import { isNull } from "@/utils/checkUtil";
import axios from "axios";
const formStore = useFormModelStore();
const AttachmentData = ref<AttachFileInfo[]>([]);
const TasklistAttachFileData = ref<AttachFileInfo[]>([]);
const dialogVisible = ref(false);
const dialogUploadVisible = ref(false);
const ModelList = ref<String[]>([]);
const selectedModel = ref<String>();
const fileList = ref<UploadUserFile>();

onMounted(async () => {
  initData();
});

watch(() => formStore.modelRoutingHeader);

const initData = () => {
  getAttachmentInfo();
  getTaskInfo();
  getModelInfo();
};

const getAttachmentInfo = async () => {
  const attachmentResponse = await getAttachments("FR", 19604);
  AttachmentData.value = [...attachmentResponse];
};

const selectModel = (value: String) => {
  selectedModel.value = value;
};

const getModelInfo = async () => {
  const modelResponse = await getModelList("FORMFR");
  ModelList.value = [...modelResponse];
};

const getTaskInfo = async () => {
  const taskListAttachResponse = await getAttachments("FT", 19604);
  TasklistAttachFileData.value = [...taskListAttachResponse];
};

const openAdvancedAttachment = () => {
  dialogVisible.value = true;
};

const addAttachment = () => {
  dialogUploadVisible.value = true;
};

const uploadAttachment = () => {
  const formData = new FormData();
  for (let i = 0; i < fileList.value.length; i++) {
    formData.append("files", fileList.value[i].raw);
  }
  axios.post("/api/Attachment/uploadAttachment", formData).then(res => {
    if (res.data.success) {
      ElMessage({
        message: "Upload successfully",
        type: "success"
      });
    } else {
      ElMessage({
        message: "upload failed",
        type: "error"
      });
    }
    dialogUploadVisible.value = false;
  });
};

const viewFile = (row: any) => {
  downLoadFile(row.attachTitle);
};

const downLoadFile = async (fileName: string) => {
  try {
    const response = await axios.get(
      "/api/Attachment/viewAttachment?sSrcFile=" +
        fileName +
        "&bCreateOnSave=true",
      {
        responseType: "blob"
      }
    );
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage({
      message: "download failed",
      type: "error"
    });
  }
};

const downloadModel = () => {
  if (isNull(selectedModel.value)) {
    ElMessage({
      message: "No documents selected",
      type: "error"
    });
    return;
  }
  // 文件路径相对于 public 目录
  const fileUrl = "/public/Master/" + selectedModel.value; // 注意：public 目录下的文件路径以 / 开头
  // 创建隐藏的 <a> 标签触发下载
  const link = document.createElement("a");
  link.href = fileUrl;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  dialogVisible.value = false;
};

const attachmentDelete = (row: any) => {
  console.info(row.attachTitle + "-" + row.queueKey);
};

const toggleComment = (row: any) => {
  row.showComment = !row.showComment;
};

const checkOut = (row: any) => {
  alert(`Check out file: ${row.filename}`);
};
</script>

<style lang="scss" scoped></style>
