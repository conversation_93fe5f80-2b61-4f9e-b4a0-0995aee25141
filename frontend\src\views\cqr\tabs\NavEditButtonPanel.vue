<template>
  <div class="nav-button-row">
    <el-button
      v-for="button in buttons"
      v-show="button.visible"
      :id="button.id"
      :key="button.action"
      class="navLink"
      :disabled="!props.enabledButtons.includes(button.action)"
      plain
      @click="onClick(button.action)"
    >
      {{ button.label }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
const props = defineProps({
  enabledButtons: {
    type: Array,
    default: () => []
  },
  showVoid: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["action"]);

const buttons = computed(() => [
  { id: "lnkSave", label: "Save", action: "save", visible: true },
  {
    id: "lnkSaveExit",
    label: "Save & Exit",
    action: "saveexit",
    visible: true
  },
  { id: "lnkRelease", label: "Release", action: "release", visible: true },
  { id: "lnkApprove", label: "Approve", action: "approve", visible: true },
  { id: "lnkReject", label: "Reject", action: "reject", visible: true },
  {
    id: "lnkTerminate",
    label: "Terminate",
    action: "terminate",
    visible: true
  },
  {
    id: "lnkNCTVoid",
    label: "Close per Voided NCT",
    action: "void",
    visible: props.showVoid
  },
  { id: "lnkPrint", label: "Print", action: "print", visible: true },
  { id: "lnkCancel", label: "Cancel", action: "cancel", visible: true }
]);

function onClick(action) {
  emit("action", action);
}
</script>

<style scoped>
.nav-button-row {
  display: flex;
  flex-wrap: nowrap;
  gap: 5px; /* 按鈕之間的間距 */
  overflow-x: auto; /* 若過長可橫向捲動 */
  padding: 2px 0;
  background-color: rgb(203, 243, 230) e;
}
.navLink {
  flex-shrink: 0;
  color: green;
}
</style>
