﻿using CQR.Domain.CQRIHSFolder;
using Microsoft.EntityFrameworkCore;

namespace CQR.Persistence.Command.Repositories;

public class CQRIHSFolderRepository : EntityRepository<CQR_IHSFolder, int>, ICQRIHSFolderRepository
{
    public CQRIHSFolderRepository(CQRDbContext context) : base(context) { }

    //public Task<IEnumerable<CQR_IHSFolder>> GetByQueuekey(int queuekey)
    //{
    //    throw new NotImplementedException();
    //}

    //public CQRIHSFolderRepository(CQRDbContext context) : base(context)
    //{
    //}

    //public Task<CQR_IHSFolder> AddAsync(CQR_IHSFolder entity, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<IEnumerable<CQR_IHSFolder>> AddRangeAsync(IEnumerable<CQR_IHSFolder> entities, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<int> CountAsync(Expression<Func<CQR_IHSFolder, bool>> predicate = null, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task DeleteAsync(CQR_IHSFolder entity, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task DeleteAsync(int id, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task DeleteRangeAsync(IEnumerable<CQR_IHSFolder> entities, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<bool> ExistsAsync(Expression<Func<CQR_IHSFolder, bool>> predicate, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<IEnumerable<CQR_IHSFolder>> FindAsync(Expression<Func<CQR_IHSFolder, bool>> predicate, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<CQR_IHSFolder> FindSingleAsync(Expression<Func<CQR_IHSFolder, bool>> predicate, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<IEnumerable<CQR_IHSFolder>> GetAllAsync(CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<CQR_IHSFolder> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<IEnumerable<CQR_IHSFolder>> GetByQueuekey(int queuekey)
    //{
    //    throw new NotImplementedException();
    //}

    //public async Task<IEnumerable<CQR_IHSFolder>> GetByQueueKeyAsync(int queueKey, CancellationToken cancellationToken = default)
    //{
    //    return await _context.CQR_IHSFolder.Where(s => s.QueueKey == queueKey).ToListAsync();
    //}

    public async Task<IEnumerable<CQR_IHSFolder>> GetByQueueKeyAsync(int queueKey)
    {
        //throw new NotImplementedException();
        return await _context.CQR_IHSFolder.Where(s => s.QueueKey == queueKey).ToListAsync();
    }

    public Task<IEnumerable<CQR_IHSFolder>> GetByUniqueNumberAsync(int uniqueNumber)
    {
        throw new NotImplementedException();
    }

    //public Task<IEnumerable<CQR_IHSFolder>> GetByQueueKeyAsync(int queueKey)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<IEnumerable<CQR_IHSFolder>> GetByUniqueNumberAsync(int uniqueNumber, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task<IEnumerable<CQR_IHSFolder>> GetByUniqueNumberAsync(int uniqueNumber)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task UpdateAsync(CQR_IHSFolder entity, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}

    //public Task UpdateRangeAsync(IEnumerable<CQR_IHSFolder> entities, CancellationToken cancellationToken = default)
    //{
    //    throw new NotImplementedException();
    //}
}
