﻿using CQR.Domain.CQR_GDPIMPhases;
using CQR.Domain.CQR_IHSs;
using CQR.Domain.CQRHeaders;
using CQR.Domain.CQRIHSFolder;
using CQR.Domain.ROUTING_RGHeaders;

namespace CQR.Application.Repositories;

public interface IRepositoryCollection
{
    ICQRIHSRepository CQRIHSRepository { get; }
    ICQRHeaderRepository CQRHeaderRepository { get; }
    ICQRIHSFolderRepository CQRIHSFolderRepository { get; }
    IRoutingRGHeaderRepository RoutingRGHeaderRepository { get; }
    IAttachFileQueryRepository AttachFileQueryRepository { get; }
    ICQRGDPIMPhasesRepository CQRGDPIMPhasesRepository { get; }
    ICqrHeaderQueryRepository CqrHeaderQueryRepository { get; }
    IUserRoleQueryRepository UserRoleQueryRepository { get; }
    ITRSHeaderQueryRespository TRSHeaderQueryRepository { get; }
    ICQRAntaresQueryRepository CQRAntaresQueryRepository { get; }
}

