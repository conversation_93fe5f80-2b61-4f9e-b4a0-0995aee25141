//using CQRLIB.Models;
//using Microsoft.EntityFrameworkCore;

//namespace CQRLIB
//{
//    public class CQrDbContext : DbContext
//    {
//        // public DbSet<Product> P…roducts { get; set; }

//        public CQrDbContext(DbContextOptions<CQrDbContext> options) : base(options)
//        {
//            //  OptionsBuilder.UseSqlServer("DefaultConnection");
//        }
//        public DbSet<Product> Product { get; set; } // 确保包含此行
//        public DbSet<ProductDetail> ProductDetail { get; set; }

//        // 可以在此配置其他实体映射
//        protected override void OnModelCreating(ModelBuilder modelBuilder)
//        {
//            base.OnModelCreating(modelBuilder);
//            // 可以添加自定义配置
//        }

//        //     modelBuilder.Entity<Product>()
//        //         .Property(p => p.Price)
//        //         .HasColumnType("decimal(18,2)");
//        // }
//        // protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
//        // {
//        //     optionsBuilder.UseSqlServer("DefaultConnection");
//        // }
//    }
//}
