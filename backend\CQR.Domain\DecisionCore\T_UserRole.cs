﻿namespace CQR.Domain.DecisionCore;

// CQR, Version=1.0.9223.23847, Culture=neutral, PublicKeyToken=null
// CQR.clsCQR.T_UserRole
public class T_UserRole
{
    public bool bIsSAFR;

    public bool bIsAMgr;

    public bool bIsCost;

    public bool bIsPA;

    public bool bIsFran;

    public bool bIsFr00;

    public bool bIsMBnE;

    public bool bIsCQGWNFY;

    public bool bIsPetm;

    public bool bIsPgm;

    public bool bIsPrd;

    public bool bIsPsm;

    public bool bIsPDM;

    public bool bIsAME_Coord;

    public bool bIsPUR_Coord;

    public bool bIsPsr;

    public bool bIsSDir;

    public bool bIsBum;

    public bool bIsFin;

    public bool bIsPres;

    public bool bIsUkGm;

    public bool bIsUkDm;

    public bool bIsBeb;

    public bool bIsCmgr;

    public bool bIsMEPL;

    public bool bIsAIME;

    public bool bIsAIPR;

    public bool bIsCTLC;

    public string sManagerFran;

    public string sManagerBids;

    public string sManagerFin;

    public string sManagerPres;

    public string sManagerBeb;

    public string sManagerCmgr;

    public bool[] bIsGateway;

    public string[] sManagerGateway;

    public bool bIsPetmOnly;

    public T_UserRole()
    {
        bIsGateway = new bool[13];
        sManagerGateway = new string[13];
    }
}

