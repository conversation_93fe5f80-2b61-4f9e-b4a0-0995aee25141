﻿using CQR.Domain.Primitives;

namespace CQR.Domain.Interfaces.Repositories;

public interface ISpecificationRepository<T, TId> where T : BaseEntity<TId>
{
    Task<T> GetBySpecAsync(ISpecification<T> spec, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> ListAsync(ISpecification<T> spec, CancellationToken cancellationToken = default);
    Task<int> CountAsync(ISpecification<T> spec, CancellationToken cancellationToken = default);
}