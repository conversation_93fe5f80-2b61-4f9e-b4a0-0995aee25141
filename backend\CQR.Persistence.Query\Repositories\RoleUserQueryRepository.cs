﻿using CQR.Application.Dto;
using CQR.Application.Repositories;
using CQR.Domain.Enum;
using CQR.Domain.Extensions;
using CQR.Persistence.Query.Base;
using Dapper;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Query.Repositories;

public class RoleUserQueryRepository : QueryBaseRepository, IRoleUserQueryRepository
{
    public RoleUserQueryRepository(IConfiguration configuration) : base(configuration) { }

    public async Task<IEnumerable<UserFullNameDto>> getOriginatorList()
    {
        var sSQL = @"SELECT distinct UU.LastName + ', ' + UU.FirstName as FullName, UU.Userid 
                     FROM USERPROF_UserProfileHeader UU 
                     INNER JOIN CQR_Header CH ON UU.UserId = CH.OriginatorId
                     ORDER BY FullName";
        var result = await _connection.QueryAsync<UserFullNameDto>(sSQL, new { });
        return result;
    }

    public async  Task<IEnumerable<UserFullNameDto>> getRoleUserListByRole(RoleEnum roleEnum)
    {
        //throw new NotImplementedException();
        //var sSQL = @"SELECT distinct UU.LastName + ', ' + UU.FirstName as FullName, UU.Userid 
        //             FROM USERPROF_UserProfileHeader UU 
        //             INNER JOIN CQR_Header CH ON UU.UserId = CH.OriginatorId
        //             ORDER BY FullName";

        // 將 enum 轉成 string
        //RoleEnum role = RoleEnum.PETMId;
        //string roleStr = role.ToRoleString(); // 結果: "PETMId"

        string roleName = roleEnum.ToRoleString();

        //var sSQL = @"SELECT distinct UU.LastName + ', ' + UU.FirstName as FullName, UU.Userid 
        //                 FROM USERPROF_UserProfileHeader UU 
        //                 INNER JOIN CQR_Header CH ON UU.UserId = CH." + roleName + @"
        //                 WHERE CH." + roleName + @" IS NOT NULL
        //                 ORDER BY FullName";

        //var result = await _connection.QueryAsync<UserFullNameDto>(sSQL);
        return await this.getRoleUserListByRoleName(roleName);
    }

    public async Task<IEnumerable<UserFullNameDto>> getRoleUserListByRoleName(string roleName)
    {
        //throw new NotImplementedException();
        var sSQL = @"SELECT distinct UU.LastName + ', ' + UU.FirstName as FullName, UU.Userid 
                         FROM USERPROF_UserProfileHeader UU 
                         INNER JOIN CQR_Header CH ON UU.UserId = CH." + roleName + @"
                         WHERE CH." + roleName + @" IS NOT NULL
                         ORDER BY FullName";

        var result = await _connection.QueryAsync<UserFullNameDto>(sSQL);
        return result;
    }

    private void ValidateRoleName(string roleName)
    {
        var validRoles = new[] { "PETMId", "PGMId", "CostEstimatorId", "AccountMgrId" };

        if (string.IsNullOrWhiteSpace(roleName))
        {
            throw new ArgumentException("角色名稱不能為空");
        }

        if (!validRoles.Contains(roleName))
        {
            throw new ArgumentException($"不支援的角色名稱: {roleName}。有效的角色名稱為: {string.Join(", ", validRoles)}");
        }
    }

}
