﻿select * from [dbo].[CQR_GDPIMPhase]

;select * from TRS_Header where table_name = 'NUMBERS'

select top 10  * from  LOG_Modification
LOG_Modification


select * from Trs

--------------------------------------------------------------
SELECT RLS.RoleCode FROM USERPROF_UserProfileHeader HDR INNER JOIN USERPROF_UserRoles RLS ON HDR.QueueKey = RLS.QueueKey WHERE HDR.UserID = 'IBERLANG'

SELECT UR.RoleCode FROM USERPROF_UserRoles UR, USERPROF_UserProfileHeader UH WHERE UR.QueueKey = UH.QueueKey AND UH.UserId = 'IBERLANG'

SELECT * FROM CQR_GDPIMPhase WHERE QueueKey = 19645 AND GDPIMPhase = 1
SELECT * FROM CQR_GDPIMPhase WHERE QueueKey = 19645 AND GDPIMPhase = 2

SELECT AH.*  , (SELECT TOP 1 CheckoutUser FROM ATTDIR_CheckoutLog CL WHERE CL.QueueKey=AH.QueueKey AND CL.CheckinDate IS NULL) AS CheckoutUser , 
(SELECT TOP 1 CheckoutDate FROM ATTDIR_CheckoutLog CD WHERE CD.QueueKey=AH.QueueKey AND CD.CheckinDate IS NULL) AS CheckoutDate FROM ATTDIR_AttachFileDirHeader AH 
WHERE FolderTypeCode = 'FR'   AND FolderNbr = '0000019645' ORDER BY QueueKey

SELECT * FROM CQR_QRUnitCost WHERE QueueKey = 19645

SELECT AH.*  , (SELECT TOP 1 CheckoutUser FROM ATTDIR_CheckoutLog CL WHERE CL.QueueKey=AH.QueueKey AND CL.CheckinDate IS NULL) AS CheckoutUser , (SELECT TOP 1 CheckoutDate FROM ATTDIR_CheckoutLog CD WHERE CD.QueueKey=AH.QueueKey AND CD.CheckinDate IS NULL) AS CheckoutDate FROM ATTDIR_AttachFileDirHeader AH WHERE FolderTypeCode = 'QS'   AND FolderNbr = '0000019645' ORDER BY QueueKey

INSERT INTO LOG_Modification (Application, SuperUser, Button, UserActual, UserImpersonate, FolderQueueKey, OtherInfo, UserComment, FolderStatus ) VALUES ('CQR','','OPEN - Create','mitchene','',-1,'','','010100FR')