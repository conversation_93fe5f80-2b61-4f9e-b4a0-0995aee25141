<template>
  <el-card>
    <el-row>
      <el-col :span="24">
        <div v-if="formData">
          <el-main>
            <el-form ref="cqrForm" :model="formData" label-position="top">
              <!-- Header Information -->
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="CQR Description">
                    <el-input
                      v-model="formData.FranDesc"
                      placeholder="IP Configurable Switch Pack - L463 New Defender MY29"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- IHS Information -->
              <IHSFolderTable
                :data="formIHSFolderDatas"
                :criteria="mapIHSFolderCriteria"
                :is-edit="isEdit"
                @selection-change="handleSelectionChange"
                @update:data="formIHSFolderDatas = $event"
              />

              <!-- Basic Project Information -->
              <el-row :gutter="20" class="mt-20">
                <el-col :span="6">
                  <el-form-item label="Model Year">
                    <!-- <el-input v-model="formData.modelYear" /> -->
                    <el-select
                      v-model="formData.modelYear"
                      placeholder="Select Model Year"
                    >
                      <el-option
                        v-for="year in mapAwardModelYear"
                        :key="year.item1"
                        :label="year.item2"
                        :value="year.item2"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="Average Volume Per Annum">
                    <el-input v-model="formData.volumePerAnnum" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="Award Quarter">
                    <el-tooltip
                      content="The quarter in which this project was awarded"
                      placement="top"
                    >
                      <el-select
                        v-model="formData.awardQuarter"
                        placeholder="Select Quarter"
                      >
                        <el-option key="Q1" label="Q1" value="Q1" />
                        <el-option key="Q2" label="Q2" value="Q2" />
                        <el-option key="Q3" label="Q3" value="Q3" />
                        <el-option key="Q4" label="Q4" value="Q4" />
                      </el-select>
                      <!-- <el-input v-model="formData.awardQuarter" /> -->
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="Award Year">
                    <!-- <el-input v-model="formData.awardYear" /> -->
                    <el-select
                      v-model="formData.awardYear"
                      placeholder="Select Year"
                    >
                      <el-option
                        v-for="year in mapAwardModelYear"
                        :key="year.item1"
                        :label="year.item2"
                        :value="year.item2"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="Product Life">
                    <el-input v-model="formData.remainingProductLife">
                      <template #append>Years</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="Approx Average Annual Value">
                    <el-input v-model="formData.approxAnnualValue">
                      <template #prepend>$</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="Calculation Currency">
                    <el-select
                      v-model="formData.currency"
                      placeholder="Select Currency"
                    >
                      <!-- <el-option key="CNY" label="CNY" value="CNY" /> -->
                      <el-option
                        v-for="item in calcCurrOptions"
                        :key="item.table_entry"
                        :label="item._desc"
                        :value="item._desc"
                      />
                    </el-select>
                    <!-- <el-input v-model="formData.calculationCurrency" /> -->
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- Comments Section -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="Volume Comments">
                    <el-tooltip
                      content="Add comments related to volume expectations"
                      placement="top"
                    >
                      <el-input
                        v-model="formData.vpaComments"
                        type="textarea"
                        :rows="4"
                        placeholder="Volume comments..."
                      />
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="Competitor Intelligence /Target Pricing">
                    <el-input
                      v-model="formData.intPricComments"
                      type="textarea"
                      :rows="4"
                      placeholder="Target pricing comments..."
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- Due Dates -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="Expected TDR Date">
                    <div class="input-with-checkbox">
                      <el-input v-model="formData.tdrDate" />
                      <!-- <el-date-picker
                    v-model="formData.custQuoteDueDate"
                    type="date"
                    disabled
                    format="DD-MMM-YYYY"
                  /> -->
                      <el-checkbox
                        v-model="formData.tdrNoInput"
                        label="No Input"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="Customer Due Date or Gate Exit Date">
                    <!-- <el-input v-model="formData.custQuoteDueDate" /> -->
                    <el-date-picker
                      v-model="formData.custQuoteDueDate"
                      type="date"
                      format="DD-MMM-YYYY"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Customer (Start of Prod.)">
                    <el-date-picker
                      v-model="formData.customerJob1"
                      type="date"
                      format="DD-MMM-YYYY"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Regional Quoting Team">
                    <el-input v-model="formData.regionalQuotingTeam" />
                    <!-- <el-select
                  v-model="formData.regionalQuotingTeam"
                  disabled
                  placeholder="Select Team"
                >
                  <el-option key="APAC" label="APAC" value="APAC" />
                </el-select> -->
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="Opening Meeting Date">
                    <!-- <el-input v-model="formData.openingMeetingDate" /> -->

                    <el-date-picker
                      v-model="formData.openingMeetingDate"
                      type="date"
                      disabled
                      format="DD-MMM-YYYY"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- Project Team Section -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="Commercial Manager">
                    <el-select
                      v-model="formData.commercialManager"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calUserRoleColleciton?.CMGR"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="Engineering Site">
                    <el-select
                      v-model="formData.engineeringSite"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calENGSITE"
                        :key="item.table_entry"
                        :label="item._desc"
                        :value="item._desc"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- Background Information -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="Background Information">
                    <el-input
                      v-model="formData.bkRndInfComments"
                      type="textarea"
                      :rows="6"
                      placeholder="Background information..."
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="Customer Productivity Targets/Green Light Targets"
                  >
                    <el-input
                      v-model="formData.custProdComments"
                      type="textarea"
                      :rows="6"
                      placeholder="Productivity targets..."
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- Obsolescence claims -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="Obsolescence claims to be generated?">
                    <el-tooltip
                      content="Indicate if obsolescence claims are expected"
                      placement="top"
                    >
                      <el-radio-group v-model="formData.obsolescenceReqdInd">
                        <el-radio label="1">Yes</el-radio>
                        <el-radio label="0">No</el-radio>
                      </el-radio-group>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <div class="section-subheader">Product Features</div>
                </el-col>
              </el-row>

              <!-- Team information -->
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="Sales Account Manager">
                    <!-- <el-input v-model="formData.accountMgrId" /> -->
                    <el-select
                      v-model="formData.accountManager"
                      disabled
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calUserRoleColleciton?.AMGR"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Sales Account Director (SDIR)">
                    <!-- <el-input v-model="formData.salesAcctDirectorId" /> -->
                    <el-select
                      v-model="formData.accountDirector"
                      disabled
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calUserRoleColleciton?.SDIR"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Communication">
                    <el-select
                      v-model="formData.communication"
                      style="width: 100%"
                    >
                      <el-option key="" label="" value="" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="Finance Coordinator">
                    <el-select
                      v-model="formData.costEstimatorId"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calUserRoleColleciton?.CQRHRCOST"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                      <!-- <el-option
                    v-for="role in userRoles"
                    :key="role.userId"
                    :label="role.fullName"
                    :value="role.userId"
                  /> -->
                      <!-- <el-option
                        key="BWANG4"
                        label="Wang, Bill"
                        value="BWANG4"
                      /> -->
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Engineering Director">
                    <el-select v-model="formData.pgmId" style="width: 100%">
                      <el-option
                        v-for="item in calUserRoleColleciton?.CQRHRPGM"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                      <!-- <el-option key="TZHOU" label="Zhou, Tim" value="TZHOU" /> -->
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="ASIL Level">
                    <el-select v-model="formData.asilLevel" style="width: 100%">
                      <el-option key="" label="" value="" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="PM Coordinator">
                    <el-tooltip
                      content="Program Manager Coordinator"
                      placement="top"
                    >
                      <el-select v-model="formData.petmId" style="width: 100%">
                        <!-- <el-option key="JXU1" label="Xu, Janet" value="JXU1" /> -->
                        <el-option
                          v-for="item in calUserRoleColleciton?.CQRHRAME"
                          :key="item.userId"
                          :label="item.userId + ', ' + item.fullName"
                          :value="item.fullName"
                        />
                      </el-select>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Engineering Coordinator (PETM)">
                    <el-select
                      v-model="formData.engineeringManagerId"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calUserRoleColleciton?.PETM"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                      <!-- <el-option key="" label="" value="" /> -->
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="AUTOSAR">
                    <el-select v-model="formData.autosar" style="width: 100%">
                      <el-option
                        v-for="item in calUserRoleColleciton?.CQR_PF_AUTOSAR"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                      <!-- <el-option key="" label="" value="" /> -->
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="ME Coordinator">
                    <el-select
                      v-model="formData.ameCoordinator"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calUserRoleColleciton?.CQRHRAME"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                      <!-- <el-option
                        key="RZHANG"
                        label="Zhang, Ray"
                        value="RZHANG"
                      /> -->
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Purchasing Coordinator">
                    <el-select
                      v-model="formData.purchasingCoordinator"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calUserRoleColleciton?.CQRHRPUR"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                      <!-- <el-option
                        key="M1ALCARA"
                        label="Alcaraz, Montserrat"
                        value="M1ALCARA"
                      /> -->
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Manufacturing Site">
                    <el-select
                      v-model="formData.manufacturingSite"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calMANUFACTURINGSITE"
                        :key="item.table_entry"
                        :label="item._desc"
                        :value="item._desc"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Cybersecurity">
                    <el-select
                      v-model="formData.cybersecurity"
                      style="width: 100%"
                    >
                      <el-option key="" label="" value="" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="Program Administrator">
                    <el-select
                      v-model="formData.programAdministrator"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calUserRoleColleciton?.PA"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Program Support Specialist">
                    <el-select
                      v-model="formData.programSupportSpecialist"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calUserRoleColleciton?.PSS"
                        :key="item.userId"
                        :label="item.userId + ', ' + item.fullName"
                        :value="item.fullName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Gateway">
                    <el-select
                      v-model="formData.gateway"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in calGATEWAY"
                        :key="item.table_entry"
                        :label="item._desc"
                        :value="item._desc"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item
                    label="Other Relevant Features (up to 100 characters)"
                  >
                    <el-input
                      v-model="formData.otherRelevantFeatures"
                      type="textarea"
                      :rows="3"
                      placeholder="Other relevant features..."
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-main>
        </div>
        <div v-else>Loading...</div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">
import IHSFolderTable from "./IHSFolderTable.vue";
import { getUserFullNameByRole } from "@/api/user"; // 替換為實際的 API 方法
import { ref, onMounted, nextTick, watch, computed } from "vue";
import { useRoute } from "vue-router";
import { useFormModelStore } from "@/store/modules/formModel";
const formStore = useFormModelStore();
const props = defineProps<{ queueKey: string }>();

import { useLoadModel } from "@/composables/useLoadModel";
import { CQRIHSFolder } from "@/store/types";
import { emptyCQRIHSFolder } from "@/utils/emptyCQRHeader";
import { crc32 } from "zlib";
import { getIHSFolderCtierias } from "@/api/cqr";

const formData = ref<IcqrHeader>({});
const fromCQRHeaderCollection = ref<any>({});

// const formData = ref<Record<string, any>>({});
const formIHSFolderDatas = ref<CQRIHSFolder[]>([]); //ref<Partial<CQRIHSFolder>>({});
const mapIHSFolderCriteria = ref<Record<string, any>[]>([]);
// const mapAwardModelYear = ref<Record<string, any>[]>([]); // 用於存儲獎項年度數據
const dataLoading = ref(true);
const userRoles = ref([]);
const { store } = useLoadModel();
const route = useRoute();
// const queueKey = route.params.queueKey;
const searchQueueKey = ref(Number(route.params.queueKey));
// const tableIHSFolderData = computed(() => {
//   return formIHSFolderData.value ? [formIHSFolderData.value] : [];
// });
const multipleSelection = ref<any[]>([]);

const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val;
};
const refreshTable = function () {};
const isEdit = ref(true); // 控制是否可編輯
// const form = reactive<any>({
//   awardYear: "",
//   awardQuarter: "",
//   accountMgrId: "",
//   bIsCost: false
// });

// onMounted(() => {
//   if (formStore.model) {
//     formData.value = { ...formStore.model };
//     formIHSFolderData.value = { ...formStore.modelIHSFolder };
//   }
//   // let obj = { ...formStore.modelIHSFolder };
//   // debugger;
//   // console.log(object);
// });
// emptyCQRHeader =
const mapAwardModelYear = computed(() => {
  return formStore.modelAwardModelYear?.data || [];
});
const calcCurrOptions = computed(() =>
  formStore.getCodesByGroup("CQR_CALCCURR")
);
const calRegion = computed(() => {
  return formStore.codes["CQRRGNLTEAM"];
});
const calENGSITE = computed(() => {
  return formStore.codes["CQR_ENGSITE"];
});
const calMANUFACTURINGSITE = computed(() => {
  return formStore.codes["CQR_MFGSITE"];
});
const calGATEWAY = computed(() => {
  return formStore.codes["CQR_GATEWAY"];
});
const calUserRoleColleciton = computed(() => {
  return formStore.modelUserRoleCollection;
});

onMounted(async () => {
  await formStore.fetchModelIHSFolderCriteria();
  await formStore.fetchModelAwardModelYear();

  await formStore.fetchUserRoleColleciton();

  mapIHSFolderCriteria.value = { ...formStore.modelIHSFolderCriteria.data };
  // mapAwardModelYear.value = { ...formStore.modelAwardModelYear.data };
  formIHSFolderDatas.value = [emptyCQRIHSFolder];

  if (searchQueueKey.value === -1) {
    // formData.value = { ...emptyCQRHeader };
    // const data =  getIHSFolderCtierias();
    // mapIHSFolderCriteria.value = data;
    // debugger;
  } else if (formStore.modelCQRHeaderCollection) {
    formData.value = { ...formStore.modelCQRHeaderCollection.iCQRHeader };
    formIHSFolderDatas.value = [
      ...formStore.modelCQRHeaderCollection.ihsFolderRecords
    ];
    // debugger;
  }

  // if (formStore.model) {
  //   formData.value = { ...formStore.model };
  // }
  // if (formStore.modelIHSFolder) {
  //   formIHSFolderData.value = [
  //     ...formStore.modelCQRHeaderCollection.modelIHSFolder
  //   ];
  // }
});

watch(
  () => formStore.modelCQRHeaderCollection,
  newVal => {
    if (newVal) {
      fromCQRHeaderCollection.value = { ...newVal };
    }
  }
);

watch(
  () => formStore.model,
  newVal => {
    if (newVal) {
      formData.value = { ...newVal };
    }
  }
);

// watch(
//   () => formStore.modelIHSFolder,
//   newVal => {
//     if (newVal) {
//       formIHSFolderData.value = [...newVal];
//     }
//   }
// );

// watch(
//   () => formStore.model,
//   val => {
//     if (val) {
//       formData.value = { ...val };
//       formIHSFolderData.value = [...val];
//     }
//   }
// );
</script>

<style scoped>
.cqr-form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.cqr-form {
  background: #fff;
  margin-bottom: 20px;
}

.section-header {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

.section-subheader {
  font-weight: bold;
  text-decoration: underline;
  margin: 15px 0;
}

.input-with-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mt-20 {
  margin-top: 20px;
}
</style>
