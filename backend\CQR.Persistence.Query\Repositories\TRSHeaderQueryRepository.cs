using CQR.Application.Repositories;
using CQR.Persistence.Query.Base;
using Dapper;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Query.Repositories;

public class TRSHeaderQueryRepository : QueryBaseRepository, ITRSHeaderQueryRespository
{
    public TRSHeaderQueryRepository(IConfiguration configuration) : base(configuration) { }

    public async Task<IEnumerable<Tuple<string, string>>> GetAwardModelYear()
    {
        //throw new NotImplementedException();
        var sql = $@"select table_entry, _desc from TRS_Header where table_name = 'QAFMDLYR' AND ISNULL(inactive,0) <> '1' ORDER BY _desc";
        var result = await _connection.QueryAsync(sql);
        return result.Select(row => Tuple.Create((string)row.table_entry, (string)row._desc));
    }

    public Task<IEnumerable<string>> GetModelList(string tableName)
    {
        var sql = $@"
                    select _desc AS Description FROM TRS_Header WHERE table_name LIKE @tableName AND ISNULL(inactive,0) <> '1' ORDER BY _desc";
        return _connection.QueryAsync<string>(sql, new { tableName = tableName });
    }

    public async Task<IEnumerable<Tuple<string, string>>> GetSiteList()
    {
        var sql = $@"select table_entry, _desc from TRS_Header where table_name = 'MFGSITE' AND ISNULL(inactive,0) <> '1' ORDER BY _desc";
        var result = await _connection.QueryAsync(sql);
        return result.Select(row => Tuple.Create((string)row.table_entry, (string)row._desc));
    }

}
