# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a **Clean Architecture CQRS-based .NET 8 Web API** with the following key layers:

- **CQRLIB (CQR.Domain)**: Core domain entities, business logic, interfaces
- **CQR.Application**: Use cases, DTOs, application services, command/query handlers
- **CQR.Infrastructure**: External integrations (SAP, SMTP, file storage, configurations)
- **CQR.Persistence.Command**: Write operations using Entity Framework Core
- **CQR.Persistence.Query**: Read operations using SqlSugar ORM
- **CQRAPI (CQR.API)**: Web API controllers, authentication, middleware
- **CQRLIB.Tests**: Unit and integration tests

## Key Architectural Patterns

### CQRS Implementation
- **Commands**: Write operations handled through MediatR and Entity Framework
- **Queries**: Read operations use SqlSugar for optimized performance
- **Application Services**: `IApplicationServices` acts as a service locator pattern instead of using MediatR directly
- The codebase is transitioning away from MediatR in favor of ApplicationService pattern

### Data Access Strategy
- **Dual ORM approach**: Entity Framework for commands, SqlSugar for queries
- **Repository Collection**: `IRepositoryCollection` provides unified access to all repositories
- **Unit of Work**: Implemented for transaction management across operations

### Authentication
- **Azure AD JWT Bearer tokens** in production (`Authentication:UseAzureAd: true`)
- **Fake authentication handler** for development (`Authentication:UseAzureAd: false`)

## Common Development Commands

### Build & Run
```bash
# Build the solution
dotnet build

# Restore packages
dotnet restore

# Run API locally (starts on http://localhost:5000)
dotnet run --project CQRAPI

# Publish for deployment
dotnet publish -c Release
```

### Testing
```bash
# Run all tests
dotnet test

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test project
dotnet test CQRLIB.Tests
```

### Database Operations
```bash
# Add Entity Framework migration
dotnet ef migrations add <MigrationName> --project CQR.Persistence --startup-project CQRAPI

# Update database
dotnet ef database update --project CQR.Persistence --startup-project CQRAPI
```

### API Client Generation
```bash
# Generate TypeScript client from OpenAPI (requires API to be running)
npm run generate-api
```

## Configuration

### Connection Strings
Database configuration in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost\\MSSQLSERVER02;Database=TempRestore2;..."
  }
}
```

### Authentication Setup
For Azure AD integration, refer to `README.md` for complete setup instructions including:
- Azure AD App Registration configuration
- JWT token acquisition via Postman
- Required scopes and permissions

## Service Architecture

### ApplicationService Pattern
The codebase uses `IApplicationServices` as a central service locator that provides access to:
- **Command & Query Services**: `ICQRCommandHandler`, `ICQRQueryService`, `ICQRValidationService`
- **Business Services**: `IAttachmentService`, `IProductService`, `ICustomService`
- **Infrastructure Services**: `IExcelService`, `IDatabaseService`
- **External Services**: `ISapRFCService`, `ISqlConnectionService`
- **Data Access**: `IRepositoryCollection`

### Dependency Injection
All services are registered in `Program.cs` with proper lifetime management:
- Repositories: Scoped
- Services: Scoped  
- Infrastructure: Singleton for connection services

## Key Libraries & Frameworks

- **.NET 8.0** with nullable reference types
- **Entity Framework Core 8.0** for command operations
- **SqlSugar 5.1.4** for optimized query operations
- **MediatR 12.5.0** (being phased out in favor of ApplicationService)
- **FluentValidation 12.0.0** for request validation
- **Microsoft.Identity.Web** for Azure AD integration
- **Serilog** for structured logging
- **SAP NetConnector** for ERP integration
- **ClosedXML/GemBox.Spreadsheet** for Excel operations

## Development Notes

### Linting & Code Quality
The project does not have explicit lint commands defined. Ensure code follows:
- Standard C# conventions
- Nullable reference type annotations where appropriate
- Proper async/await patterns
- Repository pattern implementation

### Testing Strategy
- Unit tests in `CQRLIB.Tests/CQR.Application.Tests/`
- Integration tests for service interactions
- Test base classes available in `TestBase/TestBase.cs`
- Mock data builders in `TestHelpers/MockDataBuilder.cs`

### File Structure Conventions
- Controllers inherit from `BaseController`
- DTOs organized by domain areas in `CQR.Application/Dto/`
- Use cases follow CQRS command/query structure
- Repositories implement domain-specific interfaces

## External Integrations

### SAP Integration
- Uses `sapnco.dll` for RFC connections
- Configuration managed through `SapConfig` class
- Connection services handle SAP authentication and data retrieval

### Excel Processing
- Supports both ClosedXML and GemBox.Spreadsheet
- Template processing for CQR reports
- File upload/download capabilities in controllers

### Email Services
- SMTP configuration in `SmtpSettings`
- MailKit for email operations
- Email templates stored in `CQRAPI/EmailTemplate/`