﻿namespace CQR.Domain.CQRHeaders;
public class Antares
{
    public string? sUniqueNumber { get; set; }
    public string? sVB_ID { get; set; }
    public string? sOEMGroup { get; set; }
    public string? sOEMCustomer { get; set; }
    public string? sPlatform { get; set; }
    public string? sProgram { get; set; }
    public string? sNameplate { get; set; }
    public string? sCountry { get; set; }
    public string? sRegion { get; set; }
    public string? sNewBusinessCategory { get; set; }
    public string? sStatus { get; set; }
    public string? sProductId { get; set; }
    public string? sProductDescription { get; set; }
    public string? sProductGrouping { get; set; }
    public string? sSOP { get; set; }
    public string? sEOP { get; set; }
    public string? sSoldFrom { get; set; }
    public string? sFinalAssembly { get; set; }
    public string? sUsedByOtherCQR { get; set; }
    public bool bArchived { get; set; }
}
