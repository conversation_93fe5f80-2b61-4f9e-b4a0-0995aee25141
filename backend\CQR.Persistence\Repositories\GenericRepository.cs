using CQR.Domain.Interfaces.Repository;

//using CQR.Core.Repos;
using Microsoft.EntityFrameworkCore;
using System.Data;

namespace CQR.Persistence.Command.Repositories;

public class GenericRepository<T> : IGenericRepository<T> where T : class
{
    protected readonly CQRDbContext _context;
    // ::Dapper Use
    protected readonly IDbConnection _connection;
    public string GetConnectionString()
    {
        return _context.Database.GetDbConnection().ConnectionString;
    }

    public GenericRepository(CQRDbContext context)
    {
        _context = context;
        _connection = _context.Database.GetDbConnection();

        // 重用 EF 的連接
        //_connection = new SqlConnection(_context.Database.GetConnectionString());
    }
    // 获取连接字符串的方法
    //public string GetConnectionString()
    //{
    //    return _context.Database.GetConnectionString();
    //}

    public async Task<T> GetByIdAsync(int id)
    {
        return await _context.Set<T>().FindAsync(id);
    }

    public async Task<IEnumerable<T>> GetAllAsync()
    {
        return await _context.Set<T>().ToListAsync();
    }

    public async Task AddAsync(T entity)
    {
        await _context.Set<T>().AddAsync(entity);
    }

    public void Update(T entity)
    {
        _context.Set<T>().Update(entity);
    }

    public void Delete(T entity)
    {
        _context.Set<T>().Remove(entity);
    }

    public async Task SaveChangesAsync()
    {
        await _context.SaveChangesAsync();
    }

    public async Task<int> CountAsync()
    {
        // throw new NotImplementedException();
        return await _context.Set<T>().CountAsync();
    }
}