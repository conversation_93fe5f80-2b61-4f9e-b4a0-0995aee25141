﻿using CQR.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace CQR.API.Controllers;

[ApiController]
public abstract class BaseController : ControllerBase
{
    protected readonly ICurrentUserService _currentUser;

    protected BaseController(ICurrentUserService currentUser)
    {
        _currentUser = currentUser;
    }

    //protected bool TryBindUserId(SaveGDPIMPhaseCommand command, out IActionResult errorResult)
    //{
    //    var userId = _currentUser.UserId;
    //    if (userId == null)
    //    {
    //        errorResult = Unauthorized();
    //        return false;
    //    }

    //    command.UserId = userId;
    //    errorResult = null;
    //    return true;
    //}
}
