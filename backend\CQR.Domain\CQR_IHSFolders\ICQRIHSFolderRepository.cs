﻿using CQR.Domain.Interfaces.Repositories;

namespace CQR.Domain.CQRIHSFolder;

public interface ICQRIHSFolderRepository : IEntityRepository<CQR_IHSFolder, int>
{
    // Domain-specific query methods
    Task<IEnumerable<CQR_IHSFolder>> GetByQueueKeyAsync(int queueKey);
    Task<IEnumerable<CQR_IHSFolder>> GetByUniqueNumberAsync(int uniqueNumber);
    //Task<CQRIHSFolder> GetByQueueKeyAndUniqueNumberAsync(int queueKey, int uniqueNumber, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQR_IHSFolder>> GetByQueuekey(int queuekey);

    //// Archive-related queries
    //Task<IEnumerable<CQRIHSFolder>> GetArchivedAsync(CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetActiveAsync(CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetArchivedByUserAsync(string archivedByUser, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetArchivedInDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    //// Location-based queries
    //Task<IEnumerable<CQRIHSFolder>> GetByRegionAsync(string region, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByCountryAsync(string country, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByRegionAndCountryAsync(string region, string country, CancellationToken cancellationToken = default);

    //// Production-related queries
    //Task<IEnumerable<CQRIHSFolder>> GetByPlatformAsync(string platform, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByProgramAsync(string program, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetInProductionAsync(CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByProductionPeriodAsync(string startPeriod, string endPeriod, CancellationToken cancellationToken = default);

    //// OEM-related queries
    //Task<IEnumerable<CQRIHSFolder>> GetByOEMGroupAsync(string oemGroup, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByOEMAsync(string oem, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByOEMGroupAndOEMAsync(string oemGroup, string oem, CancellationToken cancellationToken = default);

    //// Product-related queries
    //Task<IEnumerable<CQRIHSFolder>> GetByProductDescriptionAsync(string productDescription, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByProductGroupingAsync(string productGrouping, CancellationToken cancellationToken = default);

    //// Pagination support
    //Task<(IEnumerable<CQRIHSFolder> Items, int TotalCount)> GetPagedAsync(
    //    int pageNumber,
    //    int pageSize,
    //    Expression<Func<CQRIHSFolder, bool>> predicate = null,
    //    Expression<Func<CQRIHSFolder, object>> orderBy = null,
    //    bool orderByDescending = false,
    //    CancellationToken cancellationToken = default);

    //// Search functionality
    //Task<IEnumerable<CQRIHSFolder>> SearchAsync(
    //    string searchTerm,
    //    CancellationToken cancellationToken = default);

    //// Bulk operations
    //Task<int> BulkArchiveAsync(
    //    IEnumerable<int> ids,
    //    int archivedByQueueKey,
    //    string archivedByUser,
    //    CancellationToken cancellationToken = default);

    //Task<int> BulkUnarchiveAsync(
    //    IEnumerable<int> ids,
    //    CancellationToken cancellationToken = default);
}