﻿using CQR.Application.Dto.AttachFile;
//using CQR.Application.Dto.AttachFile;

namespace CQR.Application.Repositories;

public interface IAttachFileQueryRepository
{
    Task<IEnumerable<ATTDIR_AttachFileDirHeaderCheckOutDto>> GetATTDIRAttachFileDirHeaderCheckOutDtos(int queueKey);

    Task<IEnumerable<ATTDIR_AttachFileDirHeaderCheckOutDto>> GetATTDIRFilesByFolderType(string folderType,int queueKey);
}