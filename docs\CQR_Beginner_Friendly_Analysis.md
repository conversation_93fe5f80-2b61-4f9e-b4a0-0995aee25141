# CQR 系統新手指南：從零開始理解企業報價管理系統

## 🎯 這個系統是做什麼的？

想像你在一家汽車零件公司工作，每天都有客戶（像 BMW、豐田）來詢問零件報價。CQR 系統就是幫助公司管理這些報價流程的工具，從客戶詢問到最終成交，整個過程都在這個系統裡追蹤管理。

### 簡單來說，CQR 就像是：
- 📋 **報價管理工具**：記錄誰要什麼零件、什麼時候要、預算多少
- 🔄 **工作流程系統**：確保報價按正確順序經過所有相關部門
- 📊 **數據整合平台**：連接各種內外部系統，獲取需要的資訊
- 💼 **文件管理中心**：處理 Excel 報表、技術文件、合約等

## 🏗️ 系統的基本結構（像蓋房子一樣）

### 地基層：資料庫
```
SQL Server 資料庫 = 房子的地基
├── 存放所有報價資料
├── 記錄用戶權限
├── 追蹤工作流程狀態
└── 保存歷史記錄（審計用）
```

### 建築層：網頁應用程式
```
ASP.NET WebForms = 房子的主體結構
├── 8個主要頁面（像8個房間）
├── 用戶登入和權限控制
├── 與資料庫溝通
└── 處理業務邏輯
```

### 裝潢層：用戶界面
```
HTML + CSS + JavaScript = 房子的裝潢
├── 漂亮的表單和按鈕
├── 動態效果和驗證
├── Excel 文件在線編輯
└── 即時提示和幫助
```

## 📱 8個主要頁面詳解（像手機 App 的8個功能）

### 1. 📝 Description（項目描述）- 填寫基本資料
**用途**：就像填寫「客戶需求單」
- 客戶是誰？要什麼產品？
- 預計年產量多少？價值多少？
- 哪些人負責這個案子？

**特別功能**：
- 🔍 **智能搜索**：輸入客戶名稱，自動帶出相關車型資訊
- ⚡ **即時驗證**：確保不會重複使用已存在的產品代碼
- 👥 **人員指派**：自動分配銷售、工程、財務負責人

### 2. 📊 Background（背景資訊）- 了解客戶需求
**用途**：就像「客戶訪談記錄」
- 為什麼客戶需要這個產品？
- 有什麼特殊要求或限制？
- 客戶的聯絡人是誰？

**簡單明瞭**：這是8個頁面中最簡單的，主要就是填寫一些文字說明。

### 3. 🔧 Engineering（工程評估）- 技術可行性檢查
**用途**：就像「工程師的健康檢查表」
- ✅ 這個案子技術上做得到嗎？
- ✅ 時間夠不夠？
- ✅ 需要哪些設計輸入？
- ✅ 誰負責各個技術環節？

**智能邏輯**：
```
如果「技術可行」AND「資訊充足」
→ 系統自動設定「可以開始工作」
→ 相關欄位變成灰色（因為已經確定了）
```

### 4. 💰 Quote Response（報價回應）- 財務計算
**用途**：就像「成本計算器」
- 📊 **兩種模式**：
  - 快速報價（簡單估算）
  - 完整成本模型（詳細計算）
- 📈 **Excel 整合**：可以在網頁裡直接編輯 Excel 成本表
- 💱 **多貨幣支援**：自動處理不同國家的幣別

### 5. 🛣️ Routing（路由管理）- 追蹤進度
**用途**：就像「快遞追蹤系統」
- 📍 現在案子在哪個部門？
- 👤 誰在處理？
- ⏰ 什麼時候完成的？
- 💬 有什麼意見或問題？

**特別功能**：主管可以「代理」其他人查看或操作案子。

### 6. 🎯 Milestones（里程碑）- 人員管理
**用途**：就像「通訊錄」
- 👨‍💼 銷售經理是誰？
- 👩‍💼 銷售總監是誰？

**最簡單**：這個頁面功能最少，主要就是指派負責人。

### 7. 🏁 Sales Closeout（銷售結案）- 最終結果
**用途**：就像「成績單」
- 🎉 有沒有得標？什麼時候？
- 💸 最終盈虧如何？
- 📄 相關文件都準備好了嗎？

**複雜功能**：
- 動態生成盈虧報表
- 自動計算獲利率
- 整合交接文件

### 8. 📎 Attachment（附件管理）- 文件中心
**用途**：就像「雲端硬碟」
- 📁 上傳下載各種文件
- 📊 **特別功能**：Excel 文件可以「借出/歸還」
  - 借出 = 我正在編輯，別人不能改
  - 歸還 = 我編輯完了，別人可以使用

## ⚙️ 系統的聰明設計

### 🔐 權限控制（像公司門禁卡）
```
你的權限 = 你的職位 + 案子的狀態 + 你是不是創建者 + 現在時間

例如：
- 銷售人員：只能改客戶資訊，不能改成本
- 工程師：只能在工程階段修改技術資料  
- 財務人員：只能在報價階段修改價格
- 超級用戶（老闆）：什麼都能改
```

### 💾 自動存檔（像 Google Docs）
```
每次你：
1. 填寫資料
2. 點擊其他頁面
3. 系統自動檢查並儲存

不怕忘記存檔！
```

### 🔒 防衝突機制（像共用文件）
```
當你編輯一個案子時：
1. 系統自動「鎖定」這個案子
2. 其他人只能看，不能改
3. 你離開時自動「解鎖」

防止多人同時修改造成混亂！
```

## 🔄 詳細工作流程（像生產線的每個環節）

### 📋 完整流程圖
```mermaid
graph TD
    A[客戶詢問] --> B[創建 CQR]
    B --> C[📝 Description 填寫]
    C --> D[📊 Background 補充]
    D --> E[🔧 Engineering 評估]
    E --> F[💰 Quote Response 計算]
    F --> G[🎯 內部審核]
    G --> H[🏁 Sales Closeout]
    H --> I[📎 文件歸檔]
    I --> J[🎉 完成或 💔 失敗]
```

### 第一階段：項目啟動（1-2天）

#### 步驟1：創建新 CQR
**誰來做**：銷售人員（AMGR 或 SDIR）
**做什麼**：
1. 🔐 登入系統，點擊「創建新 CQR」
2. 🎯 選擇 CQR 類型：
   - 全新項目（New）
   - 修改現有項目（Modify）
3. 📄 系統自動分配 CQR 編號（例如：CQR-2024-001）
4. ✅ 確認創建，進入編輯模式

#### 步驟2：📝 Description（項目描述）
**誰來做**：銷售人員
**預計時間**：1-2小時
**詳細步驟**：

1. **填寫基本資訊**：
   ```
   CQR 描述：CQR-001-BMW-X5-引擎控制模組
   車型年份：2025
   年產量：50,000 台
   預估年度價值：$2,500,000
   ```

2. **選擇汽車製造商資料**：
   - 🚗 選擇 OEM 集團（例如：BMW Group）
   - 🏭 選擇具體客戶（例如：BMW）
   - 🚙 選擇平台（例如：CLAR）
   - 🏷️ 選擇銘牌（例如：X5）
   - 📱 系統自動載入相關產品資訊

3. **設定時程**：
   ```
   客戶報價到期日：2024-03-15
   預計量產日期：2025-01-01
   TDR 日期：2024-06-30
   ```

4. **指派團隊**：
   ```
   銷售經理：John Smith
   工程經理：Mary Johnson  
   財務經理：David Chen
   ```

**完成標準**：所有必填欄位都有資料，系統顯示綠色勾勾 ✅

### 第二階段：需求分析（1天）

#### 步驟3：📊 Background（背景資訊）
**誰來做**：銷售人員 + 客戶聯絡人
**預計時間**：2-3小時
**詳細步驟**：

1. **撰寫背景說明**：
   ```
   背景資訊範例：
   BMW 正在開發新一代 X5 車型，需要更高效能的引擎控制模組。
   主要要求：
   - 符合 Euro 7 排放標準
   - 支援混合動力系統
   - 降低 15% 成本相較現有產品
   ```

2. **記錄客戶聯絡人**：
   ```
   採購員：Hans Mueller (<EMAIL>)
   工程師：Anna Schmidt (<EMAIL>)
   ```

3. **淘汰評估**：
   - ✅ 需要淘汰舊產品嗎？是/否
   - 📝 如果是，說明淘汰影響

4. **客戶生產力目標**：
   ```
   客戶期望：
   - 每年節省成本 $500,000
   - 提升燃油效率 10%
   - 減少供應商數量
   ```

**完成標準**：背景清楚、聯絡人正確、特殊要求明確

### 第三階段：技術評估（3-5天）

#### 步驟4：🔧 Engineering（工程評估）
**誰來做**：工程師（PRD、PETM）
**預計時間**：3-5天
**詳細步驟**：

1. **可行性評估**（關鍵決策點）：
   ```
   ❓ 在時間內可達成嗎？
   ✅ 是 → 繼續
   ❌ 否 → 說明原因，可能退回或終止
   
   ❓ 資訊充足嗎？
   ✅ 是 → 工作可以進行
   ❌ 否 → 列出需要的額外資訊
   ```

2. **安全和環境檢查**：
   ```
   ❓ 有健康、安全、環境問題嗎？
   ✅ 無問題 → 繼續
   ⚠️ 有問題 → 制定解決方案
   ```

3. **設計輸入需求評估**（8個項目逐一檢查）：
   ```
   1. 機械設計 → 需要/不需要 → 負責人：Mike Wilson
   2. 電氣設計 → 需要/不需要 → 負責人：Sarah Lee
   3. 軟體設計 → 需要/不需要 → 負責人：Tom Chang
   4. 測試驗證 → 需要/不需要 → 負責人：Lisa Brown
   ...等等
   ```

4. **設定關鍵日期**：
   ```
   給成本估算的日期：2024-02-15
   設計工程完成日期：2024-04-30
   ```

5. **指派團隊負責人**：
   ```
   製造負責人：Alex Johnson
   採購負責人：Emma Davis
   驗證負責人：Chris Miller
   ```

**完成標準**：所有評估完成，負責人確定，時程確認

### 第四階段：成本計算（5-10天）

#### 步驟5：💰 Quote Response（報價回應）
**誰來做**：財務人員（Cost Estimator）
**預計時間**：5-10天
**詳細步驟**：

1. **選擇計算模式**：
   ```
   🔲 快速報價（Quote Response）：2-3天
   ✅ 完整成本模型（Full Cost Model）：7-10天
   ```

2. **Excel 成本計算**：
   - 📊 開啟線上 Excel 工具
   - 📋 填入材料成本、人工成本、間接成本
   - 🧮 系統自動計算總成本和建議售價
   
   ```
   成本結構範例：
   材料成本：$85.00
   人工成本：$12.00  
   間接成本：$18.00
   總成本：$115.00
   利潤率：25%
   建議售價：$143.75
   ```

3. **填寫報價資訊**：
   ```
   工具交期：16週
   工具產能：10,000件/月
   交貨條件：EXW（工廠交貨）
   ```

4. **經濟水平日期**：
   ```
   材料經濟水平：2025-06-01
   人工經濟水平：2025-03-01
   ```

5. **狀態更新**：
   ```
   ✅ 報價準備完成，等待團隊共識會議
   ✅ 報價準備完成，等待 Gateway 2 批准
   ```

**完成標準**：成本計算準確，價格合理，時程可行

### 第五階段：內部審核（2-3天）

#### 步驟6：🎯 內部審核流程
**誰來做**：跨部門團隊
**預計時間**：2-3天

1. **團隊共識會議**：
   - 👥 參與者：銷售、工程、財務、製造、採購
   - 📋 議程：檢視技術可行性、成本合理性、時程可達性
   - ✅ 決議：同意/修改/拒絕

2. **管理層批准**：
   - 📊 準備摘要報告
   - 💼 提交 Gateway 2 審核
   - ⏰ 等待批准結果

3. **修改和調整**：
   - 🔄 如有需要，回到相應頁面修改
   - 📝 更新成本或技術規格
   - 🔁 重新提交審核

### 第六階段：客戶報價（1-2天）

#### 步驟7：📤 提交客戶報價
**誰來做**：銷售人員
**預計時間**：1-2天

1. **準備報價文件**：
   - 📄 生成正式報價單
   - 📎 附加技術規格文件
   - 📊 包含交期和條件

2. **客戶提交**：
   - 📧 Email 發送報價
   - 📞 電話跟進確認收到
   - 📅 設定客戶回覆期限

### 第七階段：追蹤和結案（持續追蹤）

#### 步驟8：🏁 Sales Closeout（銷售結案）
**誰來做**：銷售人員
**預計時間**：持續更新

1. **報價狀態更新**：
   ```
   🔲 未報價
   🔲 口頭報價
   ✅ 正式報價函 → 日期：2024-03-15
   ```

2. **獲獎狀態追蹤**：
   ```
   等待客戶回覆...
   
   結果選項：
   🎉 得標 → 記錄得標日期和合約金額
   💔 失標 → 記錄失標原因
   ⏸️ 其他 → 說明具體情況
   ```

3. **盈虧分析**：
   - 📊 實際成本 vs 預估成本
   - 💰 實際售價 vs 建議售價
   - 📈 利潤率分析

4. **後續行動**：
   ```
   如果得標：
   - 📋 準備交接文件
   - 🏭 啟動生產準備
   - 👥 指派專案經理
   
   如果失標：
   - 📝 分析失標原因
   - 💡 記錄改進建議
   - 🔄 為下次報價做準備
   ```

#### 步驟9：📎 Attachment（文件歸檔）
**誰來做**：所有相關人員
**預計時間**：隨時更新

1. **文件上傳**：
   ```
   - 客戶規格書
   - 技術圖紙
   - 成本計算表
   - 報價單
   - 合約文件（如果得標）
   ```

2. **Tasklist 管理**：
   - 📝 Excel 任務清單「借出」編輯
   - ✅ 完成後「歸還」系統
   - 🔄 版本控制和追蹤

### 🚦 流程控制點

#### 關鍵決策點：
1. **可行性檢查**（Engineering 階段）：
   - ❌ 不可行 → 終止或重新評估
   - ✅ 可行 → 繼續流程

2. **成本審核**（Quote Response 階段）：
   - ❌ 成本過高 → 重新計算或談判
   - ✅ 成本合理 → 繼續報價

3. **管理層批准**（內部審核階段）：
   - ❌ 不批准 → 修改或終止
   - ✅ 批准 → 提交客戶

#### 異常處理：
1. **緊急案件**：
   ```
   正常流程：8個步驟，15-20天
   緊急流程：5個步驤，5-7天
   - 簡化 Background
   - 快速 Engineering 評估
   - 使用快速報價模式
   ```

2. **退回修改**：
   ```
   任何階段都可能退回：
   - 資訊不完整 → 退回補充
   - 成本過高 → 退回重算
   - 技術問題 → 退回重評
   ```

3. **並行處理**：
   ```
   可以同時進行：
   - Engineering 評估 + Background 補充
   - 初步成本計算 + 技術規格確認
   ```

### 📊 流程監控

#### 🛣️ Routing 頁面功能：
```
即時顯示：
- 📍 目前在哪個階段？
- 👤 誰在處理？
- ⏰ 預計完成時間？
- 💬 有什麼問題或意見？
- 🚨 是否有延遲？
```

#### 📈 進度追蹤：
```
整體進度：60% 完成
目前階段：Quote Response
負責人：David Chen
預計完成：2024-02-20
狀態：正常進行中 🟢
```

這個詳細的流程描述讓新手能夠清楚了解：
- 🎯 **每個階段要做什麼**
- 👤 **誰來負責**
- ⏰ **需要多少時間**
- ✅ **完成標準是什麼**
- 🚨 **可能遇到的問題**
- 🔄 **如何處理異常情況**

## 🧩 外部系統整合（像插頭轉接器）

### 為什麼需要連接其他系統？
因為報價需要很多資訊，不可能全部手工輸入：

1. **Antares 系統**：汽車製造商資料
   - 哪家車廠？什麼車型？什麼平台？
   - 預計生產日期？停產日期？

2. **IHS 系統**：另一套汽車資料
   - 工廠代碼、產品描述
   - 銷售地區、最終組裝地

3. **TRS 系統**：公司內部資料
   - 人員清單、部門資料
   - 產品分類、地點資料

4. **Excel 系統**：成本計算
   - 複雜的成本模型
   - 熟悉的試算表介面

### 整合方式：
```
1. 下拉選單連動：選了車廠 → 自動顯示車型
2. 即時搜索：輸入關鍵字 → 立即顯示結果  
3. 資料驗證：檢查是否重複 → 防止錯誤
4. 在線編輯：直接在網頁編輯 Excel
```

## 🚧 系統的一些限制（誠實說明）

### 技術方面：
- 📅 **技術較舊**：使用 2000 年代的技術，但穩定可靠
- 🖥️ **Windows 專用**：只能在 Windows 伺服器運行
- 📱 **桌面優先**：在手機上使用體驗不佳
- 🔄 **擴展困難**：增加新功能需要較多時間

### 使用方面：
- 📚 **學習曲線**：新人需要時間熟悉
- 🐛 **IE 相容性**：最好用 Internet Explorer
- 💾 **記憶體消耗**：長時間使用可能變慢
- 🔌 **網路依賴**：離線無法使用

## 💡 使用小技巧

### 新手建議：
1. **從簡單開始**：先熟悉 Background 和 Milestones 頁面
2. **多用搜索**：善用下拉選單的搜索功能
3. **經常存檔**：雖然有自動存檔，但主動存檔更安全
4. **詢問同事**：系統有很多隱藏功能，老鳥知道更多技巧

### 進階技巧：
1. **批量操作**：某些頁面支援批量修改
2. **快捷鍵**：熟悉常用的鍵盤快捷鍵
3. **報表匯出**：善用各種匯出功能
4. **權限申請**：需要更多權限時知道找誰申請

## 🔮 系統的未來

### 可能的改進：
- 📱 **手機版本**：支援手機和平板使用
- 🚀 **效能提升**：更快的載入速度
- 🎨 **介面美化**：更現代的使用者介面
- 🔗 **API 開放**：與其他系統更容易整合

### 為什麼不全部重寫？
```
重寫的風險 > 改進的好處

風險：
- 可能遺失重要的業務邏輯
- 需要重新訓練所有用戶
- 與現有系統的整合可能中斷
- 開發時間長，成本高

好處：
- 新技術帶來的改進
- 更好的使用者體驗
- 更容易維護

結論：漸進式改進比全部重寫更安全
```

## 🎓 學習路線圖

### 第一週：熟悉基本操作
- [ ] 學會登入和導航
- [ ] 熟悉 Description 頁面
- [ ] 嘗試建立一個測試案例

### 第二週：理解工作流程
- [ ] 學習各個頁面的用途
- [ ] 理解權限控制邏輯
- [ ] 練習完整的報價流程

### 第三週：掌握進階功能
- [ ] 學會使用 Excel 整合功能
- [ ] 熟悉附件管理
- [ ] 理解路由和狀態管理

### 第四週：成為進階用戶
- [ ] 掌握所有快捷操作
- [ ] 學會處理異常狀況
- [ ] 能夠協助其他新人

## 🤝 總結

CQR 系統雖然技術不算最新，但它承載了公司多年的業務經驗和流程智慧。作為新手，重點不是批評它的技術選擇，而是理解它為什麼這樣設計，以及如何有效地使用它來完成工作。

記住：**好用的系統不一定是最新的系統，而是最適合業務需求的系統。**

### 需要幫助時：
- 📖 查看線上說明文件
- 👥 詢問有經驗的同事
- 🎫 提交 IT 支援票據
- 📞 聯絡系統管理員

歡迎來到 CQR 系統的世界！雖然一開始可能覺得複雜，但一旦熟悉了，你會發現它是一個功能強大且可靠的工作夥伴。