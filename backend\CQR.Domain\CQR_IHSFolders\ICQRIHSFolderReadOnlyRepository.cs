﻿using CQR.Domain.Interfaces.Repositories;

namespace CQR.Domain.CQRIHSFolder;

public interface ICQRIHSFolderReadOnlyRepository : IReadOnlyRepository<CQR_IHSFolder, int>
{
    // Read-only domain-specific query methods
    //Task<IEnumerable<CQR_IHSFolder>> GetByQueueKeyAsync(int queueKey, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByUniqueNumberAsync(int uniqueNumber, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetArchivedAsync(CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetActiveAsync(CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByRegionAsync(string region, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByCountryAsync(string country, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByPlatformAsync(string platform, CancellationToken cancellationToken = default);
    //Task<IEnumerable<CQRIHSFolder>> GetByOEMAsync(string oem, CancellationToken cancellationToken = default);

    //Task<(IEnumerable<CQRIHSFolder> Items, int TotalCount)> GetPagedAsync(
    //    int pageNumber,
    //    int pageSize,
    //    Expression<Func<CQRIHSFolder, bool>> predicate = null,
    //    Expression<Func<CQRIHSFolder, object>> orderBy = null,
    //    bool orderByDescending = false,
    //    CancellationToken cancellationToken = default);

    //Task<IEnumerable<CQRIHSFolder>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
}