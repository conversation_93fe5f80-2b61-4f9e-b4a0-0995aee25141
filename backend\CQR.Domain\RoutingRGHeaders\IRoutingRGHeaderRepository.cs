﻿using CQR.Domain.RoutingRGHeaders;

namespace CQR.Domain.ROUTING_RGHeaders;

public interface IRoutingRGHeaderRepository
{
    //Task<RoutingRGHeader?> GetByIdAsync(int queueKey);
    Task<IEnumerable<ROUTING_RGHeader>> GetAllAsync();

    Task<IEnumerable<ROUTING_RGHeader>> GetByFoldNBR(int queueKey);
    Task<IEnumerable<ROUTING_RGHeader>> GetByFoldNbrAndFolerType(int queueKey,string folderType);



    //Task AddAsync(ROUTING_RGHeader entity);
    //Task UpdateAsync(ROUTING_RGHeader entity);
    //Task DeleteAsync(int queueKey);
}
