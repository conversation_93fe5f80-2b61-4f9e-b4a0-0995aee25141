﻿using CQR.Application.Dto.CQRHead;
using CQR.Application.Repositories;
using CQR.Domain.CQR_GDPIMPhases;

namespace CQR.Application.Queries;

public class GetCQRHeaderReportHandler
{
    private readonly IUserRoleQueryRepository _userRepository;
    private readonly ICQRGDPIMPhasesRepository _gdpepRepository;

    public GetCQRHeaderReportHandler(
        IUserRoleQueryRepository userRepository,
        ICQRGDPIMPhasesRepository gdpepRepository
    )
    {
        _userRepository = userRepository;
        _gdpepRepository = gdpepRepository;
    }

    public async Task<CQRHeaderCollection> HandleAsync(int userId, int queueKey)
    {
        //var userRoles = await _userRepository.GetUserRolesAsync(userId);

        //var result = new CQRHeaderCollection
        //{
        //    UserRoles = userRoles,
        //    Gateway = new GDPEP_APPROVAL[3]
        //};

        //for (int phase = 0; phase < 3; phase++)
        //{
        //    var approval = await _gdpepRepository.GetApprovalAsync(queueKey, phase);
        //    result.Gateway[phase] = approval;
        //}

        return null;
    }
}
