﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;

using System.Net;
using System.Net.Mail;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

namespace CQR.Domain.DecisionCore;
[StandardModule]
public  class modTools
{
    public const string COMMENTS_HELD = "[Comments Held]\r\n";

    public static string APP_NAME;

    public static extern long GetTickCount();

    public static string mDateScreenToCygnet(string asDate)
    {
        string result = asDate;
        if (Information.IsDate(asDate))
        {
            result = Strings.Format(Conversions.ToDate(asDate), "yyyyMMdd");
        }
        return result;
    }

    public static string mDateCygnetToScreen(string asDate)
    {
        string result = asDate;
        if (!(!Versioned.IsNumeric(asDate) & !Information.IsDate(asDate)) && (Strings.Len(asDate) <= 0 || Strings.Asc(Strings.Left(asDate, 1)) != 0))
        {
            string text = "";
            if ((Strings.InStr(asDate, "/") | Strings.InStr(asDate, "-")) != 0)
            {
                text = Conversions.ToString(DateAndTime.DateSerial(DateAndTime.DatePart("yyyy", asDate), DateAndTime.DatePart("m", asDate), DateAndTime.DatePart("d", asDate)));
                if (DateAndTime.DatePart("yyyy", text) < 1960)
                {
                    text = Conversions.ToString(DateAndTime.DateSerial(checked((int)Math.Round(2000.0 + Conversion.Val(Strings.Right(Conversions.ToString(DateAndTime.DatePart("yyyy", text)), 2)))), DateAndTime.DatePart("m", asDate), DateAndTime.DatePart("d", asDate)));
                }
            }
            else if (Strings.Len(Strings.Trim(asDate)) == 8)
            {
                string text2 = Strings.Left(asDate, 4);
                if (Operators.CompareString(text2, "1900", TextCompare: false) < 0)
                {
                    text2 = "1997";
                }
                text = Conversions.ToString(DateAndTime.DateSerial(Conversions.ToInteger(text2), Conversions.ToInteger(Strings.Mid(asDate, 5, 2)), Conversions.ToInteger(Strings.Mid(asDate, 7, 2))));
            }
            if (!Information.IsDate(text))
            {
                result = "";
            }
            else
            {
                if (Strings.InStr("/-", Strings.Mid(text, 2, 1)) > 0)
                {
                    text = "0" + text;
                }
                if (Strings.InStr("/-", Strings.Mid(text, 5, 1)) > 0)
                {
                    text = Strings.Left(text, 3) + "0" + Strings.Mid(text, 4);
                }
                text = MakeDateInternational(text);
                result = text;
            }
        }
        return result;
    }

    public static string MakeDateInternational(string dateString)
    {
        if (!Information.IsDate(dateString))
        {
            return dateString;
        }
        return Strings.Format(Conversions.ToDate(dateString), "dd-MMM-yyyy");
    }

    public static void TableRemoveLastRow(Table tTable)
    {
        //tTable.Rows.RemoveAt(checked(tTable.Rows.Count - 1));
    }

    public static void TableRemoveLastCol(Table tTable)
    {
        //checked
        //{
        //    int num = tTable.Rows.Count - 1;
        //    for (int i = 0; i <= num; i++)
        //    {
        //        tTable.Rows[i].Cells.RemoveAt(tTable.Rows[i].Cells.Count - 1);
        //    }
        //}
    }

    public static void CommentSplit(string sComment, ref string asOld, ref string asNew, ref string asOrg, bool bSuperUser)
    {
        if (bSuperUser)
        {
            asNew = sComment;
            asOrg = sComment;
            return;
        }
        int num = Strings.InStr(sComment, "[Comments Held]\r\n");
        checked
        {
            if (num == 0)
            {
                asOld = sComment;
                asNew = "";
                if (Operators.CompareString(asOld, "", TextCompare: false) != 0 && ((Operators.CompareString(Strings.Right(asOld, Strings.Len("\r\n")), "\r\n", TextCompare: false) != 0) | (Strings.InStr(asOld, "]") == 0)))
                {
                    asOld += "\r\n[Unknown Date]   Unknown User\r\n\r\n";
                }
            }
            else
            {
                asOld = Strings.Left(sComment, num - 1);
                asNew = Strings.Mid(sComment, num + Strings.Len("[Comments Held]\r\n"));
            }
        }
    }

    public static string CommentStampText(string asUserId)
    {
        return "\r\n[" + Strings.Format(DateAndTime.Now, "MMMM dd, yyyy") + "]   " + modUserProf.UserNameFromId(asUserId) + "\r\n\r\n";
    }

    public static string CommentStamp(string asOld, ref string asNew, ref string asOrg, bool abReleasing, string asUserId, bool bSuperUser)
    {
        string result;
        if (Operators.CompareString(Strings.UCase(asUserId), "SUPER", TextCompare: false) == 0 || bSuperUser)
        {
            //asUserId = Conversions.ToString(HttpContext.Current.Session["UserActual"]);
            if (Operators.CompareString(asNew, asOrg, TextCompare: false) == 0)
            {
                result = asNew;
                goto IL_00a2;
            }
        }
        string text;
        if (bSuperUser)
        {
            text = Strings.Trim(asNew);
        }
        else
        {
            text = Strings.Trim(asOld);
            if (Operators.CompareString(Strings.Trim(asNew), "", TextCompare: false) != 0)
            {
                if (!abReleasing)
                {
                    text += "[Comments Held]\r\n";
                }
                text += Strings.Trim(asNew);
                if (abReleasing)
                {
                    text += CommentStampText(asUserId);
                }
            }
        }
        result = text;
        asOrg = text;
        goto IL_00a2;
    IL_00a2:
        return result;
    }

    public static string CommentStampNewAtTop(string asOld, ref string asNew, bool abReleasing, string asUserId, bool bSuperUser = false)
    {
        string text = "";
        if (bSuperUser)
        {
            return Strings.Trim(asNew);
        }
        if (Operators.CompareString(Strings.Trim(asNew), "", TextCompare: false) != 0)
        {
            if (!abReleasing)
            {
                text += "[Comments Held]\r\n";
            }
            text += Strings.Trim(asNew);
            if (abReleasing)
            {
                text += CommentStampText(asUserId);
            }
        }
        return text + Strings.Trim(asOld);
    }

    public static string mStripPath(string asPath)
    {
        string result = asPath;
        checked
        {
            for (int i = Strings.Len(asPath); i >= 1; i += -1)
            {
                if ((Operators.CompareString(Strings.Mid(asPath, i, 1), "\\", TextCompare: false) == 0) | (Operators.CompareString(Strings.Mid(asPath, i, 1), "/", TextCompare: false) == 0) | (Operators.CompareString(Strings.Mid(asPath, i, 1), ":", TextCompare: false) == 0))
                {
                    result = Strings.Mid(asPath, i + 1, Strings.Len(asPath) - i);
                    break;
                }
            }
            return result;
        }
    }

    //public static void optimizeDisabledCombo(DropDownList cmb)
    //{
    //    if (cmb.Enabled)
    //    {
    //        return;
    //    }
    //    checked
    //    {
    //        for (int i = cmb.Items.Count - 1; i >= 0; i += -1)
    //        {
    //            if (cmb.SelectedIndex != i)
    //            {
    //                cmb.Items.RemoveAt(i);
    //            }
    //        }
    //    }
    //}

    public static bool isLocalhost_()
    {
        return false;
        //return Conversions.ToBoolean(Operators.OrObject(Operators.OrObject(Operators.OrObject(Operators.CompareString(Strings.LCase(HttpContext.Current.Request.ServerVariables["SERVER_NAME"]), "localhost", TextCompare: false) == 0, Operators.CompareObjectEqual(HttpContext.Current.Session["UserActual"], "ED", TextCompare: false)), Operators.CompareObjectEqual(HttpContext.Current.Session["UserActual"], "FRANK", TextCompare: false)), Operators.CompareObjectEqual(HttpContext.Current.Session["UserActual"], "MRANSOM", TextCompare: false)));
    }

    public static void SendEmail(string folderNumber, string body, string subject)
    {
        string text = modTRS.System_Configuration_ConfigurationSettings_AppSettings("SMTPServer");
        string address = "<EMAIL>";
        SmtpClient smtpClient;
        if (Operators.CompareString(text, "aws3", TextCompare: false) == 0)
        {
            int port = 587;
            string userName = "AKIAI72R2L5CURPOYNJQ";
            string password = "Arjo+GscvjJNzrpd9aA/+m007V0Yftlix/86ipBkofZe";
            text = "email-smtp.us-east-1.amazonaws.com";
            smtpClient = new SmtpClient(text, port);
            smtpClient.EnableSsl = true;
            smtpClient.UseDefaultCredentials = false;
            smtpClient.Credentials = new NetworkCredential(userName, password);
            address = "<EMAIL>";
        }
        else
        {
            if (isLocalhost_())
            {
                text = "********";
            }
            if (Operators.CompareString(text, "", TextCompare: false) == 0)
            {
                text = "smtp-amer1.ad.one-bcs.com";
            }
            smtpClient = new SmtpClient(text, 25);
        }
        try
        {
            MailMessage mailMessage = new MailMessage();
            mailMessage.IsBodyHtml = true;
            mailMessage.From = new MailAddress(address);
            mailMessage.To.Add("<EMAIL>");
            mailMessage.Subject = subject;
            mailMessage.Body = body;
            smtpClient.Send(mailMessage);
            //_ = null;
        }
        catch (Exception ex)
        {
            ProjectData.SetProjectError(ex);
            Exception ex2 = ex;
            ex2.ToString();
            ProjectData.ClearProjectError();
        }
    }
}
