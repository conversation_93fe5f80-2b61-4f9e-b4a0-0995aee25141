using CQR.Application.Dto;
using CQR.Application.Dto.CQRHead;
using CQR.Application.Dto.IHSFolder;
using CQR.Application.Queries;
using CQR.Domain.CQRHeaders;

namespace CQR.Application.Services;

public interface ICQRQueryService
{
    Task<CQRHeaderCollection?> GetCollectionAsync(int queueKey);
    Task<CQR_Header?> GetHeaderByKeyAsync(int queueKey);
    Task<List<CQR_Header>> GetHeadersAsync(CQRHeaderQueryRequest request);
    Task<SearchCQRResponse> SearchCQRAsync(SearchCQRQuery query);
    Task<IHSFolderCriteria> GetIHSFolderCriteriasAsync();
}