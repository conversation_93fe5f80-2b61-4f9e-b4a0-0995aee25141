import type { HttpResponseResult } from "@/store/types";
import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export const getOEMHiearchy = () => {
  const url = baseUrlApi("ERPMaster/OEMHiearchy");
  return http.request<HttpResponseResult>("get", url, {});
};

// export const getRoleUseCollection = () => {
//   const url = baseUrlApi("RoleUser/role/roleCollection");
//   return http.request<HttpResponseResult>("get", url, {});
// };

//todo  	Account Manager (AMGR):,Product Group Manager (PGM):,Estimator:,Product Dev. Mgr (PDM):
