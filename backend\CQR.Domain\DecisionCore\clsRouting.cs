﻿using Microsoft.Data.SqlClient;
using Microsoft.VisualBasic;


// decisionCore, Version=1.0.9056.27004, Culture=neutral, PublicKeyToken=null
// decisionCore.clsRouting
using Microsoft.VisualBasic.CompilerServices;

namespace CQR.Domain.DecisionCore;
public class clsRouting
{
    public class T_ROUTING
    {
        public string sQueueKey;

        public string sTask;

        public string sTaskAction;

        public string sTaskRouting;

        public string sAssigned;

        public string sAssignedDate;

        public string sAssignedTime;

        public string sDue;

        public string sResult;

        public string sDrawingNbr;

        public bool bCurrentInd;

        public bool bToBeViewedInd;

        public bool bDoneInd;

        public string sDoneDate;

        public string sDoneTime;

        public string sDoneUser;

        public int iSaveFlag;

        public bool bNote;

        public string AssignedDate()
        {
            if (Operators.CompareString(sAssignedDate, "", TextCompare: false) == 0)
            {
                return Conversions.ToString(DateAndTime.Now);
            }
            return modTools.mDateCygnetToScreen(sAssignedDate) + " " + Strings.Left(sAssignedTime, 2) + ":" + Strings.Mid(sAssignedTime, 3);
        }
    }

    public corePage tPage;

    private const int Save_Nothing = 0;

    private const int Save_Create = 1;

    private const int Save_Complete = 2;

    private const int Save_Changed = 4;

    private const int Save_NewUser = 8;

    private const int Save_Delete = 16;

    private const int Save_Reopen = 32;

    private const int Save_Terminate = 64;

    public int countRerouted;

    private string sType;

    private string sUser;

    private T_ROUTING[] tRtg;

    private int iRtgCount;

    public clsRouting()
    {
        //tPage = new corePage();
        countRerouted = 0;
    }

    public void mInitialize(string asType, string asUser)
    {
        sType = asType;
        sUser = asUser;
    }

    public void mLoadData(string asFolder, string asOrderBy = "")
    {
        string text = "SELECT * FROM ROUTING_RGHeader";
        text = text + " WHERE FolderNbr = " + Conversions.ToString(Conversion.Val(asFolder));
        text = text + " AND FolderType = '" + sType + "'";
        text += " ORDER BY ";
        text = ((Operators.CompareString(asOrderBy, "", TextCompare: false) != 0) ? (text + asOrderBy) : (text + "SortDate, SortTime, TaskCode, PartDwgNbr, ActionTaskCode, RoutingTaskCode"));
        SqlDataReader rst = tPage.cnExecute(text);
        iRtgCount = 0;
        checked
        {
            while (rst.Read())
            {
                iRtgCount++;
                ref T_ROUTING[] reference = ref tRtg;
                reference = (T_ROUTING[])Utils.CopyArray(reference, new T_ROUTING[iRtgCount + 1]);
                tRtg[iRtgCount - 1] = new T_ROUTING();
                T_ROUTING t_ROUTING = tRtg[iRtgCount - 1];
                t_ROUTING.sQueueKey = Conversions.ToString(rst["QueueKey"]);
                t_ROUTING.sTask = tPage.rstString(ref rst, "TaskCode");
                t_ROUTING.sTaskAction = tPage.rstString(ref rst, "ActionTaskCode");
                t_ROUTING.sTaskRouting = tPage.rstString(ref rst, "RoutingTaskCode");
                t_ROUTING.sAssigned = tPage.rstString(ref rst, "AssignedTo");
                t_ROUTING.sAssignedDate = tPage.rstString(ref rst, "AssignedDate");
                t_ROUTING.sAssignedTime = tPage.rstString(ref rst, "AsignedTime");
                t_ROUTING.sDue = tPage.rstString(ref rst, "DueDate");
                t_ROUTING.sResult = tPage.rstString(ref rst, "Result");
                t_ROUTING.bNote = Conversions.ToBoolean(Interaction.IIf(Operators.CompareString(tPage.rstString(ref rst, "NotificationInd"), "1", TextCompare: false) == 0, true, false));
                if (Operators.CompareString(Strings.UCase(Strings.Left(t_ROUTING.sResult, 3)), "ACK", TextCompare: false) == 0)
                {
                    t_ROUTING.sResult = "Acknowledged";
                }
                if (Operators.CompareString(Strings.UCase(Strings.Left(t_ROUTING.sResult, 3)), "TER", TextCompare: false) == 0)
                {
                    t_ROUTING.sResult = "Terminated";
                }
                t_ROUTING.sDrawingNbr = tPage.rstString(ref rst, "PartDwgNbr");
                t_ROUTING.bCurrentInd = Conversions.ToBoolean(Interaction.IIf(Operators.CompareString(tPage.rstString(ref rst, "CurrentTaskInd"), "1", TextCompare: false) == 0, true, false));
                t_ROUTING.bDoneInd = Conversions.ToBoolean(Interaction.IIf(Operators.CompareString(tPage.rstString(ref rst, "DoneInd"), "1", TextCompare: false) == 0, true, false));
                t_ROUTING.bToBeViewedInd = Conversions.ToBoolean(Interaction.IIf(Operators.CompareString(tPage.rstString(ref rst, "ToBeViewedInd"), "", TextCompare: false) == 0, true, false));
                t_ROUTING.sDoneDate = tPage.rstString(ref rst, "DoneDate");
                t_ROUTING.sDoneTime = tPage.rstString(ref rst, "DoneTm");
                t_ROUTING.sDoneUser = tPage.rstString(ref rst, "DoneByName");
                t_ROUTING = null;
            }
            text = "Update Routing_RGHeader Set ToBeViewedInd = 1 ";
            text = text + " WHERE FolderNbr_int = " + asFolder;
            text = text + " AND FolderType = '" + sType + "'";
            text = text + "   AND AssignedTo = '" + sUser + "'";
            tPage.cnExecuteNonQuery(text);
            tPage.cnClose();
            tPage.cnClose2();
        }
    }

    private void z_CompleteRecord(int aiIndex)
    {
        tRtg[aiIndex].sDoneUser = sUser;
        tRtg[aiIndex].sDoneTime = Strings.Format(DateAndTime.Now, "HHmm");
        tRtg[aiIndex].sDoneDate = modTools.mDateScreenToCygnet(Conversions.ToString(DateAndTime.Now));
        string text = "UPDATE ROUTING_RGHeader SET ";
        text += " DoneInd = '1', ";
        text = text + " DoneByName = '" + tRtg[aiIndex].sDoneUser + "', ";
        text = text + " DoneDate = '" + tRtg[aiIndex].sDoneDate + "', ";
        text = text + " DoneTm = '" + tRtg[aiIndex].sDoneTime + "', ";
        text = text + " Result = '" + Strings.Left(tRtg[aiIndex].sResult, 8) + "', ";
        text += " CurrentTaskInd = ''";
        text = text + " WHERE QueueKey = " + tRtg[aiIndex].sQueueKey;
        tPage.cnExecuteNonQuery(text);
    }

    private void z_UncompleteRecord(int aiIndex)
    {
        string text = "UPDATE ROUTING_RGHeader SET ";
        text += " DoneInd = '', ";
        text += " DoneByName = '', ";
        text += " DoneDate = '', ";
        text += " DoneTm = '', ";
        text += " Result = '', ";
        text += " CurrentTaskInd = '1', ";
        text += " EmailInd = '1'";
        text = text + " WHERE QueueKey = " + tRtg[aiIndex].sQueueKey;
        tPage.cnExecuteNonQuery(text);
    }

    public void mSaveDataTask(string asFolder, int taskIndex, string sDate = "", string sTime = "")
    {
        if (Operators.CompareString(sDate, "", TextCompare: false) == 0)
        {
            sDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
            sTime = Strings.Format(DateAndTime.Now, "HHmm");
        }
        if (taskIndex == -1)
        {
            taskIndex = checked(iRtgCount - 1);
        }
        T_ROUTING t_ROUTING = tRtg[taskIndex];
        if (((uint)t_ROUTING.iSaveFlag & (true ? 1u : 0u)) != 0)
        {
            string text = Strings.Right("**********" + Conversions.ToString(Conversions.ToLong(asFolder)), 10);
            string text2 = "INSERT INTO ROUTING_RGHeader";
            text2 += " (FolderType, FolderNbr, TaskCode, PartDwgNbr, ActionTaskCode, ";
            text2 += "  RoutingTaskCode, AssignedTo, SortDate, SortTime, AssignedDate, ";
            text2 += "  AsignedTime, DueDate, CurrentTaskInd, NotificationInd, EmailInd) ";
            text2 += " VALUES(";
            text2 = text2 + "'" + sType + "', ";
            text2 = text2 + "'" + text + "', ";
            text2 = text2 + "'" + t_ROUTING.sTask + "', ";
            text2 = text2 + "'" + t_ROUTING.sDrawingNbr + "', ";
            text2 = text2 + "'" + t_ROUTING.sTaskAction + "', ";
            text2 = text2 + "'" + t_ROUTING.sTaskRouting + "', ";
            text2 = text2 + "'" + t_ROUTING.sAssigned + "', ";
            text2 = ((!((Operators.CompareString(t_ROUTING.sDue, "", TextCompare: false) != 0) & Information.IsDate(modTools.mDateCygnetToScreen(t_ROUTING.sDue)))) ? (text2 + "'', ") : Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(text2 + "'", Interaction.IIf(Operators.CompareString(t_ROUTING.sDue, "", TextCompare: false) != 0, Strings.Format(Conversions.ToDate(modTools.mDateCygnetToScreen(t_ROUTING.sDue)), "yyyyMMdd"), sDate)), "', ")));
            text2 = Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(text2 + "'", Interaction.IIf(Operators.CompareString(t_ROUTING.sDue, "", TextCompare: false) != 0, "", sTime + Strings.Format(taskIndex, "00"))), "', "));
            text2 = text2 + "'" + sDate + "', ";
            text2 = text2 + "'" + sTime + "', ";
            text2 = text2 + "'" + t_ROUTING.sDue + "', ";
            text2 += "'1', ";
            text2 = Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(text2 + "'", Interaction.IIf(t_ROUTING.bNote, "1", "")), "', "));
            text2 += "'1') ";
            t_ROUTING.sQueueKey = Conversions.ToString(Conversion.Val(tPage.cnExecuteGetIdentity(text2)));
            if (((uint)t_ROUTING.iSaveFlag & 2u) != 0)
            {
                z_CompleteRecord(taskIndex);
                t_ROUTING.iSaveFlag = 0;
            }
        }
        if (((uint)t_ROUTING.iSaveFlag & 0x20u) != 0)
        {
            z_UncompleteRecord(taskIndex);
        }
        if (((uint)t_ROUTING.iSaveFlag & 0x40u) != 0)
        {
            z_CompleteRecord(taskIndex);
        }
        if (((uint)t_ROUTING.iSaveFlag & 0x10u) != 0)
        {
            string text2 = "DELETE FROM ROUTING_RGHeader ";
            text2 = text2 + " WHERE QueueKey = " + t_ROUTING.sQueueKey;
            tPage.cnExecuteNonQuery(text2);
        }
        if (((uint)t_ROUTING.iSaveFlag & 8u) != 0)
        {
            string text2 = "UPDATE FROM ROUTING_RGHeader SET AssignedTo = '" + t_ROUTING.sAssigned + "'";
            text2 = text2 + " WHERE QueueKey = " + t_ROUTING.sQueueKey;
            tPage.cnExecuteNonQuery(text2);
        }
        if (((uint)t_ROUTING.iSaveFlag & 4u) != 0)
        {
            string text2 = "UPDATE FROM ROUTING_RGHeader SET PartDwgNbr = '" + t_ROUTING.sDrawingNbr + "'";
            text2 = text2 + " WHERE QueueKey = " + t_ROUTING.sQueueKey;
            tPage.cnExecuteNonQuery(text2);
        }
        if (((uint)t_ROUTING.iSaveFlag & 2u) != 0)
        {
            z_CompleteRecord(taskIndex);
        }
        _ = t_ROUTING.iSaveFlag;
        _ = 0;
        t_ROUTING.iSaveFlag = 0;
        t_ROUTING = null;
    }

    public void mSaveData(string asFolder)
    {
        string sDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
        string sTime = Strings.Format(DateAndTime.Now, "HHmm");
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                mSaveDataTask(asFolder, i, sDate, sTime);
            }
        }
    }

    public int mTaskIndex(string asTask, string asAction, string asRtg, string asUser, string asDwg = "", bool abClosedOk = false)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskRouting, asRtg, TextCompare: false) == 0) & ((Operators.CompareString(tRtg[i].sAssigned, asUser, TextCompare: false) == 0) | (Operators.CompareString(asUser, "*", TextCompare: false) == 0)) & (abClosedOk | !tRtg[i].bDoneInd)) && ((Operators.CompareString(asDwg, "", TextCompare: false) == 0) | (Operators.CompareString(tRtg[i].sDrawingNbr, asDwg, TextCompare: false) == 0)))
                {
                    return i;
                }
            }
            return -1;
        }
    }

    public bool mHasToBeViewedTask(string asTask, string asAction, string asUserId)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0)) && ((Operators.CompareString(tRtg[i].sAssigned, asUserId, TextCompare: false) == 0) & tRtg[i].bToBeViewedInd & !tRtg[i].bDoneInd))
                {
                    return true;
                }
            }
            return false;
        }
    }

    public bool mHasClosedTask(string asTask, string asAction)
    {
        int num = checked(iRtgCount - 1);
        for (int i = 0; i <= num; i = checked(i + 1))
        {
            if (((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0)) && ((0u - ((Operators.CompareString(tRtg[i].sDoneUser, "", TextCompare: false) != 0) ? 1u : 0u)) | ((uint)tRtg[i].iSaveFlag & 2u)) != 0)
            {
                return true;
            }
        }
        bool result = default(bool);
        return result;
    }

    public bool mHasTask(string asTask, string asAction, string asDwgNbr = "", string asUser = "")
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (!((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0)))
                {
                    continue;
                }
                if (Operators.CompareString(asDwgNbr, "", TextCompare: false) == 0)
                {
                    if ((Operators.CompareString(tRtg[i].sAssigned, asUser, TextCompare: false) == 0) | (Operators.CompareString(asUser, "", TextCompare: false) == 0))
                    {
                        return true;
                    }
                }
                else if (Operators.CompareString(tRtg[i].sDrawingNbr, asDwgNbr, TextCompare: false) == 0)
                {
                    return true;
                }
            }
            bool result = default(bool);
            return result;
        }
    }

    public bool mHasCurrentTask(string asTask, string asAction, string asRtg, string asUser, string asDwgNbr = "xxx")
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                string right = Conversions.ToString(Interaction.IIf(Operators.CompareString(asDwgNbr, "xxx", TextCompare: false) == 0, tRtg[i].sDrawingNbr, asDwgNbr));
                if (((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskRouting, asRtg, TextCompare: false) == 0) & ((Operators.CompareString(tRtg[i].sAssigned, asUser, TextCompare: false) == 0) | (Operators.CompareString(asUser, "*", TextCompare: false) == 0)) & (Operators.CompareString(tRtg[i].sDrawingNbr, right, TextCompare: false) == 0)) && !tRtg[i].bDoneInd)
                {
                    return true;
                }
            }
            bool result = default(bool);
            return result;
        }
    }

    public void mReopenTask(string asTask, string asAction, string asRtg, string asOldUser, string asDwg = "")
    {
        int num = mTaskIndex(asTask, asAction, asRtg, asOldUser, asDwg, abClosedOk: true);
        if (num != -1)
        {
            tRtg[num].iSaveFlag = tRtg[num].iSaveFlag | 0x20;
            tRtg[num].bDoneInd = false;
            tRtg[num].sDoneDate = "";
            tRtg[num].sDoneTime = "";
            tRtg[num].sDoneUser = "";
            tRtg[num].sResult = "";
        }
    }

    public bool mRerouteTask(string asTask, string asAction, string asRtg, string asOldUser, string asNewUser, string asDwg = "")
    {
        bool result = false;
        int num = mTaskIndex(asTask, asAction, asRtg, asOldUser, asDwg);
        checked
        {
            if (num != -1 && !tRtg[num].bDoneInd)
            {
                mCloseTaskAt(num, "Rerouted");
                string sDue = tRtg[num].sDue;
                bool bNote = tRtg[num].bNote;
                if ((Operators.CompareString(asNewUser, "N/A", TextCompare: false) != 0) & (Operators.CompareString(asNewUser, "", TextCompare: false) != 0))
                {
                    mCreateTask(asTask, asAction, asRtg, asNewUser, sDue, bNote, asDwg);
                }
                result = true;
                countRerouted++;
            }
            return result;
        }
    }

    public void mDeleteTask(string asTask, string asAction, string asRtg, string asUser, string asDwg = "")
    {
        int num = mTaskIndex(asTask, asAction, asRtg, asUser, asDwg);
        if (num != -1)
        {
            tRtg[num].iSaveFlag = tRtg[num].iSaveFlag | 0x10;
        }
    }

    public bool mDeleteCurrentTask(string asTask, string asAction, string asRtg, string asUser, string asDwgNbr = "xxx")
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                string right = Conversions.ToString(Interaction.IIf(Operators.CompareString(asDwgNbr, "xxx", TextCompare: false) == 0, tRtg[i].sDrawingNbr, asDwgNbr));
                if (((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskRouting, asRtg, TextCompare: false) == 0) & ((Operators.CompareString(tRtg[i].sAssigned, asUser, TextCompare: false) == 0) | (Operators.CompareString(asUser, "*", TextCompare: false) == 0)) & (Operators.CompareString(tRtg[i].sDrawingNbr, right, TextCompare: false) == 0)) && !tRtg[i].bDoneInd)
                {
                    mDeleteTaskAt(i);
                }
            }
            bool result = default(bool);
            return result;
        }
    }

    public void mDeleteAllTasks(string asTask, string asAction, string asRtg, string asUser)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if ((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskRouting, asRtg, TextCompare: false) == 0) & ((Operators.CompareString(tRtg[i].sAssigned, asUser, TextCompare: false) == 0) | (Operators.CompareString(asUser, "*", TextCompare: false) == 0)))
                {
                    tRtg[i].iSaveFlag = tRtg[i].iSaveFlag | 0x10;
                }
            }
        }
    }

    public void mDeleteAllTasksForDrawing(string asDwg)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (Operators.CompareString(tRtg[i].sDrawingNbr, asDwg, TextCompare: false) == 0)
                {
                    tRtg[i].iSaveFlag = tRtg[i].iSaveFlag | 0x10;
                }
            }
        }
    }

    public void TerminateAllTasks(ref string[] sUserList, ref int iUserCount)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (tRtg[i].bCurrentInd & (Operators.CompareString(tRtg[i].sAssigned, "", TextCompare: false) != 0))
                {
                    tRtg[i].iSaveFlag = tRtg[i].iSaveFlag | 0x40;
                    tRtg[i].sResult = "Terminat";
                    AddUserToArray(tRtg[i].sAssigned, ref sUserList, ref iUserCount);
                }
            }
        }
    }

    public void mCloseTask(string asTask, string asAction, string asRtg, string asUser, string asResult, string sDwg = "")
    {
        mCloseTaskAt(mTaskIndex(asTask, asAction, asRtg, asUser, sDwg), asResult);
    }

    public void mCloseAllTasks(string asResult)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                mCloseTaskAt(i, asResult);
            }
        }
    }

    public void mCloseAllTasks(string asResult, ref string[] sUsers, ref int iUserCount)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (!tRtg[i].bDoneInd & (Operators.CompareString(tRtg[i].sAssigned, "", TextCompare: false) != 0))
                {
                    AddUserToArray(tRtg[i].sAssigned, ref sUsers, ref iUserCount);
                }
            }
            mCloseAllTasks(asResult);
        }
    }

    public void mCloseTaskAt(int aiIndex, string asResult)
    {
        if (((aiIndex >= 0) & (aiIndex < iRtgCount)) && (!tRtg[aiIndex].bDoneInd & (Operators.CompareString(tRtg[aiIndex].sAssigned, "", TextCompare: false) != 0)))
        {
            tRtg[aiIndex].bDoneInd = true;
            tRtg[aiIndex].sResult = asResult;
            tRtg[aiIndex].iSaveFlag = tRtg[aiIndex].iSaveFlag | 2;
        }
    }

    public bool AddUserArrayToArray(string[] asUserID, ref string[] asUserList, ref int aiUserCount)
    {
        int num = Information.UBound(asUserID);
        for (int i = 0; i <= num; i = checked(i + 1))
        {
            AddUserToArray(asUserID[i], ref asUserList, ref aiUserCount);
        }
        bool result = default(bool);
        return result;
    }

    public bool AddUserToArray(string asUserID, ref string[] asUserList, ref int aiUserCount)
    {
        bool result = false;
        checked
        {
            if (!((Operators.CompareString(Strings.Trim(asUserID), "", TextCompare: false) == 0) | (Operators.CompareString(Strings.Trim(asUserID), "N/A", TextCompare: false) == 0)))
            {
                int num = aiUserCount - 1;
                int num2 = 0;
                while (true)
                {
                    if (num2 <= num)
                    {
                        if (Operators.CompareString(Strings.Trim(Strings.LCase(asUserList[num2])), Strings.Trim(Strings.LCase(asUserID)), TextCompare: false) == 0)
                        {
                            break;
                        }
                        num2++;
                        continue;
                    }
                    aiUserCount++;
                    asUserList = (string[])Utils.CopyArray(asUserList, new string[aiUserCount + 1]);
                    asUserList[aiUserCount - 1] = asUserID;
                    result = true;
                    break;
                }
            }
            return result;
        }
    }

    public void AddRoleToArray(string asRole, ref string[] tArray, ref int iArrayCount, string asLocFilter = "")
    {
        //corePage corePage2 = new corePage();
        //string text = "SELECT UP.* FROM USERPROF_UserProfileHeader UP, USERPROF_UserRoles UR ";
        //text = text + " WHERE UP.QueueKey = UR.QueueKey AND UR.RoleCode = '" + asRole + "'";
        //text += "   AND ISNULL(Inactive,'0')<>'1'";
        //if (Operators.CompareString(asLocFilter, "", TextCompare: false) != 0)
        //{
        //    text = text + " AND LocationCode = '" + asLocFilter + "'";
        //}
        //SqlDataReader rst = corePage2.cnExecute(text);
        //while (rst.Read())
        //{
        //    AddUserToArray(corePage2.rstString(ref rst, "UserId"), ref tArray, ref iArrayCount);
        //}
        //rst.Close();
        //corePage2.cnClose();
    }

    public void mCreateTask(string asTask, string asAction, string asRtg, string asUser, string asDue = "", bool abNote = false, string asDwgNbr = "")
    {
        checked
        {
            if (Operators.CompareString(asUser, "N/A", TextCompare: false) != 0)
            {
                iRtgCount++;
                ref T_ROUTING[] reference = ref tRtg;
                reference = (T_ROUTING[])Utils.CopyArray(reference, new T_ROUTING[iRtgCount + 1]);
                tRtg[iRtgCount - 1] = new T_ROUTING();
                T_ROUTING obj = tRtg[iRtgCount - 1];
                obj.sTask = asTask;
                obj.sTaskAction = asAction;
                obj.sTaskRouting = asRtg;
                obj.sAssigned = asUser;
                obj.sDue = asDue;
                obj.sDrawingNbr = asDwgNbr;
                obj.bToBeViewedInd = true;
                obj.bNote = abNote;
                obj.iSaveFlag = 1;
                //_ = null;
            }
        }
    }

    public int mLoadGrid(corePage thisPage, coreTable acSheet, bool canImpersonate = false)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                T_ROUTING t_ROUTING = tRtg[i];
                //modTRS.T_TRSCODE t_TRSCODE = modTRS.mTRSGetCodeInfo("TASK" + sType, t_ROUTING.sTask + t_ROUTING.sTaskRouting);
                //string text = t_TRSCODE.sDesc;
                string text = "";
                if (Operators.CompareString(text, "", TextCompare: false) == 0)
                {
                    text = "Unknown Task: " + t_ROUTING.sTask + t_ROUTING.sTaskRouting;
                }
                if (Operators.CompareString(Strings.Left(text, 1), " ", TextCompare: false) == 0)
                {
                    text = Strings.Trim(text);
                    text = "&nbsp;&nbsp;&nbsp;&nbsp;" + text;
                }
                if (Operators.CompareString(Strings.UCase(Strings.Left(text, 3)), "NN:", TextCompare: false) == 0)
                {
                    text = "Notification: " + Strings.Mid(text, 4);
                }
                if (canImpersonate)
                {
                    text = text + " (" + tRtg[i].sTask + tRtg[i].sTaskAction + ")";
                }
                if (Operators.CompareString(t_ROUTING.sDrawingNbr, "", TextCompare: false) != 0)
                {
                    if ((Operators.CompareString(t_ROUTING.sTask, "24", TextCompare: false) == 0) & (Operators.CompareString(t_ROUTING.sTaskAction, "AA", TextCompare: false) == 0) & (Operators.CompareString(sType, "FR", TextCompare: false) == 0))
                    {
                        text = text + " - '" + mQafNameFromKey(t_ROUTING.sDrawingNbr) + "'";
                    }
                    if (Operators.CompareString(sType, "RC", TextCompare: false) == 0 && ((Operators.CompareString(t_ROUTING.sTask + t_ROUTING.sTaskAction, "04BA", TextCompare: false) == 0) | (Operators.CompareString(Strings.Trim(t_ROUTING.sTaskAction), "", TextCompare: false) == 0) | (Operators.CompareString(t_ROUTING.sTaskAction, t_ROUTING.sTaskRouting, TextCompare: false) == 0)))
                    {
                        text = text + " " + t_ROUTING.sDrawingNbr;
                    }
                    if ((Operators.CompareString(t_ROUTING.sTask, "10", TextCompare: false) == 0) & ((Operators.CompareString(t_ROUTING.sTaskAction, "AA", TextCompare: false) == 0) | (Operators.CompareString(t_ROUTING.sTaskAction, "BA", TextCompare: false) == 0)))
                    {
                        text = text + ", " + t_ROUTING.sDrawingNbr;
                    }
                }
                //if ((t_ROUTING.iSaveFlag & 0x10) == 0)
                //{
                //    TableRow tableRow = acSheet.AddRow();
                //    tableRow.ID = "routingRow_" + Conversions.ToString(i);
                //    tableRow.Attributes.Add("taskType", t_TRSCODE.sFormattedData);
                //    _ = null;
                //    acSheet.AddCell(text);
                //    if (Operators.CompareString(Strings.Left(t_ROUTING.sAssigned, 1), "*", TextCompare: false) == 0)
                //    {
                //        acSheet.AddCell(modTRS.mTRSGetSingleDesc("USERROLE", Strings.Mid(t_ROUTING.sAssigned, 2)));
                //    }
                //    else if (t_ROUTING.sAssigned.StartsWith("GPN_"))
                //    {
                //        string text2 = thisPage.cnExecuteForSingleValue("SELECT _desc FROM TRS_Header WHERE table_name='GPN_SALESEMAIL' AND table_entry='" + t_ROUTING.sAssigned + "'");
                //        acSheet.AddCell(Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject("<nobr>", Interaction.IIf(Operators.CompareString(text2, "", TextCompare: false) == 0, t_ROUTING.sAssigned, text2)), "</nobr>")));
                //    }
                //    else if (canImpersonate & !t_ROUTING.bNote & t_ROUTING.bCurrentInd)
                //    {
                //        acSheet.AddCell("<a href='javascript:goRouting(" + HttpContext.Current.Request["QueueKey"] + ",\"" + t_ROUTING.sAssigned + "\");'><nobr>" + modUserProf.UserNameFromId(t_ROUTING.sAssigned) + "</nobr></a>");
                //    }
                //    else
                //    {
                //        acSheet.AddCell("<nobr>" + modUserProf.UserNameFromId(t_ROUTING.sAssigned) + "</nobr>");
                //    }
                //    acSheet.AddCell(t_ROUTING.sResult);
                //    acSheet.AddCell(modTools.mDateCygnetToScreen(t_ROUTING.sDoneDate));
                //    acSheet.AddCell(Conversions.ToString(Interaction.IIf(Operators.CompareString(t_ROUTING.sDoneTime, "", TextCompare: false) == 0, "", Strings.Left(t_ROUTING.sDoneTime, 2) + ":" + Strings.Right(t_ROUTING.sDoneTime, 2))));
                //    acSheet.AddCell("<nobr>" + modUserProf.UserNameFromId(t_ROUTING.sDoneUser) + "</nobr>");
                //}
                t_ROUTING = null;
            }
            string text3 = "";
            text3 += "function goRouting(queueKey, assignedUser) {\r\n";
            text3 += "  if(document.getElementById(\"__EVENTTARGET\") != null)\r\n";
            text3 += "    document.getElementById(\"__EVENTTARGET\").value = \"routing\";\r\n";
            text3 += "  document.location = 'routing.aspx?QueueKey='+queueKey+'&Imp='+assignedUser;\r\n";
            text3 += "}\r\n";
            //thisPage.ClientScript.RegisterClientScriptBlock(tPage.GetType(), "goRouting", text3, addScriptTags: true);
            //if (iRtgCount == 0)
            //{
            //    acSheet.AddRow();
            //    acSheet.AddCellEx("No Routing records were located for this folder.", HorizontalAlign.Left, VerticalAlign.Middle, 20);
            //}
            int result = default(int);
            return result;
        }
    }

    public int mCount()
    {
        return iRtgCount;
    }

    public void mUncloseTaskAt(int aiIndex)
    {
        if ((aiIndex >= 0) | (aiIndex < iRtgCount))
        {
            tRtg[aiIndex].bDoneInd = false;
            tRtg[aiIndex].sResult = "";
            tRtg[aiIndex].iSaveFlag = tRtg[aiIndex].iSaveFlag & -3;
        }
    }

    public void mChangeTaskAt(int aiIndex, string asDwg)
    {
        if ((aiIndex >= 0) | (aiIndex < iRtgCount))
        {
            tRtg[aiIndex].sDrawingNbr = asDwg;
            tRtg[aiIndex].iSaveFlag = tRtg[aiIndex].iSaveFlag | 4;
        }
    }

    public void mDeleteTaskAt(int aiIndex)
    {
        if ((aiIndex >= 0) | (aiIndex < iRtgCount))
        {
            tRtg[aiIndex].iSaveFlag = tRtg[aiIndex].iSaveFlag | 0x10;
        }
    }

    public void mPNLTaskUpdate(string asQafKey)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if ((Operators.CompareString(tRtg[i].sTaskRouting, "AA", TextCompare: false) != 0) & (Operators.CompareString(tRtg[i].sDrawingNbr, Strings.Right("**********" + asQafKey, 10), TextCompare: false) == 0) & (Operators.CompareString(Strings.Left(Strings.UCase(tRtg[i].sResult), 3), "REJ", TextCompare: false) != 0))
                {
                    mDeleteTaskAt(i);
                }
            }
        }
    }

    public bool mPNLRGTaskComplete(string asTask, string asAction, string asRtg, string asUser, string asDwgNbr, string asResult)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskRouting, asRtg, TextCompare: false) == 0) & ((Operators.CompareString(tRtg[i].sAssigned, asUser, TextCompare: false) == 0) | (Operators.CompareString(asUser, "*", TextCompare: false) == 0)) & (Operators.CompareString(tRtg[i].sDrawingNbr, asDwgNbr, TextCompare: false) == 0)) && !tRtg[i].bDoneInd)
                {
                    tRtg[i].bDoneInd = true;
                    tRtg[i].sResult = asResult;
                    tRtg[i].iSaveFlag = tRtg[i].iSaveFlag | 2;
                }
            }
            bool result = default(bool);
            return result;
        }
    }

    public void mPNLRGDelete(bool isSales)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (isSales)
                {
                    if (Conversion.Val(tRtg[i].sTask) >= 28.0)
                    {
                        mDeleteTaskAt(i);
                    }
                    continue;
                }
                if ((Conversion.Val(tRtg[i].sTask) >= 23.0) & (Operators.CompareString(tRtg[i].sTaskAction, "N1", TextCompare: false) == 0))
                {
                    mDeleteTaskAt(i);
                }
                if ((Conversion.Val(tRtg[i].sTask) >= 24.0) & (Operators.CompareString(tRtg[i].sTaskAction, "N1", TextCompare: false) == 0))
                {
                    mDeleteTaskAt(i);
                }
            }
        }
    }

    public string mQafNameFromKey(string asKey)
    {
        //corePage obj = new corePage();
        //string result = obj.cnExecuteForSingleValue("SELECT PNL_Name FROM CQR_PNLFields WHERE PNL_AttachmentKey = " + asKey);
        //obj.cnClose();
        //return result;
        throw new NotImplementedException();
    }

    public int mGetTaskCount()
    {
        return iRtgCount;
    }

    public T_ROUTING mGetTaskInfo(int iIndex)
    {
        T_ROUTING result = null;
        if (!((iIndex < 0) & (iIndex >= iRtgCount)))
        {
            result = tRtg[iIndex];
        }
        return result;
    }

    public T_ROUTING mGetCurrentTaskInfo(string asTask, string asAction, string asRtg, string asUser, string asDwgNbr = "xxx")
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                string right = Conversions.ToString(Interaction.IIf(Operators.CompareString(asDwgNbr, "xxx", TextCompare: false) == 0, tRtg[i].sDrawingNbr, asDwgNbr));
                if (((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskRouting, asRtg, TextCompare: false) == 0) & ((Operators.CompareString(tRtg[i].sAssigned, asUser, TextCompare: false) == 0) | (Operators.CompareString(asUser, "*", TextCompare: false) == 0)) & (Operators.CompareString(tRtg[i].sDrawingNbr, right, TextCompare: false) == 0)) && !tRtg[i].bDoneInd)
                {
                    return tRtg[i];
                }
            }
            return null;
        }
    }

    public T_ROUTING mGetMostRecentTaskInfo(string asTask, string asAction, string asRtg, string asUser, string asDwgNbr = "xxx")
    {
        checked
        {
            for (int i = iRtgCount - 1; i >= 0; i += -1)
            {
                string right = Conversions.ToString(Interaction.IIf(Operators.CompareString(asDwgNbr, "xxx", TextCompare: false) == 0, tRtg[i].sDrawingNbr, asDwgNbr));
                if ((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskRouting, asRtg, TextCompare: false) == 0) & ((Operators.CompareString(tRtg[i].sAssigned, asUser, TextCompare: false) == 0) | (Operators.CompareString(asUser, "*", TextCompare: false) == 0)) & (Operators.CompareString(tRtg[i].sDrawingNbr, right, TextCompare: false) == 0))
                {
                    return tRtg[i];
                }
            }
            return null;
        }
    }

    public bool mHasTaskReturnPersonDate(string asTask, string asAction, string asDwgNbr, ref string asPerson, ref string asDate, bool abGetDonePerson, string asTaskClosedMethod = "*")
    {
        checked
        {
            int num = iRtgCount - 1;
            bool result = default(bool);
            for (int i = 0; i <= num; i++)
            {
                if (!((Operators.CompareString(tRtg[i].sTask, asTask, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, asAction, TextCompare: false) == 0)) || !((Operators.CompareString(asTaskClosedMethod, "*", TextCompare: false) == 0) | (Operators.CompareString(asTaskClosedMethod, tRtg[i].sResult, TextCompare: false) == 0)))
                {
                    continue;
                }
                if (Operators.CompareString(asDwgNbr, "", TextCompare: false) == 0)
                {
                    result = true;
                    asPerson = modUserProf.UserNameFromId(tRtg[i].sDoneUser);
                    asDate = modTools.mDateCygnetToScreen(tRtg[i].sDoneDate);
                    break;
                }
                if (Operators.CompareString(tRtg[i].sDrawingNbr, asDwgNbr, TextCompare: false) == 0)
                {
                    result = true;
                    if (abGetDonePerson)
                    {
                        asPerson = modUserProf.UserNameFromId(tRtg[i].sDoneUser);
                        asDate = modTools.mDateCygnetToScreen(tRtg[i].sDoneDate);
                    }
                    else
                    {
                        asPerson = modUserProf.UserNameFromId(tRtg[i].sAssigned);
                        asDate = modTools.mDateCygnetToScreen(tRtg[i].sAssignedDate);
                    }
                    break;
                }
            }
            return result;
        }
    }

    public void mNotifyRole(string sRole, string asTask, string asAction, string dwgNumber = "")
    {
        string[] tArray = null;
        int iArrayCount = default(int);
        AddRoleToArray(sRole, ref tArray, ref iArrayCount);
        mNotifyUserArray(tArray, iArrayCount, asTask, asAction, dwgNumber);
    }

    public void mNotifyUserArray(string[] sUsers, int iUserCount, string asTask, string asAction, string dwgNumber = "")
    {
        checked
        {
            int num = iUserCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (Operators.CompareString(sUsers[i], "", TextCompare: false) != 0)
                {
                    mCreateTask(asTask, asAction, asAction, sUsers[i], "", abNote: true, dwgNumber);
                }
            }
        }
    }

    public void mClearAllTasks()
    {
        iRtgCount = 0;
        tRtg = new T_ROUTING[1];
    }

    public void DeleteNotifications(string asFolder)
    {
        DateTime now = DateTime.Now;
        string text = now.ToString("yyyyMMdd");
        string text2 = now.ToString("HHmm");
        //corePage obj = new corePage();
        string text3 = "UPDATE ROUTING_RGHeader SET ";
        text3 = text3 + "Result = '" + Strings.Left("Acknowledged", 8) + "', ";
        text3 += "DoneInd = '1', ";
        text3 = text3 + "DoneDate = '" + text + "', ";
        text3 = text3 + "DoneTm = '" + text2 + "', ";
        text3 = text3 + "DoneByName = '" + sUser + "', ";
        text3 += "CurrentTaskInd = '0' ";
        text3 = text3 + " WHERE FolderType = '" + sType + "'";
        text3 = text3 + "   AND FolderNbr_int = " + asFolder;
        text3 += "   AND NotificationInd  = '1'";
        text3 = text3 + "   AND AssignedTo  = '" + sUser + "'";
        //obj.cnExecuteNonQuery(text3);
        //obj.cnClose();
        //obj.cnClose2();
    }

    public static string ToClientTime(DateTime dt)
    {
        //string text = Conversions.ToString(HttpContext.Current.Session["timezoneoffset"]);
        //if (text == null && HttpContext.Current.Request.Cookies["timezoneoffset"] != null)
        //{
        //    HttpContext.Current.Session["timezoneoffset"] = HttpContext.Current.Request.Cookies["timezoneoffset"].Value;
        //    text = Conversions.ToString(HttpContext.Current.Session["timezoneoffset"]);
        //}
        //checked
        //{
        //    if (text != null)
        //    {
        //        int num = (int)Math.Round(Conversion.Val(text));
        //        dt = dt.AddMinutes(-1 * num);
        //        return Conversions.ToString(dt);
        //    }
        //    return Conversions.ToString(dt.ToLocalTime());
        //}
        return "";
    }

    public string GetReleaseDate(string taskCode, string actionTaskCode)
    {
        checked
        {
            int num = iRtgCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (((Operators.CompareString(tRtg[i].sTask, taskCode, TextCompare: false) == 0) & (Operators.CompareString(tRtg[i].sTaskAction, actionTaskCode, TextCompare: false) == 0)) && Operators.CompareString(Strings.UCase(tRtg[i].sResult), "RELEASED", TextCompare: false) == 0)
                {
                    return tRtg[i].sDoneDate;
                }
            }
            return "";
        }
    }
}
