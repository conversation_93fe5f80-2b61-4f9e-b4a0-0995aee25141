﻿using CQRLIB.DTOs;
using System.Security.Claims;

namespace CQR.API.Utilities;

public static class HttpContextUserHelper
{
    public static UserDto GetHttpContextUserInfo(HttpContext context)
    {
        if (TryGetUserFromContext(context, out UserDto user))
        {
            // Use the 'user' object as needed
            string storedName = user.name;
            string storedEmail = user.email;

            // Now you have access to the properties of the UserLoginInfo object
            return user;
        }
        else
        {
            // Handle the case where the object is not found or not of the expected type
            return null; // or throw an exception, return a default value, etc.
        }
    }
    public static bool TryGetUserFromItems(HttpContext context, out UserDto user)
    {
        if (context.Items.TryGetValue("user", out var userObject)
            && userObject is UserDto retrievedUser)
        {
            user = retrievedUser;
            return true;
        }

        user = null;
        return false;
    }
    public static UserDto? GetUserFromItems(HttpContext context)
    {
        return TryGetUserFromItems(context, out var user) ? user : null;
    }
    /// <summary>
    /// 嘗試從 HttpContext 取得 UserDto。
    /// </summary>
    public static bool TryGetUserFromContext(HttpContext context, out UserDto user)
    {
        user = null;

        if (context?.User?.Identity?.IsAuthenticated != true)
            return false;

        var claims = context.User.Claims;

        var name = claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value;
        var email = claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value;

        if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(email))
            return false;

        user = new UserDto
        {
            name = name,
            email = email
        };
        return true;
    }

    /// <summary>
    /// 從 HttpContext 取得 UserDto，找不到回傳 null。
    /// </summary>
    public static UserDto GetUserFromHttpContext(HttpContext context)
    {
        if (TryGetUserFromContext(context, out UserDto user))
        {
            return user;
        }
        return null;
    }
}
