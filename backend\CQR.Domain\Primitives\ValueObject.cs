﻿namespace CQR.Domain.Primitives;

public class ValueObject : IEquatable<ValueObject>
{
    // 屬性只讀，透過建構子設定
    public string Property1 { get; }
    public int Property2 { get; }

    // 建構子初始化所有屬性，確保不可變
    public ValueObject(string property1, int property2)
    {
        Property1 = property1 ?? throw new ArgumentNullException(nameof(property1));
        Property2 = property2;
    }

    // 實作相等判斷：屬性全部相等即為相等
    public override bool Equals(object? obj)
    {
        return Equals(obj as ValueObject);
    }

    public bool Equals(ValueObject? other)
    {
        if (other == null) return false;
        return Property1 == other.Property1 &&
               Property2 == other.Property2;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Property1, Property2);
    }
}

