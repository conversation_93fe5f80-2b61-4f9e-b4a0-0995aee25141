﻿namespace CQR.Domain.DecisionCore;


// CQR, Version=1.0.9223.23847, Culture=neutral, PublicKeyToken=null
// CQR.clsCQR.PNLLineItem
public struct PNLLineItem
{
    public int iPNLLineItemId;

    public string sProduct;

    public string sSite;

    public string sAverageAnnualVolume;

    public string sSalesPriceInQuoted;

    public string sSalesBusinessUnit;

    public string sQuotedCurrency;

    public string sSalesPriceSOP;

    public string sAverageAnnualSales;

    public string sAverageAnnualDVP;

    public string sAverageAnnualMPBT;

    public string sEVAFullCost;

    public string sEVAIncremental;

    public string sAwardStatus;

    public string sAwardStatusDbs;

    public string sAwardStatusDate;

    public string sAwardStatusUser;

    public string sCreateDate;
}

