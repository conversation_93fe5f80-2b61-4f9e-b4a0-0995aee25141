using CQR.Application.Dto;
using CQR.Application.Dto.RoleUser;
using CQR.Application.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace CQRAPI.Controllers;

//[Route("/")]
[Route("api/[controller]")]
[ApiController]
public class UserAuthController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly IUserQueryRepository _userRepository;

    public UserAuthController(IConfiguration configuration, IUserQueryRepository userRepository)
    {
        //_configuration = configuration;
        _userRepository = userRepository;
    }

    [HttpGet]
    [Route("getUserRole/{role}")]
    public async Task<ActionResult<IEnumerable<UserFullNameDto>>> GetUsers(string role = "CQRHRCOST")
    {
        var SelectUserId = "JFILAK";
        var result = _userRepository.GetUsers(role, SelectUserId, "", "");
        return Ok(result);
    }

    /// <summary>
    /// 取得所有使用者及其角色（可依角色與地點過濾）
    /// </summary>
    /// <param name="roles">以逗號分隔的角色代碼</param>
    /// <param name="locations">以逗號分隔的地點代碼</param>
    [HttpGet("users-with-roles")]
    public async Task<ActionResult<Dictionary<string, List<UserRoleDto>>>> GetUsersWithRoles(
        [FromQuery] string? roles,
        [FromQuery] string? locations)
    {
        var rolesArray = !string.IsNullOrWhiteSpace(roles)
            ? roles.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
            : null;

        var locationsArray = !string.IsNullOrWhiteSpace(locations)
            ? locations.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
            : null;

        var result = await _userRepository.GetUsersWithRolesAsync(rolesArray, locationsArray);

        return result
      .GroupBy(item => item.Role)
      .ToDictionary(
          group => group.Key,
          group => group.ToList() // Use the actual items, not new empty ones
      );

        //return Ok(result);
    }

    //todo 
    //[HttpGet]
    //public ActionResult<dynamic> LoadUserRoles()
    //{
    //    return Ok($"Running....");
    //}
    //[HttpGet]
    //[Route("UseDatabase")]
    //public ActionResult<string> GetUseDatabase()
    //{
    //    var msgDB = ConfigUtil.GetUseDatabase(_configuration);
    //    return Ok($"Running....{msgDB}");
    //}

    //[HttpGet]
    //[Route("ASPNETCORE_ENVIRONMENT")]
    //public ActionResult<string> GetASPNETCORE_ENVIRONMENT()
    //{
    //    // 返回字符串响应
    //    var result = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

    //    return Ok($"ASPNETCORE_ENVIRONMENT:{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}");
    //}
    ////https://learn.microsoft.com/zh-tw/azure/active-directory-b2c/enable-authentication-web-api?tabs=csharpclient
    //[HttpGet]
    //[Route("Identity")]
    //public ActionResult GetUserIdentity()
    //{
    //    return Ok(new { name = User.Identity.Name });
    //}

    //[HttpGet]
    //[Route("/Home/Error")]
    //public IActionResult Error()
    //{
    //    return StatusCode(500); // 返回500状态码
    //}

}
