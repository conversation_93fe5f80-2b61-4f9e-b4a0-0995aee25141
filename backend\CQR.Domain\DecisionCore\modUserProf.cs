﻿using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.VisualBasic;
using System.Text;
using Microsoft.AspNetCore.Mvc.Rendering;
// decisionCore, Version=1.0.9056.27004, Culture=neutral, PublicKeyToken=null
// decisionCore.modUserProf
using System.Runtime.CompilerServices;
using Microsoft.VisualBasic.CompilerServices;

namespace CQR.Domain.DecisionCore;

public class modUserProf
{
    //private  clsUPCache 
    private readonly IHttpContextAccessor _httpContextAccessor;

    //public modUserProf(IHttpContextAccessor httpContextAccessor)
    //{
    //    _httpContextAccessor = httpContextAccessor;
    //}
    //private readonly IHttpContextAccessor _httpContextAccessor;

    //public UserService(IHttpContextAccessor httpContextAccessor)
    //{
    //    _httpContextAccessor = httpContextAccessor;
    //}

    //private readonly IHttpContextAccessor _httpContextAccessor;

    public modUserProf(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public string GetUserActual()
    {
        return _httpContextAccessor.HttpContext?.Session?.GetString("UserActual");
    }

    public const string UP_ConcernsLiaison = "CCLN";

    public const string UP_ConcernsQE = "CCQE";

    public const string UP_ConcernsQETech = "CCQT";

    public const string UP_QEManager = "QMGR";

    public const string UP_PDM = "PDM";

    private static clsUPCache[] tEntryCache = new clsUPCache[0];

    public static int iUserLocation = 0;

    public static string UserNTUserIdFromId(string asUserId)
    {
        string sName = "";
        string sCompany = "";
        string sLocation = "";
        string sDepartment = "";
        string sNTUserId = "";
        UserInfoFromId(asUserId, ref sName, ref sCompany, ref sLocation, ref sDepartment, ref sNTUserId);
        //UserInfoFromId(asUserId, ref sName, ref sCompany, ref sLocation, ref sDepartment, ref sNTUserId, HttpContext);
        return sNTUserId;
    }

    public static string UserNameFromId(string asUserId)
    {
        string sName = "";
        string sCompany = "";
        string sLocation = "";
        string sDepartment = "";
        string sNTUserId = "";
        UserInfoFromId(asUserId, ref sName, ref sCompany, ref sLocation, ref sDepartment, ref sNTUserId);
        //UserInfoFromId(asUserId, ref sName, ref sCompany, ref sLocation, ref sDepartment, ref sNTUserId, HttpContext);
        return sName ?? "";
    }

    public static string UserLocationFromId(string asUserId)
    {
        string sName = "";
        string sCompany = "";
        string sLocation = "";
        string sDepartment = "";
        string sNTUserId = "";
        UserInfoFromId(asUserId, ref sName, ref sCompany, ref sLocation, ref sDepartment, ref sNTUserId);
        //UserInfoFromId(asUserId, ref sName, ref sCompany, ref sLocation, ref sDepartment, ref sNTUserId, HttpContext);
        return sLocation;
    }

    public static string UserCompanyFromId(string asUserId)
    {
        string sName = "";
        string sCompany = "";
        string sLocation = "";
        string sDepartment = "";
        string sNTUserId = "";
        UserInfoFromId(asUserId, ref sName, ref sCompany, ref sLocation, ref sDepartment, ref sNTUserId);
        //UserInfoFromId(asUserId, ref sName, ref sCompany, ref sLocation, ref sDepartment, ref sNTUserId, HttpContext);
        return sCompany;
    }

    public static void GetUserNameFromUserID(string asUserId, ref string sFirstName, ref string sMiddleInitial, ref string sLastName)
    {
        string sName = "";
        string sCompany = "";
        string sLocation = "";
        string sDepartment = "";
        string sNTUserId = "";
        //UserInfoFromId(asUserId, ref sName, ref sCompany, ref sLocation, ref sDepartment, ref sNTUserId, HttpContext);
        int num = Information.UBound(tEntryCache);
        for (int i = 0; i <= num; i = checked(i + 1))
        {
            if (Operators.CompareString(tEntryCache[i].sUserId, asUserId, TextCompare: false) == 0)
            {
                sFirstName = tEntryCache[i].sFirstName;
                sLastName = tEntryCache[i].sLastName;
                sMiddleInitial = "";
            }
        }
    }

    public static void UserInfoFromId(string asUserId, ref string sName, ref string sCompany, ref string sLocation, ref string sDepartment, ref string sNTUserId)
    //public static void UserInfoFromId(string asUserId, ref string sName, ref string sCompany, ref string sLocation, ref string sDepartment, ref string sNTUserId, HttpContext httpContext)
    {
        string noCache = "";// _httpContextAccessor.HttpContext.Request.Query["NoCache"];
        //string noCache = _httpContextAccessor.HttpContext.Request.Query["NoCache"];

        //var context = _httpContextAccessor.HttpContext;
        if (Operators.CompareString(asUserId, "", TextCompare: false) == 0)
        {
            return;
        }
        if (Operators.CompareString(Strings.LCase(asUserId), "super", TextCompare: false) == 0)
        {
            sName = "Super User";
            sLocation = "";
            sCompany = "";
            sDepartment = "";
            sNTUserId = "";
            return;
        }
        if ((Operators.CompareString(noCache, "", TextCompare: false) != 0) | (tEntryCache == null))
        {
            tEntryCache = new clsUPCache[0];
        }
        checked
        {
            try
            {
                int num = Information.UBound(tEntryCache);
                for (int i = 0; i <= num; i++)
                {
                    if (Operators.CompareString(tEntryCache[i].sUserId, asUserId, TextCompare: false) == 0)
                    {
                        if (Operators.CompareString(tEntryCache[i].sLastName, "", TextCompare: false) == 0)
                        {
                            sName = tEntryCache[i].sUserId;
                        }
                        else
                        {
                            sName = Conversions.ToString(Operators.AddObject(tEntryCache[i].sLastName, Interaction.IIf(Operators.CompareString(tEntryCache[i].sFirstName, "", TextCompare: false) != 0, ", " + tEntryCache[i].sFirstName, "")));
                        }
                        sLocation = tEntryCache[i].sLocation;
                        sCompany = tEntryCache[i].sCompany;
                        sDepartment = tEntryCache[i].sDepartment;
                        sNTUserId = tEntryCache[i].sNTUserId;
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                ProjectData.SetProjectError(ex);
                Exception ex2 = ex;
                tEntryCache = new clsUPCache[0];
                ProjectData.ClearProjectError();
            }
            //corePage corePage2 = new corePage();
            string sSQL = "SELECT LastName, FirstName, CompanyCode, LocationCode, DepartmentCode, NTUserId FROM USERPROF_UserProfileHeader WHERE UserId = '" + Strings.Replace(asUserId, "'", "''") + "'";
            //SqlDataReader rst = corePage2.cnExecute(sSQL);
            //rst.Read();
            tEntryCache = (clsUPCache[])Utils.CopyArray(tEntryCache, new clsUPCache[Information.UBound(tEntryCache) + 1 + 1]);
            tEntryCache[Information.UBound(tEntryCache)] = new clsUPCache();
            clsUPCache clsUPCache = tEntryCache[Information.UBound(tEntryCache)];
            clsUPCache.sUserId = asUserId;
            //clsUPCache.sLastName = corePage2.rstString(ref rst, "LastName");
            //clsUPCache.sFirstName = corePage2.rstString(ref rst, "FirstName");
            //clsUPCache.sLocation = corePage2.rstString(ref rst, "LocationCode");
            //clsUPCache.sCompany = corePage2.rstString(ref rst, "CompanyCode");
            //clsUPCache.sDepartment = corePage2.rstString(ref rst, "DepartmentCode");
            //clsUPCache.sNTUserId = corePage2.rstString(ref rst, "NTUserId");
            if (Operators.CompareString(clsUPCache.sLastName, "", TextCompare: false) == 0)
            {
                clsUPCache.sLastName = asUserId;
            }
            sName = Conversions.ToString(Operators.AddObject(clsUPCache.sLastName, Interaction.IIf(Operators.CompareString(clsUPCache.sFirstName, "", TextCompare: false) != 0, ", " + clsUPCache.sFirstName, "")));
            sLocation = clsUPCache.sLocation;
            sCompany = clsUPCache.sCompany;
            sDepartment = clsUPCache.sDepartment;
            sNTUserId = clsUPCache.sNTUserId;
            clsUPCache = null;
            //rst.Close();
            //corePage2.cnClose();
        }
    }

    public static string getSessionUser(corePage tPage, string applicationCode, HttpRequest theRequest, bool bAllowLocation = false)
    {
        string UserActual = "";
        //CONTEX
        //var context = _httpContextAccessor.HttpContext;
        //var context = _httpContextAccessor.HttpContext;
        //var session = context.Session;
        string LOGON_USER = "?";
        string text2 = LOGON_USER;
        checked
        {
            string text;
            if (Operators.ConditionalCompareObjectNotEqual(UserActual, "", TextCompare: false))
            //if (Operators.ConditionalCompareObjectNotEqual(context.Session["UserActual"], "", TextCompare: false))
            {
                text = Conversions.ToString(UserActual);
            }
            else
            {
                string sUserId = "";
                //HttpContext current = HttpContext.Current;
                if (Operators.ConditionalCompareObjectEqual(UserActual, "", TextCompare: false))
                {
                    //string text2 = current.Request.ServerVariables["LOGON_USER"];
                    if (Strings.LCase(text2).Contains("expertpdf"))
                    {
                        text2 = "ddc";
                    }
                    if (Strings.InStrRev(text2, "\\") > 0)
                    {
                        text2 = Strings.Mid(text2, Strings.InStrRev(text2, "\\") + 1);
                    }
                    //corePage corePage2 = new corePage();
                    if (Operators.CompareString(tPage.dbConnNonQuery.ConnectionString, "", TextCompare: false) != 0)
                    {
                        //corePage2.dbConnNonQuery.ConnectionString = tPage.dbConnNonQuery.ConnectionString;
                        //corePage2.dbConnNonQuery.Open();
                    }
                    //if (!UserProfTranslateUserId(corePage2, Strings.UCase(text2), ref sUserId))
                    //{
                    //    corePage2.cnClose2();
                    //    corePage2.cnClose();
                    //    //AccessDeniedMessage();
                    //    throw new UnauthorizedAccessException("Access Denied");
                    //}
                    //corePage2.cnClose2();
                    //corePage2.cnClose();
                    if (Operators.CompareString(tPage.dbConnNonQuery.ConnectionString, "", TextCompare: false) != 0)
                    {
                        //corePage2.dbConnNonQuery.ConnectionString = tPage.dbConnNonQuery.ConnectionString;
                        //corePage2.dbConnNonQuery.Open();
                    }
                    //iUserLocation = (int)Math.Round(Conversion.Val(corePage2.cnExecuteForSingleValue("SELECT LocationCode FROM USERPROF_UserProfileHeader WHERE UserId='" + sUserId + "'")));
                    //corePage2.cnClose2();
                    //corePage2.cnClose();
                    if (!bAllowLocation & isLocation18(iUserLocation))
                    {
                        //AccessDeniedMessage();
                        throw new UnauthorizedAccessException("Access Denied");
                    }
                    //session.Session["UserActual"] = sUserId;
                    //current.Session["UserActualLocation"] = iUserLocation;
                }
                //text = Conversions.ToString(current.Session["UserActual"]);
                text = "";
                string text3 = "UNKNOWN";
                try
                {
                    //text3 = theRequest.ServerVariables["REMOTE_ADDR"];
                }
                catch (Exception projectError)
                {
                    ProjectData.SetProjectError(projectError);
                    ProjectData.ClearProjectError();
                }
                //tPage.cnExecuteNonQuery("INSERT INTO sys_UserAccess (UserId,Application,Page,IPAddress) VALUES((SELECT QueueKey FROM USERPROF_UserProfileHeader WHERE UserId='" + text + "'),'" + applicationCode + "','" + theRequest.ServerVariables["PATH_TRANSLATED"] + "','" + text3 + "')");
                tPage.cnClose();
                //current = null;
                tPage.cnClose();
                tPage.cnClose2();
            }
            return text;
        }
    }

    public string SystemName()
    {
        var request = _httpContextAccessor.HttpContext.Request;
        // 使用 Request.Path 來模擬取得 WebForm 中的 "APPL_PHYSICAL_PATH"
        var physicalPath = request.Path.Value;


        //if (Strings.LCase(_httpContextAccessor.HttpContext.GetServerVariable"APPL_PHYSICAL_PATH"]).Contains("capture"))
        if (physicalPath != null && physicalPath.ToLower().Contains("capture"))
        {
            return "Value Capture Application";
        }
        //if (Operators.CompareString(Strings.LCase(MyProject.Computer.Name), "vlebtn-ereq", TextCompare: false) == 0)
        if (string.Equals(System.Environment.MachineName, "vlebtn-ereq", StringComparison.OrdinalIgnoreCase))

        {
            return "Lebanon eREQ";
        }
        return "BCS Total Connect Applications - Change Management System";
    }

    public static string AccessDeniedEmail()
    {
        return modTRS.mTRSGetSingleDesc("ACCDENIED", "EmailContact");
    }

    //public static void AccessDeniedMessageNonEngineering()
    //{
    //    if (!modTRS.isDecisionDesignUser(Conversions.ToString(HttpContext.Current.Session["userActual"])))
    //    {
    //        HttpContext.Current.Response.Redirect("../_coreForm/TotalConnect Non-Engineering Access Denied page.htm");
    //    }
    //}

    //public static bool IsEngineeringApplicationEx(string theApp)
    //{
    //    return Strings.LCase(HttpContext.Current.Request.ApplicationPath).Contains(Strings.LCase(theApp));
    //}

    public bool IsEngineeringApplication()
    {
        if (IsEngineeringApplicationEx("B2BMetrics"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("CommercialIssues"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("CQR"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("Deviation"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("eBOMEditor"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("GMAMTC"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("GMASR"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("NCRENG"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("NCRMFG"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("PartNumbering"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("PDM"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("PDMSRC"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("RCTECO"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("SAPBrowse"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("ToolId"))
        {
            return true;
        }
        if (IsEngineeringApplicationEx("TotalConnectAdmin"))
        {
            return true;
        }
        return false;
    }
    //public  bool IsEngineeringApplicationEx(string theApp)
    //{
    //    return Strings.LCase(_httpContextAccessor.HttpContext.Request.ApplicationPath).Contains(Strings.LCase(theApp));
    //}
    public bool IsEngineeringApplicationEx(string theApp)
    {
        return _httpContextAccessor.HttpContext.Request.Path.Value.ToLower().Contains(theApp.ToLower());
    }


    //public static void AccessDeniedMessage()
    //{
    //    if (!modTRS.isDecisionDesignUser(Conversions.ToString(HttpContext.Current.Session["userActual"])))
    //    {
    //        if (!IsEngineeringApplication())
    //        {
    //            AccessDeniedMessageNonEngineering();
    //        }
    //        HttpContext current = HttpContext.Current;
    //        string text = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
    //        string text2 = "<a href=\"mailto:;?Subject=Total Connect Access - Manager Approval Request&Body=First Name:%0dLast Name:%0dLocation:%0dActive Directory UserID: " + current.Request.ServerVariables["LOGON_USER"] + "%0de-mail address:%0dRole – Department:%0d%0dForward manager approval to: " + AccessDeniedEmail() + "\">email approval</a>";
    //        current.Response.Write("SERVER:" + HttpContext.Current.Request.ServerVariables["APPL_PHYSICAL_PATH"] + "<BR>");
    //        current.Response.Write("USER: " + HttpContext.Current.Request.ServerVariables["LOGON_USER"] + "<BR>");
    //        current.Response.Write("<b>Access Denied</b>:  " + SystemName() + "<BR><BR>");
    //        current.Response.Write("If you require access to the system, <b><u>send an e-mail to your manager to obtain email approval</b></u><BR><BR>");
    //        current.Response.Write("Once that is received, provide that approval <b><u>and</b></u> the following information below to: " + AccessDeniedEmail() + "<br><br>");
    //        current.Response.Write(text + "First Name:<BR>");
    //        current.Response.Write(text + "Last Name:<BR>");
    //        current.Response.Write(text + "Location:<BR>");
    //        current.Response.Write(text + "Active Directory UserID (Used to Login to your PC):<BR>");
    //        current.Response.Write(text + "e-mail address:<BR>");
    //        current.Response.Write(text + "Role - Department:<BR><BR>");
    //        current.Response.Write(text + "User with Similar Role:<BR><BR>");
    //        current.Response.Write("<b>**<u>Note</u>**</b>:  Access can only be granted <u>after</u> you provide an <u>" + text2 + "</u> from your manager.<BR><BR>");
    //        current.Response.End();
    //        current = null;
    //    }
    //}

    public static bool UserProfTranslateUserId(corePage tPage, string Value, ref string sUserId)
    {
        sUserId = Strings.Mid(Strings.UCase(Value), checked(Strings.InStrRev(Value, "\\") + 1));
        string text = "SELECT UserId FROM USERPROF_UserProfileHeader WITH (NOLOCK) ";
        text = text + " WHERE NTUserId = '" + sUserId + "'";
        text += "   AND ISNULL(Inactive,'0') <> '1'";
        string text2 = "";//tPage.cnExecuteForSingleValue(text);
        bool result;
        if (Operators.CompareString(text2, "", TextCompare: false) != 0)
        {
            sUserId = text2;
            result = true;
        }
        else
        {
            result = false;
        }
        if ((Operators.CompareString(Strings.UCase(sUserId), "ED", TextCompare: false) == 0) | (Operators.CompareString(Strings.UCase(sUserId), "FRANK", TextCompare: false) == 0))
        {
            result = true;
        }
        return result;
    }

    public static string mUPGetManager(string asRole)
    {
        //corePage obj = new corePage();
        string text = "SELECT HDR.UserID";
        text += " FROM USERPROF_UserProfileHeader HDR INNER JOIN";
        text += " USERPROF_UserRoles RLS ON HDR.QueueKey = RLS.QueueKey";
        text = text + " WHERE RLS.RoleCode = '" + asRole + "'";
        text += " AND RLS.PrimaryInd = 'Y'";
        //string text2 = obj.cnExecuteForSingleValue(text);
        //string result = Conversions.ToString(Interaction.IIf(Operators.CompareString(Strings.Trim(text2), "", TextCompare: false) == 0, "MGR_" + asRole, text2));
        //obj.cnClose();
        //obj.cnClose2();
        string result = "";
        return result;
    }

    public static bool mUserInactive(string asUser, string roleCode)
    {
        if (Operators.CompareString(asUser, "", TextCompare: false) == 0)
        {
            return false;
        }
        if (!mUserHasRole(asUser, roleCode))
        {
            return true;
        }
        //corePage obj = new corePage();
        string sSQL = "SELECT Inactive FROM USERPROF_UserProfileHeader WHERE UserId='" + asUser + "'";
        //return Conversions.ToBoolean(Interaction.IIf(Conversion.Val(obj.cnExecuteForSingleValue(sSQL)) == 0.0, false, true));
        //var result = Conversions.ToBoolean(Interaction.IIf(Conversion.Val(obj.cnExecuteForSingleValue(sSQL)) == 0.0, false, true));
        var result = false;
        return result;
    }

    public static bool mUserHasRole(string asUser, string asRole)
    {
        //corePage obj = new corePage();
        string text = "SELECT HDR.UserID";
        text += " FROM USERPROF_UserProfileHeader HDR INNER JOIN";
        text += " USERPROF_UserRoles RLS ON HDR.QueueKey = RLS.QueueKey";
        text = text + " WHERE RLS.RoleCode IN ('" + asRole + "')";
        text = text + " AND HDR.UserID = '" + asUser + " '";
        //bool result = Conversions.ToBoolean(Interaction.IIf(Operators.CompareString(obj.cnExecuteForSingleValue(text), "", TextCompare: false) == 0, false, true));
        //obj.cnClose();
        //obj.cnClose2();
        bool result = false;
        return result;
    }

    //public static void mUPPopulateControl(string asRole, DropDownList acControl, string asSelectCode = "", bool abClearBox = false, string asLocationFilter = "", string asTopEntryText = "", string asValueField = "UserId")
    //{
    //    mUPPopulateControl_(asRole, acControl, asSelectCode, abClearBox, asLocationFilter, asTopEntryText, asValueField);
    //}

    public static void mUPPopulateControl_(string asRole, object acControl, string asSelectCode = "", bool abClearBox = false, string asLocationFilter = "", string asTopEntryText = "", string asValueField = "UserId")
    {
        //corePage corePage2 = new corePage();
        if (abClearBox)
        {
            NewLateBinding.LateCall(NewLateBinding.LateGet(acControl, null, "Items", new object[0], null, null, null), null, "Clear", new object[0], null, null, null, IgnoreReturn: true);
        }
        string text = " WHERE ";
        if (Operators.CompareString(asSelectCode, "", TextCompare: false) != 0)
        {
            text = text + " UserId = '" + asSelectCode + "' OR ";
        }
        text += " (ISNULL(Inactive,0) = 0 ";
        checked
        {
            int i;
            if (Operators.CompareString(asRole, "", TextCompare: false) != 0)
            {
                string[] array = Strings.Split(asRole, ",");
                text += " AND RoleCode IN (";
                int num = Information.UBound(array);
                for (i = 0; i <= num; i++)
                {
                    text = Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(text, Interaction.IIf(i == 0, "", ", ")), "'"), array[i]), "'"));
                }
                text += ")";
            }
            if (Operators.CompareString(asLocationFilter, "", TextCompare: false) != 0)
            {
                text += " AND ";
                string[] array = Strings.Split(asLocationFilter, ",");
                text += "LocationCode IN (";
                int num2 = Information.UBound(array);
                for (i = 0; i <= num2; i++)
                {
                    text = Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(text, Interaction.IIf(i == 0, "", ", ")), "'"), array[i]), "'"));
                }
                text += ")";
            }
            text += ") ";
            if (Operators.CompareString(Strings.Left(asTopEntryText, 2), "OR", TextCompare: false) == 0)
            {
                text += asTopEntryText;
                asTopEntryText = "";
            }
            string text2 = "SELECT DISTINCT LastName+', '+FirstName AS FullName, UPPER(" + asValueField + ") AS UserId ";
            text2 += " FROM USERPROF_UserProfileHeader UP ";
            if (Operators.CompareString(asRole, "", TextCompare: false) != 0)
            {
                text2 += " INNER JOIN USERPROF_UserRoles UR ON UP.QueueKey = UR.QueueKey";
            }
            text2 = text2 + text + " ORDER BY LastName+', '+FirstName";
            SqlDataReader sqlDataReader = null;// corePage2.cnExecute(text2);
            NewLateBinding.LateSet(acControl, null, "DataSource", new object[1] { sqlDataReader }, null, null);
            NewLateBinding.LateSet(acControl, null, "DataValueField", new object[1] { "UserId" }, null, null);
            NewLateBinding.LateSet(acControl, null, "DataTextField", new object[1] { "FullName" }, null, null);
            NewLateBinding.LateCall(acControl, null, "DataBind", new object[0], null, null, null, IgnoreReturn: true);
            if (Operators.CompareString(asTopEntryText, "xxx", TextCompare: false) != 0)
            {
                //NewLateBinding.LateCall(NewLateBinding.LateGet(acControl, null, "Items", new object[0], null, null, null), null, "Insert", new object[2]
                //{
                //    0,
                //    //new ListItem(asTopEntryText, "")
                //}, null, null, null, IgnoreReturn: true);
            }
            //sqlDataReader.Close();
            //corePage2.cnClose();//
            int num3 = Conversions.ToInteger(Operators.SubtractObject(NewLateBinding.LateGet(NewLateBinding.LateGet(acControl, null, "Items", new object[0], null, null, null), null, "Count", new object[0], null, null, null), 1));
            bool flag = default(bool);
            for (i = 0; i <= num3; i++)
            {
                Type typeFromHandle = typeof(Strings);
                object[] array2 = new object[1];
                object[] array3;
                bool[] array4;
                object obj = NewLateBinding.LateGet(acControl, null, "Items", array3 = new object[1] { i }, null, null, array4 = new bool[1] { true });
                if (array4[0])
                {
                    i = (int)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array3[0]), typeof(int));
                }
                object instance = obj;
                array2[0] = NewLateBinding.LateGet(obj, null, "Value", new object[0], null, null, null);
                object[] array5 = array2;
                bool[] array6;
                object left = NewLateBinding.LateGet(null, typeFromHandle, "UCase", array2, null, null, array6 = new bool[1] { true });
                if (array6[0])
                {
                    NewLateBinding.LateSetComplex(instance, null, "Value", new object[1] { array5[0] }, null, null, OptimisticSet: true, RValueBase: true);
                }
                if (Operators.ConditionalCompareObjectEqual(left, Strings.UCase(asSelectCode), TextCompare: false))
                {
                    NewLateBinding.LateSet(acControl, null, "SelectedIndex", new object[1] { i }, null, null);
                    flag = true;
                    break;
                }
            }
            if (!flag & (Operators.CompareString(asSelectCode, "", TextCompare: false) != 0))
            {
                //NewLateBinding.LateCall(NewLateBinding.LateGet(acControl, null, "Items", new object[0], null, null, null), null, "Add", new object[1]
                //{
                //    //new ListItem(UserNameFromId(asSelectCode), asSelectCode)
                //}, null, null, null, IgnoreReturn: true);
                NewLateBinding.LateSet(acControl, null, "SelectedIndex", new object[1] { i }, null, null);
            }
            //modTRS.AddTip(RuntimeHelpers.GetObjectValue(acControl), "ROLE: " + asRole);
        }
    }

    public static void mUPPopulateControlDistinct(string asTable, string asField, string asSelectCode = "")
    //public static void mUPPopulateControlDistinct(DropDownList acControl, string asTable, string asField, string asSelectCode = "")
    {
        int num = 0;
        //corePage corePage2 = new corePage();
        string text = "SELECT DISTINCT " + asField + ", UP.LastName+', '+UP.Firstname AS UserName";
        text = text + " FROM " + asTable + " INNER JOIN USERPROF_UserProfileHeader UP on UP.UserId = " + asField;
        text += " ORDER BY UP.LastName+', '+UP.Firstname DESC";
        SqlDataReader rst = null;//corePage2.cnExecute(text);
        while (rst.Read())
        {
            //acControl.Items.Add(new ListItem(corePage2.rstString(ref rst, "UserName"), corePage2.rstString(ref rst, asField)));
        }
        //acControl.Items.Insert(0, new ListItem("*", ""));
        //rst.Close();
        //corePage2.cnClose();
        checked
        {
            //int num = acControl.Items.Count - 1;
            for (int i = 0; i <= num; i++)
            {
                //if (Operators.CompareString(acControl.Items[i].Value, asSelectCode, TextCompare: false) == 0)
                //{
                //    acControl.Items[i].Selected = true;
                //    break;
                //}
            }
        }
    }

    public static bool mUserHasCommonRole(string asCurUser, string asWithUser)
    {
        string text = "";
        //corePage corePage2 = new corePage();
        bool result = true;
        if (Operators.CompareString(asWithUser, asCurUser, TextCompare: false) != 0)
        {
            result = false;
            string text2 = "SELECT RoleCode FROM USERPROF_UserProfileHeader UH, USERPROF_UserRoles UR";
            text2 += " WHERE UH.QueueKey = UR.QueueKey";
            text2 += " AND UH.UserId = '";
            SqlDataReader rst = null;//corePage2.cnExecute(text2 + asWithUser + "'");
            //SqlDataReader rst = corePage2.cnExecute(text2 + asWithUser + "'");
            while (rst.Read())
            {
                //if (Operators.CompareString(corePage2.rstString(ref rst, "RoleCode"), "MAIL", TextCompare: false) != 0)
                //{
                //    //text = text + corePage2.rstString(ref rst, "RoleCode") + ",";
                //}
            }
            rst.Close();
            if (Operators.CompareString(text, "", TextCompare: false) == 0)
            {
                //corePage2.cnClose();
                result = false;
            }
            else
            {
                //rst = corePage2.cnExecute(text2 + asCurUser + "'");
                while (rst.Read())
                {
                    //if (Strings.InStr(text, corePage2.rstString(ref rst, "RoleCode")) > 0)
                    //{
                    //    result = true;
                    //    break;
                    //}
                }
                //rst.Close();
                //corePage2.cnClose();
            }
        }
        return result;
    }

    public static string mGetNTName(string asUserFirstName, string asUserLastName)
    {
        //corePage obj = new corePage();
        string text = "SELECT HDR.UserID";
        text += " FROM USERPROF_UserProfileHeader HDR";
        text = text + " WHERE HDR.FirstName = '" + asUserFirstName + "'";
        text = text + " AND HDR.LastName = '" + asUserLastName + "'";
        //string result = obj.cnExecuteForSingleValue(text);
        string result = "";
        //obj.cnClose();
        //obj.cnClose2();
        return result;
    }

    public static bool isLocation18(int iLoc)
    {
        return false;
    }

    //public bool isLocation18()
    //{
    //    return isLocation18(getCurrentUserLocation());
    //}

  

    //public static int getCurrentUserLocation()
    //{
    //    checked
    //    {
    //        if (Operators.ConditionalCompareObjectNotEqual(Operators.ConcatenateObject( HttpContext.Current.Session["UserImpersonateLocation"], ""), "", TextCompare: false))
    //        {
    //            return (int)Math.Round(Conversion.Val(RuntimeHelpers.GetObjectValue(HttpContext.Current.Session["UserImpersonateLocation"])));
    //        }
    //        return (int)Math.Round(Conversion.Val(RuntimeHelpers.GetObjectValue(HttpContext.Current.Session["UserActualLocation"])));
    //    }
    //}

    public int GetCurrentUserLocation(IHttpContextAccessor httpContextAccessor)
    {
        var session = httpContextAccessor.HttpContext.Session;

        // 如果 Session 中的 "UserImpersonateLocation" 不為空
        if (!string.IsNullOrEmpty(session.GetString("UserImpersonateLocation")))
        {
            // 返回 "UserImpersonateLocation" 的數值
            return (int)Math.Round(double.Parse(session.GetString("UserImpersonateLocation")));
        }

        // 如果 "UserImpersonateLocation" 為空，則返回 "UserActualLocation" 的數值
        return (int)Math.Round(double.Parse(session.GetString("UserActualLocation")));
    }

    public static void PopulateDistinctUser( string selectedValue, string tableName, string fieldToUseForJoin, string fieldToUseForValue = "", bool addBlank = true)
    //public static void PopulateDistinctUser(DropDownList dd, string selectedValue, string tableName, string fieldToUseForJoin, string fieldToUseForValue = "", bool addBlank = true)
    {

        var dd = new List<SelectListItem>();
        if (Operators.CompareString(fieldToUseForValue, "", TextCompare: false) == 0)
        {
            fieldToUseForValue = fieldToUseForJoin;
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.AppendLine("SELECT DISTINCT " + fieldToUseForValue + " AS FieldValue, ISNULL(LastName+', '+FirstName," + fieldToUseForValue + ") AS FieldText, LastName");
        stringBuilder.AppendLine(" FROM " + tableName);
        if (Operators.CompareString(fieldToUseForJoin, "ContactPerson", TextCompare: false) == 0)
        {
            stringBuilder.AppendLine(" LEFT JOIN USERPROF_UserProfileHeader UP ON " + fieldToUseForJoin + " LIKE '%@!@'+UserId+'@!@%'");
        }
        else
        {
            stringBuilder.AppendLine(" LEFT JOIN USERPROF_UserProfileHeader UP ON " + fieldToUseForJoin + "=UserId");
        }
        stringBuilder.AppendLine(" WHERE ISNULL(" + fieldToUseForValue + ",'') <> ''");
        stringBuilder.AppendLine(" ORDER BY ISNULL(LastName+', '+FirstName," + fieldToUseForValue + ")");
        //corePage corePage2 = new corePage();
        SqlDataReader sqlDataReader = null;//corePage2.cnExecute(stringBuilder.ToString());
        if (sqlDataReader != null)
        {
            //dd.DataSource = sqlDataReader;
            //dd.DataValueField = "FieldValue";
            //dd.DataTextField = "FieldText";
            //dd.DataBind();
            if (addBlank)
            {
                //dd.Items.Insert(0, new ListItem(""));
            }
            try
            {
                //dd.SelectedValue = selectedValue;
            }
            catch (Exception ex)
            {
                ProjectData.SetProjectError(ex);
                Exception ex2 = ex;
                ProjectData.ClearProjectError();
            }
            //if (Operators.CompareString(dd.SelectedValue, selectedValue, TextCompare: false) != 0 && addBlank)
            //{
            //    dd.Items.Add(new ListItem(selectedValue));
            //    dd.SelectedValue = selectedValue;
            //}
            sqlDataReader.Close();
        }
        //corePage2.cnClose();
        //corePage2.cnClose2();
    }
}
