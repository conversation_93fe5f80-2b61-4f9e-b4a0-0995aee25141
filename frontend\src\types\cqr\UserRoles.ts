export class UserRoles {
  bIsSAFR: boolean = false;
  bIsAMgr: boolean = false;
  bIsCost: boolean = false;
  bIsPA: boolean = false;
  bIsFran: boolean = false;
  bIsFr00: boolean = false;
  bIsMBnE: boolean = false;
  bIsCQGWNFY: boolean = false;
  bIsPetm: boolean = false;
  bIsPgm: boolean = false;
  bIsPrd: boolean = false;
  bIsPsm: boolean = false;
  bIsPDM: boolean = false;
  bIsAME_Coord: boolean = false;
  bIsPUR_Coord: boolean = false;
  bIsPsr: boolean = false;
  bIsSDir: boolean = false;
  bIsBum: boolean = false;
  bIsFin: boolean = false;
  bIsPres: boolean = false;
  bIsUkGm: boolean = false;
  bIsUkDm: boolean = false;
  bIsBeb: boolean = false;
  bIsCmgr: boolean = false;
  bIsMEPL: boolean = false;
  bIsAIME: boolean = false;
  bIsAIPR: boolean = false;
  bIsCTLC: boolean = false;

  sManagerFran?: string;
  sManagerBids?: string;
  sManagerFin?: string;
  sManagerPres?: string;
  sManagerBeb?: string;
  sManagerCmgr?: string;

  bIsGateway: boolean[];
  sManagerGateway: string[];

  bIsPetmOnly: boolean = false;
}
