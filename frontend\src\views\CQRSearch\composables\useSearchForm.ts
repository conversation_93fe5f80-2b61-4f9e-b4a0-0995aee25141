import { ref, reactive, computed, watch } from "vue";
import type { Ref } from "vue";
import { ElMessage } from "element-plus";

export interface SearchFormData {
  // 基本搜尋條件
  cqrFromLeft: string;
  cqrFromRight: string;
  cqrToLeft: string;
  cqrToRight: string;
  manufacturingSite: string;
  cqrOriginator: string;
  initialReleaseDateFrom: string;
  initialReleaseDateTo: string;
  productDescription: string;
  platform: string;
  dueDateFrom: string;
  dueDateTo: string;
  modelYear: string;
  accountManager: string;
}

export interface AdvancedFormData {
  // 進階搜尋條件
  oemGroup: string;
  oemCustomer: string;
  estimator: string;
  assignedPDM: string;
  assignedPGM: string;
  quoteType: string;
  customerRFQNumber: string;
  wonDateFrom: string;
  wonDateTo: string;
  openingMeetingDateFrom: string;
  openingMeetingDateTo: string;

  // 篩選選項
  onlyQuoteLettersWon: boolean;
  openCQRFolderOnly: boolean;
  quoteReport: boolean;
  findLastTwoFolders: boolean;
}

export interface SearchQuery {
  // 基本條件 - 匹配後端 SearchCQRQuery 類型
  ProjectNumber?: string;
  Originator?: string;
  Status?: string;
  ProductDescription?: string;
  Customer?: string;
  Platform?: string;
  DateFrom?: string;
  DateTo?: string;
  
  // CQR 編號範圍
  CQRFromLeft?: string;
  CQRFromRight?: string;
  CQRToLeft?: string;
  CQRToRight?: string;

  // 基本條件
  ManufacturingSite?: string;
  CQROriginator?: string;
  AccountManager?: string;

  // 日期範圍
  InitialReleaseDateFrom?: string;
  DueDateFrom?: string;

  // 篩選選項
  FindLastTwoFolders: boolean;
  QuoteReport?: string;
  UseWildcard: boolean;
  SortDescending: boolean;

  // 分頁和排序
  PageNumber: number;
  PageSize: number;
  SortBy?: string;
  SortDirection?: string;
}

export function useSearchForm() {
  // 表單狀態
  const searchForm = reactive<SearchFormData>({
    cqrFromLeft: "",
    cqrFromRight: "",
    cqrToLeft: "",
    cqrToRight: "",
    manufacturingSite: "",
    cqrOriginator: "",
    initialReleaseDateFrom: "",
    initialReleaseDateTo: "",
    productDescription: "",
    platform: "",
    dueDateFrom: "",
    dueDateTo: "",
    modelYear: "",
    accountManager: ""
  });

  const advancedForm = reactive<AdvancedFormData>({
    oemGroup: "",
    oemCustomer: "",
    estimator: "",
    assignedPDM: "",
    assignedPGM: "",
    quoteType: "",
    customerRFQNumber: "",
    wonDateFrom: "",
    wonDateTo: "",
    openingMeetingDateFrom: "",
    openingMeetingDateTo: "",
    onlyQuoteLettersWon: false,
    openCQRFolderOnly: false,
    quoteReport: false,
    findLastTwoFolders: false
  });

  // UI 狀態
  const showAdvanced = ref(false);
  const searchLoading = ref(false);

  // 分頁狀態
  const currentPage = ref(1);
  const pageSize = ref(50);

  // 驗證搜尋條件
  const validateSearchForm = (): boolean => {
    // 檢查 CQR 編號範圍是否有效
    if (searchForm.cqrFromLeft && searchForm.cqrToLeft) {
      const fromLeft = parseInt(searchForm.cqrFromLeft);
      const toLeft = parseInt(searchForm.cqrToLeft);

      if (fromLeft > toLeft) {
        ElMessage.warning("CQR 編號範圍無效：起始編號不能大於結束編號");
        return false;
      }
    }

    // 檢查日期範圍是否有效
    const dateRanges = [
      {
        from: searchForm.initialReleaseDateFrom,
        to: searchForm.initialReleaseDateTo,
        name: "初始發佈日期"
      },
      {
        from: searchForm.dueDateFrom,
        to: searchForm.dueDateTo,
        name: "到期日期"
      },
      {
        from: advancedForm.wonDateFrom,
        to: advancedForm.wonDateTo,
        name: "獲勝日期"
      },
      {
        from: advancedForm.openingMeetingDateFrom,
        to: advancedForm.openingMeetingDateTo,
        name: "開會日期"
      }
    ];

    for (const range of dateRanges) {
      if (range.from && range.to && range.from > range.to) {
        ElMessage.warning(`${range.name}範圍無效：開始日期不能晚於結束日期`);
        return false;
      }
    }

    return true;
  };

  // 檢查是否有搜尋條件
  const hasSearchCriteria = computed(() => {
    const basicCriteria = [
      searchForm.cqrFromLeft,
      searchForm.cqrFromRight,
      searchForm.cqrToLeft,
      searchForm.cqrToRight,
      searchForm.manufacturingSite,
      searchForm.cqrOriginator,
      searchForm.productDescription,
      searchForm.platform,
      searchForm.modelYear,
      searchForm.accountManager,
      searchForm.initialReleaseDateFrom,
      searchForm.initialReleaseDateTo,
      searchForm.dueDateFrom,
      searchForm.dueDateTo
    ].some(field => field !== "");

    const advancedCriteria = [
      advancedForm.oemGroup,
      advancedForm.oemCustomer,
      advancedForm.estimator,
      advancedForm.assignedPDM,
      advancedForm.assignedPGM,
      advancedForm.quoteType,
      advancedForm.customerRFQNumber,
      advancedForm.wonDateFrom,
      advancedForm.wonDateTo,
      advancedForm.openingMeetingDateFrom,
      advancedForm.openingMeetingDateTo
    ].some(field => field !== "");

    const checkboxCriteria = [
      advancedForm.onlyQuoteLettersWon,
      advancedForm.openCQRFolderOnly,
      advancedForm.quoteReport,
      advancedForm.findLastTwoFolders
    ].some(field => field === true);

    return basicCriteria || advancedCriteria || checkboxCriteria;
  });

  // 建構搜尋查詢物件
  const buildSearchQuery = (): SearchQuery => {
    const query: SearchQuery = {
      PageNumber: currentPage.value,
      PageSize: pageSize.value,
      SortDescending: true,
      UseWildcard: true,
      FindLastTwoFolders: advancedForm.findLastTwoFolders,
      QuoteReport: advancedForm.quoteReport ? "true" : undefined
    };

    // 基本搜尋條件
    if (searchForm.cqrFromLeft)
      query.CQRFromLeft = searchForm.cqrFromLeft;
    if (searchForm.cqrFromRight)
      query.CQRFromRight = searchForm.cqrFromRight;
    if (searchForm.cqrToLeft) 
      query.CQRToLeft = searchForm.cqrToLeft;
    if (searchForm.cqrToRight)
      query.CQRToRight = searchForm.cqrToRight;

    if (searchForm.manufacturingSite)
      query.ManufacturingSite = searchForm.manufacturingSite;
    if (searchForm.cqrOriginator)
      query.CQROriginator = searchForm.cqrOriginator;
    if (searchForm.productDescription)
      query.ProductDescription = searchForm.productDescription;
    if (searchForm.platform) 
      query.Platform = searchForm.platform;
    if (searchForm.accountManager)
      query.AccountManager = searchForm.accountManager;

    // 日期範圍
    if (searchForm.initialReleaseDateFrom)
      query.InitialReleaseDateFrom = searchForm.initialReleaseDateFrom;
    if (searchForm.dueDateFrom) 
      query.DueDateFrom = searchForm.dueDateFrom;

    return query;
  };

  // 清除所有搜尋條件
  const clearAllFilters = () => {
    // 清除基本表單
    Object.keys(searchForm).forEach(key => {
      searchForm[key as keyof SearchFormData] = "";
    });

    // 清除進階表單
    Object.keys(advancedForm).forEach(key => {
      const field = key as keyof AdvancedFormData;
      if (typeof advancedForm[field] === "boolean") {
        advancedForm[field] = false;
      } else {
        advancedForm[field] = "";
      }
    });

    // 重置分頁
    currentPage.value = 1;

    ElMessage.success("已清除所有搜尋條件");
  };

  // 切換進階搜尋顯示
  const toggleAdvancedSearch = () => {
    showAdvanced.value = !showAdvanced.value;
  };

  // 監聽 quoteReport 變化，自動啟用/禁用 findLastTwoFolders
  watch(
    () => advancedForm.quoteReport,
    newValue => {
      if (!newValue) {
        advancedForm.findLastTwoFolders = false;
      }
    }
  );

  // 格式化日期顯示
  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "";
    return date.toLocaleDateString("zh-TW");
  };

  // 格式化 CQR 編號，移除前面的 0
  const formatCQRNumber = (cqrNumber: string) => {
    if (!cqrNumber) return "";
    // 分割主編號和版本號 (例如: "08067.0001" -> "8067.0001")
    const parts = cqrNumber.split('.');
    if (parts.length === 2) {
      const mainNumber = parseInt(parts[0], 10).toString(); // 移除前導零
      return `${mainNumber}.${parts[1]}`;
    }
    return cqrNumber;
  };

  return {
    // 表單狀態
    searchForm,
    advancedForm,

    // UI 狀態
    showAdvanced,
    searchLoading,

    // 分頁狀態
    currentPage,
    pageSize,

    // 計算屬性
    hasSearchCriteria,

    // 方法
    validateSearchForm,
    buildSearchQuery,
    clearAllFilters,
    toggleAdvancedSearch,
    formatDate,
    formatCQRNumber
  };
}
