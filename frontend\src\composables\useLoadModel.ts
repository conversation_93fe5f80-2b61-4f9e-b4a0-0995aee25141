// composables/useLoadModel.ts
import { useFormModelStore } from "@/store/modules/formModel";
import { useRoute } from "vue-router";
import { ref, onMounted } from "vue";

export function useLoadModel() {
  const store = useFormModelStore();
  const route = useRoute();
  const searchQueueKey = ref(Number(route.params.queueKey));

  onMounted(async () => {
    if (!store.model || store.queueKey !== searchQueueKey.value) {
      // await store.fetchModelData(searchQueueKey.value);
      // await store.fetchModelRoutingHeaderByQueuekey(searchQueueKey.value);
      // await store.fetchModelAttachFileByQueuekey(searchQueueKey.value);
      // await store.fetchModelCQRHeaderCoolectionByQueuekey(searchQueueKey.value);
    }
  });

  return { store };
}
