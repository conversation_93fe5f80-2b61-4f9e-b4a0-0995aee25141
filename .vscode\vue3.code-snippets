{"Vue3 Setup Script": {"prefix": "vue3setup", "body": ["<template>", "  <div>", "    $0", "  </div>", "</template>", "", "<script setup lang=\"ts\">", "import { ref, reactive, computed, onMounted } from 'vue'", "", "// Props", "interface Props {", "  // Define props here", "}", "", "// Emits", "interface Emits {", "  // Define emits here", "}", "", "// Reactive data", "const state = reactive({", "  // Define reactive state here", "})", "", "// Computed", "// const computed = computed(() => {})", "", "// Methods", "// const method = () => {}", "", "// Lifecycle", "onMounted(() => {", "  // Component mounted", "})", "</script>", "", "<style scoped>", "/* Component styles */", "</style>"], "description": "Vue3 component with setup script"}, "Vue3 Composition API": {"prefix": "vue3comp", "body": ["<template>", "  <div>", "    {{ $1 }}", "  </div>", "</template>", "", "<script lang=\"ts\">", "import { defineComponent, ref, reactive, computed, onMounted } from 'vue'", "", "export default defineComponent({", "  name: '$2',", "  setup() {", "    const $3 = ref('')", "    ", "    return {", "      $3", "    }", "  }", "})", "</script>"], "description": "Vue3 component with Composition API"}, "Vue3 Ref": {"prefix": "ref", "body": "const ${1:name} = ref(${2:value})", "description": "Vue3 ref"}, "Vue3 Reactive": {"prefix": "reactive", "body": "const ${1:name} = reactive(${2:{}})", "description": "Vue3 reactive"}, "Vue3 Computed": {"prefix": "computed", "body": "const ${1:name} = computed(() => ${2:value})", "description": "Vue3 computed"}}