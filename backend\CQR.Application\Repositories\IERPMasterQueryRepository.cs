﻿using CQR.Application.Dto;

namespace CQR.Application.Repositories;

public interface IERPMasterQueryRepository
{
    Task<IEnumerable<OEMHiearchyDto>> getOEMHiearchy();
    //Task<IEnumerable<DueDateStatusReportDto>> GetSQLDueDateStatusReport();

    //Task<IEnumerable<SalesReportDto>> GetSQLReportSalesReport();
    //Task<IEnumerable<PipelineReportDto>> GetSQLReportPipeline();
    //Task<IEnumerable<OpenCQRStatusReportDto>> GetSQLReportOpenCQRStatus();

}
