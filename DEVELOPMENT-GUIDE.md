# CQR 開發指南 - 多工具支援

本專案現在採用 **統一 Repository** 架構，但支援多種開發工具和工作方式。

## 🏗️ Repository 結構

```
your-project/
├── backend/           # ASP.NET Core API
├── frontend/          # Vue3 + Pinia
├── docs/              # 文檔
├── shared/            # 共用資源
├── legacy/            # 舊版程式碼（僅供參考）
└── .vscode/           # 統一的 VS Code 配置
```

## 🔧 開發方式選擇

### 方案 1: 全端開發者 - 統一工作區 ⭐️

**適合**: 需要同時開發前端和後端的開發者

```bash
# 克隆專案
git clone https://dev.azure.com/BCS-AIS-SAP/NewCQR/_git/new-cqr-api-prj-sln
cd new-cqr-api-prj-sln
git checkout unified-repository

# 使用 VS Code 開啟統一工作區
code your-project.code-workspace
```

**功能**:
- ✅ F5 啟動 ASP.NET Core API 偵錯
- ✅ 統一的 Git 管理
- ✅ 內建 build 和 test 任務
- ✅ 前後端代碼智能提示

---

### 方案 2: 後端開發者 - Visual Studio

**適合**: 只開發 ASP.NET Core API 的後端開發者

```bash
# 克隆專案
git clone https://dev.azure.com/BCS-AIS-SAP/NewCQR/_git/new-cqr-api-prj-sln
cd new-cqr-api-prj-sln
git checkout unified-repository

# 使用 Visual Studio 開啟後端 solution
# 打開: backend/backend-only.sln
```

**優點**:
- 🎯 **只關注後端代碼** - 不會被前端檔案干擾
- 🔧 **完整的 Visual Studio 功能** - IntelliSense、偵錯、測試
- 📦 **所有 .NET 專案** - API、Domain、Infrastructure 等
- 🚀 **熟悉的開發體驗** - F5 偵錯、NuGet 管理

---

### 方案 3: 前端開發者 - VS Code Frontend

**適合**: 只開發 Vue3 前端的開發者

```bash
# 克隆專案
git clone https://dev.azure.com/BCS-AIS-SAP/NewCQR/_git/new-cqr-api-prj-sln
cd new-cqr-api-prj-sln
git checkout unified-repository

# 進入 frontend 目錄
cd frontend

# 安裝相依套件
npm install

# 使用 VS Code 開啟前端工作區
code frontend-only.code-workspace
```

**優點**:
- 🎨 **只關注前端代碼** - 不會被後端檔案干擾  
- ⚡ **Vue3 優化配置** - Volar、TypeScript、ESLint
- 🔥 **熱重載開發** - npm run dev
- 📱 **前端特化工具** - Tailwind、prettier

---

## 💾 Git 工作流程

**重要**: 無論使用哪種開發方式，Git 操作都是一樣的！

### 日常開發流程

```bash
# 1. 取得最新代碼
git pull origin unified-repository

# 2. 創建功能分支
git checkout -b feature/your-feature-name

# 3. 進行開發 (使用你喜歡的工具)
# - Visual Studio 開發後端
# - VS Code 開發前端 
# - 或者使用統一工作區

# 4. 提交變更
git add .
git commit -m "Add your changes"

# 5. 推送分支
git push -u origin feature/your-feature-name

# 6. 創建 Pull Request
# 使用 Azure DevOps 或 GitHub 介面
```

### 提交最佳實踐

- ✅ **針對性提交** - 只提交相關的檔案變更
- ✅ **清晰的提交訊息** - 說明做了什麼改變
- ✅ **小而頻繁的提交** - 不要累積太多變更
- ✅ **測試通過再提交** - 確保代碼品質

## 🚀 各開發環境的 Git 操作

### Visual Studio 後端開發者

```bash
# 在 backend/ 目錄工作
cd backend

# 查看狀態 (包含整個專案的變更)
git status

# 只提交後端相關變更
git add *.cs **/*.cs *.csproj **/*.csproj
git commit -m "Update backend API logic"

# 推送
git push
```

### VS Code 前端開發者  

```bash
# 在 frontend/ 目錄工作
cd frontend

# 查看狀態 (包含整個專案的變更)
git status

# 只提交前端相關變更
git add *.vue *.ts *.js package.json
git commit -m "Update frontend components"

# 推送
git push
```

### 統一工作區開發者

```bash
# 在根目錄工作
# 可以提交前端和後端的變更
git add backend/ frontend/
git commit -m "Update both frontend and backend"
git push
```

## 🔍 常見問題解答

### Q: 我習慣用 Visual Studio，會不會看到很多前端檔案？
**A**: 不會！使用 `backend/backend-only.sln` 只會顯示後端專案。

### Q: 前端開發者需要安裝 .NET 嗎？
**A**: 不需要！使用 `frontend/frontend-only.code-workspace` 只關注 Node.js/Vue3。

### Q: Git 操作會不會很複雜？
**A**: 不會！Git 操作和以前完全一樣，只是現在在同一個 repository。

### Q: 如何避免提交不相關的檔案？
**A**: 使用 `git add` 指定特定檔案，或使用 VS Code / Visual Studio 的 Git 介面選擇性提交。

### Q: 團隊協作會有問題嗎？
**A**: 不會！每個人可以用自己習慣的工具，Git 合併時自動整合。

## 🎯 建議配置

### 後端開發者
- **主要工具**: Visual Studio 2022
- **Solution**: `backend/backend-only.sln`
- **Git 範圍**: 主要關注 `backend/` 目錄

### 前端開發者
- **主要工具**: VS Code
- **Workspace**: `frontend/frontend-only.code-workspace`  
- **Git 範圍**: 主要關注 `frontend/` 目錄

### 全端開發者
- **主要工具**: VS Code
- **Workspace**: `your-project.code-workspace`
- **Git 範圍**: 整個專案

---

## 📞 需要幫助？

- 📚 **文檔**: 查看 `docs/` 目錄
- 🐛 **問題回報**: 使用 Azure DevOps Issues
- 💬 **團隊討論**: 聯繫專案負責人