﻿namespace CQR.Core;

public static class ModConstants
{
    // Version and Application Name
    public const string VERSION = "v2.2.01i";
    public const string APP_NAME = "CQR";

    // Drop down list constants
    public const int noneSelected = -99;
    public const int newRecord = -1;
    public const string MFGSITE_REYNOSA = "00000004";

    // Quote Types
    public const string QUOTETYPE_CustomerPreCQR = "0";
    public const string QUOTETYPE_CustomerRFQ = "1";
    public const string QUOTETYPE_GateExitorInternalorReview = "2";
    public const string QUOTETYPE_ECR = "3";

    // Status Values
    public const string status010100FR = "010100FR"; // "Being Initiated"
    public const string status010150FR = "010150FR"; // "Requires Review by CMGR"
    public const string status010200FR = "010200FR"; // "Requires Review by Sales"
    public const string status010300FR = "010300FR"; // "Returned by Sales"
    public const string status020100FR = "020100FR"; // "Issued / Needs Finance Coordinator"
    public const string status020150FR = "020150FR"; // "Engineering Assessment In Progress"
    public const string status020200FR = "020200FR"; // "Internal FRAN Initiated / Needs Finance Coordinator"
    public const string status020300FR = "020300FR"; // "Issued / Awaiting Engineering Assessment"
    public const string status020400FR = "020400FR"; // "Internal FRAN Initiated / Awaiting Engineering Assessment"
    public const string status020500FR = "020500FR"; // "Returned by Engineering"
    public const string status020600FR = "020600FR"; // "Reissued"
    public const string status030100FR = "030100FR";
    public const string status030200FR = "030200FR"; // "Engineering Package In Progress"
    public const string status030300FR = "030300FR"; // "Awaiting Cost Estimates"
    public const string status030400FR = "030400FR"; // "Design Proposal In Progress"
    public const string status030600FR = "030600FR"; // "Task Lists In Progress"
    public const string status040100FR = "040100FR"; // "Quote Response In Progress"
    public const string status040125FR = "040125FR"; // "Business Case Ready for Review"
    public const string status040150FR = "040150FR"; // "QAF Awaiting Approval"
    public const string status040175FR = "040175FR"; // "Awaiting Customer Response"
    public const string status040190FR = "040190FR"; // "Quote Response ready for Gate 2 release"
    public const string status040200FR = "040200FR"; // "Quote Response Sent to Sales"
    public const string status040250FR = "040250FR"; // "Close out CQR"
    public const string status040300FR = "040300FR"; // "Quote Response Received"
    public const string status040400FR = "040400FR"; // "Design Proposal Costing Complete"
    public const string status040700FR = "040700FR"; // "Review Won Quote"
    public const string status090100FR = "090100FR"; // "Closed Out"
    public const string status090900FR = "090900FR"; // "Terminated by Originator"

    public const string status010100QF = "010100QF"; // "Initiating QAF"
    public const string status010200QF = "010200QF"; // "QAF Awaiting PIM Review"
    public const string status010300QF = "010300QF"; // "QAF Awaiting Management Review"
    public const string status010400QF = "010400QF"; // "QAF Approved"

    public const string FOLDER_TYPE = "FR";

    // Optional aiReroute parameter to 'CompleteRGTask'
    public const int STAT_Close = 0;
    public const int STAT_Delete = 1;
    public const int STAT_Reopen = 2;
    public const int STAT_Reroute = 3;
    public const int STAT_DueDate = 4;
    public const int STAT_NewUser = 5;

    // Colors (integer values represent RGB color codes)
    public const int DDC_BROWN = 0x999966;
    public const int DDC_LT_BROWN = 0xB5B68C;
    public const int DDC_BLUE = 0x3063;
    public const int LIGHT_GREY = 0xE5E5E5;

    // Other Constants
    public const string COMMENTS_HELD = "[Comments Held]\r\n";

    // This would require more context for proper translation:
    //public static string RenderControlToText(System.Web.UI.Control controlToRender)
    //{
    //    using (var theStringWriter = new System.IO.StringWriter())
    //    {
    //        using (var theHtmlTextWriter = new System.Web.UI.HtmlTextWriter(theStringWriter))
    //        {
    //            controlToRender.RenderControl(theHtmlTextWriter);
    //            return theStringWriter.ToString();
    //        }
    //    }
    //}
}
