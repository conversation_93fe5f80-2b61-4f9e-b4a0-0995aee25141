﻿namespace CQR.Application.Dto.RoutingTask;

using TypeGen.Core.TypeAnnotations;

[ExportTsInterface]
public class RoutingTaskDto
{
    public string? QueueKey { get; set; }

    public string? TaskCode { get; set; }

    public string? ActionTaskCode { get; set; }

    public string? RoutingTaskCode { get; set; }

    public string? AssignedTo { get; set; }

    public string? AssignedDate { get; set; }

    public string? AsignedTime { get; set; } // 注意拼字 AsignedTime（原始欄位）

    public string? DueDate { get; set; }

    public string? Result { get; set; } // "Acknowledged", "Terminated", or original

    public string? PartDwgNbr { get; set; }

    public string? NotificationInd { get; set; }

    public string? CurrentTaskInd { get; set; }

    public string? DoneInd { get; set; }

    public string? ToBeViewedInd { get; set; }

    public string? DoneDate { get; set; }

    public string? DoneTm { get; set; }

    public string? DoneByName { get; set; }
}
