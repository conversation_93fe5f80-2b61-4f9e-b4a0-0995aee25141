﻿using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.VisualBasic;
using System.Data;
using System.Reflection;

using System.Runtime.CompilerServices;
using Microsoft.VisualBasic.CompilerServices;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace CQR.Domain.DecisionCore;
public class corePage : PageModel
{
    public const int noneSelected = -99;

    public const int newRecord = -1;

    public const string colorLightGrey = "#999999";

    public const string colorDarkGrey = "#666666";

    public const string colorRed = "#FE3035";

    public SqlConnection dbConn;

    public SqlConnection dbConnNonQuery;

    public int lTrack;

    private static int lTrackTotal = 0;

    private int iRetryCount;

    private readonly IConfiguration _configuration;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public corePage(IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
    {
        _configuration = configuration;
        _httpContextAccessor = httpContextAccessor;
        dbConn = new SqlConnection();
        dbConnNonQuery = new SqlConnection();
        lTrack = GetTickCount();
        iRetryCount = 0;
    }

    //public corePage()
    //{
    //    dbConn = new SqlConnection();
    //    dbConnNonQuery = new SqlConnection();
    //    lTrack = GetTickCount();
    //    iRetryCount = 0;
    //}
    private static extern int GetTickCount();

    ~corePage()
    {
        cnClose();
        cnClose2();
    }

    //public string getConnectionString(string asConnectionEntry)
    //{
    //    return Strings.Replace(Strings.Replace(ConfigurationManager.AppSettings[asConnectionEntry], "xxPLACEHOLDERxx", "mp_mcH@rt"), "yyPLACEHOLDERyy", "wfamssql");
    //}

    public string GetConnectionString(string connectionEntryName)
    {
        string connectionString = _configuration.GetConnectionString(connectionEntryName)
                                ?? _configuration[connectionEntryName]
                                ?? string.Empty;

        return connectionString
            .Replace("xxPLACEHOLDERxx", "mp_mcH@rt")
            .Replace("yyPLACEHOLDERyy", "wfamssql");
    }

    //public string GetConnectionString(string connectionEntryName)
    //{
    //    string connectionString = _configuration.GetConnectionString(connectionEntryName)
    //                            ?? _configuration[connectionEntryName]
    //                            ?? string.Empty;

    //    return connectionString
    //        .Replace("xxPLACEHOLDERxx", "mp_mcH@rt")
    //        .Replace("yyPLACEHOLDERyy", "wfamssql");
    //}

    public void InitializeSQL1(string asConnectionEntry = "connectionString_core", bool bCloseIfDifferent = true)
    {
        string connectionString = GetConnectionString(asConnectionEntry);
        if (bCloseIfDifferent & (Operators.CompareString(dbConn.ConnectionString, connectionString, TextCompare: false) != 0))
        {
            cnClose();
        }
        if (dbConn.State == ConnectionState.Closed)
        {
            dbConn.ConnectionString = connectionString;
            dbConn.Open();
        }
    }

    public void InitializeSQL2(string asConnectionEntry = "connectionString_core", bool bCloseIfDifferent = true)
    {
        string connectionString = GetConnectionString(asConnectionEntry);
        if (bCloseIfDifferent & (Operators.CompareString(dbConnNonQuery.ConnectionString, connectionString, TextCompare: false) != 0))
        {
            cnClose2();
        }
        if (dbConnNonQuery.State == ConnectionState.Closed)
        {
            dbConnNonQuery.ConnectionString = connectionString;
            dbConnNonQuery.Open();
        }
    }

    public void cnClose()
    {
        try
        {
            dbConn.Close();
        }
        catch (Exception ex)
        {
            ProjectData.SetProjectError(ex);
            Exception ex2 = ex;
            ProjectData.ClearProjectError();
        }
    }

    public void cnClose2()
    {
        try
        {
            dbConnNonQuery.Close();
        }
        catch (Exception ex)
        {
            ProjectData.SetProjectError(ex);
            Exception ex2 = ex;
            ProjectData.ClearProjectError();
        }
    }

    public string getQueryTime()
    {
        return Strings.Format((double)lTrackTotal / 1000.0, "###0.000");
    }

    public void TrackQuery(string sSql = "")
    {
    }

    private bool isGeneralNetworkError(Exception ex)
    {
        if (Operators.CompareString(Strings.Left(ex.Message.ToString(), 21), "General network error", TextCompare: false) == 0 && iRetryCount < 5)
        {
            return true;
        }
        bool result = default(bool);
        return result;
    }

    public void cnExecuteNonQuery(string sSQL, bool bCalledFromDumpException = false)
    {
        TrackQuery();
        InitializeSQL2("connectionString_core", bCloseIfDifferent: false);
        SqlCommand sqlCommand = new SqlCommand(sSQL, dbConnNonQuery);
        try
        {
            sqlCommand.ExecuteNonQuery();
            iRetryCount = 0;
        }
        catch (Exception ex)
        {
            ProjectData.SetProjectError(ex);
            Exception ex2 = ex;
            if (isGeneralNetworkError(ex2))
            {
                Thread.Sleep(250);
                cnExecuteNonQuery(sSQL);
            }
            else if (!bCalledFromDumpException)
            {
                DumpException(ex2, sqlCommand.CommandText, "SQL String");
            }
            ProjectData.ClearProjectError();
        }
        dbConnNonQuery.Close();
        TrackQuery(sSQL);
    }

    public void cnExecuteNonQueryNoTry(string sSQL, bool bCalledFromDumpException = false)
    {
        TrackQuery();
        InitializeSQL2("connectionString_core", bCloseIfDifferent: false);
        new SqlCommand(sSQL, dbConnNonQuery).ExecuteNonQuery();
        dbConnNonQuery.Close();
        TrackQuery(sSQL);
    }

    public object cnExecuteForSingleValue(string sql, Dictionary<string, object> parameters = null)
    {
        object result = null;

        using (var conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
        using (var cmd = new SqlCommand(sql, conn))
        {
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                }
            }

            conn.Open();
            result = cmd.ExecuteScalar();
        }

        return result;
    }

    //public string cnExecuteForSingleValue(string sSQL)
    //{
    //    bool flag = false;
    //    string result = "";
    //    TrackQuery();
    //    InitializeSQL2("connectionString_core", bCloseIfDifferent: false);
    //    SqlCommand sqlCommand = new SqlCommand(sSQL, dbConnNonQuery);
    //    SqlDataReader sqlDataReader = null;
    //    try
    //    {
    //        sqlDataReader = sqlCommand.ExecuteReader();
    //        iRetryCount = 0;
    //    }
    //    catch (Exception ex)
    //    {
    //        ProjectData.SetProjectError(ex);
    //        Exception ex2 = ex;
    //        if (!isGeneralNetworkError(ex2))
    //        {
    //            DumpException(ex2, sqlCommand.CommandText, "SQL String");
    //            ProjectData.ClearProjectError();
    //            goto IL_00d2;
    //        }
    //        Thread.Sleep(250);
    //        result = cnExecuteForSingleValue(sSQL);
    //        flag = true;
    //        ProjectData.ClearProjectError();
    //    }
    //    if (!flag && sqlDataReader.Read())
    //    {
    //        try
    //        {
    //            result = Conversions.ToString(sqlDataReader[0]);
    //        }
    //        catch (Exception ex3)
    //        {
    //            ProjectData.SetProjectError(ex3);
    //            Exception ex4 = ex3;
    //            result = "";
    //            ProjectData.ClearProjectError();
    //        }
    //    }
    //    sqlDataReader?.Close();
    //    dbConnNonQuery.Close();
    //    TrackQuery(sSQL);
    //    goto IL_00d2;
    //IL_00d2:
    //    return result;
    //}

    public int cnExecuteGetIdentity(string sSQL)
    {
        int result = 0;
        sSQL += "; SELECT @@IDENTITY AS NEWID";
        SqlDataReader rst = cnExecute(sSQL);
        if (rst.Read())
        {
            result = rstInt(ref rst, "NEWID");
        }
        rst.Close();
        rst = null;
        return result;
    }

    public void CloseRST(SqlDataReader rst)
    {
        try
        {
            ((SqlCommand)rst.GetType().GetProperty("Command", BindingFlags.Instance | BindingFlags.NonPublic).GetValue(rst)).Cancel();
        }
        catch (Exception ex)
        {
            ProjectData.SetProjectError(ex);
            Exception ex2 = ex;
            ProjectData.ClearProjectError();
        }
        rst.Close();
    }

    public SqlDataReader cnExecute(string sSQL, int timeoutValue = 0)
    {
        TrackQuery();
        SqlCommand sqlCommand = new SqlCommand(sSQL, dbConn);
        if (timeoutValue != 0)
        {
            sqlCommand.CommandTimeout = timeoutValue;
        }
        InitializeSQL1("connectionString_core", bCloseIfDifferent: false);
        SqlDataReader result = null;
        try
        {
            result = sqlCommand.ExecuteReader();
            iRetryCount = 0;
        }
        catch (Exception ex)
        {
            ProjectData.SetProjectError(ex);
            Exception ex2 = ex;
            if (isGeneralNetworkError(ex2))
            {
                Thread.Sleep(250);
                result = cnExecute(sSQL);
            }
            else
            {
                DumpException(ex2, sqlCommand.CommandText, "SQL String");
            }
            ProjectData.ClearProjectError();
        }
        TrackQuery(sSQL);
        return result;
    }

    private void z_CheckFieldError(Exception ex, string sField)
    {
        if (Operators.CompareString(ex.GetType().ToString(), "System.IndexOutOfRangeException", TextCompare: false) == 0)
        {
            DumpException(ex, sField, "Field Name");
        }
    }

    public int rstInt(ref SqlDataReader rst, string sField, int nDefaultValue = 0)
    {
        int result = nDefaultValue;
        if (Operators.CompareString(Strings.Trim(sField), "", TextCompare: false) != 0 && rst != null)
        {
            try
            {
                result = ((!Information.IsDBNull(RuntimeHelpers.GetObjectValue(rst[sField]))) ? checked((int)Math.Round(Conversion.Val(RuntimeHelpers.GetObjectValue(rst[sField])))) : nDefaultValue);
            }
            catch (Exception ex)
            {
                ProjectData.SetProjectError(ex);
                Exception ex2 = ex;
                z_CheckFieldError(ex2, sField);
                ProjectData.ClearProjectError();
            }
        }
        return result;
    }

    public long rstLong(ref SqlDataReader rst, string sField, int nDefaultValue = 0)
    {
        long result = nDefaultValue;
        if (Operators.CompareString(Strings.Trim(sField), "", TextCompare: false) != 0 && rst != null)
        {
            try
            {
                result = ((!Information.IsDBNull(RuntimeHelpers.GetObjectValue(rst[sField]))) ? checked((long)Math.Round(Conversion.Val(RuntimeHelpers.GetObjectValue(rst[sField])))) : nDefaultValue);
            }
            catch (Exception ex)
            {
                ProjectData.SetProjectError(ex);
                Exception ex2 = ex;
                z_CheckFieldError(ex2, sField);
                ProjectData.ClearProjectError();
            }
        }
        return result;
    }

    public double rstDouble(ref SqlDataReader rst, string sField, double dDefaultValue = 0.0)
    {
        double result = dDefaultValue;
        if (Operators.CompareString(Strings.Trim(sField), "", TextCompare: false) != 0 && rst != null)
        {
            try
            {
                result = ((!Information.IsDBNull(RuntimeHelpers.GetObjectValue(rst[sField]))) ? Conversions.ToDouble(rst[sField]) : dDefaultValue);
            }
            catch (Exception ex)
            {
                ProjectData.SetProjectError(ex);
                Exception ex2 = ex;
                z_CheckFieldError(ex2, sField);
                ProjectData.ClearProjectError();
            }
        }
        return result;
    }

    public string rstDateOnlyInternational(ref SqlDataReader rst, string sField, string sDefaultValue = "")
    {
        string text = rstDateOnly(ref rst, sField, sDefaultValue);
        if (!Information.IsDate(text))
        {
            return text;
        }
        return Strings.Format(Conversions.ToDate(text), "dd-MMM-yyyy");
    }

    public string rstDateOnly(ref SqlDataReader rst, string sField, string sDefaultValue = "")
    {
        string result = "";
        string text = rstString(ref rst, sField, sDefaultValue);
        if (Operators.CompareString(text, "", TextCompare: false) != 0)
        {
            int num = Strings.InStr(text, " ");
            if (num > 0)
            {
                text = Strings.Left(text, checked(num - 1));
            }
            result = text;
        }
        return result;
    }

    public string rstDateCygnetToScreen(ref SqlDataReader rst, string sField, string sDefaultValue = "")
    {
        string text = rstString(ref rst, sField, sDefaultValue);
        string result = "";
        switch (text)
        {
            case "N/A":
                result = text;
                break;
            default:
                try
                {
                    result = Strings.Format(Conversions.ToDate(Strings.Mid(text, 5, 2) + "/" + Strings.Mid(text, 7, 2) + "/" + Strings.Left(text, 4)), "dd-MMM-yyyy");
                }
                catch (Exception ex)
                {
                    ProjectData.SetProjectError(ex);
                    Exception ex2 = ex;
                    result = text;
                    ProjectData.ClearProjectError();
                }
                break;
            case null:
            case "":
                break;
        }
        return result;
    }

    public string rstString(ref SqlDataReader rst, string sField, string sDefaultValue = "")
    {
        string result;
        try
        {
            result = Strings.RTrim(Conversions.ToString(rst[sField]));
        }
        catch (Exception projectError)
        {
            ProjectData.SetProjectError(projectError);
            result = sDefaultValue;
            ProjectData.ClearProjectError();
        }
        return result;
    }

    public bool rstBool(ref SqlDataReader rst, string sField, bool nDefaultValue = false)
    {
        bool result = default(bool);
        if (Operators.CompareString(Strings.Trim(sField), "", TextCompare: false) != 0 && rst != null)
        {
            try
            {
                result = ((!Information.IsDBNull(RuntimeHelpers.GetObjectValue(rst[sField]))) ? Conversions.ToBoolean(rst[sField]) : nDefaultValue);
            }
            catch (Exception ex)
            {
                ProjectData.SetProjectError(ex);
                Exception ex2 = ex;
                z_CheckFieldError(ex2, sField);
                result = nDefaultValue;
                ProjectData.ClearProjectError();
            }
        }
        return result;
    }

    //public void Debug(string asText)
    //{
    //    HttpResponse response = HttpContext.Current.Response;
    //    response.Write(asText + "<BR>");
    //    response.Flush();
    //    _ = null;
    //}

    //public void PopulateCombo(DropDownList acCombo, string asTable, string asFieldId, string asFieldDisp, string asWhere = "", string asSelect = "", string sDistinct = "", bool bAddBlank = false)
    //{
    //    string text = "SELECT " + sDistinct + " " + asFieldId + " AS Field1, " + asFieldDisp + " AS Field2 FROM " + asTable;
    //    if (Operators.CompareString(asWhere, "", TextCompare: false) != 0)
    //    {
    //        text = text + " WHERE " + asWhere;
    //    }
    //    text = text + " ORDER BY " + asFieldDisp;
    //    SqlDataReader rst = cnExecute(text);
    //    if (bAddBlank)
    //    {
    //        acCombo.Items.Insert(0, new ListItem("", ""));
    //    }
    //    while (rst.Read())
    //    {
    //        acCombo.Items.Add(new ListItem(rstString(ref rst, "Field2"), rstString(ref rst, "Field1")));
    //        if (Operators.CompareString(asSelect, rstString(ref rst, "Field1"), TextCompare: false) == 0)
    //        {
    //            acCombo.SelectedIndex = checked(acCombo.Items.Count - 1);
    //        }
    //    }
    //    rst.Close();
    //}

    public string cstrNull(string tgt)
    {
        string text = tgt.Trim();
        if (text.Length == 0)
        {
            return "null";
        }
        return "'" + text + "'";
    }

    public string parseStr(string tgt)
    {
        if (tgt == null)
        {
            return "''";
        }
        return tgt.Trim().Replace("'", "''");
    }

    public bool FolderIsLocked(string asTbl, string asFolderNumber, string asUserID = "")
    {
        bool result = false;
        SqlDataReader rst = cnExecute("SELECT lock_userid, lock_time, CURRENT_TIMESTAMP as Server_Time FROM " + asTbl + " WHERE QUEUEKEY = " + asFolderNumber);
        if (Operators.CompareString(rstString(ref rst, "lock_time"), "", TextCompare: false) != 0 && DateAndTime.DateDiff("n", rstString(ref rst, "lock_time"), rstString(ref rst, "Server_Time")) <= 30)
        {
            string right = rstString(ref rst, "lock_userid");
            result = Conversions.ToBoolean(Interaction.IIf((Operators.CompareString(asUserID, "", TextCompare: false) == 0) | (Operators.CompareString(asUserID, right, TextCompare: false) == 0), false, true));
        }
        rst.Close();
        return result;
    }

    public bool FolderLockLost(string asTbl, string asKeyField, string asFolderNumber, string asUserId)
    {
        //if (Operators.CompareString(asFolderNumber, "", TextCompare: false) != 0)
        //{
        //    if (Operators.CompareString(cnExecuteForSingleValue("SELECT lock_userId FROM " + asTbl + " WHERE " + asKeyField + " = " + asFolderNumber), asUserId, TextCompare: false) != 0)
        //    {
        //        return true;
        //    }
        //    return false;
        //}
        //bool result = default(bool);
        //return result;
        return false;
    }

    public void FolderLock(string asTbl, string asKeyField, string asFolderNumber, string asUserId)
    {
        if (Operators.CompareString(asFolderNumber, "", TextCompare: false) != 0)
        {
            cnExecuteNonQuery("UPDATE " + asTbl + " SET lock_userid = '" + asUserId + "', lock_time = { fn NOW() } WHERE " + asKeyField + " = " + asFolderNumber);
        }
    }

    public void FolderUnlock(string asTbl, string asKeyField, string asFolderNumber, string asUserId, bool abForce = false)
    {
        if (Operators.CompareString(asFolderNumber, "", TextCompare: false) != 0)
        {
            string text = "UPDATE " + asTbl + " SET lock_userid = null, lock_time = null WHERE lock_time < '" + Conversions.ToString(DateAndTime.DateAdd(DateInterval.Hour, -12.0, DateAndTime.Now)) + "' OR " + asKeyField + " = " + asFolderNumber;
            if (!abForce)
            {
                text = text + " AND lock_userid = '" + asUserId + "'";
            }
            cnExecuteNonQuery(text);
        }
    }

    public bool FolderCanBeEdited(string asTbl, string asKeyField, string asFolderNumber, string asUserId, string asMsg = "", bool abRestrictAll = false)
    {
        string sSQL = "SELECT lock_userid, lock_time, CURRENT_TIMESTAMP as Server_Time FROM " + asTbl + " WHERE " + asKeyField + " = " + asFolderNumber;
        SqlDataReader rst = cnExecute(sSQL);
        rst.Read();
        bool result;
        if ((Operators.CompareString(rstString(ref rst, "lock_userid"), "", TextCompare: false) != 0) & (Operators.CompareString(rstString(ref rst, "lock_userid"), "", TextCompare: false) != 0))
        {
            if (!abRestrictAll & ((Operators.CompareString(rstString(ref rst, "lock_userid"), asUserId, TextCompare: false) == 0) | (Operators.CompareString(Strings.LCase(rstString(ref rst, "lock_userid")), "create", TextCompare: false) == 0)))
            {
                FolderLock(asTbl, asKeyField, asFolderNumber, asUserId);
                result = true;
            }
            else if (DateAndTime.DateDiff("n", rstString(ref rst, "lock_time"), rstString(ref rst, "Server_Time")) > 30)
            {
                FolderUnlock(asTbl, asKeyField, asFolderNumber, asUserId);
                result = true;
            }
            else
            {
                //Session["CONCERN.LOCKEDBY." + asFolderNumber] = rstString(ref rst, "lock_userid");
                result = false;
            }
        }
        else
        {
            result = true;
        }
        rst.Close();
        rst = null;
        return result;
    }

    public string noBreak(string sValue)
    {
        if (Operators.CompareString(sValue, "", TextCompare: false) == 0)
        {
            return "&nbsp;";
        }
        return "<nobr>" + sValue + "&nbsp;</nobr>";
    }

    public bool cnIsValidQuery(string sSQL)
    {
        InitializeSQL2("connectionString_core", bCloseIfDifferent: false);
        SqlCommand sqlCommand = new SqlCommand(sSQL, dbConnNonQuery);
        bool result;
        try
        {
            sqlCommand.ExecuteNonQuery();
            result = true;
        }
        catch (Exception ex)
        {
            ProjectData.SetProjectError(ex);
            Exception ex2 = ex;
            result = false;
            ProjectData.ClearProjectError();
        }
        dbConnNonQuery.Close();
        return result;
    }

    public bool isLocalhost()
    {
        //if (Conversions.ToBoolean(Operators.OrObject(Operators.CompareObjectEqual(HttpContext.Current.Session["UserActual"], "ED", TextCompare: false), Operators.CompareObjectEqual(HttpContext.Current.Session["UserActual"], "FRANK", TextCompare: false))))
        //{
        //    return true;
        //}
        //return Operators.CompareString(Strings.LCase(HttpContext.Current.Request.ServerVariables["SERVER_NAME"]), "localhost", TextCompare: false) == 0;
        return false;
    }

    public string getDrawingWebURL()
    {
        return modTRS.mTRSGetSingleDesc("DRAWING", "WEBURL");
    }

    public void DumpException(Exception ex, string asOtherInfo, string asOtherInfoCaption = "Other Info", bool displayMessageAndEndResponse = true)
    {
        //modException.DumpExceptionHandler(ex, asOtherInfo, asOtherInfoCaption, rethrowDumpException: false, dbConn, displayMessageAndEndResponse);
    }
}
