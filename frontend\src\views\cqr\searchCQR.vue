<template>
  <el-main>
    <!-- <router-link to="TabLayout/19636">
      <button>Go to TabLayout Page -19636</button>
    </router-link> -->

    <!-- <router-link to="editCQR/19625">
      <button>Go to queueKey Page -19625</button>
    </router-link> -->

    <el-card>
      <div style="margin-bottom: 10px">
        <el-button type="default" @click="toggleForm">
          {{ showForm ? "收合條件" : "展開條件" }}
        </el-button>

        <el-button type="primary" @click="showDialog = true">
          Create {{ showForm ? "Create" : "" }}</el-button
        >
      </div>
      <el-collapse-transition>
        <div class="small-label-form">
          <el-form
            v-show="showForm"
            class="small-label-form"
            :model="searchForm"
            :inline="false"
            label-width="150px"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="CQR#:" prop="field1">
                  <!-- <el-input v-model="searchForm.field1" /> -->
                  <el-input
                    v-model="searchForm.cqrFromLeft"
                    type="text"
                    placeholder="FromLeft"
                    style="width: 100px"
                  />
                  .
                  <el-input
                    v-model="searchForm.cqrFromRight"
                    type="text"
                    placeholder="FromRight"
                    style="width: 100px"
                  />
                  &nbsp;To&nbsp;
                  <el-input
                    v-model="searchForm.cqrToLeft"
                    type="text"
                    placeholder="ToLeft"
                    style="width: 100px"
                  />
                  .
                  <el-input
                    v-model="searchForm.cqrToRight"
                    type="text"
                    placeholder="ToRight"
                    style="width: 100px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Manufacturing_Site:" prop="field2">
                  <el-select
                    v-model="searchForm.modelYear"
                    placeholder="Select model year"
                    style="width: 100%"
                    clearable
                  >
                    <el-option
                      v-for="option in modelYearOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <!-- <el-input v-model="searchForm.field2" /> -->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="CQR_Originator:"
                  prop="field3"
                  label-width="120px"
                >
                  <el-select
                    v-model="searchForm.modelYear"
                    style="width: 100%"
                    clearable
                  >
                    <el-option
                      v-for="option in modelOriginators"
                      :key="option.userId"
                      :label="option.fullName"
                      :value="option.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <!-- 下一排 -->
              <el-col :span="8">
                <el-form-item label="Initial_Release:" prop="field4">
                  <div style="display: flex; gap: 8px; align-items: center">
                    <el-date-picker
                      v-model="searchForm.orgDateFrom"
                      type="date"
                      placeholder="From"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 50%"
                    />
                    <!-- &nbsp;To&nbsp; -->
                    <el-date-picker
                      v-model="searchForm.orgDateTo"
                      type="date"
                      placeholder="To"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 50%"
                    />
                  </div>
                  <!-- <el-input v-model="searchForm.field4" /> -->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="	Product_Description:" prop="field5">
                  <el-input
                    v-model="searchForm.prodDesc"
                    placeholder="Enter description"
                  />
                  <!-- <el-input v-model="searchForm.field5" /> -->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="	Platform:" prop="field6">
                  <el-input v-model="searchForm.field6" />
                </el-form-item>
              </el-col>

              <!-- 下一排 -->
              <el-col :span="8">
                <el-form-item label="Due Date:" prop="field4">
                  <div style="display: flex; gap: 8px; align-items: center">
                    <el-date-picker
                      v-model="searchForm.dueDateFrom"
                      type="date"
                      placeholder="From"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                    <el-date-picker
                      v-model="searchForm.dueDateTo"
                      type="date"
                      placeholder="To"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </div>
                  <!-- <el-input v-model="searchForm.field7" /> -->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Model Year:" prop="field5">
                  <!-- <el-input v-model="searchForm.field8" /> -->
                  <el-select
                    v-model="searchForm.modelYear"
                    style="width: 100%"
                    clearable
                  >
                    <el-option
                      v-for="option in modelYearOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Account_Manager(AMGR):" prop="field6">
                  <el-select v-model="searchForm.modelYear" clearable>
                    <el-option
                      v-for="option in modelAccountMgr"
                      :key="option.userId"
                      :label="option.fullName"
                      :value="option.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-collapse-transition>
    </el-card>

    <el-card>
      <div style="margin-bottom: 10px">
        <el-button type="primary" @click="toggleForm">
          {{ showForm ? "收合條件2" : "展開條件2" }}
        </el-button>
      </div>

      <!-- 使用 el-collapse-transition 讓表單收合有動畫 -->
      <el-collapse-transition>
        <div v-show="showForm">
          <el-form :model="searchForm" label-width="160px" class="mb-2">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="OEM Group:" prop="product">
                  <el-select
                    v-model="searchForm.modelYear"
                    placeholder="Select"
                  >
                    <el-option
                      v-for="option in modelOEMGroups"
                      :key="option.oemgroup"
                      :label="option.oemgrouP_NAME"
                      :value="option.oemgroup"
                    />
                  </el-select>
                  <!-- <el-input v-model="searchForm.product" /> -->
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="Estimator:" prop="platform">
                  <el-select
                    v-model="searchForm.modelYear"
                    placeholder="Select"
                  >
                    <el-select v-model="searchForm.modelYear" clearable>
                      <el-option
                        v-for="option in modelCostEstimator"
                        :key="option.userId"
                        :label="option.fullName"
                        :value="option.userId"
                      />
                    </el-select>
                    <!-- <el-option label="2024" value="2024" /> -->
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="OEM Customer:" prop="modelYear">
                  <el-select
                    v-model="searchForm.modelYear"
                    placeholder="Select"
                  >
                    <el-option
                      v-for="option in modelOEMs"
                      :key="option.oem"
                      :label="option.oeM_NAME"
                      :value="option.oem"
                    />
                    <!-- <el-option label="2024" value="2024" /> -->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Product_Dev.Mgr(PDM):" prop="modelYear">
                  <el-select
                    v-model="searchForm.modelYear"
                    placeholder="Select"
                  >
                    <el-option
                      v-for="option in modelPETM"
                      :key="option.userId"
                      :label="option.fullName"
                      :value="option.userId"
                    />
                    <!-- <el-option label="2024" value="2024" /> -->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Quote_Type:" prop="modelYear">
                  <el-select
                    v-model="searchForm.modelYear"
                    placeholder="Select"
                  >
                    <el-option
                      v-for="option in modelQuoteTypeOtions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                    <!-- <el-option label="2024" value="2024" /> -->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Customer_RFQ_Number:" prop="modelYear">
                  <el-input v-model="searchForm.cqrFromLeft" type="text" />
                  <!-- <el-select
                    v-model="searchForm.modelYear"
                    placeholder="Select"
                  >
                    <el-option label="2024" value="2024" />
                  </el-select> -->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="Product_Group_Manager(PGM):"
                  prop="modelYear"
                >
                  <el-select
                    v-model="searchForm.modelYear"
                    placeholder="Select"
                  >
                    <el-option
                      v-for="option in modelPGM"
                      :key="option.userId"
                      :label="option.fullName"
                      :value="option.userId"
                    />
                    <!-- <el-option label="2024" value="2024" /> -->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Won Date:" prop="modelYear">
                  <div style="display: flex; gap: 8px; align-items: center">
                    <el-date-picker
                      v-model="searchForm.orgDateFrom"
                      type="date"
                      placeholder="From"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 50%"
                    />
                    <!-- &nbsp;To&nbsp; -->
                    <el-date-picker
                      v-model="searchForm.orgDateTo"
                      type="date"
                      placeholder="To"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 50%"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Opening Meeting Date:" prop="modelYear">
                  <div style="display: flex; gap: 8px; align-items: center">
                    <el-date-picker
                      v-model="searchForm.orgDateFrom"
                      type="date"
                      placeholder="From"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 50%"
                    />
                    <!-- &nbsp;To&nbsp; -->
                    <el-date-picker
                      v-model="searchForm.orgDateTo"
                      type="date"
                      placeholder="To"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 50%"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <el-form-item label="" prop="modelYear">
                  <el-checkbox-group
                    v-model="checkedList"
                    @change="handleChange"
                  >
                    <el-checkbox value="onlyQuoteLettersWon"
                      >Only Quote Letters Won</el-checkbox
                    >
                    <el-checkbox value="showOnlyOpenCQR"
                      >Show Only Open CQR</el-checkbox
                    >
                    <el-checkbox value="quoteStatusReport"
                      >Quote Status Business Report</el-checkbox
                    >
                    <el-checkbox value="findLastTwoFolders"
                      >Find Last Two Pending Folders</el-checkbox
                    >
                    <!-- 隱藏的 checkbox 也可以先用 v-show 控制 -->
                    <el-checkbox value="excludeFolders"
                      >Exclude Terminated and Closed Folders</el-checkbox
                    >
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-collapse-transition>
    </el-card>

    <div class="div-action">
      <el-button type="primary" @click="dialogVisible = true"
        >打開對話視窗</el-button
      >
      <!-- 切換按鈕 -->
      <el-button type="primary" @click="isFullscreen = !isFullscreen">
        {{ isFullscreen ? "退出全螢幕" : "全螢幕顯示" }}
      </el-button>
      <el-button type="primary" @click="btnSearchCQRHeaders">
        Search
      </el-button>
    </div>
    <!-- 表格區塊 -->
    <div :class="['table-container', { fullscreen: isFullscreen }]">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; max-height: 400px; overflow-y: auto"
        border
        height="500"
      >
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="goToEdit(scope.row.queueKey)"
              >編輯</el-button
            >
          </template>
        </el-table-column>
        <!-- <el-table-column prop="cqrId" label="CQR 編號" width="120" /> -->
        <el-table-column label="CQR #" width="200">
          <template #default="{ row }">
            {{ row.queueKey }}
          </template>
        </el-table-column>
        <el-table-column label="CQR #" width="200">
          <template #default="{ row }">
            {{ row.projectNbr }} - {{ row.revNbr }}
          </template>
        </el-table-column>
        <el-table-column prop="originator" label="發起人" width="160" />
        <el-table-column label="OEM Group" prop="oemGroup" />
        <el-table-column label="OEM Customer" prop="oemCustomer" />
        <el-table-column label="Account Manager" prop="accountManager" />
        <el-table-column label="Estimator" prop="estimator" />
        <el-table-column
          label="Engineering Director"
          prop="engineeringDirector"
        />
        <el-table-column label="Program Manager" prop="programManager" />
        <el-table-column
          show-overflow-tooltip
          fit="true"
          label="Engineering Coordinator"
          prop="engineeringCoordinator"
        />
        <el-table-column
          show-overflow-tooltip
          label="Responsible Design Eng."
          prop="responsibleDesignEng"
        />
        <el-table-column
          show-overflow-tooltip
          label="Product Description"
          prop="productDescription"
          width="200"
        />
        <el-table-column label="Gate Exit" prop="gateExit" />
        <el-table-column label="Status" prop="status" />
        <el-table-column label="Release Date" prop="releaseDate" />
        <el-table-column label="Issue Date" prop="issueDate" />
        <el-table-column
          label="Due Date from Engineering"
          prop="dueDateFromEng"
        />
        <el-table-column label="Due Date to B&E" prop="dueDateToBE" />
        <el-table-column
          label="Quote Response Due Date"
          prop="quoteResponseDueDate"
        />
        <el-table-column
          label="Opening Meeting Date"
          prop="openingMeetingDate"
        />
        <el-table-column
          label="CQR Description"
          prop="cqrDescription"
          width="250"
        />
        <el-table-column label="Vehicle" prop="vehicle" />
        <el-table-column label="Model Year" prop="modelYear" />
        <el-table-column label="Volume Per Annum" prop="volumePerAnnum" />
        <el-table-column
          label="Approx. Annual Value $"
          prop="approxAnnualValue"
        />
        <el-table-column
          label="Background Info"
          prop="backgroundInfo"
          width="250"
        />
        <el-table-column
          label="Antares Unique Number"
          prop="antaresUniqueNumber"
        />
        <el-table-column
          label="Eng. Pkg. Comment"
          prop="engPkgComment"
          width="250"
        />
        <el-table-column label="Manufacturing Site" prop="manufacturingSite" />
        <!-- <el-table-column
          v-for="col in columns"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :width="col.width"
          :show-overflow-tooltip="
            [
              'bkRndInfComments',
              'otherInfo',
              'actionComments',
              'qrToolingComments'
            ].includes(col.prop)
          "
        /> -->
      </el-table>
    </div>
    <el-pagination
      :current-page="page"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      background
      style="margin-top: 20px; text-align: right"
      @current-change="handlePageChange"
    />
    <CreateCQRDialog
      v-model="showDialog"
      :form="createCQRForm"
      @create="createNewCQRForm"
      @changeCQRType="onCQRTypeChange"
      @changeQuoteType="onQuoteTypeChange"
    />
  </el-main>
</template>

<script setup lang="ts">
import CreateCQRDialog from "@/components/CQR/CreateCQRDialog.vue";
import { QuestionFilled } from "@element-plus/icons-vue";
import { ref, onMounted, computed } from "vue";
// 假設有一個 API 方法 fetchData 來獲取用戶資料
import { getCqrList } from "@/api/cqr"; // 替換為實際的 API 方法
import { useRouter } from "vue-router";
import { getOriginatorList, getRoleUseCollection } from "@/api/roleUser";
import { useCreateCQRFormStore } from "@/store/modules/createCQRForm";
import { useFormModelStore } from "@/store/modules/formModel";
// import { useCqrStore } from "@/store/cqr"; // 假設你的 store 路徑
import type {
  // CreateCQRForm,
  HttpResponseResult,
  OEM,
  OEMHGroup,
  OEMHiearchy,
  RoleUserCollectionComplete,
  RoleUserInfo
} from "@/store/types";
import { getOEMHiearchy } from "@/api/sapERP";
import { validateCreateCQRCriteria } from "@/api/cqr";

type Option = {
  value: string;
  label: string;
};
// const createCQRForm = useCreateCQRFormStore().criteria;

const router = useRouter();
// const showForm = false; // 控制是否顯示表單
// const cqrStore = useCqrStore();
const showForm = ref(true);
const toggleForm = () => {
  showForm.value = !showForm.value;
};
const checkedList = ref<string[]>([]);

const modelQuoteTypeOtions: Option[] = [
  { value: "", label: "*" },
  { value: "Customer", label: "C" },
  { value: "Internal", label: "T" }
];
const sUseFormModelStore = useFormModelStore();
const sUseCreateCQRFormStore = useCreateCQRFormStore();
// 包整個 criteria 成 reactive 的 computed 對象
const createCQRForm = computed({
  get: () => sUseCreateCQRFormStore.criteria,
  set: val => (sUseCreateCQRFormStore.criteria = val)
});

const showDialog = ref(false);
// const showForm = ref(true);

const isFullscreen = ref(false);

function goToEdit(id) {
  // 使用命名路由跳轉，這樣可以自動處理參數和路徑
  router.push({ name: "cqrEdit", params: { queueKey: id } });
}

async function createNewCQRForm() {
  //TODO 假如選modify選項 沒有填值就不能往下. 畫面檢查

  //TODO  後台檢查 modify
  const result = await validateCreateCQRCriteria(
    sUseCreateCQRFormStore.criteria
  );

  console.log(sUseCreateCQRFormStore.criteria);

  const queueKey: number = sUseFormModelStore.createNewCQRHeaderDraft();

  // 假設有一個 API 方法來創建 CQR
  // await cqrStore.createCQR(createCQRForm.value);
  // TODO queuekey大於0 才能轉.
  if (queueKey > 0) {
    showDialog.value = false; // 關閉對話框
    // TODO 換頁
  }

  router.push({ name: "cqrEdit", params: { queueKey: queueKey } });
  // 呼叫 API 建立 CQR
  // 需自行實作 API
  // const queueKey = result.data.queueKey; // 後台回傳的 QueueKey

  // 存到 store
  // cqrStore.setCurrentCqr({
  //   ...createCQRForm.value,
  //   queueKey
  // });

  // 跳轉到編輯頁面
  // const dummyQueueKey = -1; // 假設這是後端返回的 queueKey
}

const dialogVisible = ref(false);

// 搜尋條件
const searchForm = ref({
  name: "",
  email: "",
  queueKey: ""
});
const showAdvanced = ref(false);
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value;
};

const doSearch = () => {
  // 可加後端搜尋請求邏輯
};

const tableData = ref<any[]>([]);
const page = ref<number>(1);
const pageSize = 10;
const total = ref<number>(0);
const loading = ref<boolean>(false);
const searchText = ref<string>("");

const modelOriginators = ref<RoleUserInfo[]>([]);
const modelPGM = ref<RoleUserInfo[]>([]);
const modelPETM = ref<RoleUserInfo[]>([]);
const modelCostEstimator = ref<RoleUserInfo[]>([]);
const modelAccountMgr = ref<RoleUserInfo[]>([]);

const modelOEMs = ref<OEM[]>([]);
const modelOEMGroups = ref<OEMHGroup[]>([]);

//todo 搬出來
const fetchData = async (): Promise<void> => {
  loading.value = true;
  try {
    const respOEMHiearchys = await getOEMHiearchy();
    const OEMHiearchies = respOEMHiearchys.data as OEMHiearchy[];

    const mapOEMs: OEM[] = Array.from(
      new Map(
        OEMHiearchies.map(s => [s.oem, { oem: s.oem, oeM_NAME: s.oeM_NAME }])
      ).values()
    );
    const mapOEMGroups: OEMHGroup[] = Array.from(
      new Map(
        OEMHiearchies.map(s => [
          s.oemgroup,
          { oemgroup: s.oemgroup, oemgrouP_NAME: s.oemgrouP_NAME }
        ])
      ).values()
    );
    modelOEMs.value = mapOEMs;
    modelOEMGroups.value = mapOEMGroups;
    // debugger;

    // Load Role Users
    const respRoleUsers: HttpResponseResult = await getRoleUseCollection();
    const roleCollection = respRoleUsers.data as RoleUserCollectionComplete[];
    const Originators = roleCollection.find(
      r => r.roleName === "OriginatorId"
    ).users;

    const PETMs = roleCollection.find(r => r.roleName === "PETMId").users;
    const PGMs = roleCollection.find(r => r.roleName === "PGMId").users;
    // const PETMs = roleCollection.find(r => r.roleName === "PETMId");
    const CostEstimators = roleCollection.find(
      r => r.roleName === "CostEstimatorId"
    ).users;
    const AccountMgrs = roleCollection.find(
      r => r.roleName === "AccountMgrId"
    ).users;

    modelOriginators.value = Originators;
    modelPETM.value = PETMs;
    modelPGM.value = PGMs;
    modelCostEstimator.value = CostEstimators;
    modelAccountMgr.value = AccountMgrs;

    const res: any = await getCqrList();
    console.log(res.data);
    tableData.value = res.data;
    // total.value = res.total;
  } finally {
    loading.value = false;
  }

  // debugger;
};

// const createNewCQRForm = () => {
//   // 處理送出邏輯
//   console.log("create:", createCQRForm);
//   showDialog.value = false;
// };
const store = useCreateCQRFormStore();
const onCQRTypeChange = () => {
  // 做些額外邏輯
  console.log("🔍 Current CQR Form:", store.criteria);
  //TODO 選New 時只能選前2種qute type.
  if (
    store.criteria.cqrType == "New" &&
    (store.criteria.quoteType == "3" || store.criteria.quoteType == "4")
  ) {
    //TODO 無法點選 button  送出.
  }
};
const btnSearchCQRHeaders = () => {
  //
};

const onQuoteTypeChange = () => {
  // 做些額外邏輯
};

const handlePageChange = (newPage: number): void => {
  page.value = newPage;
  fetchData();
};
const handleChange = (value: any) => {
  // console.log("changed:", value);
  // 你的邏輯
};

const handleSearch = (): void => {
  page.value = 1;
  fetchData();
};

// onMounted(fetchData);
const columns = ref<{ prop: string; label: string }[]>([]);

function handleClose(done) {
  // 你可以在這裡做一些確認動作
  done();
}

onMounted(async () => {
  await fetchData();
  //load role user

  if (tableData.value.length > 0) {
    columns.value = Object.keys(tableData.value[0]).map(key => ({
      prop: key,
      label: key // 可以改成轉換函式 formatLabel(key)
    }));
  }
});
</script>

<style scoped>
/* .small-label-form .el-form-item__label {
  font-size: 10px;
} */
/* :deep(*) {
  font-size: 11px !important;
} */

.table-container {
  margin-top: 16px;
  height: 400px;
  transition: all 0.3s ease;
}

.table-container.fullscreen {
  position: fixed;
  inset: 0; /* top: 0; left: 0; right: 0; bottom: 0 */
  z-index: 999;
  background-color: white;
  padding: 16px;
  height: 100vh;
  box-sizing: border-box;
}

.el-table th {
  white-space: nowrap;
}
</style>
