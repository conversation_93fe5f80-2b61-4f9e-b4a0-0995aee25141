﻿using CQR.Application.Dto;
using CQR.Application.Dto.RoleUser;

namespace CQR.Application.Repositories;

public interface IUserQueryRepository
{
    Task<List<UserRoleDto>> GetUsersWithRolesAsync(string[]? roles = null, string[]? locationCodes = null);
    IEnumerable<UserFullNameDto> GetUsers(string role, string selectCode = "", string locationFilter = "", string topEntryText = "", string valueField = "UserId");
}
