using Microsoft.AspNetCore.Http;

namespace CQR.Infrastructure.Utilities
{
    public class FileUtils
    {

        public static async Task UploadFile(string floder, List<IFormFile> files) 
        {
            foreach (var file in files)
            {
                var filePath = Path.Combine(Directory.GetCurrentDirectory(), floder, file.FileName);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
                if (!Directory.Exists(Path.GetDirectoryName(filePath))) 
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(filePath));
                }
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }
            }
        }

        public static async Task DeleteFile(string filePath)
        {
            if (File.Exists(filePath))
            {
                await Task.Run(() => File.Delete(filePath));
            }
        }

        

    }
}
