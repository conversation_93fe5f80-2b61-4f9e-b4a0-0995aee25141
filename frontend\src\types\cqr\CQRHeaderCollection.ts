// import type { CQR_Header } from "../CQR_Header";
import type { Antares } from "./Antares";
import type { CQR_IHSFolder } from "./CQR_IHSFolder";
import type { GDPEP_APPROVAL } from "./GDPEP_APPROVAL";
import type { RoutingTaskDto } from "./RoutingTaskDto";
import type { UserRoles } from "./UserRoles";

export interface CQRHeaderCollection {
  iCQRHeader?: CQR_Header;
  sDbsCommentsAction?: string;
  sNewCommentsAction?: string;
  sTotalCommentsAction?: string;

  iQueueKey: number;
  iOpenMode: number;
  bFolderWasLocked: boolean;
  sStatus?: string;
  sStatusDate?: string;
  sOriginator?: string;
  sOriginationDate?: string;
  bNewCQR: boolean;
  iSaveMode: number;
  bSuperUser: boolean;
  sFolderNum?: string;
  sProjectNum?: string;
  sRevision?: string;
  sCQRIssueDate?: string;
  sFranNewModRepl?: string;
  sQuoteType?: string;
  sUserStamp?: string;
  sSecurityMode?: string;
  sCommentsHeld?: string;

  // Section 1
  sComment?: string;
  sNewComment?: string;
  sOrgComment?: string;
  sCQRDesc?: string;
  sProductDescription?: string;
  sVehicleBuildId?: string;
  sOEMGroup?: string;
  sCustomerBuyer?: string;
  sCustomerEngineer?: string;
  sPlatform?: string;
  sNameplate?: string;
  sModelYear?: string;
  sRFQRefNum?: string;
  sRFQRecDate?: string;
  sCustomerQuoteDueDateLast?: string;
  sCustomerQuoteDueDateOrig?: string;
  sCustomerQuoteDueDateChangeComment?: string;
  sQuoteRespDueDate?: string;
  sQuoteRespDueDateDbs?: string;
  sManufacturingSite?: string;
  sManufacturingSiteDbs?: string;
  sPIMSite?: string;
  sPIMSiteDbs?: string;
  sEngineeringSite?: string;
  sEngineeringSiteDbs?: string;
  sApproxAnnualValue?: string;
  sObsolescenceRequiredInd?: string;
  sCommissionRequiredInd?: string;
  sCommissionPercentage?: string;
  sManufacturerRepCompany?: string;
  sManufacturerRepIndividual?: string;
  sAwardQuarter?: string;
  sAwardYear?: string;
  sGateExit?: string;

  // Section 2~4、routingTasks、其他欄位略...
  routingTasks?: RoutingTaskDto[];
  ihsFolderRecords?: CQR_IHSFolder[];
  cUserRoles?: UserRoles;

  tGateway: [GDPEP_APPROVAL, GDPEP_APPROVAL, GDPEP_APPROVAL];

  bUserIsOrig: boolean;
  bUserHasOrigRole: boolean;
  bOrigIsPgm: boolean;
  bOrigIsPetm: boolean;
  bOrigIsAMgr: boolean;
  bOrigIsSDir: boolean;
  bOrigIsCost: boolean;
  bOrigIsMbne: boolean;
  bDualRelease: boolean;
  bHasChecked: boolean;

  sIndTask?: string;
  antaresRecords: Antares;
}
