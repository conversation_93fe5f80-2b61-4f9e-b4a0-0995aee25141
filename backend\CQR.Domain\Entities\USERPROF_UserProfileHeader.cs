﻿using System.ComponentModel.DataAnnotations;

namespace CQR.Domain.Entities;
public class USERPROF_UserProfileHeader
{
    [Required]
    [StringLength(10)]
    public int QueueKey { get; set; }

    [StringLength(8)]
    public string UserId { get; set; }

    [StringLength(30)]
    public string LastName { get; set; }

    [StringLength(30)]
    public string FirstName { get; set; }

    [StringLength(1)]
    public string MiddleInitial { get; set; }

    [StringLength(3)]
    public string CompanyCode { get; set; }

    [StringLength(2)]
    public string LocationCode { get; set; }

    [StringLength(3)]
    public string DepartmentCode { get; set; }

    [StringLength(15)]
    public string NTUserId { get; set; }

    [StringLength(15)]
    public string GWMailId { get; set; }

    [StringLength(1)]
    public string Inactive { get; set; }

    [StringLength(20)]
    public string Flags { get; set; }

    [StringLength(50)]
    public string EreqPlant { get; set; }

    [StringLength(1)]
    public string OverrideEmail { get; set; }

    [StringLength(60)]
    public string OverrideEmailAddress { get; set; }

    public byte? RemindAllActions { get; set; }

    public DateTime? LastUpdatedDate { get; set; }

    [StringLength(25)]
    public string LastUpdatedBy { get; set; }

    public DateTime? CreatedDate { get; set; } = DateTime.Now;

    [StringLength(25)]
    public string CreatedBy { get; set; }
}
