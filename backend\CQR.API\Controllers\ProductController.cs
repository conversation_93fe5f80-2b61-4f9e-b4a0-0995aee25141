using CQR.Application.Services;
using Microsoft.AspNetCore.Mvc;

namespace CQRAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProductsController : ControllerBase
    {
        private readonly IApplicationServices _appServices;
        
        public ProductsController(IApplicationServices appServices)
        {
            _appServices = appServices;
        }

        [HttpPost]
        public async Task<IActionResult> Create()
        {
            // TODO: Implement product creation through ApplicationServices
            return BadRequest("Product creation not yet implemented in ApplicationService pattern");
        }
        //private readonly IValidator<Product> _productValidator;
        //private readonly IProductService _productService;
        //public ProductController(IMediator mediator,IProductService productService, IValidator<Product> productValidator)
        //{
        //    _productService = productService;
        //    _productValidator = productValidator;
        //}
        //[HttpGet("getAllProducts")]
        //public async Task<ActionResult<IEnumerable<Product>>> GetAllProducts()
        //{
        //    var products = await _productService.GetProductsAsync();
        //    return Ok(products);
        //}
        //[HttpGet("Product/{productid}")]
        //public async Task<ActionResult<Product>> GetProductById([FromRoute] int productid = 0)
        //{
        //    var entity = await _productService.GetProductByIdAsync(productid);
        //    if (entity == null)
        //    {
        //        return NotFound("Product not found.");
        //    }
        //    return Ok(entity);
        //}
        //[HttpPost("create")]
        //public async Task<IActionResult> CreateProduct([FromBody] Product product)
        //{
        //    var validationResult = await _productValidator.ValidateAsync(product);
        //    if (!validationResult.IsValid)
        //    {
        //        return BadRequest(validationResult.Errors);
        //    }

        //    await _productService.AddProductAsync(product);

        //    // return Ok(product);  // 返回添加的产品或其他你想返回的结果
        //    return CreatedAtAction(nameof(GetProductById), new { id = product.Id }, product);
        //}
        //[HttpPost("createWithDetails")]
        //public async Task<IActionResult> CreateProductWithDetails([FromBody] ProductWithDetailsDto productWithDetailsDto)
        //{
        //    await _productService.CreateProductWithDetails(productWithDetailsDto);
        //    return CreatedAtAction(nameof(GetAllProducts), new { id = productWithDetailsDto.Product.Id }, productWithDetailsDto.Product);
        //}
        //// PUT: api/product/5
        //[HttpPut("{id}")]
        //public async Task<IActionResult> PutProduct(int id, [FromBody] Product product)
        //{
        //    if (product == null || id != product.Id)
        //    {
        //        return BadRequest();
        //    }

        //    await _productService.UpdateProductAsync(product);
        //    return NoContent(); // 204 No Content, update 成功但没有返回数据
        //}
        //// DELETE: api/product/{id}
        //[HttpDelete("{id}")]
        //public async Task<IActionResult> DeleteProduct(int id)
        //{
        //    await _productService.DeleteProductAsync(id);
        //    return NoContent();
        //}
    }
}
