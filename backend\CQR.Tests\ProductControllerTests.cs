using CQR.Domain.Entities;
using CQR.Domainore.Services;
using FluentValidation;
using Moq;

public class ProductControllerTests
{
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IValidator<Product>> _mockProductValidator;  // Mocking the IValidator<Product>

    //private readonly ProductController _productController;
    public ProductControllerTests()
    {
        _mockProductService = new Mock<IProductService>();
        _mockProductValidator = new Mock<IValidator<Product>>();  // Creating mock for IValidator<Product>

        //_productController = new ProductController(_mockProductService.Object, _mockProductValidator.Object);
    }

    // 测试 GET api/product
    [Fact]
    public async Task GetProducts_ShouldReturnOkResult_WithProducts()
    {
        // Arrange
        var products = new List<Product>
        {
            new Product { Id = 1, Name = "Product 1", Price = 10, Stock = 100 },
            new Product { Id = 2, Name = "Product 2", Price = 20, Stock = 200 }
        };
        _mockProductService.Setup(service => service.GetProductsAsync())
                           .ReturnsAsync(products);

        // Act
        //var result = await _productController.GetAllProducts();

        // Assert
        //var okResult = Assert.IsType<OkObjectResult>(result);  // Assert it's OkObjectResult
        //var returnedProducts = Assert.IsType<IEnumerable<Product>>(okResult.Value);  // Assert it's IEnumerable<Product>

        // Ensure returned products are not null or empty
        //Assert.NotNull(returnedProducts);
        //Assert.NotEmpty(returnedProducts);

        //// Assert the count matches expected value
        //Assert.Equal(2, returnedProducts.Count());
        // // Assert
        // var okResult = Assert.IsType<OkObjectResult>(result);
        // var returnedProducts = Assert.IsType<IEnumerable<Product>>(okResult.Value);
        // // Use LINQ to get the count of returned products
        // Assert.Equal(2, returnedProducts.Count());
    }

    // 测试 GET api/product/{id}
    //[Fact]
    //public async Task GetProduct_ShouldReturnOkResult_WithProduct()
    //{
    //    // Arrange
    //    var product = new Product { Id = 1, Name = "Product 1", Price = 10, Stock = 100 };
    //    _mockProductService.Setup(service => service.GetProductByIdAsync(1))
    //                       .ReturnsAsync(product);

    //    // Act
    //    var result = await _productController.GetProductById(1);

    //    // Assert
    //    var okResult = Assert.IsType<OkObjectResult>(result);
    //    var returnedProduct = Assert.IsType<Product>(okResult.Value);
    //    Assert.Equal(1, returnedProduct.Id);
    //}

    // 测试 POST api/product
    //[Fact]
    //public async Task PostProduct_ShouldReturnCreatedResult()
    //{
    //    // Arrange
    //    var product = new Product { Name = "New Product", Price = 15, Stock = 50 };
    //    _mockProductService.Setup(service => service.AddProductAsync(It.IsAny<Product>()))
    //                       .Returns(Task.CompletedTask);

    //    // Act
    //    var result = await _productController.CreateProduct(product);

    //    // Assert
    //    var createdResult = Assert.IsType<CreatedAtActionResult>(result);  // Assert CreatedAtActionResult
    //    var returnedProduct = Assert.IsType<Product>(createdResult.Value);  // Assert the returned product is of type Product
    //    Assert.Equal("New Product", returnedProduct.Name);  // Check the product name

    //    // Verify that AddProductAsync was called exactly once
    //    _mockProductService.Verify(service => service.AddProductAsync(It.Is<Product>(p => p.Name == "New Product")), Times.Once);
    //}

    // 测试 PUT api/product/{id}
    //[Fact]
    //public async Task PutProduct_ShouldReturnNoContent_WhenUpdateIsSuccessful()
    //{
    //    // Arrange
    //    var product = new Product { Id = 1, Name = "Updated Product", Price = 20, Stock = 100 };
    //    _mockProductService.Setup(service => service.UpdateProductAsync(It.IsAny<Product>()))
    //                       .Returns(Task.CompletedTask);
    //    // Act
    //    var result = await _productController.PutProduct(1, product);
    //    // Assert
    //    Assert.IsType<NoContentResult>(result); // 204 No Content
    //}

    // 测试 DELETE api/product/{id}
    //[Fact]
    //public async Task DeleteProduct_ShouldReturnNoContent_WhenProductIsDeleted()
    //{
    //    // Arrange
    //    _mockProductService.Setup(service => service.DeleteProductAsync(1))
    //                       .Returns(Task.CompletedTask);

    //    // Act
    //    var result = await _productController.DeleteProduct(1);

    //    // Assert
    //    Assert.IsType<NoContentResult>(result); // 204 No Content
    //}
}
