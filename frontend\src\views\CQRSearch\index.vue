<template>
  <div class="cqr-search-container">
    <!-- 搜尋標題和工具欄 -->
    <div class="search-header">
      <div class="title-section">
        <h2>CQR 搜尋</h2>
        <el-tag v-if="searchResults.totalCount > 0" type="info">
          找到 {{ searchResults.totalCount }} 筆記錄
        </el-tag>
      </div>
      <div class="toolbar">
        <el-button @click="clearAllFilters" icon="Delete">清除條件</el-button>
        <el-button @click="exportResults" icon="Download" type="primary">匯出結果</el-button>
      </div>
    </div>

    <!-- 基本搜尋條件 -->
    <el-card class="search-card" shadow="never">
      <template #header>
        <span class="card-header">基本搜尋條件</span>
      </template>
      
      <el-form :model="searchForm" label-width="120px" class="search-form">
        <el-row :gutter="20">
          <!-- CQR 編號範圍 -->
          <el-col :span="8">
            <el-form-item label="CQR #:">
              <div class="cqr-number-range">
                <el-input
                  v-model="searchForm.cqrFromLeft"
                  placeholder="專案編號"
                  style="width: 80px"
                  @keyup.enter="handleSearch"
                />
                <span>.</span>
                <el-input
                  v-model="searchForm.cqrFromRight"
                  placeholder="版本"
                  style="width: 60px"
                  @keyup.enter="handleSearch"
                />
                <span style="margin: 0 8px">To</span>
                <el-input
                  v-model="searchForm.cqrToLeft"
                  placeholder="專案編號"
                  style="width: 80px"
                  @keyup.enter="handleSearch"
                />
                <span>.</span>
                <el-input
                  v-model="searchForm.cqrToRight"
                  placeholder="版本"
                  style="width: 60px"
                  @keyup.enter="handleSearch"
                />
              </div>
            </el-form-item>
          </el-col>

          <!-- 製造地點 -->
          <el-col :span="8">
            <el-form-item label="Manufacturing Site:">
              <el-select
                v-model="searchForm.manufacturingSite"
                placeholder="選擇製造地點"
                clearable
                filterable
              >
                <el-option
                  v-for="site in manufacturingSiteOptions"
                  :key="site.value"
                  :label="site.text"
                  :value="site.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- CQR 發起人 -->
          <el-col :span="8">
            <el-form-item label="CQR Originator:">
              <el-select
                v-model="searchForm.cqrOriginator"
                placeholder="選擇發起人"
                clearable
                filterable
              >
                <el-option
                  v-for="user in originatorOptions"
                  :key="user.value"
                  :label="user.text"
                  :value="user.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 初始發佈日期 -->
          <el-col :span="8">
            <el-form-item label="Initial Release:">
              <div class="date-range">
                <el-date-picker
                  v-model="searchForm.initialReleaseDateFrom"
                  type="date"
                  placeholder="開始日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 110px"
                />
                <span style="margin: 0 8px">To</span>
                <el-date-picker
                  v-model="searchForm.initialReleaseDateTo"
                  type="date"
                  placeholder="結束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 110px"
                />
              </div>
            </el-form-item>
          </el-col>

          <!-- 產品描述 -->
          <el-col :span="8">
            <el-form-item label="Product Description:">
              <el-input
                v-model="searchForm.productDescription"
                placeholder="產品描述 (*支援萬用字元)"
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>

          <!-- 平台 -->
          <el-col :span="8">
            <el-form-item label="Platform:">
              <el-input
                v-model="searchForm.platform"
                placeholder="平台名稱"
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 到期日期 -->
          <el-col :span="8">
            <el-form-item label="Due Date:">
              <div class="date-range">
                <el-date-picker
                  v-model="searchForm.dueDateFrom"
                  type="date"
                  placeholder="開始日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 110px"
                />
                <span style="margin: 0 8px">To</span>
                <el-date-picker
                  v-model="searchForm.dueDateTo"
                  type="date"
                  placeholder="結束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 110px"
                />
              </div>
            </el-form-item>
          </el-col>

          <!-- 年式 -->
          <el-col :span="8">
            <el-form-item label="Model Year:">
              <el-select
                v-model="searchForm.modelYear"
                placeholder="選擇年式"
                clearable
              >
                <el-option
                  v-for="year in modelYearOptions"
                  :key="year.value"
                  :label="year.text"
                  :value="year.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 客戶經理 -->
          <el-col :span="8">
            <el-form-item label="Account Manager:">
              <el-select
                v-model="searchForm.accountManager"
                placeholder="選擇客戶經理"
                clearable
                filterable
              >
                <el-option
                  v-for="user in accountManagerOptions"
                  :key="user.value"
                  :label="user.text"
                  :value="user.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="search-actions">
              <el-button @click="handleSearch" type="primary" icon="Search" :loading="searchLoading">
                搜尋
              </el-button>
              <el-button @click="toggleAdvancedSearch" type="info" plain>
                {{ showAdvanced ? '隱藏' : '顯示' }}進階搜尋
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 進階搜尋條件 -->
    <el-card v-show="showAdvanced" class="search-card advanced-search" shadow="never">
      <template #header>
        <span class="card-header">進階搜尋條件</span>
      </template>

      <el-form :model="advancedForm" label-width="120px" class="search-form">
        <el-row :gutter="20">
          <!-- OEM Group -->
          <el-col :span="8">
            <el-form-item label="OEM Group:">
              <el-select
                v-model="advancedForm.oemGroup"
                placeholder="選擇 OEM Group"
                clearable
                filterable
              >
                <el-option
                  v-for="group in oemGroupOptions"
                  :key="group.value"
                  :label="group.text"
                  :value="group.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- Estimator -->
          <el-col :span="8">
            <el-form-item label="Estimator:">
              <el-select
                v-model="advancedForm.estimator"
                placeholder="選擇估價員"
                clearable
                filterable
              >
                <el-option
                  v-for="user in estimatorOptions"
                  :key="user.value"
                  :label="user.text"
                  :value="user.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 篩選選項 -->
          <el-col :span="8">
            <el-form-item label="篩選選項:">
              <div class="filter-checkboxes">
                <el-checkbox v-model="advancedForm.onlyQuoteLettersWon">
                  Only Quote Letters Won
                </el-checkbox>
                <el-checkbox v-model="advancedForm.openCQRFolderOnly">
                  Show Only Open CQR
                </el-checkbox>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- OEM Customer -->
          <el-col :span="8">
            <el-form-item label="OEM Customer:">
              <el-select
                v-model="advancedForm.oemCustomer"
                placeholder="選擇 OEM Customer"
                clearable
                filterable
              >
                <el-option
                  v-for="customer in oemCustomerOptions"
                  :key="customer.value"
                  :label="customer.text"
                  :value="customer.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- PDM -->
          <el-col :span="8">
            <el-form-item label="Product Dev. Mgr:">
              <el-select
                v-model="advancedForm.assignedPDM"
                placeholder="選擇 PDM"
                clearable
                filterable
              >
                <el-option
                  v-for="user in pdmOptions"
                  :key="user.value"
                  :label="user.text"
                  :value="user.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 報表選項 -->
          <el-col :span="8">
            <el-form-item label="報表選項:">
              <div class="filter-checkboxes">
                <el-checkbox v-model="advancedForm.quoteReport">
                  Quote Status Business Report
                </el-checkbox>
                <el-checkbox 
                  v-model="advancedForm.findLastTwoFolders"
                  :disabled="!advancedForm.quoteReport"
                >
                  Find Last Two Pending Folders
                </el-checkbox>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- PGM -->
          <el-col :span="8">
            <el-form-item label="Product Group Mgr:">
              <el-select
                v-model="advancedForm.assignedPGM"
                placeholder="選擇 PGM"
                clearable
                filterable
              >
                <el-option
                  v-for="user in pgmOptions"
                  :key="user.value"
                  :label="user.text"
                  :value="user.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- Won Date -->
          <el-col :span="8">
            <el-form-item label="Won Date:">
              <div class="date-range">
                <el-date-picker
                  v-model="advancedForm.wonDateFrom"
                  type="date"
                  placeholder="From"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 110px"
                />
                <span style="margin: 0 8px">To</span>
                <el-date-picker
                  v-model="advancedForm.wonDateTo"
                  type="date"
                  placeholder="To"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 110px"
                />
              </div>
            </el-form-item>
          </el-col>

          <!-- Opening Meeting Date -->
          <el-col :span="8">
            <el-form-item label="Opening Meeting:">
              <div class="date-range">
                <el-date-picker
                  v-model="advancedForm.openingMeetingDateFrom"
                  type="date"
                  placeholder="From"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 110px"
                />
                <span style="margin: 0 8px">To</span>
                <el-date-picker
                  v-model="advancedForm.openingMeetingDateTo"
                  type="date"
                  placeholder="To"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 110px"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 搜尋結果表格 -->
    <el-card class="results-card" shadow="never">
      <template #header>
        <div class="results-header">
          <span class="card-header">搜尋結果</span>
          <div class="results-info" v-if="searchResults.totalCount > 0">
            <el-tag type="success">
              第 {{ searchResults.pageNumber }} 頁，共 {{ searchResults.totalPages }} 頁
            </el-tag>
          </div>
        </div>
      </template>

      <div v-if="searchResults.results?.length === 0 && !searchLoading" class="no-results">
        <el-empty description="無搜尋結果" />
      </div>

      <div v-else>
        <!-- 表格排序控制 -->
        <div class="table-controls" v-if="searchResults.results?.length > 0">
          <div class="sort-controls">
            <span>排序：</span>
            <el-select
              v-model="currentSortField"
              placeholder="選擇排序欄位"
              style="width: 150px"
              @change="handleSortFieldChange"
            >
              <el-option
                v-for="preset in sortPresets"
                :key="preset.field"
                :label="preset.label"
                :value="preset.field"
              />
            </el-select>
            <el-radio-group v-model="currentSortDirection" @change="handleSortDirectionChange">
              <el-radio-button label="asc">升序</el-radio-button>
              <el-radio-button label="desc">降序</el-radio-button>
            </el-radio-group>
          </div>
          <div class="view-controls">
            <span>每頁顯示：</span>
            <el-select
              v-model="pageSize"
              style="width: 80px"
              @change="handlePageSizeChange"
            >
              <el-option
                v-for="size in pageSizeOptions"
                :key="size"
                :label="size"
                :value="size"
              />
            </el-select>
          </div>
        </div>

        <!-- 結果表格 -->
        <el-table
          ref="resultTable"
          :data="searchResults.results"
          style="width: 100%"
          height="600"
          :loading="searchLoading"
          stripe
          highlight-current-row
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
          @sort-change="handleTableSortChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column
            prop="cqrNumber"
            label="CQR #"
            width="120"
            fixed="left"
            sortable="custom"
            :sort-orders="['descending', 'ascending']"
          >
            <template #default="scope">
              <el-link @click="viewCQRDetails(scope.row)" type="primary">
                {{ formatCQRNumber(scope.row.cqrNumber) }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column prop="oemGroup" label="OEM Group" width="120" sortable="custom" />
          <el-table-column prop="oemCustomer" label="OEM Customer" width="150" sortable="custom" />
          <el-table-column prop="accountManagerName" label="Account Manager" width="130" sortable="custom" />
          <el-table-column prop="estimatorName" label="Estimator" width="120" sortable="custom" />
          <el-table-column prop="pgmName" label="Engineering Director" width="140" sortable="custom" />
          <el-table-column prop="pdmName" label="Program Manager" width="130" sortable="custom" />
          <el-table-column prop="engMgrName" label="Engineering Manager" width="140" sortable="custom" />
          <el-table-column prop="prdName" label="Responsible Design Eng." width="160" sortable="custom" />
          <el-table-column prop="productDesc" label="Product Description" width="200" show-overflow-tooltip sortable="custom" />
          <el-table-column prop="gateExitTRS" label="Gate Exit" width="100" sortable="custom" />
          <el-table-column prop="statusTRS" label="Status" width="100" sortable="custom" />

          <el-table-column prop="releaseDate" label="Release Date" width="110" sortable="custom" :sort-orders="['descending', 'ascending']">
            <template #default="scope">
              {{ formatDate(scope.row.releaseDate) }}
            </template>
          </el-table-column>

          <el-table-column prop="franIssueDate" label="Issue Date" width="110" sortable="custom" :sort-orders="['descending', 'ascending']">
            <template #default="scope">
              {{ formatDate(scope.row.franIssueDate) }}
            </template>
          </el-table-column>

          <el-table-column prop="dueDateFromEng" label="Due Date from Engineering" width="150" sortable="custom" :sort-orders="['ascending', 'descending']">
            <template #default="scope">
              {{ formatDate(scope.row.dueDateFromEng) }}
            </template>
          </el-table-column>

          <el-table-column prop="dueDateToBnE" label="Due Date to B&E" width="130" sortable="custom" :sort-orders="['ascending', 'descending']">
            <template #default="scope">
              {{ formatDate(scope.row.dueDateToBnE) }}
            </template>
          </el-table-column>

          <el-table-column prop="custQuoteDueDate" label="Quote Response Due Date" width="170" sortable="custom" :sort-orders="['ascending', 'descending']">
            <template #default="scope">
              {{ formatDate(scope.row.custQuoteDueDate) }}
            </template>
          </el-table-column>

          <el-table-column prop="openingMeetingDate" label="Opening Meeting Date" width="150" sortable="custom" :sort-orders="['descending', 'ascending']">
            <template #default="scope">
              {{ formatDate(scope.row.openingMeetingDate) }}
            </template>
          </el-table-column>

          <el-table-column prop="franDesc" label="CQR Description" width="200" show-overflow-tooltip sortable="custom" />
          <el-table-column prop="platformName" label="Vehicle" width="120" sortable="custom" />
          <el-table-column prop="modelYearTRS" label="Model Year" width="100" sortable="custom" />
          <el-table-column prop="volumePerAnnum" label="Volume Per Annum" width="130" sortable="custom" :sort-orders="['descending', 'ascending']" />
          <el-table-column prop="approxAnnualValue" label="Approx. Annual Value $" width="150" sortable="custom" :sort-orders="['descending', 'ascending']" />
          <el-table-column prop="bkRndInfComments" label="Background Info" width="200" show-overflow-tooltip />
          <el-table-column prop="uniqueNumber" label="Antares Unique Number" width="150" />
          <el-table-column prop="engPkgComments" label="Eng. Pkg. Comment" width="200" show-overflow-tooltip />
          <el-table-column prop="mfgSiteTRS" label="Manufacturing Site" width="130" />

          <!-- Quote Report 額外欄位 -->
          <template v-if="advancedForm.quoteReport">
            <el-table-column prop="qsCustomerResp" label="Customer Response" width="120" />
            <el-table-column prop="qsComments" label="Comments" width="200" show-overflow-tooltip />
          </template>
        </el-table>

        <!-- 分頁組件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[20, 50, 100, 200]"
            :total="searchResults.totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSearchForm } from './composables/useSearchForm'
import { useSearchResults } from './composables/useSearchResults'
import {
  getManufacturingSiteOptions,
  getModelYearOptions,
  getOriginatorOptions,
  getAccountManagerOptions,
  getEstimatorOptions,
  getPDMOptions,
  getPGMOptions,
  getOEMGroupOptions,
  getOEMCustomerOptions,
  exportSearchResults
} from '@/api/search'
import type { SelectOption, UserOption } from '@/api/search'

export default defineComponent({
  name: 'CQRSearch',
  setup() {
    // 使用 composables
    const {
      searchForm,
      advancedForm,
      showAdvanced,
      searchLoading,
      currentPage,
      pageSize,
      hasSearchCriteria,
      validateSearchForm,
      buildSearchQuery,
      clearAllFilters,
      toggleAdvancedSearch,
      formatDate,
      formatCQRNumber
    } = useSearchForm()

    // 排序和分頁狀態
    const currentSortField = ref('cqrnumber')
    const currentSortDirection = ref('desc')
    const pageSizeOptions = [20, 50, 100, 200]
    
    // 排序預設選項
    const sortPresets = [
      { field: 'cqrnumber', label: 'CQR編號' },
      { field: 'releasedate', label: '發佈日期' },
      { field: 'duedate', label: '到期日期' },
      { field: 'productiondesc', label: '產品描述' },
      { field: 'status', label: '狀態' },
      { field: 'oemgroup', label: 'OEM群組' },
      { field: 'oemcustomer', label: 'OEM客戶' },
      { field: 'accountmanager', label: '客戶經理' },
      { field: 'volumeperannum', label: '年產量' },
      { field: 'approxannualvalue', label: '預估年產值' }
    ]

    const {
      searchResults,
      selectedRows,
      selectedRowKeys,
      executeSearch,
      executeQuickSearch,
      handleRowClick,
      handleSelectionChange,
      viewCQRDetails,
      exportResults,
      getSelectionStats,
      clearResults
    } = useSearchResults()

    // 下拉選項資料
    const manufacturingSiteOptions = ref<SelectOption[]>([])
    const modelYearOptions = ref<SelectOption[]>([])
    const originatorOptions = ref<UserOption[]>([])
    const accountManagerOptions = ref<UserOption[]>([])
    const estimatorOptions = ref<UserOption[]>([])
    const pdmOptions = ref<UserOption[]>([])
    const pgmOptions = ref<UserOption[]>([])
    const oemGroupOptions = ref<SelectOption[]>([])
    const oemCustomerOptions = ref<SelectOption[]>([])

    // 載入所有下拉選項
    const loadDropdownOptions = async () => {
      try {
        const promises = [
          getManufacturingSiteOptions(),
          getModelYearOptions(),
          getOriginatorOptions(),
          getAccountManagerOptions(),
          getEstimatorOptions(),
          getPDMOptions(),
          getPGMOptions(),
          getOEMGroupOptions(),
          getOEMCustomerOptions()
        ]

        const [
          manufacturingSites,
          modelYears,
          originators,
          accountManagers,
          estimators,
          pdms,
          pgms,
          oemGroups,
          oemCustomers
        ] = await Promise.allSettled(promises)

        // 處理成功的回應
        if (manufacturingSites.status === 'fulfilled' && manufacturingSites.value.success) {
          manufacturingSiteOptions.value = manufacturingSites.value.data
        }
        if (modelYears.status === 'fulfilled' && modelYears.value.success) {
          modelYearOptions.value = modelYears.value.data
        }
        if (originators.status === 'fulfilled' && originators.value.success) {
          originatorOptions.value = originators.value.data
        }
        if (accountManagers.status === 'fulfilled' && accountManagers.value.success) {
          accountManagerOptions.value = accountManagers.value.data
        }
        if (estimators.status === 'fulfilled' && estimators.value.success) {
          estimatorOptions.value = estimators.value.data
        }
        if (pdms.status === 'fulfilled' && pdms.value.success) {
          pdmOptions.value = pdms.value.data
        }
        if (pgms.status === 'fulfilled' && pgms.value.success) {
          pgmOptions.value = pgms.value.data
        }
        if (oemGroups.status === 'fulfilled' && oemGroups.value.success) {
          oemGroupOptions.value = oemGroups.value.data
        }
        if (oemCustomers.status === 'fulfilled' && oemCustomers.value.success) {
          oemCustomerOptions.value = oemCustomers.value.data
        }

      } catch (error) {
        console.error('載入下拉選項失敗:', error)
        ElMessage.warning('部分下拉選項載入失敗')
      }
    }

    // 執行搜尋
    const handleSearch = async (skipConfirm = false) => {
      if (!validateSearchForm()) {
        return
      }

      // 檢查用戶偏好設定是否需要顯示空搜尋確認彈窗
      if (!hasSearchCriteria.value && !skipConfirm) {
        const { useUserPreferences } = await import('@/composables/useUserPreferences');
        const { shouldShowEmptySearchConfirm } = useUserPreferences();
        if (shouldShowEmptySearchConfirm()) {
          const confirm = await ElMessageBox.confirm(
            '沒有指定搜尋條件，將顯示前 50 筆記錄。是否繼續？',
            '確認搜尋',
            {
              confirmButtonText: '繼續',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).catch(() => false);

          if (!confirm) return;
        }
      }

      try {
        searchLoading.value = true
        const query = buildSearchQuery()
        
        // 加入排序和分頁參數
        query.sortBy = currentSortField.value
        query.sortDescending = currentSortDirection.value === 'desc'
        query.pageNumber = currentPage.value
        query.pageSize = pageSize.value
        
        await executeSearch(query)
      } catch (error) {
        console.error('搜尋失敗:', error)
      } finally {
        searchLoading.value = false
      }
    }

    // 排序欄位變更處理
    const handleSortFieldChange = async () => {
      if (searchResults.totalCount > 0) {
        await handleSearch()
      }
    }

    // 排序方向變更處理
    const handleSortDirectionChange = async () => {
      if (searchResults.totalCount > 0) {
        await handleSearch()
      }
    }

    // 表格排序變更處理
    const handleTableSortChange = ({ column, prop, order }) => {
      if (order) {
        currentSortField.value = prop
        currentSortDirection.value = order === 'ascending' ? 'asc' : 'desc'
        handleSearch()
      }
    }

    // 處理分頁大小變更
    const handlePageSizeChange = async (newSize: number) => {
      pageSize.value = newSize
      currentPage.value = 1
      
      if (searchResults.totalCount > 0) {
        await handleSearch()
      }
    }

    // 處理分頁大小變更（來自分頁組件）
    const handleSizeChange = async (newSize: number) => {
      await handlePageSizeChange(newSize)
    }

    // 處理當前頁變更
    const handleCurrentChange = async (newPage: number) => {
      currentPage.value = newPage
      
      if (searchResults.totalCount > 0) {
        await handleSearch()
      }
    }

    // 匯出搜尋結果
    const exportResultsHandler = async () => {
      if (searchResults.results?.length === 0) {
        ElMessage.warning('沒有搜尋結果可以匯出')
        return
      }

      try {
        const format = await ElMessageBox.prompt(
          '請選擇匯出格式',
          '匯出搜尋結果',
          {
            confirmButtonText: '匯出',
            cancelButtonText: '取消',
            inputPattern: /^(excel|csv)$/i,
            inputErrorMessage: '請輸入 excel 或 csv',
            inputValue: 'excel'
          }
        )

        const query = buildSearchQuery()
        query.pageNumber = 1
        query.pageSize = Number.MAX_SAFE_INTEGER // 匯出所有結果

        await exportResults(query, format.value.toLowerCase() as 'excel' | 'csv')
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('匯出失敗:', error)
        }
      }
    }

    // 組件掛載時初始化
    onMounted(async () => {
      await loadDropdownOptions()
      // 首次載入時自動執行搜尋，顯示所有資料，跳過確認彈窗
      await handleSearch(true)
    })

    return {
      // 表單狀態
      searchForm,
      advancedForm,
      showAdvanced,
      searchLoading,
      
      // 分頁和排序狀態
      currentPage,
      pageSize,
      currentSortField,
      currentSortDirection,
      pageSizeOptions,
      sortPresets,
      
      // 搜尋結果
      searchResults,
      selectedRows,
      selectedRowKeys,
      
      // 下拉選項
      manufacturingSiteOptions,
      modelYearOptions,
      originatorOptions,
      accountManagerOptions,
      estimatorOptions,
      pdmOptions,
      pgmOptions,
      oemGroupOptions,
      oemCustomerOptions,
      
      // 計算屬性
      hasSearchCriteria,
      
      // 方法
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      handlePageSizeChange,
      handleSortFieldChange,
      handleSortDirectionChange,
      handleTableSortChange,
      handleRowClick,
      handleSelectionChange,
      viewCQRDetails,
      exportResults: exportResultsHandler,
      clearAllFilters,
      toggleAdvancedSearch,
      formatDate,
      formatCQRNumber
    }
  }
})
</script>

<style scoped>
.cqr-search-container {
  padding: 20px;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-section h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.toolbar {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: 600;
  font-size: 16px;
}

.search-form {
  padding: 10px 0;
}

.cqr-number-range {
  display: flex;
  align-items: center;
  gap: 4px;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 20px 0 10px;
}

.advanced-search {
  border-top: none;
}

.filter-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.results-card {
  margin-top: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-info {
  display: flex;
  gap: 8px;
}

.no-results {
  padding: 40px 0;
  text-align: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .search-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .cqr-number-range {
    flex-wrap: wrap;
    gap: 8px;
  }

  .date-range {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .search-actions {
    flex-direction: column;
  }

  .toolbar {
    justify-content: center;
  }
}

/* 表格樣式優化 */
:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f0f9ff;
}

:deep(.el-table .el-link) {
  font-weight: 600;
}

/* 表格控制區域 */
.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sort-controls span {
  font-weight: 500;
  color: #606266;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.view-controls span {
  font-weight: 500;
  color: #606266;
}

/* 進階搜尋動畫 */
.advanced-search {
  transition: all 0.3s ease;
}

/* Loading 狀態 */
.search-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 排序指示器優化 */
:deep(.el-table th.is-sortable) {
  cursor: pointer;
}

:deep(.el-table th.is-sortable:hover) {
  background-color: #f0f9ff;
}

:deep(.el-table .ascending .sort-caret.ascending) {
  border-bottom-color: #409eff;
}

:deep(.el-table .descending .sort-caret.descending) {
  border-top-color: #409eff;
}
</style>