﻿using GPL_Core.Utilities;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Net.Mail;
using System.Net;
using System.Text;
using NLog;
using CQRLIB.DTOs;
using CQR.API.Utilities;
//using CQR.Core.ConfigureSection;

namespace CQR.API.Filters.ExceptionFilter
{
    public class CustomExceptionFilter : IExceptionFilter
    {
        private UserDto user;
        //private readonly ILogger<CustomExceptionFilter> _logger;
        //private IOptions<SmtpSettings> smtpSettings;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _contextAccessor;
        private readonly Logger _logger2 = LogManager.GetCurrentClassLogger();



        public CustomExceptionFilter(ILogger<CustomExceptionFilter> logger,
            //IOptions<SmtpSettings> smtpSettings,
            IConfiguration configuration,
            IHttpContextAccessor contextAccessor)
        {
            //_logger = logger;
            //this.smtpSettings = smtpSettings;
            _configuration = configuration;
            _contextAccessor = contextAccessor;
            user = HttpContextUserHelper.GetHttpContextUserInfo(_contextAccessor.HttpContext);
        }
        private string GetRequestBody(HttpRequest request)
        {
            request.EnableBuffering(); // 允許多次讀取請求主體
            var body = request.Body;
            var buffer = new byte[Convert.ToInt32(request.ContentLength)];
            body.Read(buffer, 0, buffer.Length);
            var requestBody = Encoding.UTF8.GetString(buffer);
            body.Seek(0, SeekOrigin.Begin); // 重設請求主體位置
            return requestBody;
        }

        async public void OnException(ExceptionContext context)
        {

            // 获取请求的方法
            var method = context.HttpContext.Request.Method;


            // Read the request body
            string requestBody = "";
            if (context.HttpContext.Request.Body.CanSeek)
            {
                context.HttpContext.Request.Body.Seek(0, SeekOrigin.Begin);
                using (StreamReader reader = new StreamReader(context.HttpContext.Request.Body, Encoding.UTF8, true, 1024, true))
                {
                    //requestBody = reader.ReadToEndAsync();
                    requestBody = await reader.ReadToEndAsync();
                }
                context.HttpContext.Request.Body.Seek(0, SeekOrigin.Begin); // Reset the position for further processing
            }
            // 存取 request body
            ////string requestBody = GetRequestBody(context.HttpContext.Request);

            //context.HttpContext.Request.Body.Position = 0;
            //StreamReader str = new StreamReader(context.HttpContext.Request.Body);
            //string body2 = await str.ReadToEndAsync();
            //context.HttpContext.Request.Body.Position = 0;



            //// 获取请求的 Payload 数据
            //var bodyString = "";
            ////var bodyString = await context.HttpContext.Request.Body.ReadAsStringAsync();
            //using (StreamReader reader = new StreamReader(context.HttpContext.Request.Body, Encoding.UTF8))
            //{
            //    bodyString = await reader.ReadToEndAsync();
            //}
            //using (var reader = new StreamReader(context.HttpContext.Request.Body))
            //{
            //    var body = reader.ReadToEnd();

            //    // Do something else
            //}


            // 记录异常信息
            //_logger2.lo.LogError(context.Exception, "An unhandled exception occurred.");
            //_logger2.LogError(context.Exception, "An unhandled exception occurred.");


            useSendEmailException(context, requestBody);
            useLoggerException(context, requestBody);

            // Handle the exception and generate a response
            var response = new { error = context.Exception.Message + context.Exception.StackTrace };
            var payload = JsonConvert.SerializeObject(response);
            context.Result = new ContentResult
            {
                Content = payload,
                ContentType = "application/json",
                StatusCode = (int)HttpStatusCode.InternalServerError
            };
            context.ExceptionHandled = true;
        }
        private string getMsgException(ExceptionContext context, string bodyString)
        {
            var exception = context.Exception;

            // 获取HttpContext对象
            var httpContext = context.HttpContext;
            var method = httpContext.Request.Method;
            //var payLoad = httpContext.Request.Body;
            // 获取请求的URL
            var requestUrl = $"{httpContext.Request.Scheme}://{httpContext.Request.Host}{httpContext.Request.Path}{httpContext.Request.QueryString}";
            var connectDBInfo = ConfigUtil.GetUseDatabase(_configuration);

            string userName = user?.name ?? string.Empty;
            string body = $@"""
                           UserEmail: {userName}
                           Datbase:{connectDBInfo}
                           Resource:{requestUrl} 
                           Method:{method}
                           PayLoad:{bodyString}
                           Exception:
            {exception.Message} ,{exception.StackTrace}
                            """;
            return body;
        }
        private bool useSendEmailException(ExceptionContext context, string bodyString)
        {
            //todo get -post body data

            //var exception = context.Exception;

            // 获取HttpContext对象
            //var httpContext = context.HttpContext;

            // 获取请求的URL
            //var requestUrl = $"{httpContext.Request.Scheme}://{httpContext.Request.Host}{httpContext.Request.Path}{httpContext.Request.QueryString}";
            //var connectDBInfo = ConfigUtil.GetUseDatabase(_configuration);
            //string body = $@"""
            //               UserEmail: {user.name}
            //               Datbase:{connectDBInfo}
            //               Resource:{requestUrl} 
            //               Method:{httpContext.Request.Method}
            //               Exception:{exception.Message} ,{exception.StackTrace}
            //                """;

            var httpContext = context.HttpContext;
            //var payLoad = httpContext.Request.Body;
            string userName = user?.name ?? string.Empty;
            //context.Exception.StackTrace
            string body = getMsgException(context, bodyString);
            body += $"payload:{bodyString}";


            var mailMessage = new MailMessage
            {
                //environment 
                From = new MailAddress("<EMAIL>"),
                Subject = $"GPLAPI Error-User:{userName}",
                Body = body,
                IsBodyHtml = false,
            };
            var strSystemmainter = ConfigUtil.GetSectionValue(_configuration, "systemmainter");
            foreach (var recipient in strSystemmainter.Split(';').Where(s => !string.IsNullOrEmpty(s.Trim())))
            {
                mailMessage.To.Add(new MailAddress(recipient.Trim()));
            }
            //mailMessage.To.Add(new MailAddress("<EMAIL>"));…
            //var result = SmtpUtil.SendMail(mailMessage, smtpSettings);
            //return result;
            return false;
        }

        private bool useLoggerException(ExceptionContext context, string bodyString)
        {
            var msgException = getMsgException(context, bodyString);
            LogManager.GetCurrentClassLogger().Error(msgException);
            return true;
        }
    }
}
