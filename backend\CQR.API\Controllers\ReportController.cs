using CQR.Application.Dto.Report;
using CQR.Application.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace CQR.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReportController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IReportQueryRepository reportDao;

        public ReportController(IReportQueryRepository _reportDao)
        {
            reportDao = _reportDao;
        }
        [HttpGet]
        [Route("GetSQLReportWinLoss")]
        public async Task<ActionResult<IEnumerable<WinLossReportDto>>> GetSQLReportWinLoss()
        {
            var result = await reportDao.GetSQLReportWinLoss();
            return Ok(result);
        }
        [HttpGet]
        [Route("GetSQLDueDateStatusReport")]
        public async Task<ActionResult<IEnumerable<DueDateStatusReportDto>>> GetSQLDueDateStatusReport()
        {
            var result = await reportDao.GetSQLDueDateStatusReport();
            return Ok(result);
        }
        [HttpGet]
        [Route("GetSQLReportOpenCQRStatus")]
        public async Task<ActionResult<IEnumerable<OpenCQRStatusReportDto>>> GetSQLReportOpenCQRStatus()
        {
            var result = await reportDao.GetSQLReportOpenCQRStatus();
            return Ok(result);
        }
        [HttpGet]
        [Route("GetSQLReportPipeline")]
        public async Task<ActionResult<IEnumerable<PipelineReportDto>>> GetSQLReportPipeline()
        {
            var result = await reportDao.GetSQLReportPipeline();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetSQLReportSalesReport")]
        public async Task<ActionResult<IEnumerable<SalesReportDto>>> GetSQLReportSalesReport()
        {
            var result = await reportDao.GetSQLReportSalesReport();
            return Ok(result);
        }


        //[Route("groupCodeDapper")]
        //[HttpGet]
        //public async Task<ActionResult<IEnumerable<CQR_TRS_Header>>> GetGPLCodesAsync()
        //{
        //    var result = await codeDao.GetGPLCodesAsync();
        //    return Ok(result);
        //}


        //[HttpGet]
        //[Route("countAllCQRHeader")]
        //public async Task<ActionResult<int>> countCQRHeader()
        //{
        //    var result = await _cqrIhsRepository.countTotalAsync();
        //    return Ok(result);
        //}
        //[HttpGet]
        //[Route("UseDatabase")]
        //public ActionResult<string> GetUseDatabase()
        //{
        //    var msgDB = ConfigUtil.GetUseDatabase(_configuration);
        //    return Ok($"Running....{msgDB}");
        //}

        //[HttpGet]
        //[Route("ASPNETCORE_ENVIRONMENT")]
        //public ActionResult<string> GetASPNETCORE_ENVIRONMENT()
        //{
        //    // 返�?字符串�?�?
        //    var result = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        //    return Ok($"ASPNETCORE_ENVIRONMENT:{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}");
        //}
        ////https://learn.microsoft.com/zh-tw/azure/active-directory-b2c/enable-authentication-web-api?tabs=csharpclient
        //[HttpGet]
        //[Route("Identity")]
        //public ActionResult GetUserIdentity()
        //{
        //    return Ok(new { name = User.Identity.Name });
        //}

        //[HttpGet]
        //[Route("/Home/Error")]
        //public IActionResult Error()
        //{
        //    return StatusCode(500); // 返�?500?�态�?
        //}

    }
}
