// msal.ts
import {
  PublicClientApplication,
  type Configuration
} from "@azure/msal-browser";

const msalConfig: Configuration = {
  auth: {
    clientId: import.meta.env.VITE_CLIENT_ID,
    authority: import.meta.env.VITE_AUTHORITY,
    redirectUri: import.meta.env.VITE_REDIRECT_URI
    // clientId: "c89dd02e-f016-41e1-8491-db6e4c76bc8e", //"你的-client-id",
    // authority:
    //   "https://login.microsoftonline.com/dc94cd8a-025c-455a-9e35-c80612a79987",
    // redirectUri: "http://localhost:8848/login"
  }
};

export const msalInstance = new PublicClientApplication(msalConfig);
