// // import { $t } from "@/plugins/i18n";
// import { fighting } from "@/router/enums";
// // 最简代码，也就是这些字段必须有
// export default {
//   path: "/fighting",
//   meta: {
//     // title: "加油",
//     title: "励志",
//     // title: $t("menus.pureFlowChart"),
//     rank: fighting
//   },
//   children: [
//     {
//       path: "/fighting/index",
//       name: "Fighting",
//       component: () => import("@/views/fighting/index.vue"),
//       meta: {
//         title: "加油",
//         // 通过设置showParent为true，显示父级
//         showParent: true
//       }
//     },
//     {
//       path: "/fighting/effort",
//       name: "Effort",
//       component: () => import("@/views/fighting/effort.vue"),
//       meta: {
//         title: "努力"
//       }
//     }
//   ] as Array<RouteConfigsTable>
// } satisfies RouteConfigsTable;

export default [];
