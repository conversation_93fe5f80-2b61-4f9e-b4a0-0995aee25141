﻿using CQR.Domain.Interfaces.Repositories;

namespace CQR.Domain.CQR_IHSs;

public interface ICQRIHSRepository : IEntityRepository<CQR_IHS, int>
{
    Task<IEnumerable<string>> GetDisplayName(string fieldName);

    //Task<IEnumerable<CQR_IHS>> GetByQueuekey(int queuekey);
    //    //Task<IEnumerable<CQR_GDPIMPhase>> GetByAllAsync();
    //    //Task<CQR_GDPIMPhase> GetByQueueKeyAsync(int queueKey);
    //    //Task<int> InsertAsync(CQR_GDPIMPhase cqrIhs);
    //    //Task<int> countTotalAsync();


    //    // PopulateAntaresField
    //    //Task<IEnumerable<SelectItem>> GetAntaresFieldAsync(string fieldName, string valueField, bool addBlank);
    //    //// PopulateIHSField
    //    //Task<IEnumerable<SelectItem>> GetIHSFieldAsync(string fieldName, string valueField);

    //    //List<Dictionary<string, object>> GetAntaresData(AntaresQueryDto dto);
}
