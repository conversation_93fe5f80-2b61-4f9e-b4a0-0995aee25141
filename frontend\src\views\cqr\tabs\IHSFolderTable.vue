<template>
  <el-table
    :cell-style="{ fontSize: '12px' }"
    :data="data"
    height="200"
    border
    style="width: 100%"
    @selection-change="onSelectionChange"
  >
    <el-table-column label="" width="40">
      <template #default="scope">
        <el-icon
          v-if="
            isEdit &&
            scope.row.uniqueNumber === -1 &&
            scope.$index === data.length - 1
          "
          style="cursor: pointer; color: #67c23a"
          @click="addRow"
        >
          <Plus />
        </el-icon>
        <el-icon
          v-else
          style="cursor: pointer; color: #f56c6c"
          @click="removeRow(scope.$index)"
        >
          <Minus />
        </el-icon>
      </template>
    </el-table-column>
    <!-- <el-table-column type="selection" width="55" /> -->
    <el-table-column
      sortable
      prop="coreNameplatePlantMnemonic"
      label="Core Nameplate Plant Mnemonic"
    >
      <template #default="scope">
        <el-input
          v-model="scope.row.coreNameplatePlantMnemonic"
          :disabled="!isEdit"
          size="small"
        />
      </template>
    </el-table-column>
    <el-table-column sortable prop="oemGroup" label="OEM Group">
      <template #default="scope">
        <el-select
          v-model="scope.row.oemGroup"
          :disabled="!isEdit"
          size="small"
          placeholder="Select OEM Group"
        >
          <el-option
            v-for="item in props.criteria?.oemGroup || []"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column sortable prop="oem" label="OEM">
      <template #default="scope">
        <el-select
          v-model="scope.row.oem"
          :disabled="!isEdit"
          size="small"
          placeholder="Select OEM"
        >
          <el-option
            v-for="item in props.criteria?.oem || []"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column sortable prop="platform" label="Platform">
      <template #default="scope">
        <el-select
          v-model="scope.row.platform"
          :disabled="!isEdit"
          size="small"
          placeholder="Select Platform"
        >
          <el-option
            v-for="item in props.criteria?.platform || []"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column sortable prop="program" label="Program">
      <template #default="scope">
        <el-select
          v-model="scope.row.program"
          :disabled="!isEdit"
          size="small"
          placeholder="Select Program"
        >
          <el-option
            v-for="item in props.criteria?.program || []"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column
      sortable
      prop="productionNameplate"
      label="Production Nameplate"
    >
      <template #default="scope">
        <!-- <el-input
      v-model="scope.row.productionNameplate"
      :disabled="!isEdit"
      size="small"
      /> -->
        <el-select
          v-model="scope.row.productionNameplate"
          :disabled="!isEdit"
          size="small"
          placeholder="Select productionNameplate"
        >
          <el-option
            v-for="item in props.criteria?.productionNameplate || []"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column sortable prop="region" label="Region">
      <template #default="scope">
        <el-input v-model="scope.row.region" :disabled="!isEdit" size="small" />
      </template>
    </el-table-column>
    <el-table-column sortable prop="country" label="Country">
      <template #default="scope">
        <el-input
          v-model="scope.row.country"
          :disabled="!isEdit"
          size="small"
        />
      </template>
    </el-table-column>
    <el-table-column sortable prop="startOfProduction" label="SOP">
      <template #default="scope">
        <el-input
          v-model="scope.row.startOfProduction"
          :disabled="!isEdit"
          size="small"
        />
      </template>
    </el-table-column>
    <el-table-column sortable prop="endOfProduction" label="EOP">
      <template #default="scope">
        <el-input
          v-model="scope.row.endOfProduction"
          :disabled="!isEdit"
          size="small"
        />
      </template>
    </el-table-column>
    <el-table-column prop="productDescription" label="Product Description">
      <template #default="scope">
        <!-- <el-input
          v-model="scope.row.productDescription"
          :disabled="!isEdit"
          size="small"
        /> -->
        <el-select
          v-model="scope.row.product"
          :disabled="!isEdit"
          size="small"
          placeholder="Select product"
        >
          <el-option
            v-for="item in props.criteria?.product || []"
            :key="item.item1"
            :label="item.item1 + ',' + item.item2"
            :value="item.item2"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column prop="soldFrom" label="Sold From">
      <template #default="scope">
        <el-select
          v-model="scope.row.soldFrom"
          :disabled="!isEdit"
          size="small"
          placeholder="Select Sold From"
        >
          <el-option
            v-for="item in props.criteria?.site || []"
            :key="item.item1"
            :label="item.item2"
            :value="item.item2"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column prop="finalAssembly" label="Final Assembly">
      <template #default="scope">
        <el-select
          v-model="scope.row.finalAssembly"
          :disabled="!isEdit"
          size="small"
          placeholder="Select Final Assembly"
        >
          <el-option
            v-for="item in props.criteria?.site || []"
            :key="item.item1"
            :label="item.item2"
            :value="item.item2"
          />
        </el-select>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref } from "vue";
import { ElIcon } from "element-plus";
import { Plus, Minus } from "@element-plus/icons-vue";
import { useFormModelStore } from "@/store/modules/formModel";
const formStore = useFormModelStore();
const formIHSFolderData = ref<any>({});
const emit = defineEmits<{
  (e: "selection-change", selectedRows: any[]): void;
  (e: "update:data", newData: Array<Record<string, any>>): void;
}>();

function onSelectionChange(val: any[]) {
  emit("selection-change", val);
}

const props = defineProps<{
  data: Array<Record<string, any>>;
  criteria?: Array<Record<string, any>>;
  isEdit?: boolean;
}>();

function addRow() {
  console.log("Adding new row..."); // 調試信息

  // 使用最高效的方式：直接操作數組
  const newData = [...props.data];
  const lastIndex = newData.length - 1;

  // 檢查最後一行是否為添加按鈕行
  if (lastIndex >= 0 && newData[lastIndex].uniqueNumber === -1) {
    // 直接修改最後一行，將其轉換為正常數據行
    newData[lastIndex] = {
      ...newData[lastIndex],
      uniqueNumber: Date.now(), // 給予唯一ID
      rowDisabled: false
    };

    // 添加新的添加按鈕行
    const addRowTemplate = {
      uniqueNumber: -1,
      coreNameplatePlantMnemonic: "",
      oemGroup: "",
      oem: "",
      platform: "",
      program: "",
      productionNameplate: "",
      region: "",
      country: "",
      startOfProduction: "",
      endOfProduction: "",
      productDescription: "",
      product: "",
      soldFrom: "",
      finalAssembly: "",
      rowDisabled: false
    };

    newData.push(addRowTemplate);

    console.log("New data:", newData); // 調試信息
    emit("update:data", newData);
  } else {
    console.warn("No add row found or data structure issue");
  }
}
import { onMounted } from "vue";

onMounted(() => {
  // 載入 modelIHSFolderCriteria 到表格
  // if (formStore.modelIHSFolderCriteria && Array.isArray(formStore.modelIHSFolderCriteria)) {
  //   emit("update:data", formStore.modelIHSFolderCriteria);
  // }
});

function removeRow(index: number) {
  const newData = props.data.slice();
  newData.splice(index, 1);
  emit("update:data", newData);
}
</script>
