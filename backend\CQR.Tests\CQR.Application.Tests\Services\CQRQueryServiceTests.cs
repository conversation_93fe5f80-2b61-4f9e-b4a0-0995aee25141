using CQR.Application.Dto;
using CQR.Application.Dto.CQRHead;
using CQR.Application.Repositories;
using CQR.Application.Services;
using CQRLIB.CQR_Headers;
using CQRLIB.CQR_IHSFolders;
using Moq;
using Xunit;

namespace CQR.Application.Tests.Services;

public class CQRQueryServiceTests
{
    private readonly Mock<IRepositoryCollection> _mockRepositoryCollection;
    private readonly Mock<IUserRoleQueryRepository> _mockUserRoleQueryRepository;
    private readonly Mock<ICQRGDPIMPhasesRepository> _mockCQRGDPIMPhasesRepository;
    private readonly Mock<ICQRHeaderRepository> _mockCQRHeaderRepository;
    private readonly Mock<ICQRIHSFolderRepository> _mockCQRIHSFolderRepository;
    private readonly Mock<ICqrHeaderQueryRepository> _mockCqrHeaderQueryRepository;
    
    private readonly ICQRQueryService _cqrQueryService;

    public CQRQueryServiceTests()
    {
        _mockRepositoryCollection = new Mock<IRepositoryCollection>();
        _mockUserRoleQueryRepository = new Mock<IUserRoleQueryRepository>();
        _mockCQRGDPIMPhasesRepository = new Mock<ICQRGDPIMPhasesRepository>();
        _mockCQRHeaderRepository = new Mock<ICQRHeaderRepository>();
        _mockCQRIHSFolderRepository = new Mock<ICQRIHSFolderRepository>();
        _mockCqrHeaderQueryRepository = new Mock<ICqrHeaderQueryRepository>();

        // Setup repository collection
        _mockRepositoryCollection.Setup(x => x.UserRoleQueryRepository).Returns(_mockUserRoleQueryRepository.Object);
        _mockRepositoryCollection.Setup(x => x.CQRGDPIMPhasesRepository).Returns(_mockCQRGDPIMPhasesRepository.Object);
        _mockRepositoryCollection.Setup(x => x.CQRHeaderRepository).Returns(_mockCQRHeaderRepository.Object);
        _mockRepositoryCollection.Setup(x => x.CQRIHSFolderRepository).Returns(_mockCQRIHSFolderRepository.Object);
        _mockRepositoryCollection.Setup(x => x.CqrHeaderQueryRepository).Returns(_mockCqrHeaderQueryRepository.Object);

        _cqrQueryService = new CQRQueryService(_mockRepositoryCollection.Object);
    }

    [Fact]
    public async Task GetCollectionAsync_ShouldReturnCQRHeaderCollection_WhenValidQueueKey()
    {
        // Arrange
        var queueKey = 12345;
        var roles = new List<string> { "AMGR", "CQRHRCOST" };
        var cqrHeader = new CQR_Header 
        { 
            QueueKey = queueKey, 
            ActionComments = "Test comments" 
        };
        var ihsRecords = new List<CQR_IHSFolder>();
        var routingTasks = new List<RoutingTaskDto>();

        _mockUserRoleQueryRepository.Setup(x => x.GetRolesNames("IBERLANG"))
            .ReturnsAsync(roles);
        _mockCQRGDPIMPhasesRepository.Setup(x => x.GetByQueueAndPhase(queueKey, 1))
            .ReturnsAsync((object)null);
        _mockCQRGDPIMPhasesRepository.Setup(x => x.GetByQueueAndPhase(queueKey, 2))
            .ReturnsAsync((object)null);
        _mockCQRHeaderRepository.Setup(x => x.GetByIdAsync(queueKey))
            .ReturnsAsync(cqrHeader);
        _mockCQRIHSFolderRepository.Setup(x => x.GetByQueueKeyAsync(queueKey))
            .ReturnsAsync(ihsRecords);
        _mockCqrHeaderQueryRepository.Setup(x => x.GetRoutingTaskByQueuekey(queueKey, "FR"))
            .ReturnsAsync(routingTasks);
        _mockUserRoleQueryRepository.Setup(x => x.GetManagerByRole(It.IsAny<string>()))
            .Returns("TestManager");

        // Act
        var result = await _cqrQueryService.GetCollectionAsync(queueKey);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cqrHeader, result.iCQRHeader);
        Assert.NotNull(result.cUserRoles);
        Assert.True(result.cUserRoles.bIsAMgr); // AMGR role should be set
        Assert.True(result.cUserRoles.bIsCost); // CQRHRCOST role should be set
        Assert.Equal(ihsRecords, result.ihsFolderRecords);
        Assert.Equal(routingTasks, result.routingTasks);
    }

    [Fact]
    public async Task GetCollectionAsync_ShouldHandleEmptyRoles()
    {
        // Arrange
        var queueKey = 12345;
        var roles = new List<string>();
        var cqrHeader = new CQR_Header { QueueKey = queueKey };

        _mockUserRoleQueryRepository.Setup(x => x.GetRolesNames("IBERLANG"))
            .ReturnsAsync(roles);
        _mockCQRHeaderRepository.Setup(x => x.GetByIdAsync(queueKey))
            .ReturnsAsync(cqrHeader);
        _mockCQRIHSFolderRepository.Setup(x => x.GetByQueueKeyAsync(queueKey))
            .ReturnsAsync(new List<CQR_IHSFolder>());
        _mockCqrHeaderQueryRepository.Setup(x => x.GetRoutingTaskByQueuekey(queueKey, "FR"))
            .ReturnsAsync(new List<RoutingTaskDto>());
        _mockUserRoleQueryRepository.Setup(x => x.GetManagerByRole(It.IsAny<string>()))
            .Returns("TestManager");

        // Act
        var result = await _cqrQueryService.GetCollectionAsync(queueKey);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.cUserRoles);
        Assert.False(result.cUserRoles.bIsAMgr);
        Assert.False(result.cUserRoles.bIsCost);
    }

    [Fact]
    public async Task GetCollectionAsync_ShouldHandleNullCQRHeader()
    {
        // Arrange
        var queueKey = 12345;
        var roles = new List<string>();

        _mockUserRoleQueryRepository.Setup(x => x.GetRolesNames("IBERLANG"))
            .ReturnsAsync(roles);
        _mockCQRHeaderRepository.Setup(x => x.GetByIdAsync(queueKey))
            .ReturnsAsync((CQR_Header)null);
        _mockCQRIHSFolderRepository.Setup(x => x.GetByQueueKeyAsync(queueKey))
            .ReturnsAsync(new List<CQR_IHSFolder>());
        _mockCqrHeaderQueryRepository.Setup(x => x.GetRoutingTaskByQueuekey(queueKey, "FR"))
            .ReturnsAsync(new List<RoutingTaskDto>());
        _mockUserRoleQueryRepository.Setup(x => x.GetManagerByRole(It.IsAny<string>()))
            .Returns("TestManager");

        // Act
        var result = await _cqrQueryService.GetCollectionAsync(queueKey);

        // Assert
        Assert.NotNull(result);
        Assert.Null(result.iCQRHeader);
        Assert.Null(result.sDbsCommentsAction);
        Assert.Null(result.sTotalCommentsAction);
    }

    [Fact]
    public async Task GetHeaderByKeyAsync_ShouldReturnCQRHeader_WhenValidQueueKey()
    {
        // Arrange
        var queueKey = 12345;
        var expectedHeader = new CQR_Header { QueueKey = queueKey };

        _mockCQRHeaderRepository.Setup(x => x.GetByIdAsync(queueKey))
            .ReturnsAsync(expectedHeader);

        // Act
        var result = await _cqrQueryService.GetHeaderByKeyAsync(queueKey);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedHeader, result);
        Assert.Equal(queueKey, result.QueueKey);
    }

    [Fact]
    public async Task GetHeaderByKeyAsync_ShouldReturnNull_WhenHeaderNotFound()
    {
        // Arrange
        var queueKey = 99999;

        _mockCQRHeaderRepository.Setup(x => x.GetByIdAsync(queueKey))
            .ReturnsAsync((CQR_Header)null);

        // Act
        var result = await _cqrQueryService.GetHeaderByKeyAsync(queueKey);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetHeadersAsync_ShouldThrowNotImplementedException()
    {
        // Arrange
        var request = new CQRHeaderQueryRequest();

        // Act & Assert
        await Assert.ThrowsAsync<NotImplementedException>(() => 
            _cqrQueryService.GetHeadersAsync(request));
    }

    [Theory]
    [InlineData("AMGR")]
    [InlineData("CQRHRCOST")]
    [InlineData("FRAN")]
    [InlineData("PSR")]
    public async Task GetCollectionAsync_ShouldSetCorrectUserRoles_ForValidRoles(string roleCode)
    {
        // Arrange
        var queueKey = 12345;
        var roles = new List<string> { roleCode };
        var cqrHeader = new CQR_Header { QueueKey = queueKey };

        _mockUserRoleQueryRepository.Setup(x => x.GetRolesNames("IBERLANG"))
            .ReturnsAsync(roles);
        _mockCQRHeaderRepository.Setup(x => x.GetByIdAsync(queueKey))
            .ReturnsAsync(cqrHeader);
        _mockCQRIHSFolderRepository.Setup(x => x.GetByQueueKeyAsync(queueKey))
            .ReturnsAsync(new List<CQR_IHSFolder>());
        _mockCqrHeaderQueryRepository.Setup(x => x.GetRoutingTaskByQueuekey(queueKey, "FR"))
            .ReturnsAsync(new List<RoutingTaskDto>());
        _mockUserRoleQueryRepository.Setup(x => x.GetManagerByRole(It.IsAny<string>()))
            .Returns("TestManager");

        // Act
        var result = await _cqrQueryService.GetCollectionAsync(queueKey);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.cUserRoles);
        
        switch (roleCode)
        {
            case "AMGR":
                Assert.True(result.cUserRoles.bIsAMgr);
                break;
            case "CQRHRCOST":
                Assert.True(result.cUserRoles.bIsCost);
                break;
            case "FRAN":
                Assert.True(result.cUserRoles.bIsFran);
                break;
            case "PSR":
                Assert.True(result.cUserRoles.bIsPsr);
                Assert.True(result.cUserRoles.bIsPA);
                break;
        }
    }
}