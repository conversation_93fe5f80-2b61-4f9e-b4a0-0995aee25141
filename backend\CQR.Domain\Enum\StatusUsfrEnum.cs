﻿using System.ComponentModel;
using System.Reflection;

namespace CQR.Domain.Enum;


public enum StatusUsfrEnum
{
    [Description("Being Initiated")]
    status010100FR,

    [Description("Requires Review by CMGR")]
    status010150FR,

    [Description("Requires Review by Sales")]
    status010200FR,

    [Description("Returned by Sales")]
    status010300FR,

    [Description("Issued / Needs Finance Coordinator")]
    status020100FR,

    [Description("Engineering Assessment In Progress")]
    status020150FR,

    [Description("Internal FRAN Initiated / Needs Finance Coordinator")]
    status020200FR,

    [Description("Issued / Awaiting Engineering Assessment")]
    status020300FR,

    [Description("Internal FRAN Initiated / Awaiting Engineering Assessment")]
    status020400FR,

    [Description("Returned by Engineering")]
    status020500FR,

    [Description("Reissued")]
    status020600FR,

    [Description("'Engineering Needs More Info / Time")]
    status030100FR,

    [Description("Engineering Package In Progress")]
    status030200FR,

    [Description("Awaiting Cost Estimates")]
    status030300FR,

    [Description("Design Proposal In Progress")]
    status030400FR,

    [Description("Task Lists In Progress")]
    status030600FR,

    [Description("Quote Response In Progress")]
    status040100FR,

    [Description("Business Case  Ready for Review")]
    status040125FR,

    [Description("QAF Awaiting Approval")]
    status040150FR,

    [Description("Awaiting Customer Response")]
    status040175FR,

    [Description("Quote Response ready for Gate 2 release")]
    status040190FR,

    [Description("Quote Response Sent to Sales")]
    status040200FR,

    [Description("Close out CQR")]
    status040250FR,

    [Description("Quote Response Received")]
    status040300FR,

    [Description("Design Proposal Costing Complete")]
    status040400FR,

    [Description("Review Won Quote")]
    status040700FR,

    [Description("Closed Out")]
    status090100FR,

    [Description("Terminated by Originator")]
    status090900FR,

    [Description("Intitiating QAF")]
    status010100QF,

    [Description("QAF Awaiting PIM Review")]
    status010200QF,

    [Description("QAF Awaiting Management Review")]
    status010300QF,

    [Description("QAF Approved")]
    status010400QF
}


public static class StatusUsfrEnumExtensions
{
    public static string GetCodeByDescription(string description)
    {
        foreach (var field in typeof(StatusUsfrEnum).GetFields())
        {
            var attr = field.GetCustomAttribute<DescriptionAttribute>();
            if (attr != null && attr.Description == description)
            {
                return field.Name; // 回傳 enum 名稱 (就是 code)
            }
        }
        return null;
    }
}


//string desc = "Requires Review by Sales";
//string code = StatusCodeExtensions.GetCodeByDescription(desc);
//Console.WriteLine(code); // 輸出: status010200FR