using CQR.Domain.CQR_TRS_Headers;
using CQR.Domain.TRS_Headers;
using Microsoft.AspNetCore.Mvc;

namespace CQR.API.Controllers;

[Route("api/[controller]")]
[ApiController]
public class CodeController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly ICodeRepository codeDao;

    // 构造函数注入
    public CodeController(ICodeRepository _codeDao)
    {
        codeDao = _codeDao;
    }

    [HttpGet]
    [Route("groupCodes")]
    public async Task<ActionResult<Dictionary<string, List<CQR_TRS_Header>>>> GetDictCode()
    {
        var result = await codeDao.GetDictCode();
        return Ok(result);
    }

    [HttpGet]
    [Route("groupCodeDapper")]
    public async Task<ActionResult<IEnumerable<CQR_TRS_Header>>> GetGPLCodesAsync()
    {
        var result = await codeDao.GetGPLCodesAsync();
        return Ok(result);
    }

    //[HttpGet]
    //[Route("countAllCQRHeader")]
    //public async Task<ActionResult<int>> countCQRHeader()
    //{
    //    var result = await _cqrIhsRepository.countTotalAsync();
    //    return Ok(result);
    //}
    //[HttpGet]
    //[Route("UseDatabase")]
    //public ActionResult<string> GetUseDatabase()
    //{
    //    var msgDB = ConfigUtil.GetUseDatabase(_configuration);
    //    return Ok($"Running....{msgDB}");
    //}

    //[HttpGet]
    //[Route("ASPNETCORE_ENVIRONMENT")]
    //public ActionResult<string> GetASPNETCORE_ENVIRONMENT()
    //{
    //    // 返回字符串响应
    //    var result = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

    //    return Ok($"ASPNETCORE_ENVIRONMENT:{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}");
    //}
    ////https://learn.microsoft.com/zh-tw/azure/active-directory-b2c/enable-authentication-web-api?tabs=csharpclient
    //[HttpGet]
    //[Route("Identity")]
    //public ActionResult GetUserIdentity()
    //{
    //    return Ok(new { name = User.Identity.Name });
    //}

    //[HttpGet]
    //[Route("/Home/Error")]
    //public IActionResult Error()
    //{
    //    return StatusCode(500); // 返回500状态码
    //}
}
