@use "theme";
@use "transition";
@use "element-plus";
@use "sidebar";
@use "dark";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;

  /* 常用border-color 需要时可取用 */
  --pure-border-color: rgb(5 5 5 / 6%);

  /* switch关闭状态下的color 需要时可取用 */
  --pure-switch-off-color: #a6a6a6;

  /** 主题色 */
  --pure-theme-sub-menu-active-text: initial;
  --pure-theme-menu-bg: none;
  --pure-theme-menu-hover: none;
  --pure-theme-sub-menu-bg: transparent;
  --pure-theme-menu-text: initial;
  --pure-theme-sidebar-logo: none;
  --pure-theme-menu-title-hover: initial;
  --pure-theme-menu-active-before: transparent;
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}



/* Element Plus 组件字体大小强制覆盖 */
.el-button,
.el-input__inner,
.el-textarea__inner,
.el-select .el-input__inner,
.el-form-item__label,
.el-dialog__header,
.el-dialog__body,
.el-table,
.el-table td,
.el-table th,
.el-menu-item,
.el-submenu__title,
.el-dropdown-menu__item,
.el-pagination,
.el-breadcrumb__inner,
.el-tabs__item,
.el-card__body,
.el-message,
.el-notification {
  font-size: 16px !important;
}

/* 小字体组件 */
.el-tag,
.el-badge__content,
.el-tooltip__popper {
  font-size: 14px !important;
}

/* 大字体组件 */
.el-dialog__title,
.el-page-header__title {
  font-size: 20px !important;
}


/* 在 index.scss 最后添加 */
* {
  font-size: 11px !important;
}

/* 或者针对整个应用 */
#app {
  font-size: 11px;
}

#app * {
  font-size: inherit !important;
}
