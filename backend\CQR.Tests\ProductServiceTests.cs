using Microsoft.EntityFrameworkCore;
using CQR.Domain.Entities;
using CQR.Persistence.Command.Repositories;

public class ProductServiceTests
{
    //private readonly Mock<IProductRepository> _mockProductRepo;
    //private readonly Mock<IProductDetailRepository> _mockProductDetailRepo;
    private readonly CQRDbContext _context;
    //private readonly ProductService _productService;

    public ProductServiceTests()
    {
        // 初始化模拟对象
        //_mockProductRepo = new Mock<CQR.Core.Interfaces.IProductRepository>();
        //_mockProductDetailRepo = new Mock<IProductDetailRepository>();

        // 创建一个 In-Memory 数据库上下文
        var options = new DbContextOptionsBuilder<CQRDbContext>()
            .UseInMemoryDatabase("TestDb")  // 使用临时的 In-Memory 数据库
            .Options;
        _context = new CQRDbContext(options);

        // 将模拟的 Repository 注入 Service
        //_productService = new ProductService(_mockProductRepo.Object, _mockProductDetailRepo.Object, _context);
    }


    [Fact]
    public async Task GetProductCountAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        //_mockProductRepo.Setup(repo => repo.CountAsync()).ReturnsAsync(5); // 模拟返回 5 个产品

        // Act
        //var count = await _productService.GetProductCountAsync();

        // Assert
        //Assert.Equal(5, count); // 确认返回结果是 5
    }
    [Fact]
    public async Task AddProductAsync_ShouldAddProduct()
    {
        // Arrange
        var product = new Product { Name = "New Product", Price = 12, Stock = 40 };
        // 设置模拟的 IProductRepository 的行为
        //_mockProductRepo.Setup(repo => repo.AddAsync(It.IsAny<Product>()))
        //    .Returns(Task.CompletedTask);  // 模拟 AddAsync 方法

        //_mockProductRepo.Setup(repo => repo.SaveChangesAsync())
        //    .Returns(Task.CompletedTask);  // 模拟 SaveChangesAsync 方法

        // Act
        //await _productService.AddProductAsync(product);  // 调用方法

        // Assert
        //_mockProductRepo.Verify(repo => repo.AddAsync(It.Is<Product>(p => p.Name == "New Product")), Times.Once);
        //_mockProductRepo.Verify(repo => repo.SaveChangesAsync(), Times.Once);

    }
}
