buttons:
  pureAccountSettings: 账户设置
  pureBackTop: 回到顶部
  pureClickCollapse: 点击折叠
  pureClickExpand: 点击展开
  pureClose: 关闭
  pureCloseAllTabs: 关闭全部标签页
  pureCloseCurrentTab: 关闭当前标签页
  pureCloseLeftTabs: 关闭左侧标签页
  pureCloseOtherTabs: 关闭其他标签页
  pureCloseRightTabs: 关闭右侧标签页
  pureCloseText: 关
  pureConfirm: 确认
  pureContentExitFullScreen: 内容区退出全屏
  pureContentFullScreen: 内容区全屏
  pureLogin: 登录
  pureLoginOut: 退出系统
  pureOpenSystemSet: 打开系统配置
  pureOpenText: 开
  pureReload: 重新加载
  pureSwitch: 切换
login:
  pureAlipayLogin: 支付宝登录
  pureBack: 返回
  pureDefinite: 确定
  pureForget: 忘记密码?
  pureGetVerifyCode: 获取验证码
  pureInfo: 秒后重新获取
  pureLogin: 登录
  pureLoginFail: 登录失败
  pureLoginSuccess: 登录成功
  purePassWordDifferentReg: 两次密码不一致!
  purePassWordReg: 请输入密码
  purePassWordRuleReg: 密码格式应为8-18位数字、字母、符号的任意两种组合
  purePassWordSureReg: 请输入确认密码
  purePassWordUpdateReg: 修改密码成功
  purePassword: 密码
  purePhone: 手机号码
  purePhoneCorrectReg: 请输入正确的手机号码格式
  purePhoneLogin: 手机登录
  purePhoneReg: 请输入手机号码
  purePrivacyPolicy: 《隐私政策》
  pureQQLogin: QQ登录
  pureQRCodeLogin: 二维码登录
  pureReadAccept: 我已仔细阅读并接受
  pureRegister: 注册
  pureRegisterSuccess: 注册成功
  pureRemember: 天内免登录
  pureRememberInfo: 勾选并登录后，规定天数内无需输入用户名和密码会自动登入系统
  pureSmsVerifyCode: 短信验证码
  pureSure: 确认密码
  pureTest: 模拟测试
  pureThirdLogin: 第三方登录
  pureTickPrivacy: 请勾选隐私政策
  pureTip: 扫码后点击"确认"，即可完成登录
  pureUsername: 账号
  pureUsernameReg: 请输入账号
  pureVerifyCode: 验证码
  pureVerifyCodeCorrectReg: 请输入正确的验证码
  pureVerifyCodeReg: 请输入验证码
  pureVerifyCodeSixReg: 请输入6位数字验证码
  pureWeChatLogin: 微信登录
  pureWeiBoLogin: 微博登录
menus:
  pureAble: 功能
  pureAbnormal: 异常页面
  pureAbout: 关于
  pureAnimatecss: animate.css选择器
  pureBarcode: 条形码
  pureBoard: 艺术画板
  pureButton: 按钮动效
  pureCardList: 卡片列表页
  pureCascader: 区域级联选择器
  pureCheckButton: 可选按钮
  pureCheckCard: 多选卡片
  pureChildMenuOverflow: 菜单超出显示 Tooltip 文字提示
  pureCodeMirror: 代码编辑器
  pureCollapse: 折叠面板
  pureColorHuntDoc: 调色板
  pureColorPicker: 颜色选择器
  pureComponents: 组件
  pureContextmenu: 右键菜单
  pureCountTo: 数字动画
  pureCropping: 图片裁剪
  pureDanmaku: 弹幕
  pureDatePicker: 日期选择器
  pureDateTimePicker: 日期时间选择器
  pureDebounce: 防抖节流
  pureDept: 部门管理
  pureDialog: 函数式弹框
  pureDownload: 下载
  pureDraggable: 拖拽
  pureDrawer: 函数式抽屉
  pureEditor: 编辑器
  pureElButton: 按钮
  pureEmbeddedDoc: 文档内嵌
  pureEmpty: 无Layout页
  pureEpDoc: element-plus
  pureExcel: 导出Excel
  pureExternalDoc: 文档外链
  pureExternalLink: vue-pure-admin
  pureExternalPage: 外部页面
  pureFail: 失败页面
  pureFive: '500'
  pureFlowChart: 流程图2
  pureFormDesign: 表单设计器
  pureFourZeroFour: '404'
  pureFourZeroOne: '403'
  pureGanttastic: 甘特图
  pureGuide: 引导页
  pureHome: 首页
  pureIconSelect: 图标选择器
  pureInfiniteScroll: 表格无限滚动
  pureJsonEditor: JSON编辑器
  pureLineTree: 树形连接线
  pureList: 列表页面
  pureLogin: 登录
  pureLoginLog: 登录日志
  pureMap: 地图
  pureMarkdown: Markdown
  pureMenu1: 菜单1
  pureMenu1-1: 菜单1-1
  pureMenu1-2: 菜单1-2
  pureMenu1-2-1: 菜单1-2-1
  pureMenu1-2-2: 菜单1-2-2
  pureMenu1-3: 菜单1-3
  pureMenu2: 菜单二
  pureMenuOverflow: 目录超出显示 Tooltip 文字提示
  pureMenuTree: 菜单树结构
  pureMenus: 多级菜单
  pureMessage: 消息提示
  pureMindMap: 思维导图
  pureMqtt: MQTT客户端(mqtt)
  pureOnlineUser: 在线用户
  pureOperationLog: 操作日志
  pureOptimize: 防抖、截流、复制、长按指令
  purePdf: PDF预览
  purePermission: 权限管理
  purePermissionButton: 按钮权限
  purePermissionButtonLogin: 登录接口返回按钮权限
  purePermissionButtonRouter: 路由返回按钮权限
  purePermissionPage: 页面权限
  purePiniaDoc: pinia
  purePinyin: 汉语拼音
  purePrint: 打印
  pureProgress: 进度条
  pureQrcode: 二维码
  pureResult: 结果页面
  pureRipple: 波纹(Ripple)
  pureRole: 角色管理
  pureRouterDoc: vue-router
  pureSchemaForm: 表单
  pureSeamless: 无缝滚动
  pureSegmented: 分段控制器
  pureSelector: 范围选择器
  pureSensitive: 敏感词过滤
  pureSlider: 滑块
  pureSplitPane: 切割面板
  pureStatistic: 统计组件
  pureSuccess: 成功页面
  pureSwiper: Swiper插件
  pureSysManagement: 系统管理
  pureSysMonitor: 系统监控
  pureSystemLog: 系统日志
  pureSystemMenu: 菜单管理
  pureTable: 表格
  pureTableBase: 基础用法
  pureTableEdit: 可编辑用法
  pureTableHigh: 高级用法
  pureTabs: 标签页操作
  pureTag: 标签
  pureTailwindcssDoc: tailwindcss
  pureText: 文本省略
  pureTimePicker: 时间选择器
  pureTimeline: 时间线
  pureTypeit: 打字机
  pureUiGradients: 渐变色
  pureUpload: 文件上传
  pureUser: 用户管理
  pureUtilsLink: pure-admin-utils
  pureVerify: 图形验证码
  pureVideo: 视频
  pureVideoFrame: 视频帧截取-wasm版
  pureVirtualList: 虚拟列表
  pureViteDoc: vite
  pureVueDoc: vue3
  pureVxeTable: 虚拟滚动
  pureWaterfall: 瀑布流无限滚动
  pureWatermark: 水印
  pureWavesurfer: 音频可视化
panel:
  pureClearCache: 清空缓存
  pureClearCacheAndToLogin: 清空缓存并返回登录页
  pureCloseSystemSet: 关闭配置
  pureGreyModel: 灰色模式
  pureHiddenFooter: 隐藏页脚
  pureHiddenTags: 隐藏标签页
  pureHorizontalTip: 顶部菜单，简洁概览
  pureInterfaceDisplay: 界面显示
  pureLayoutModel: 导航模式
  pureMixTip: 混合菜单，灵活多变
  pureMultiTagsCache: 页签持久化
  pureOverallStyle: 整体风格
  pureOverallStyleDark: 深色
  pureOverallStyleDarkTip: 月光序曲，沉醉于夜的静谧雅致
  pureOverallStyleLight: 浅色
  pureOverallStyleLightTip: 清新启航，点亮舒适的工作界面
  pureOverallStyleSystem: 自动
  pureOverallStyleSystemTip: 同步时光，界面随晨昏自然呼应
  pureStretch: 页宽
  pureStretchCustom: 自定义
  pureStretchCustomTip: 最小1280、最大1600
  pureStretchFixed: 固定
  pureStretchFixedTip: 紧凑页面，轻松找到所需信息
  pureSystemSet: 系统配置
  pureTagsStyle: 页签风格
  pureTagsStyleCard: 卡片
  pureTagsStyleCardTip: 卡片标签，高效浏览
  pureTagsStyleChrome: 谷歌
  pureTagsStyleChromeTip: 谷歌风格，经典美观
  pureTagsStyleSmart: 灵动
  pureTagsStyleSmartTip: 灵动标签，添趣生辉
  pureThemeColor: 主题色
  pureVerticalTip: 左侧菜单，亲切熟悉
  pureWeakModel: 色弱模式
search:
  pureCollect: 收藏
  pureDragSort: （可拖拽排序）
  pureEmpty: 暂无搜索结果
  pureHistory: 搜索历史
  purePlaceholder: 搜索菜单（支持拼音搜索）
  pureTotal: 共
status:
  pureLoad: 加载中...
  pureMessage: 消息
  pureNoMessage: 暂无消息
  pureNoNotify: 暂无通知
  pureNoTodo: 暂无待办
  pureNotify: 通知
  pureTodo: 待办
