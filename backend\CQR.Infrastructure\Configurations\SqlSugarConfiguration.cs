using SqlSugar;

namespace CQRLIB.Config.SqlSugar
{
    public class SqlSugarConfiguration
    {
        public static SqlSugarClient GetDbContext(string connectionString)
        {
            var db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = connectionString, // 连接字符串
                DbType = DbType.SqlServer,            // 数据库类型，可以是 SqlServer, MySql, Oracle 等
                IsAutoCloseConnection = true,         // 是否自动关闭连接
                InitKeyType = InitKeyType.Attribute,  // 数据库表字段对应的主键类型
                // IsShardSameThread = true              // 支持分库分表
            });
            return db;
        }
    }
}
