using System.Text;
using CQR.Application.Dto;
using CQR.Application.Dto.CQRHead;
using CQR.Application.Dto.IHSFolder;
using CQR.Application.Helpers;
using CQR.Application.Queries;
using CQR.Application.Repositories;
using CQR.Domain.Constants;
using CQR.Domain.CQRHeaders;
using CQR.Domain.Enum;
using Microsoft.Extensions.Logging;

namespace CQR.Application.Services;

public class CQRQueryService : ICQRQueryService
{
    private readonly IRepositoryCollection _repos;
    private readonly ILogger<CQRQueryService> _logger;

    public CQRQueryService(IRepositoryCollection repos, ILogger<CQRQueryService> logger)
    {
        _repos = repos;
        _logger = logger;
    }

    public async Task<CQRHeaderCollection?> GetCollectionAsync(int queueKey)
    {
        var user_id = "IBERLANG";

        var roles = await _repos.UserRoleQueryRepository.GetRolesNames(user_id);
        var phase1 = await _repos.CQRGDPIMPhasesRepository.GetByQueueAndPhase(queueKey, 1);
        var phase2 = await _repos.CQRGDPIMPhasesRepository.GetByQueueAndPhase(queueKey, 2);

        var userHasRole = new UserRoles();
        var tGateway = new GDPEP_APPROVAL[]
        {
            new GDPEP_APPROVAL(),
            new GDPEP_APPROVAL(),
            new GDPEP_APPROVAL(),
        };

        foreach (string roleCode in roles)
        {
            if (!UserRoleCodeExtensions.TryParseRole(roleCode, out var roleEnum))
            {
                continue;
            }
            switch (roleEnum)
            {
                case UserRoleCode.AMGR:
                    userHasRole.bIsAMgr = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.CQRHRCOST:
                    userHasRole.bIsCost = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.CQRHRPGM:
                    userHasRole.bIsPgm = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.CQRHRPM:
                    userHasRole.bIsPDM = true;
                    break;
                case UserRoleCode.CQRHRAME:
                    userHasRole.bIsAME_Coord = true;
                    break;
                case UserRoleCode.CQRHRPUR:
                    userHasRole.bIsPUR_Coord = true;
                    break;
                case UserRoleCode.FRAN:
                    userHasRole.bIsFran = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.FR00:
                    userHasRole.bIsFr00 = true;
                    break;
                case UserRoleCode.MBnE:
                    userHasRole.bIsMBnE = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.CQGWNFY:
                    userHasRole.bIsCQGWNFY = true;
                    break;
                case UserRoleCode.PETM:
                    userHasRole.bIsPetm = true;
                    break;
                case UserRoleCode.PRD:
                    userHasRole.bIsPrd = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.PSR:
                    userHasRole.bIsPsr = true;
                    userHasRole.bIsPA = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.PSM:
                    userHasRole.bIsPsm = true;
                    userHasRole.bIsPsr = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.SDIR:
                    userHasRole.bIsSDir = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.BUM:
                    userHasRole.bIsBum = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.FIN:
                    userHasRole.bIsFin = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.PRES:
                    userHasRole.bIsPres = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.UKGM:
                    userHasRole.bIsUkGm = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.UKDM:
                    userHasRole.bIsUkDm = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.CMGR:
                    userHasRole.bIsCmgr = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.BEB:
                    userHasRole.bIsBeb = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.MEPL:
                    userHasRole.bIsMEPL = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.AIME:
                    userHasRole.bIsAIME = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.AIPR:
                    userHasRole.bIsAIPR = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.CTLC:
                    userHasRole.bIsCTLC = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
                case UserRoleCode.PA:
                    userHasRole.bIsPA = true;
                    userHasRole.bIsPetmOnly = false;
                    break;
            }

            for (int i = 1; i <= CqrConstants.GDPEP_ROLE_COUNT; i++)
            {
                if (tGateway[1].sApprovalRole[i] == roleCode)
                    userHasRole.bIsGateway[i] = true;
            }
        }

        userHasRole.sManagerFin = _repos.UserRoleQueryRepository.GetManagerByRole("FIN");
        userHasRole.sManagerPres = _repos.UserRoleQueryRepository.GetManagerByRole("PRES");
        userHasRole.sManagerFran = _repos.UserRoleQueryRepository.GetManagerByRole("FRAN");
        userHasRole.sManagerBids = _repos.UserRoleQueryRepository.GetManagerByRole("MB&E");
        userHasRole.sManagerBeb = _repos.UserRoleQueryRepository.GetManagerByRole("BEB");
        userHasRole.sManagerCmgr = _repos.UserRoleQueryRepository.GetManagerByRole("CMGR");

        for (int i = 0; i < CqrConstants.GDPEP_ROLE_COUNT; i++)
        {
            userHasRole.sManagerGateway[i] = _repos.UserRoleQueryRepository.GetManagerByRole(
                tGateway[1].sApprovalRole[i]
            );
        }

        var cqrHeader = await _repos.CQRHeaderRepository.GetByIdAsync(queueKey);
        var ihsRecords = await _repos.CQRIHSFolderRepository.GetByQueueKeyAsync(queueKey);

        var result = new CQRHeaderCollection()
        {
            iCQRHeader = cqrHeader,
            cUserRoles = userHasRole,
            tGateway = tGateway,
            ihsFolderRecords = ihsRecords,
        };

        var isSuperUser = false;
        if (cqrHeader != null)
        {
            var tuplesSplit = CommentHelper.SplitComments(cqrHeader.ActionComments, isSuperUser);
            result.sDbsCommentsAction = tuplesSplit.asOld;
            result.sTotalCommentsAction = tuplesSplit.asNew + tuplesSplit.asOld;
        }

        result.routingTasks = await _repos.CqrHeaderQueryRepository.GetRoutingTaskByQueuekey(
            queueKey,
            "FR"
        );
        return result;
    }

    public async Task<CQR_Header?> GetHeaderByKeyAsync(int queueKey)
    {
        return await _repos.CQRHeaderRepository.GetByIdAsync(queueKey);
    }

    public async Task<List<CQR_Header>> GetHeadersAsync(CQRHeaderQueryRequest request)
    {
        // TODO: Implement based on existing query logic
        throw new NotImplementedException("GetHeadersAsync not implemented yet");
    }
    public async Task<SearchCQRResponse> SearchCQRAsync(SearchCQRQuery query)
    {
        try
        {
            _logger.LogInformation("開始處理CQR搜尋請求");

            // 建立搜尋條件
            var searchConditions = BuildSearchConditions(query);

            // 執行搜尋
            var searchResults = await ExecuteSearchAsync(query, searchConditions);

            // 計算分頁資訊
            var totalPages = (int)Math.Ceiling((double)searchResults.TotalCount / query.PageSize);

            var response = new SearchCQRResponse
            {
                Results = searchResults.Results,
                TotalCount = searchResults.TotalCount,
                PageNumber = query.PageNumber,
                PageSize = query.PageSize,
                TotalPages = totalPages,
                HasPreviousPage = query.PageNumber > 1,
                HasNextPage = query.PageNumber < totalPages,
                SearchSummary = GenerateSearchSummary(query, searchResults.TotalCount),
            };

            _logger.LogInformation($"CQR搜尋完成，找到 {searchResults.TotalCount} 筆記錄");

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CQR搜尋處理失敗");
            throw;
        }
    }

    #region Private Search Methods

    private SearchConditions BuildSearchConditions(SearchCQRQuery request)
    {
        // 簡化的搜索條件建構器
        return new SearchConditions();
    }

    private async Task<(List<CQRSearchResultDto> Results, int TotalCount)> ExecuteSearchAsync(
        SearchCQRQuery request,
        SearchConditions conditions
    )
    {
        try
        {
            // 使用 SQL 查詢，一次性取出需要的數據，避免載入所有記錄到記憶體
            var sql = BuildSearchSql(request);
            var countSql = BuildCountSql(request);
            var parameters = BuildParameters(request);

            // 先獲取總數
            var totalCount = await _repos.CqrHeaderQueryRepository.ExecuteScalarAsync<int>(
                countSql,
                parameters
            );

            // 如果沒有結果，直接返回空列表
            if (totalCount == 0)
            {
                return (new List<CQRSearchResultDto>(), 0);
            }

            // 獲取分頁數據
            var offset = (request.PageNumber - 1) * request.PageSize;
            var pagedSql =
                $"{sql} ORDER BY CH.ProjectNbr DESC, CH.RevNbr DESC OFFSET {offset} ROWS FETCH NEXT {request.PageSize} ROWS ONLY";

            var results = await _repos.CqrHeaderQueryRepository.QueryAsync<CQRSearchResultDto>(
                pagedSql,
                parameters
            );

            return (results.ToList(), totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "執行搜尋時發生錯誤: {Error}", ex.Message);
            return (new List<CQRSearchResultDto>(), 0);
        }
    }

    private string BuildSearchSql(SearchCQRQuery request)
    {
        var sql = new StringBuilder();
        sql.AppendLine("SELECT");
        sql.AppendLine("    CH.QueueKey,");
        sql.AppendLine("    CAST(CH.ProjectNbr AS VARCHAR) + '.' + CAST(CH.RevNbr AS VARCHAR) AS CQRNumber,");
        sql.AppendLine("    CH.ProjectNbr,");
        sql.AppendLine("    CH.RevNbr,");
        sql.AppendLine("    CH.ProductDesc,");
        sql.AppendLine("    CH.Status AS StatusTRS,");
        sql.AppendLine("    TRY_CAST(CH.OriginationDate AS DATETIME) AS ReleaseDate,");
        sql.AppendLine("    TRY_CAST(CH.DueDateFromEng AS DATETIME) AS DueDateFromEng,");
        sql.AppendLine("    TRY_CAST(CH.DueDateToBnE AS DATETIME) AS DueDateToBnE,");
        sql.AppendLine("    TRY_CAST(CH.CustQuoteDueDate AS DATETIME) AS CustQuoteDueDate,");
        sql.AppendLine("    TRY_CAST(CH.FRANIssueDate AS DATETIME) AS FRANIssueDate,");
        sql.AppendLine("    TRY_CAST(CH.OpeningMeetingDate AS DATETIME) AS OpeningMeetingDate,");
        sql.AppendLine("    ISNULL(UP1.LastName + ', ' + UP1.FirstName, CH.AccountMgrId) AS AccountManagerName,");
        sql.AppendLine("    ISNULL(UP2.LastName + ', ' + UP2.FirstName, CH.CostEstimatorId) AS EstimatorName,");
        sql.AppendLine("    ISNULL(UP3.LastName + ', ' + UP3.FirstName, CH.PGMId) AS PGMName,");
        sql.AppendLine("    ISNULL(UP4.LastName + ', ' + UP4.FirstName, CH.PETMId) AS PDMName,");
        sql.AppendLine("    ISNULL(UP5.LastName + ', ' + UP5.FirstName, CH.EngineeringManagerId) AS EngMgrName,");
        sql.AppendLine("    ISNULL(UP6.LastName + ', ' + UP6.FirstName, CH.PRDId) AS PRDName,");
        sql.AppendLine("    ISNULL(TR1._desc, '客戶資料暫缺') AS OEMCustomer,");
        sql.AppendLine("    '' AS OEMGroup,");
        sql.AppendLine("    '' AS GateExitTRS,");
        sql.AppendLine("    CH.FRANDesc,");
        sql.AppendLine("    CH.Vehicle AS PlatformName,");
        sql.AppendLine("    CH.ModelYear AS ModelYearTRS,");
        sql.AppendLine("    CH.VolumePerAnnum,");
        sql.AppendLine("    CH.ApproxAnnualValue,");
        sql.AppendLine("    CH.BkRndInfComments,");
        sql.AppendLine("    '' AS UniqueNumber,");
        sql.AppendLine("    CH.EngPkgComments,");
        sql.AppendLine("    CH.ManufacturingSite AS MfgSiteTRS,");
        sql.AppendLine("    CH.QSCustomerResp,");
        sql.AppendLine("    CH.QSComments,");
        sql.AppendLine("    0 AS IsSelected,");
        sql.AppendLine("    TRY_CAST(CH.LastUpdatedDate AS DATETIME) AS LastUpdatedDate");
        sql.AppendLine("FROM CQR_Header CH");
        sql.AppendLine("    LEFT JOIN USERPROF_UserProfileHeader UP1 ON UP1.UserId = CH.AccountMgrId");
        sql.AppendLine("    LEFT JOIN USERPROF_UserProfileHeader UP2 ON UP2.UserId = CH.CostEstimatorId");
        sql.AppendLine("    LEFT JOIN USERPROF_UserProfileHeader UP3 ON UP3.UserId = CH.PGMId");
        sql.AppendLine("    LEFT JOIN USERPROF_UserProfileHeader UP4 ON UP4.UserId = CH.PETMId");
        sql.AppendLine("    LEFT JOIN USERPROF_UserProfileHeader UP5 ON UP5.UserId = CH.EngineeringManagerId");
        sql.AppendLine("    LEFT JOIN USERPROF_UserProfileHeader UP6 ON UP6.UserId = CH.PRDId");
        sql.AppendLine("    LEFT JOIN TRS_Header TR1 ON TR1.table_name = 'Customer' AND TR1.table_entry = CH.CustNbr");
        sql.AppendLine(BuildWhereClause(request));

        return sql.ToString();
    }

    private string BuildCountSql(SearchCQRQuery request)
    {
        var sql = new StringBuilder();
        sql.AppendLine("SELECT COUNT(*)");
        sql.AppendLine("FROM CQR_Header CH");
        sql.AppendLine(BuildWhereClause(request));

        return sql.ToString();
    }

    private string BuildWhereClause(SearchCQRQuery request)
    {
        var conditions = new List<string>();

        if (!string.IsNullOrEmpty(request.ProductDescription))
        {
            conditions.Add("CH.ProductDesc LIKE @ProductDescription");
        }

        if (!string.IsNullOrEmpty(request.CQRFromLeft))
        {
            conditions.Add("TRY_CAST(CH.ProjectNbr AS INT) >= @CQRFromLeft");
        }

        if (!string.IsNullOrEmpty(request.CQRToLeft))
        {
            conditions.Add("TRY_CAST(CH.ProjectNbr AS INT) <= @CQRToLeft");
        }

        if (!string.IsNullOrEmpty(request.CQRFromRight))
        {
            conditions.Add("TRY_CAST(CH.RevNbr AS INT) >= @CQRFromRight");
        }

        if (!string.IsNullOrEmpty(request.CQRToRight))
        {
            conditions.Add("TRY_CAST(CH.RevNbr AS INT) <= @CQRToRight");
        }

        if (!string.IsNullOrEmpty(request.Status))
        {
            conditions.Add("CH.Status = @Status");
        }

        if (!string.IsNullOrEmpty(request.AccountManager))
        {
            conditions.Add("CH.AccountMgrId = @AccountManager");
        }

        if (!string.IsNullOrEmpty(request.CQROriginator))
        {
            conditions.Add("CH.OriginatorId = @CQROriginator");
        }

        if (conditions.Count == 0)
        {
            return "WHERE 1=1";
        }

        return "WHERE " + string.Join(" AND ", conditions);
    }

    private Dictionary<string, object> BuildParameters(SearchCQRQuery request)
    {
        var parameters = new Dictionary<string, object>();

        if (!string.IsNullOrEmpty(request.ProductDescription))
        {
            parameters["@ProductDescription"] = $"%{request.ProductDescription}%";
        }

        if (
            !string.IsNullOrEmpty(request.CQRFromLeft)
            && int.TryParse(request.CQRFromLeft, out var fromLeft)
        )
        {
            parameters["@CQRFromLeft"] = fromLeft;
        }

        if (
            !string.IsNullOrEmpty(request.CQRToLeft)
            && int.TryParse(request.CQRToLeft, out var toLeft)
        )
        {
            parameters["@CQRToLeft"] = toLeft;
        }

        if (
            !string.IsNullOrEmpty(request.CQRFromRight)
            && int.TryParse(request.CQRFromRight, out var fromRight)
        )
        {
            parameters["@CQRFromRight"] = fromRight;
        }

        if (
            !string.IsNullOrEmpty(request.CQRToRight)
            && int.TryParse(request.CQRToRight, out var toRight)
        )
        {
            parameters["@CQRToRight"] = toRight;
        }

        if (!string.IsNullOrEmpty(request.Status))
        {
            parameters["@Status"] = request.Status;
        }

        if (!string.IsNullOrEmpty(request.AccountManager))
        {
            parameters["@AccountManager"] = request.AccountManager;
        }

        if (!string.IsNullOrEmpty(request.CQROriginator))
        {
            parameters["@CQROriginator"] = request.CQROriginator;
        }

        return parameters;
    }

    private string GenerateSearchSummary(SearchCQRQuery request, int totalCount)
    {
        return $"找到 {totalCount} 筆記錄";
    }

    public async Task<IHSFolderCriteria> GetIHSFolderCriteriasAsync()
    {
        try
        {
            _logger.LogInformation("Getting IHS Folder Criterias");

            // 創建一個基本的 IHSFolderCriteria 對象，包含一些示例數據
            // 這裡應該從數據庫或配置中獲取實際的條件數據
            var criteria = new IHSFolderCriteria
            {
                OEM = new List<string> { "BMW", "Mercedes", "Audi", "Volkswagen" },
                OEMGroup = new List<string> { "BMW Group", "Daimler", "VAG" },
                Platform = new List<string> { "CLAR", "MFA", "MLB", "MQB" },
                Program = new List<string> { "G20", "W206", "A8", "Golf 8" },
                ProductionNameplate = new List<string> { "Munich", "Stuttgart", "Ingolstadt", "Wolfsburg" },
                Product = new List<Tuple<string, string>>
                {
                    new("PROD001", "Engine Control Unit"),
                    new("PROD002", "Transmission Control"),
                    new("PROD003", "Body Control Module"),
                    new("PROD004", "Infotainment System")
                },
                Site = new List<Tuple<string, string>>
                {
                    new("SITE001", "Germany - Munich"),
                    new("SITE002", "Germany - Stuttgart"),
                    new("SITE003", "China - Shanghai"),
                    new("SITE004", "USA - Detroit")
                }
            };

            _logger.LogInformation("Successfully retrieved IHS Folder Criterias");
            return criteria;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting IHS Folder Criterias");
            throw;
        }
    }

    #endregion
}

public class SearchConditions
{
    public List<string> Conditions { get; private set; } = new();
    public Dictionary<string, object> Parameters { get; private set; } = new();
    private int _paramCounter = 0;

    public void AddCondition(string field, string op, object value)
    {
        var paramName = $"@param{_paramCounter++}";
        Conditions.Add($"{field} {op} {paramName}");
        Parameters[paramName] = value;
    }

    public string BuildWhereClause()
    {
        if (Conditions.Count == 0)
            return "WHERE 1=1";

        return "WHERE 1=1 AND " + string.Join(" AND ", Conditions);
    }
}
