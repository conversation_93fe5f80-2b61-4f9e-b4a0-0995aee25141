using Microsoft.Data.SqlClient;
using System.Data;
using CQR.Domain.IServices;

namespace CQR.Domain.Services.Implementations;

public class SqlConnectionService : ISqlConnectionService
{
    private readonly string _connectionString;

    public SqlConnectionService(string connectionString)
    {
        _connectionString = connectionString;
    }

    //// 獲取SqlConnection實例
    //public Task async<SqlConnection> GetConnection()
    //{
    //    return new SqlConnection(_connectionString);
    //}

    // 異步建立連接
    public async Task<SqlConnection> CreateConnectionAsync()
    {
        var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        return connection;
    }

    // 關閉連接
    public async Task CloseConnectionAsync(SqlConnection connection)
    {
        if (connection.State == ConnectionState.Open)
        {
            await connection.CloseAsync();
        }
    }

    public async Task<SqlConnection> GetConnection()
    {
        //throw new NotImplementedException();
        return new SqlConnection(_connectionString);
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                await connection.OpenAsync();
                return true; // 連接成功
            }
        }
        catch (Exception)
        {
            return false; // 連接失敗
        }
    }
}
