<template>
  <div class="advanced-filter">
    <el-dialog
      v-model="visible"
      title="進階篩選設定"
      width="900px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="filter-content">
        <!-- 篩選條件列表 -->
        <div class="filter-rules">
          <h4>篩選條件</h4>
          <div class="rule-list">
            <div
              v-for="(rule, index) in filterRules"
              :key="index"
              class="filter-rule"
            >
              <!-- 邏輯運算符 -->
              <el-select
                v-if="index > 0"
                v-model="rule.logicalOperator"
                style="width: 80px"
                size="small"
              >
                <el-option label="AND" value="AND" />
                <el-option label="OR" value="OR" />
              </el-select>

              <!-- 欄位選擇 -->
              <el-select
                v-model="rule.field"
                placeholder="選擇欄位"
                style="width: 200px"
                size="small"
                @change="handleFieldChange(index)"
              >
                <el-option-group
                  v-for="group in fieldGroups"
                  :key="group.label"
                  :label="group.label"
                >
                  <el-option
                    v-for="field in group.options"
                    :key="field.value"
                    :label="field.label"
                    :value="field.value"
                  />
                </el-option-group>
              </el-select>

              <!-- 運算符選擇 -->
              <el-select
                v-model="rule.operator"
                placeholder="運算符"
                style="width: 120px"
                size="small"
              >
                <el-option
                  v-for="op in getOperatorsForField(rule.field)"
                  :key="op.value"
                  :label="op.label"
                  :value="op.value"
                />
              </el-select>

              <!-- 值輸入 -->
              <div class="rule-value">
                <!-- 文字輸入 -->
                <el-input
                  v-if="getFieldType(rule.field) === 'text'"
                  v-model="rule.value"
                  placeholder="輸入值"
                  size="small"
                  style="width: 200px"
                />

                <!-- 數字輸入 -->
                <el-input-number
                  v-else-if="getFieldType(rule.field) === 'number'"
                  v-model="rule.value"
                  size="small"
                  style="width: 200px"
                />

                <!-- 日期輸入 -->
                <el-date-picker
                  v-else-if="getFieldType(rule.field) === 'date'"
                  v-model="rule.value"
                  type="date"
                  size="small"
                  style="width: 200px"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />

                <!-- 下拉選擇 -->
                <el-select
                  v-else-if="getFieldType(rule.field) === 'select'"
                  v-model="rule.value"
                  placeholder="選擇值"
                  size="small"
                  style="width: 200px"
                  filterable
                >
                  <el-option
                    v-for="option in getFieldOptions(rule.field)"
                    :key="option.value"
                    :label="option.text"
                    :value="option.value"
                  />
                </el-select>

                <!-- 布林值選擇 -->
                <el-select
                  v-else-if="getFieldType(rule.field) === 'boolean'"
                  v-model="rule.value"
                  size="small"
                  style="width: 200px"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </div>

              <!-- 操作按鈕 -->
              <div class="rule-actions">
                <el-button
                  size="small"
                  type="danger"
                  icon="Delete"
                  @click="removeRule(index)"
                  :disabled="filterRules.length === 1"
                />
              </div>
            </div>
          </div>

          <div class="rule-actions-bar">
            <el-button
              size="small"
              type="primary"
              icon="Plus"
              @click="addRule"
            >
              新增條件
            </el-button>
          </div>
        </div>

        <!-- 快速篩選範本 -->
        <div class="filter-templates">
          <h4>快速篩選範本</h4>
          <div class="template-list">
            <el-button
              v-for="template in filterTemplates"
              :key="template.name"
              size="small"
              @click="applyTemplate(template)"
            >
              {{ template.name }}
            </el-button>
          </div>
        </div>

        <!-- 儲存和載入預設 -->
        <div class="preset-management">
          <h4>預設管理</h4>
          <div class="preset-actions">
            <el-input
              v-model="newPresetName"
              placeholder="預設名稱"
              style="width: 200px"
              size="small"
            />
            <el-button
              size="small"
              type="primary"
              @click="savePreset"
              :disabled="!newPresetName"
            >
              儲存預設
            </el-button>
          </div>

          <div class="preset-list">
            <el-select
              v-model="selectedPreset"
              placeholder="選擇預設"
              style="width: 200px"
              size="small"
              @change="loadPreset"
            >
              <el-option
                v-for="preset in savedPresets"
                :key="preset.id"
                :label="preset.name"
                :value="preset.id"
              />
            </el-select>
            <el-button
              size="small"
              type="danger"
              @click="deletePreset"
              :disabled="!selectedPreset"
            >
              刪除預設
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="visible = false">取消</el-button>
          <el-button @click="clearAllRules" type="info">清空條件</el-button>
          <el-button @click="applyFilter" type="primary">套用篩選</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export interface FilterRule {
  field: string
  operator: string
  value: any
  logicalOperator: 'AND' | 'OR'
}

export interface FilterTemplate {
  name: string
  rules: FilterRule[]
}

export interface FilterPreset {
  id: string
  name: string
  rules: FilterRule[]
  createdDate: string
}

// Props
interface Props {
  modelValue: boolean
  initialRules?: FilterRule[]
}

// Emits
interface Emits {
  (event: 'update:modelValue', value: boolean): void
  (event: 'apply', rules: FilterRule[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  initialRules: () => []
})

const emit = defineEmits<Emits>()

// 響應式狀態
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const filterRules = ref<FilterRule[]>([
  {
    field: '',
    operator: '=',
    value: '',
    logicalOperator: 'AND'
  }
])

const newPresetName = ref('')
const selectedPreset = ref('')
const savedPresets = ref<FilterPreset[]>([])

// 欄位定義
const fieldGroups = [
  {
    label: '基本資訊',
    options: [
      { label: 'CQR編號', value: 'cqrNumber' },
      { label: '專案編號', value: 'projectNbr' },
      { label: '版本號', value: 'revNbr' },
      { label: '狀態', value: 'statusTRS' },
      { label: '產品描述', value: 'productDesc' },
      { label: '平台', value: 'platformName' },
      { label: '年式', value: 'modelYearTRS' }
    ]
  },
  {
    label: '客戶資訊',
    options: [
      { label: 'OEM群組', value: 'oemGroup' },
      { label: 'OEM客戶', value: 'oemCustomer' },
      { label: '客戶RFQ號碼', value: 'rfqRefNbr' }
    ]
  },
  {
    label: '人員資訊',
    options: [
      { label: '客戶經理', value: 'accountManagerName' },
      { label: '估價員', value: 'estimatorName' },
      { label: 'PGM', value: 'pgmName' },
      { label: 'PDM', value: 'pdmName' },
      { label: '工程經理', value: 'engMgrName' }
    ]
  },
  {
    label: '日期資訊',
    options: [
      { label: '發佈日期', value: 'releaseDate' },
      { label: '發行日期', value: 'franIssueDate' },
      { label: '工程到期日', value: 'dueDateFromEng' },
      { label: 'B&E到期日', value: 'dueDateToBnE' },
      { label: '報價到期日', value: 'custQuoteDueDate' },
      { label: '開會日期', value: 'openingMeetingDate' }
    ]
  },
  {
    label: '數值資訊',
    options: [
      { label: '年產量', value: 'volumePerAnnum' },
      { label: '預估年產值', value: 'approxAnnualValue' }
    ]
  }
]

// 運算符定義
const operators = {
  text: [
    { label: '等於', value: '=' },
    { label: '不等於', value: '!=' },
    { label: '包含', value: 'LIKE' },
    { label: '不包含', value: 'NOT LIKE' },
    { label: '開始於', value: 'STARTS_WITH' },
    { label: '結束於', value: 'ENDS_WITH' },
    { label: '為空', value: 'IS NULL' },
    { label: '不為空', value: 'IS NOT NULL' }
  ],
  number: [
    { label: '等於', value: '=' },
    { label: '不等於', value: '!=' },
    { label: '大於', value: '>' },
    { label: '大於等於', value: '>=' },
    { label: '小於', value: '<' },
    { label: '小於等於', value: '<=' },
    { label: '介於', value: 'BETWEEN' },
    { label: '為空', value: 'IS NULL' },
    { label: '不為空', value: 'IS NOT NULL' }
  ],
  date: [
    { label: '等於', value: '=' },
    { label: '不等於', value: '!=' },
    { label: '早於', value: '<' },
    { label: '早於或等於', value: '<=' },
    { label: '晚於', value: '>' },
    { label: '晚於或等於', value: '>=' },
    { label: '介於', value: 'BETWEEN' },
    { label: '為空', value: 'IS NULL' },
    { label: '不為空', value: 'IS NOT NULL' }
  ],
  select: [
    { label: '等於', value: '=' },
    { label: '不等於', value: '!=' },
    { label: '包含於', value: 'IN' },
    { label: '不包含於', value: 'NOT IN' }
  ],
  boolean: [
    { label: '是', value: '=' },
    { label: '否', value: '!=' }
  ]
}

// 欄位類型映射
const fieldTypeMap: Record<string, string> = {
  cqrNumber: 'text',
  projectNbr: 'number',
  revNbr: 'number',
  statusTRS: 'select',
  productDesc: 'text',
  platformName: 'text',
  modelYearTRS: 'select',
  oemGroup: 'select',
  oemCustomer: 'select',
  rfqRefNbr: 'text',
  accountManagerName: 'select',
  estimatorName: 'select',
  pgmName: 'select',
  pdmName: 'select',
  engMgrName: 'select',
  releaseDate: 'date',
  franIssueDate: 'date',
  dueDateFromEng: 'date',
  dueDateToBnE: 'date',
  custQuoteDueDate: 'date',
  openingMeetingDate: 'date',
  volumePerAnnum: 'number',
  approxAnnualValue: 'number'
}

// 預設篩選範本
const filterTemplates: FilterTemplate[] = [
  {
    name: '進行中的CQR',
    rules: [
      {
        field: 'statusTRS',
        operator: 'NOT IN',
        value: ['已關閉', '已終止'],
        logicalOperator: 'AND'
      }
    ]
  },
  {
    name: '本月新增',
    rules: [
      {
        field: 'releaseDate',
        operator: '>=',
        value: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().slice(0, 10),
        logicalOperator: 'AND'
      }
    ]
  },
  {
    name: '即將到期',
    rules: [
      {
        field: 'custQuoteDueDate',
        operator: '<=',
        value: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
        logicalOperator: 'AND'
      },
      {
        field: 'custQuoteDueDate',
        operator: '>=',
        value: new Date().toISOString().slice(0, 10),
        logicalOperator: 'AND'
      }
    ]
  }
]

// 方法
const getFieldType = (field: string): string => {
  return fieldTypeMap[field] || 'text'
}

const getOperatorsForField = (field: string) => {
  const fieldType = getFieldType(field)
  return operators[fieldType as keyof typeof operators] || operators.text
}

const getFieldOptions = (field: string) => {
  // 這裡應該根據欄位返回相應的選項
  // 實際實作中應該從API獲取
  return []
}

const handleFieldChange = (index: number) => {
  // 當欄位改變時，重置運算符和值
  filterRules.value[index].operator = '='
  filterRules.value[index].value = ''
}

const addRule = () => {
  filterRules.value.push({
    field: '',
    operator: '=',
    value: '',
    logicalOperator: 'AND'
  })
}

const removeRule = (index: number) => {
  if (filterRules.value.length > 1) {
    filterRules.value.splice(index, 1)
  }
}

const clearAllRules = () => {
  filterRules.value = [
    {
      field: '',
      operator: '=',
      value: '',
      logicalOperator: 'AND'
    }
  ]
}

const applyTemplate = (template: FilterTemplate) => {
  filterRules.value = [...template.rules]
  ElMessage.success(`已套用範本：${template.name}`)
}

const applyFilter = () => {
  // 驗證規則
  const validRules = filterRules.value.filter(rule => rule.field && rule.value !== '')
  
  if (validRules.length === 0) {
    ElMessage.warning('請至少設定一個有效的篩選條件')
    return
  }

  emit('apply', validRules)
  visible.value = false
  ElMessage.success('篩選條件已套用')
}

const savePreset = async () => {
  if (!newPresetName.value.trim()) {
    ElMessage.warning('請輸入預設名稱')
    return
  }

  const validRules = filterRules.value.filter(rule => rule.field && rule.value !== '')
  
  if (validRules.length === 0) {
    ElMessage.warning('請設定至少一個篩選條件')
    return
  }

  const preset: FilterPreset = {
    id: Date.now().toString(),
    name: newPresetName.value.trim(),
    rules: validRules,
    createdDate: new Date().toISOString()
  }

  savedPresets.value.push(preset)
  newPresetName.value = ''
  
  ElMessage.success('預設已儲存')
}

const loadPreset = () => {
  const preset = savedPresets.value.find(p => p.id === selectedPreset.value)
  if (preset) {
    filterRules.value = [...preset.rules]
    ElMessage.success(`已載入預設：${preset.name}`)
  }
}

const deletePreset = async () => {
  if (!selectedPreset.value) return

  const preset = savedPresets.value.find(p => p.id === selectedPreset.value)
  if (!preset) return

  try {
    await ElMessageBox.confirm(
      `確定要刪除預設「${preset.name}」嗎？`,
      '確認刪除',
      {
        confirmButtonText: '刪除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    savedPresets.value = savedPresets.value.filter(p => p.id !== selectedPreset.value)
    selectedPreset.value = ''
    
    ElMessage.success('預設已刪除')
  } catch {
    // 取消刪除
  }
}

// 初始化
watch(() => props.initialRules, (newRules) => {
  if (newRules.length > 0) {
    filterRules.value = [...newRules]
  }
}, { immediate: true })
</script>

<style scoped>
.advanced-filter {
  /* 樣式會由對話框處理 */
}

.filter-content {
  max-height: 600px;
  overflow-y: auto;
}

.filter-rules {
  margin-bottom: 30px;
}

.filter-rules h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.rule-list {
  margin-bottom: 16px;
}

.filter-rule {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.rule-value {
  flex: 1;
}

.rule-actions {
  display: flex;
  gap: 8px;
}

.rule-actions-bar {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
}

.filter-templates {
  margin-bottom: 30px;
}

.filter-templates h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.template-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preset-management h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.preset-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.preset-list {
  display: flex;
  gap: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .filter-rule {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .preset-actions,
  .preset-list {
    flex-direction: column;
    gap: 8px;
  }
}
</style>