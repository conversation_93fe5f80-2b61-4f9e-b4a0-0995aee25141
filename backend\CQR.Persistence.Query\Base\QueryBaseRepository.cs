﻿using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Data;

//namespace CQR.Persistence.Query.Base
//{
//    internal class QueryBaseRepository
//    {
//    }
//}


namespace CQR.Persistence.Query.Base
{
    public abstract class QueryBaseRepository
    {
        protected readonly IDbConnection _connection;

        protected QueryBaseRepository(IConfiguration configuration)
        {
            // 從 appsettings.json 或環境變數取得連線字串
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            _connection = new SqlConnection(connectionString);
        }

        protected async Task<IEnumerable<T>> QueryAsync<T>(string sql, object param = null)
        {
            return await _connection.QueryAsync<T>(sql, param);
        }

        protected async Task<T> QuerySingleAsync<T>(string sql, object param = null)
        {
            return await _connection.QuerySingleOrDefaultAsync<T>(sql, param);
        }

        // 若需額外支援 Execute、QueryFirst、Transaction，可擴充此類
    }
}
