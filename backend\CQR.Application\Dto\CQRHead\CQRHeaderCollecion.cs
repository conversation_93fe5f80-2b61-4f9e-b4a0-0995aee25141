﻿using CQR.Application.Dto.RoutingTask;
using CQR.Domain.CQRHeaders;
using CQR.Domain.CQRIHSFolder;
using CQR.Domain.Enum;

namespace CQR.Application.Dto.CQRHead;

public class CQRHeaderCollection
{
    public CQR_Header? iCQRHeader { get; set; }
    public string? sDbsCommentsAction { get; set; }
    public string? sNewCommentsAction { get; set; }
    public string? sTotalCommentsAction { get; set; }

    public long iQueueKey { get; set; }
    public int iOpenMode { get; set; }
    public bool bFolderWasLocked { get; set; }
    public string? sStatus { get; set; }
    public string? sStatusDate { get; set; }
    public string? sOriginator { get; set; }
    public string? sOriginationDate { get; set; }
    public bool bNewCQR { get; set; }
    public int iSaveMode { get; set; }
    public bool bSuperUser { get; set; }
    public string? sFolderNum { get; set; }
    public string? sProjectNum { get; set; }
    public string? sRevision { get; set; }
    public string? sCQRIssueDate { get; set; }
    public string? sFranNewModRepl { get; set; }
    public string? sQuoteType { get; set; }
    public string? sUserStamp { get; set; }
    public string? sSecurityMode { get; set; }
    public string? sCommentsHeld { get; set; }

    // Section 1
    public string? sComment { get; set; }
    public string? sNewComment { get; set; }
    public string? sOrgComment { get; set; }
    public string? sCQRDesc { get; set; }
    public string? sProductDescription { get; set; }
    public string? sVehicleBuildId { get; set; }
    public string? sOEMGroup { get; set; }
    public string? sCustomerBuyer { get; set; }
    public string? sCustomerEngineer { get; set; }
    public string? sPlatform { get; set; }
    public string? sNameplate { get; set; }
    public string? sModelYear { get; set; }
    public string? sRFQRefNum { get; set; }
    public string? sRFQRecDate { get; set; }
    public string? sCustomerQuoteDueDateLast { get; set; }
    public string? sCustomerQuoteDueDateOrig { get; set; }
    public string? sCustomerQuoteDueDateChangeComment { get; set; }
    public string? sQuoteRespDueDate { get; set; }
    public string? sQuoteRespDueDateDbs { get; set; }
    public string? sManufacturingSite { get; set; }
    public string? sManufacturingSiteDbs { get; set; }
    public string? sPIMSite { get; set; }
    public string? sPIMSiteDbs { get; set; }
    public string? sEngineeringSite { get; set; }
    public string? sEngineeringSiteDbs { get; set; }
    public string? sApproxAnnualValue { get; set; }
    public string? sObsolescenceRequiredInd { get; set; }
    public string? sCommissionRequiredInd { get; set; }
    public string? sCommissionPercentage { get; set; }
    public string? sManufacturerRepCompany { get; set; }
    public string? sManufacturerRepIndividual { get; set; }
    public string? sAwardQuarter;
    public string? sAwardYear;
    public string? sGateExit;

    public string? sCustomerGroup;
    public string? sProductCategory;
    public string? sBusinessPlan;

    // Section 2
    public string? sIfCheckedInd;
    public string? sProductionInd;
    public string? sPrototypeInd;
    public string? sPrototypeQuantity;
    public string? sPiecePriceInd;
    public string? sToolingInd;
    public string? sDesignProposalInd;
    public string? sTimingPlanInd;

    // Section 3
    public string? sMilestone1Date;
    public string? sMilestone2Date;
    public string? sMilestone3Date;
    public string? sMilestone4Date;
    public string? sMilestone5Date;
    public string? sCommercialManager;
    public string? sCommercialBusinessManager;
    public string? sAccountManager;
    public string? sSalesAccountDirector;
    public string? sCostEstimator;
    public string? sPGM_aka_CoC;
    public string? sPDM_aka_ProgramManager;
    public string? sAMECoordinator;
    public string? sPurchasingCoordinator;
    public string? sPA;
    public string? sPSM;

    public string? sTDRDate;
    public string? sTDRNoInput;
    public string? sCommunication;
    public string? sASILLevel;
    public string? sAUTOSAR;
    public string? sCybersecurity;
    public string? sOtherRelevantFeatures;

    public string? sEngineeringManager;
    public string? sEngineeringManagerDbs;
    public string? sCalculationCurrency;

    public string? sCommercialManagerDbs;
    public string? sAccountManagerDbs;
    public string? sSalesAccountDirectorDbs;
    public string? sCostEstimatorDbs;
    public string? sPGM_aka_CoCDbs;
    public string? sPDM_aka_ProgramManagerDbs;
    public string? sAMECoordinatorDbs;
    public string? sPurchasingCoordinatorDbs;

    // Section 4
    public string? sTimeFrameOkInd;
    public string? sInformationOkInd;
    public string? sWorkProceedOkInd;
    public string? sHealthInd;
    public string? sModNeedsCostInd;

    public string[] sElecInputReqdInd = new string[9];
    public string[] sElecPRD = new string[9];
    public string[] sElecPRDdbs = new string[9];
    public string[] sElecPRDNoInput = new string[9];

    public string? sEngineerAssigned;
    public string? sEngineerAssignedDbs;
    public string? sLeadManufacturing;
    public string? sLeadPurchasing;
    public string? sLeadValidation;
    public string? sLeadManufacturingNoCostImpact;
    public string? sLeadPurchasingNoCostImpact;
    public string? sLeadValidationNoCostImpact;
    public string? sLeadManufacturingDbs;
    public string? sLeadPurchasingDbs;
    public string? sLeadValidationDbs;
    public string? sQS9000Considered;
    public string? sDateOfCSR;
    public string? sDueDateFromEngineering;
    public string? sDueDateFromEngineeringDbs;
    public string? sDueDateToBidsAndEstimating;
    public string? sDueDateToBidsAndEstimatingDbs;
    public string? sGDPEPPhase1Required;
    public string? sGDPEPPhase1RequiredDbs;
    public string? sGDPEPPhase2Required;
    public string? sGDPEPPhase2RequiredDbs;

    //RoutingTask
    public IEnumerable<RoutingTaskDto>? routingTasks { get; set; }

    // flaga
    private bool bCreate;
    private bool bModify;
    private string? sLockedKey;
    private string? sLockedEntry;
    private string? sLockedNumber;

    public bool bUserIsOrig;
    public bool bUserHasOrigRole;
    public bool bOrigIsPgm;
    public bool bOrigIsPetm;
    public bool bOrigIsAMgr;
    public bool bOrigIsSDir;
    public bool bOrigIsCost;
    public bool bOrigIsMbne;
    public bool bDualRelease; // Used for dual release of PGM/PETM tasks
    public bool bHasChecked = false;
    public string? sIndTask;
    public Antares antaresRecords;

    //public T_IHS ihsRecords;
    public IEnumerable<CQR_IHSFolder>? ihsFolderRecords { get; set; }
    public UserRoles? cUserRoles { get; set; }

    public GDPEP_APPROVAL[] tGateway { get; set; } =
        { new GDPEP_APPROVAL(), new GDPEP_APPROVAL(), new GDPEP_APPROVAL() };

    public bool CanAddTasklist()
    {
        if (bSuperUser)
            return true;

        if (bUserIsOrig)
        {
            if (Enum.TryParse<StatusUsfrEnum>(this.sStatus, out var currentStatus))
            {
                if (currentStatus < StatusUsfrEnum.status040100FR)
                    return true;
            }
            else
            {
                // 轉換失敗，看你需求要怎麼處理
                return false;
            }
        }
        return false;
    }

    //public bool CanAddTasklist()
    //{
    //    StatusUsfrEnum status = StatusUsfrEnum.status040100FR;

    //    // 取得名稱（字串）：
    //    string name = status.ToString();  // "status020500FR"

    //    if (bSuperUser) return true;
    //    if (bUserIsOrig && this.sStatus < name) return true;
    //    return false;
    //}
}

public struct T_IHS
{
    public string sCoreNameplatePlantMnemonic;

    public string sOEMGroup;

    public string sOEMCustomer;

    public string sPlatform;

    public string sProgram;

    public string sNameplate;

    public string sRegion;

    public string sCountry;

    public string sSOP;

    public string sEOP;

    public string sProductDescription;

    public string sProductGrouping;

    public string sSoldFrom;

    public string sFinalAssembly;

    public bool bArchived;
}
