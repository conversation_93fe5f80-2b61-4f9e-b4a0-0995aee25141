namespace CQR.Infrastructure;

using System.Data;
using Dapper;

public interface IBaseRepository<T>
    where T : class
{
    Task<int> AddAsync(T entity);
    Task<int> UpdateAsync(T entity);
    Task<int> DeleteAsync(int id);
    Task<T> GetByIdAsync(int id);
    Task<IEnumerable<T>> GetAllAsync();
}

public class BaseRepository<T> : IBaseRepository<T>
    where T : class
{
    private readonly IDbConnection _dbConnection;

    public BaseRepository(IDbConnection dbConnection)
    {
        _dbConnection = dbConnection;
    }

    public async Task<int> AddAsync(T entity)
    {
        var query =
            $"INSERT INTO {typeof(T).Name} ({string.Join(", ", typeof(T).GetProperties().Select(p => p.Name))}) "
            + $"VALUES ({string.Join(", ", typeof(T).GetProperties().Select(p => "@" + p.Name))})";

        return await _dbConnection.ExecuteAsync(query, entity);
    }

    public async Task<int> UpdateAsync(T entity)
    {
        var query =
            $"UPDATE {typeof(T).Name} SET {string.Join(", ", typeof(T).GetProperties().Select(p => $"{p.Name} = @{p.Name}"))} WHERE Id = @Id";

        return await _dbConnection.ExecuteAsync(query, entity);
    }

    public async Task<int> DeleteAsync(int id)
    {
        var query = $"DELETE FROM {typeof(T).Name} WHERE Id = @Id";

        return await _dbConnection.ExecuteAsync(query, new { Id = id });
    }

    public async Task<T> GetByIdAsync(int id)
    {
        var query = $"SELECT * FROM {typeof(T).Name} WHERE Id = @Id";

        return await _dbConnection.QuerySingleOrDefaultAsync<T>(query, new { Id = id });
    }

    public async Task<IEnumerable<T>> GetAllAsync()
    {
        var query = $"SELECT * FROM {typeof(T).Name}";

        return await _dbConnection.QueryAsync<T>(query);
    }
}
