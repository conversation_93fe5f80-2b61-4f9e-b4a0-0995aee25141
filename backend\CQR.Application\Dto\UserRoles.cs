﻿using CQR.Domain.Constants;

namespace CQR.Application.Dto;

public class UserRoles
{
    public bool bIsSAFR { get; set; }

    public bool bIsAMgr { get; set; }

    public bool bIsCost { get; set; }

    public bool bIsPA { get; set; }

    public bool bIsFran { get; set; }

    public bool bIsFr00 { get; set; }

    public bool bIsMBnE { get; set; }

    public bool bIsCQGWNFY { get; set; }

    public bool bIsPetm { get; set; }

    public bool bIsPgm { get; set; }

    public bool bIsPrd { get; set; }

    public bool bIsPsm { get; set; }

    public bool bIsPDM { get; set; }

    public bool bIsAME_Coord { get; set; }

    public bool bIsPUR_Coord { get; set; }

    public bool bIsPsr { get; set; }

    public bool bIsSDir { get; set; }

    public bool bIsBum { get; set; }

    public bool bIsFin { get; set; }

    public bool bIsPres { get; set; }

    public bool bIsUkGm { get; set; }

    public bool bIsUkDm { get; set; }

    public bool bIsBeb { get; set; }

    public bool bIsCmgr { get; set; }

    public bool bIsMEPL { get; set; }

    public bool bIsAIME { get; set; }

    public bool bIsAIPR { get; set; }

    public bool bIsCTLC { get; set; }

    public string? sManagerFran { get; set; }

    public string? sManagerBids { get; set; }

    public string? sManagerFin { get; set; }

    public string? sManagerPres { get; set; }

    public string? sManagerBeb { get; set; }

    public string? sManagerCmgr { get; set; }

    //public bool[] bIsGateway;
    public bool[] bIsGateway { get; set; } = new bool[CqrConstants.GDPEP_ROLE_COUNT];

    public string[] sManagerGateway { get; set; } = new string[CqrConstants.GDPEP_ROLE_COUNT];
    //public string[] sManagerGateway;

    public bool bIsPetmOnly { get; set; }

    //todo
    //public T_UserRole()
    //{
    //    bIsGateway = new bool[13];
    //    sManagerGateway = new string[13];
    //}
}