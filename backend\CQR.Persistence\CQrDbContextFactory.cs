using CQR.Persistence.Command.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Command;

public class CQrDbContextFactory : IDesignTimeDbContextFactory<CQRDbContext>
{
    public CQRDbContext CreateDbContext(string[] args = null)
    {
        var optionsBuilder = new DbContextOptionsBuilder<CQRDbContext>();

        // 使用 ConfigurationBuilder 来加载 appsettings.json 中的配置
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json") // 确保您的连接字符串在这个文件中
            .Build();

        optionsBuilder.UseSqlServer(configuration.GetConnectionString("DefaultConnection"));

        return new CQRDbContext(optionsBuilder.Options);
    }
}