using GPL_Core.Utilities;
using Microsoft.AspNetCore.Mvc;

namespace CQRAPI.Controllers
{
    [Route("/")]
    [ApiController]
    public class HomeController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        public HomeController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        //todo antaresRecords
        [HttpGet]
        public ActionResult<string> Get()
        {
            return Ok($"Running....");
        }

        [HttpGet]
        [Route("UseDatabase")]
        public ActionResult<string> GetUseDatabase()
        {
            var msgDB = ConfigUtil.GetUseDatabase(_configuration);
            return Ok($"Running....{msgDB}");
        }

        [HttpGet]
        [Route("ASPNETCORE_ENVIRONMENT")]
        public ActionResult<string> GetASPNETCORE_ENVIRONMENT()
        {
            // 返回字符串响应
            var result = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            return Ok(
                $"ASPNETCORE_ENVIRONMENT:{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}"
            );
        }

        //https://learn.microsoft.com/zh-tw/azure/active-directory-b2c/enable-authentication-web-api?tabs=csharpclient
        [HttpGet]
        [Route("Identity")]
        public ActionResult GetUserIdentity()
        {
            return Ok(new { name = User.Identity.Name });
        }

        [HttpGet]
        [Route("/Home/Error")]
        public IActionResult Error()
        {
            return StatusCode(500); // 返回500状态码
        }
    }
}
