import type { HttpResponseResult, RoleUserInfo } from "@/store/types";
import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export const getOriginatorList = () => {
  const url = baseUrlApi("RoleUser/OriginatorList");
  return http.request<RoleUserInfo>("get", url, {});
};

export const getRoleUseCollection = () => {
  const url = baseUrlApi("RoleUser/role/roleCollection");
  return http.request<HttpResponseResult>("get", url, {});
};

//todo  	Account Manager (AMGR):,Product Group Manager (PGM):,Estimator:,Product Dev. Mgr (PDM):
