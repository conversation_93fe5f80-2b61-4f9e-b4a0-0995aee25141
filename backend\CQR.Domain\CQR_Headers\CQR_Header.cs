﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CQR.Domain.CQRHeaders;

[Table("CQR_Header")]
public class CQR_Header
{
    [Key]
    [Column("QueueKey")]
    public int QueueKey { get; set; }

    [MaxLength(50)]
    [Column("lock_userid")]
    public string? LockUserId { get; set; }

    [Column("lock_time")]
    public DateTime? LockTime { get; set; }

    [MaxLength(50)]
    public string? AwardYear { get; set; }

    [MaxLength(50)]
    public string? AwardQuarter { get; set; }

    [MaxLength(50)]
    public string? ProjectNbr { get; set; }

    [MaxLength(50)]
    public string? RevNbr { get; set; }

    public string? CalculationCurrency { get; set; }
    public string? RegionalQuotingTeam { get; set; }
    public string? OpeningMeetingDate { get; set; }
    public string? Communication { get; set; }
    public string? ASILLevel { get; set; }
    public string? AUTOSAR { get; set; }
    public string? AMECoordinator { get; set; }
    public string? PurchasingCoordinator { get; set; }
    public string? Cybersecurity { get; set; }
    public string? TDRNoInput { get; set; }

    [MaxLength(20)]
    public string? Status { get; set; }

    [MaxLength(20)]
    public string? StatusDate { get; set; }

    [MaxLength(100)]
    public string? ProductDesc { get; set; }

    [MaxLength(50)]
    public string? CustNbr { get; set; }

    [MaxLength(100)]
    public string? CustBuyerName { get; set; }

    [MaxLength(100)]
    public string? CustEngineerName { get; set; }

    [MaxLength(50)]
    public string? Vehicle { get; set; }

    [MaxLength(10)]
    public string? ModelYear { get; set; }

    [MaxLength(50)]
    public string? RFQRefNbr { get; set; }

    [MaxLength(20)]
    public string? RFQRcvdDate { get; set; }

    [MaxLength(20)]
    public string? VolumePerAnnum { get; set; }

    [MaxLength(20)]
    public string? OriginalProductLife { get; set; }

    [MaxLength(20)]
    public string? RemainingProductLife { get; set; }

    [MaxLength(20)]
    public string? CustQuoteDueDate { get; set; }
    public string? TDRDate { get; set; }

    [MaxLength(20)]
    public string? QuoteResponseDueDate { get; set; }

    [MaxLength(20)]
    public string? FRANIssueDate { get; set; }

    [MaxLength(50)]
    public string? ManufacturingSite { get; set; }

    [MaxLength(50)]
    public string? EngineeringSite { get; set; }

    [MaxLength(50)]
    public string? PIMSite { get; set; }

    [MaxLength(10)]
    public string? NewModRepl { get; set; }

    [MaxLength(1)]
    public string? InForecastInd { get; set; }

    [MaxLength(20)]
    public string? ApproxAnnualValue { get; set; }

    [MaxLength(10)]
    public string? QuoteType { get; set; }

    [MaxLength(50)]
    public string? Gateway { get; set; }

    [MaxLength(1)]
    public string? IfCheckedInd { get; set; }

    [MaxLength(1)]
    public string? IfCheckedDoneInd { get; set; }

    [MaxLength(1)]
    public string? ProductionReqdInd { get; set; }

    [MaxLength(1)]
    public string? PrototypeReqdInd { get; set; }

    [MaxLength(20)]
    public string? PrototypeQty { get; set; }

    [MaxLength(1)]
    public string? PiecePriceReqdInd { get; set; }

    [MaxLength(1)]
    public string? ToolingReqdInd { get; set; }

    [MaxLength(1)]
    public string? DesignPropReqdInd { get; set; }

    [MaxLength(1)]
    public string? TimingPlanReqdInd { get; set; }

    [MaxLength(20)]
    public string? Milestone1Date { get; set; }

    [MaxLength(20)]
    public string? Milestone2Date { get; set; }

    [MaxLength(20)]
    public string? Milestone3Date { get; set; }

    [MaxLength(20)]
    public string? Milestone4Date { get; set; }

    [MaxLength(20)]
    public string? Milestone5Date { get; set; }

    [MaxLength(1)]
    public string? ObsolescenceReqdInd { get; set; }

    [MaxLength(1)]
    public string? CommissionReqdInd { get; set; }

    [MaxLength(20)]
    public string? CommissionPercent { get; set; }

    [MaxLength(50)]
    public string? MfgRepCompany { get; set; }

    [MaxLength(100)]
    public string? MfgRepIndividual { get; set; }

    [MaxLength(50)]
    public string? OriginatorId { get; set; }

    [MaxLength(20)]
    public string? OriginationDate { get; set; }

    [MaxLength(50)]
    public string? AccountMgrId { get; set; }

    [MaxLength(50)]
    public string? SalesAcctDirectorId { get; set; }

    [MaxLength(50)]
    public string? CostEstimatorId { get; set; }

    [MaxLength(50)]
    public string? PGMId { get; set; }

    [MaxLength(50)]
    public string? PETMId { get; set; }

    [MaxLength(50)]
    public string? PRDId { get; set; }

    [MaxLength(50)]
    public string? EngineeringManagerId { get; set; }
    public string? CommercialManager { get; set; }

    [MaxLength(1)]
    public string? TimeframeOKInd { get; set; }

    [MaxLength(1)]
    public string? InformationOKInd { get; set; }

    [MaxLength(1)]
    public string? WorkProceedOKInd { get; set; }

    [MaxLength(1)]
    public string? ElecInputReqdInd { get; set; }

    [MaxLength(1)]
    public string? ElecInputReqdInd2 { get; set; }

    [MaxLength(1)]
    public string? ElecInputReqdInd3 { get; set; }

    [MaxLength(1)]
    public string? ElecInputReqdInd4 { get; set; }

    [MaxLength(50)]
    public string? ElecPETMId { get; set; }

    [MaxLength(50)]
    public string? ElecPETMId2 { get; set; }

    [MaxLength(50)]
    public string? ElecPETMId3 { get; set; }

    [MaxLength(50)]
    public string? ElecPETMId4 { get; set; }

    [MaxLength(20)]
    public string? DueDateFromEng { get; set; }

    [MaxLength(20)]
    public string? DueDateToBnE { get; set; }

    [MaxLength(20)]
    public string? Unused1 { get; set; }

    [MaxLength(1)]
    public string? NoRctFolders { get; set; }

    [MaxLength(1)]
    public string? Unused2 { get; set; }

    [MaxLength(20)]
    public string? LastUpdatedDate { get; set; }

    [MaxLength(20)]
    public string? LastUpdatedTime { get; set; }

    [MaxLength(10)]
    public string? LastUpdatedVersion { get; set; }

    [MaxLength(50)]
    public string? LastUpdatedBy { get; set; }

    [MaxLength(20)]
    public string? QuoteCompletedSent { get; set; }

    [MaxLength(20)]
    public string? QuoteResponseSent { get; set; }

    [MaxLength(20)]
    public string? FranClosedOut { get; set; }

    [MaxLength(1)]
    public string? Unused_002 { get; set; }

    [MaxLength(50)]
    public string? QRFOB { get; set; }

    [MaxLength(20)]
    public string? QRToolLeadTime { get; set; }

    [MaxLength(20)]
    public string? QRMaterialDate { get; set; }

    [MaxLength(20)]
    public string? QRLaborDate { get; set; }

    [MaxLength(20)]
    public string? QRToolingCapacity { get; set; }

    [MaxLength(20)]
    public string? QRProgramClass { get; set; }

    [MaxLength(200)]
    public string? FRANDesc { get; set; }

    [MaxLength(1)]
    public string? HealthAndSafetyInd { get; set; }

    [MaxLength(50)]
    public string? QSQuoteStatus { get; set; }

    [MaxLength(50)]
    public string? QSAwardStatus { get; set; }

    [MaxLength(20)]
    public string? QSQuoteDate { get; set; }

    [MaxLength(100)]
    public string? QSCustomerResp { get; set; }

    [MaxLength(50)]
    public string? QSCustomerPO { get; set; }

    [MaxLength(50)]
    public string? QSCustomerAuth { get; set; }

    [MaxLength(50)]
    public string? QSCustomerFran { get; set; }

    [MaxLength(100)]
    public string? QSCommentId { get; set; }

    [MaxLength(1)]
    public string? ModNeedsCostInd { get; set; }

    [MaxLength(20)]
    public string? Milestone6Date { get; set; }

    [MaxLength(50)]
    public string? PSMId { get; set; }

    [MaxLength(50)]
    public string? PAId { get; set; }

    [MaxLength(200)]
    public string? VPAComments { get; set; }

    [MaxLength(200)]
    public string? IntPricComments { get; set; }

    [MaxLength(200)]
    public string? BkRndInfComments { get; set; }

    [MaxLength(200)]
    public string? OtherInfo { get; set; }

    [MaxLength(200)]
    public string? ActionComments { get; set; }

    [MaxLength(200)]
    public string? QRToolingComments { get; set; }

    [MaxLength(200)]
    public string? QAFComments { get; set; }

    [MaxLength(200)]
    public string? EngPkgComments { get; set; }

    [MaxLength(200)]
    public string? QSComments { get; set; }

    [MaxLength(200)]
    public string? CustProdComments { get; set; }

    [MaxLength(10)]
    public string? ResponseLevel { get; set; }

    [MaxLength(50)]
    public string? BusinessClassif { get; set; }

    [MaxLength(50)]
    public string? ProductCategory { get; set; }

    [MaxLength(50)]
    public string? ProductLine { get; set; }

    [MaxLength(100)]
    public string? WarrantyRqmts { get; set; }

    [MaxLength(100)]
    public string? PerformanceRqmts { get; set; }

    [MaxLength(50)]
    public string? CurrentSupplier { get; set; }

    [MaxLength(50)]
    public string? CustomerJob1 { get; set; }

    [MaxLength(50)]
    public string? LocationSales { get; set; }

    [MaxLength(50)]
    public string? LocationEngineer { get; set; }

    [MaxLength(50)]
    public string? LocationShipping { get; set; }
}
