{"version": "2.0.0", "tasks": [{"label": "build-backend", "group": "build", "type": "shell", "command": "dotnet", "args": ["build", "${workspaceFolder}/backend/CQR.API/CQR.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"reveal": "silent"}, "problemMatcher": "$msCompile"}, {"label": "publish-backend", "group": "build", "type": "shell", "command": "dotnet", "args": ["publish", "${workspaceFolder}/backend/CQR.API/CQR.API.csproj", "--configuration", "Release", "--output", "${workspaceFolder}/backend/publish"], "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"reveal": "silent"}, "problemMatcher": "$msCompile"}, {"label": "watch-backend", "group": "build", "type": "shell", "command": "dotnet", "args": ["watch", "run", "--project", "${workspaceFolder}/backend/CQR.API/CQR.API.csproj"], "options": {"cwd": "${workspaceFolder}/backend/CQR.API"}, "presentation": {"reveal": "always"}, "isBackground": true, "problemMatcher": {"owner": "dotnet", "source": "dotnet", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error)\\s+(\\w*):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "code": 5, "message": 6}, "background": {"activeOnStart": true, "beginsPattern": "^.*Building\\.\\.\\.$", "endsPattern": "^.*Application started\\. Press Ctrl\\+C to shut down\\.$"}}}, {"label": "clean-backend", "group": "build", "type": "shell", "command": "dotnet", "args": ["clean", "${workspaceFolder}/backend/CQR.API/CQR.API.csproj"], "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"reveal": "silent"}, "problemMatcher": "$msCompile"}, {"label": "restore-backend", "group": "build", "type": "shell", "command": "dotnet", "args": ["restore", "${workspaceFolder}/backend/CQR.API/CQR.API.csproj"], "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"reveal": "silent"}, "problemMatcher": []}, {"label": "test-backend", "group": {"kind": "test", "isDefault": true}, "type": "shell", "command": "dotnet", "args": ["test", "${workspaceFolder}/backend/CQR.Tests/CQR.Test.csproj", "--logger", "trx", "--results-directory", "${workspaceFolder}/backend/TestResults"], "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"reveal": "always"}, "problemMatcher": "$msCompile"}, {"label": "build-frontend", "group": "build", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"reveal": "silent"}, "problemMatcher": []}, {"label": "dev-frontend", "group": "build", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"reveal": "always"}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^.*$", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "^.*Local:.*$", "endsPattern": "^.*ready in.*$"}}}, {"label": "build-all", "dependsOrder": "parallel", "dependsOn": ["build-backend", "build-frontend"], "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always"}}]}