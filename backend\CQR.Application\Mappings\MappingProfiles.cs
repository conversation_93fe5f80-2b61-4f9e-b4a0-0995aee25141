using AutoMapper;
using CQR.Application.Dto;
using CQR.Domain.Entities;

namespace CQR.Application.Mappings;

public class MapperConfig : Profile
{
    public MapperConfig()
    {
        // 定義從 SourceModel 到 DestinationModel 的映射
        CreateMap<ProductWithDetailsDto, Product>();

        // 可以添加更多的映射
        // CreateMap<AnotherSourceModel, AnotherDestinationModel>();

        //TODO
        /*
          // CreateProductRequest -> Product
    CreateMap<CreateProductRequest, Product>()
        .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
        .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price));

    // CreateProductRequest -> ProductCategory
    CreateMap<CreateProductRequest, ProductCategory>()
        .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.CategoryName));
        */
    }
}
