export class CQR_IHSFolder {
  public id?: number; // 對應 BaseEntity<int> 的 Id

  public queueKey?: number | null;
  public uniqueNumber?: number | null;
  public archivedByUser?: string | null;
  public coreNameplatePlantMnemonic?: string | null;
  public region?: string | null;
  public country?: string | null;
  public platform?: string | null;
  public program?: string | null;
  public productionNameplate?: string | null;
  public startOfProduction?: string | null;
  public endOfProduction?: string | null;
  public oemGroup?: string | null;
  public oem?: string | null;
  public productDescription?: string | null;
  public productGrouping?: string | null;
  public soldFrom?: string | null;
  public finalAssembly?: string | null;

  public archived?: boolean | null;
  public archivedByQueueKey?: number | null;
  public archivedDate?: Date | null;
  public dateAdded?: Date | null;
  public dateUpdated?: Date | null;
}
