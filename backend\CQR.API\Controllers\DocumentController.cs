using CQR.Infrastructure.Utilities;
using Microsoft.AspNetCore.Mvc;

namespace CQRAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DocumentController : ControllerBase
{
    // private UserDto user;
    private readonly IHttpContextAccessor _contextAccessor;

    public DocumentController(IHttpContextAccessor httpContextAccessor)
    {
        _contextAccessor = httpContextAccessor;
        // user = HelperUtil.GetHttpContextUserInfo(_contextAccessor.HttpContext);
    }

    // [HttpPost("upload")]
    // [Route("api/Document/upload")]
    [HttpPost]
    [RequestSizeLimit(50 * 1024 * 1024)] // 50 MB
    [Route("upload")]
    public async Task<IActionResult> uploadAttach([FromForm] List<IFormFile> oFiles)
    {
        // if(files.Count == 0) throw new NotFoundException("Files is empty!");
        if (oFiles == null || oFiles.Count == 0)
        {
            return BadRequest("没有上传文件");
        }
        var results = new List<int>();
        foreach (var file in oFiles)
        {
            var originalname = file.FileName;
            var mimetype = file.ContentType;
            //   int oDocumentKey = repositoryC.attachmentDao.GetDummyNewDocIdBySequence();
            using (var memoryStream = new MemoryStream())
            {
                file.CopyTo(memoryStream);
                var oBuffer = memoryStream.ToArray();
                var fileStorageName = "DocumentKey" + "_" + HelperUtil.SafeFileName(file.FileName);

                var oNewDirName = HelperUtil.GetFSPathName(fileStorageName);
                System.IO.File.WriteAllBytes(oNewDirName, oBuffer);
            }
        }
        //todo sync gplBW4Price
        return Ok(true);
    }
}
