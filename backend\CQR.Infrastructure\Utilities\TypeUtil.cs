
using CQRLIB.Exceptions;

namespace CQRLIB.Utilities
{
    public class TypeUtil
    {
        //public static  ConvertFromJsonString(string strJsonElement,Type )
        //{
        //    try
        //    {
        //        // 使用 JsonSerializer 進行轉換
        //        var oEntity = JsonSerializer.Deserialize<T>(strJsonElement);

        //        return oEntity;
        //    }
        //    catch (JsonException jsonException)
        //    {
        //        // 處理 JSON 轉換錯誤
        //        throw new JsonException(strJsonElement, jsonException);
        //    }
        //    catch (Exception ex)
        //    {
        //        // 處理其他異常情況
        //        throw new Exception("Internal Server Error");
        //    }
        //}
        public static DateTime ConvertStringToDateTime(string? dateString)
        {
            // Define the expected date format
            string[] formats = { "yyyy-MM-dd", "yyyy/MM/dd", "MM-dd-yyyy", "MM/dd/yyyy" };

            // Try to parse the input string using the specified formats
            if (DateTime.TryParseExact(dateString, formats, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out DateTime result))
            {
                // Parsing successful
                return result;
            }
            else
            {
                throw new TypeConversionException("Invalid date format. Please provide the date in one of the supported formats: yyyy-MM-dd, yyyy/MM/dd, MM-dd-yyyy, MM/dd/yyyy");
            }
        }
        public static bool ValidStringToDateTime(string? dateString)
        {
            // Define the expected date format
            string[] formats = { "yyyy-MM-dd", "yyyy/MM/dd", "MM-dd-yyyy", "MM/dd/yyyy" };
            // Try to parse the input string using the specified formats
            if (DateTime.TryParseExact(dateString, formats, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out DateTime result))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public static int ConvertToIntFromString(string inputString)
        {
            int result;
            if (int.TryParse(inputString, out result))
            {
                // Parsing successful, and 'result' now contains the integer value
                Console.WriteLine("Parsed value: " + result);
            }
            else
            {
                // Parsing failed, handle the error
                Console.WriteLine("Invalid input string");
            }
            return result;
        }
        public static bool ConvertToBoolFromStringBool(string stringValue)
        {
            try
            {
                bool boolValue = bool.Parse(stringValue);
                Console.WriteLine($"Using bool.Parse: {boolValue}");
            }
            catch (FormatException)
            {
                Console.WriteLine("Invalid boolean format");
            }

            // Using bool.TryParse
            if (bool.TryParse(stringValue, out bool result))
            {
                Console.WriteLine($"Using bool.TryParse: {result}");
            }
            else
            {
                Console.WriteLine("Invalid boolean format");
            }
            return result;
        }
        public static bool ConvertToBoolFromStringInt(string stringValue)
        {
            // Example string

            // Convert "1" to bool
            bool boolValue = stringValue == "1";
            return boolValue;
        }
        public static bool ConvertBoolean(object oVal)
        {
            if (oVal == null)
            {
                return false;
            }

            // 將 oVal 轉換為字串，並嘗試將其轉換為布林值
            if (bool.TryParse(oVal.ToString(), out bool result))
            {
                return result;
            }

            // 如果轉換失敗，返回預設值 false
            return false;
        }
    }
}
