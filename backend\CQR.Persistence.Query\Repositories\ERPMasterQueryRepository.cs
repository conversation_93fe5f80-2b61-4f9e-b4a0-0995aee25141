﻿using CQR.Application.Dto;
using CQR.Application.Repositories;
using CQR.Persistence.Query.Base;
using Dapper;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Query.Repositories;

public class ERPMasterQueryRepository : QueryBaseRepository, IERPMasterQueryRepository
{
    public ERPMasterQueryRepository(IConfiguration configuration) : base(configuration)    {}

    public  async Task<IEnumerable<OEMHiearchyDto>> getOEMHiearchy()
    {
        //throw new NotImplementedException();
        var sql = $@"exec  [dbo].[sp_oem_hiearchy] '535A'";

        return await _connection.QueryAsync<OEMHiearchyDto>(sql, new {  });
    }
}
  