﻿using CQR.Application.Dto.Report;
using CQR.Application.Repositories;
using CQR.Domain.Entities;
using CQR.Persistence;
using Dapper;

namespace CQR.Repos.Impls;

public class ReportQueryRepository : GenericRepository<CQR_Header>, IReportQueryRepository
{
    //public ReportRepository(CQrDbContext context) : base(context)
    //{
    //}
    public async Task<IEnumerable<SalesReportDto>> GetSQLReportSalesReport()
    {
        var sb = new System.Text.StringBuilder();

        sb.AppendLine("SELECT CQ.QueueKey");
        sb.AppendLine(",CAST(CAST(ProjectNbr AS INTEGER) AS VARCHAR) + '.' + CAST(CAST(RevNbr AS INTEGER) AS VARCHAR) AS [CQRNumber]");
        sb.AppendLine(",STAT._desc AS [Folder Status]");
        sb.AppendLine(",FLDT._desc AS [QuoteType], ProductDesc AS QuoteDescription");
        sb.AppendLine(",ISNULL(CAF.ProductDescription,CQ.FRANDesc) AS 'CQR Description'");
        sb.AppendLine(",ISNULL(CAF.VB_ID,CQ.VehicleBuildId) AS 'Vehicle Build ID'");
        sb.AppendLine(",ISNULL(TRPD._desc,CQ.ProductDesc) AS 'Product Description'");
        sb.AppendLine(",ISNULL(RQTT._desc,CQ.RegionalQuotingTeam) AS 'RegionalQuotingTeam'");
        sb.AppendLine(",ISNULL(CAF.OEMGroup,ISNULL(CUST._desc,CQ.CustNbr)) AS 'OEM Group'");
        sb.AppendLine(",ISNULL(CAF.Platform,ISNULL(PLAT._desc,CQ.Vehicle)) AS 'Platform'");
        sb.AppendLine(",CQ.VolumePerAnnum AS 'Volume Per Annum'");
        sb.AppendLine(",CQ.ApproxAnnualValue AS 'Approx. Annual Value $'");
        sb.AppendLine(",(SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WITH(NOLOCK) WHERE FolderType='FR' AND FolderNbr=CQ.QueueKey AND TaskCode = '01' AND ActionTaskCode IN ('BA','BQ') ORDER BY QueueKey DESC) AS 'Release Date'");
        sb.AppendLine(",CQ.CustQuoteDueDate AS 'Customer Due Date'");
        sb.AppendLine(",ISNULL(CAF.SOP,CQ.CustomerJob1) AS 'SOP Date'");
        sb.AppendLine(",MFGS._desc AS 'Manufacturing Site'");
        sb.AppendLine(",BkRndInfComments AS 'Background Information'");
        sb.AppendLine(",ISNULL(CAST(AMGR.LastName + ', ' + AMGR.FirstName AS VARCHAR), '') AS 'Sales Account Manager'");
        sb.AppendLine(",BPLN._desc AS 'Business Plan'");
        sb.AppendLine(",GDPM.ApprovalDate11 AS 'Gateway 2 Approval Date'");
        sb.AppendLine(",QSAwardStatus AS 'Win Loss Status'");
        // Optional commented line
        // sb.AppendLine(",Convert(Date, RGAwardDecision.DoneDate) AS 'Date Win Loss'");
        sb.AppendLine(",QSCommentsProceedCancel AS 'Win-Loss Comments'");
        sb.AppendLine(",(SELECT TOP 1 CAST(CAST(DoneDate AS SMALLDATETIME) AS VARCHAR(11)) FROM ROUTING_RGHeader WITH(NOLOCK) WHERE FolderType='FR' AND FolderNbr=CQ.QueueKey AND (TaskCode = '28' AND ActionTaskCode = 'SA') ORDER BY QueueKey Desc) AS 'Date Win Loss'");

        sb.AppendLine(" FROM CQR_Header AS CQ");
        sb.AppendLine(" LEFT OUTER JOIN CQR_GDPIMPhase AS GDPM ON GDPM.QueueKey=CQ.QueueKey AND GDPIMPhase=2");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS AMGR ON AMGR.UserId = CQ.AccountMgrId");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS CUST ON CUST.table_name = 'CQR_OEMGROUP' AND CUST.table_entry = CQ.CustNbr");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS BPLN ON BPLN.table_name = 'CQR_BUSPLAN' AND BPLN.table_entry = CQ.BusinessPlan");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS STAT ON STAT.table_name = 'STATUSFR' AND STAT.table_entry = CQ.STATUS");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS TRPD ON TRPD.table_name = 'CQR_PRODDESC' AND TRPD.table_entry = CQ.ProductDesc");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS PLAT ON PLAT.table_name = 'CQR_PLATFORM' AND PLAT.table_entry = CQ.Vehicle");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS MFGS ON MFGS.table_name = 'MFGSITE' AND RIGHT(MFGS.table_entry,3) = CQ.ManufacturingSite");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS SALE ON SALE.UserId = CQ.SalesAcctDirectorId");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 * FROM CQR_AntaresFolder CAF WHERE CAF.QueueKey=CQ.QueueKey ORDER BY CQRAntaresFolderId) AS CAF");

        // Optional commented section in VB.NET
        // sb.AppendLine(" LEFT OUTER JOIN ROUTING_RGHeader as RGAwardDecision ON RGAwardDecision.FolderType = 'FR' ");
        // sb.AppendLine(" AND RGAwardDecision.TaskCode = '28' and RGAwardDecision.RoutingTaskCode='SA' and RGAwardDecision.FolderNbr = CQ.Queuekey");

        //return sb.ToString();
        return await _connection.QueryAsync<SalesReportDto>(sb.ToString(), new { });
    }

    public async Task<IEnumerable<PipelineReportDto>> GetSQLReportPipeline()
    {
        string sSQL = "";
        sSQL += "SELECT CQ.QueueKey, CAST(CAST(ProjectNbr AS INTEGER) AS VARCHAR)+'.'+CAST(CAST(RevNbr AS INTEGER) AS VARCHAR) AS CQRNumber";
        sSQL += " , AMGR.LastName+', '+AMGR.FirstName AS AccountManagerName";
        sSQL += " , COST.LastName+', '+COST.FirstName AS CostEstimatorName";
        sSQL += " , ISNULL(CAF.OEMGroup,CUST._desc) AS CustomerName";
        sSQL += " , STAT._desc AS StatusName, ISNULL(CAF.Platform,ISNULL(PLAT._desc,Vehicle)) AS PlatformName";
        sSQL += " , BkRndInfComments, ISNULL(CAF.ProductDescription,ISNULL(TRPD._desc,ProductDesc)) AS ProductDesc, OriginationDate, CustQuoteDueDate";
        sSQL += " FROM CQR_Header CQ";
        sSQL += " LEFT JOIN USERPROF_UserProfileHeader AMGR ON AMGR.UserId=CQ.AccountMgrId";
        sSQL += " LEFT JOIN USERPROF_UserProfileHeader COST ON COST.UserId=CQ.CostEstimatorId";
        sSQL += " LEFT JOIN TRS_Header CUST ON CUST.table_name='CUSTOMER' AND CUST.table_entry=CQ.CustNbr";
        sSQL += " LEFT JOIN TRS_Header STAT ON STAT.table_name='STATUSFR' AND STAT.table_entry=CQ.Status";
        sSQL += " LEFT JOIN TRS_Header PLAT ON PLAT.table_name='CQR_PLATFORM' AND PLAT.table_entry=CQ.Vehicle";
        sSQL += " LEFT JOIN TRS_Header TRPD ON TRPD.table_name='CQR_PRODDESC' and TRPD.table_entry=CQ.ProductDesc";
        sSQL += " OUTER APPLY (SELECT TOP 1 * FROM CQR_AntaresFolder CAF WHERE CAF.QueueKey=CQ.QueueKey ORDER BY CQRAntaresFolderId) AS CAF";
        //return sSQL;
        return  await  _connection.QueryAsync<PipelineReportDto>(sSQL, new { });
    }

    public async Task<IEnumerable<OpenCQRStatusReportDto>> GetSQLReportOpenCQRStatus()
    {
        string sSQL = "";
        sSQL += "SELECT CQ.QueueKey, CAST(CAST(ProjectNbr AS INTEGER) AS VARCHAR)+'.'+CAST(CAST(RevNbr AS INTEGER) AS VARCHAR) AS CQRNumber";
        sSQL += " , CBM.LastName+', '+CBM.FirstName AS CBMName";
        sSQL += " , SALE.LastName+', '+SALE.FirstName AS SalesDirectorName";
        sSQL += " , AMGR.LastName+', '+AMGR.FirstName AS AccountManagerName";
        sSQL += " , ISNULL(CAF.OEMGroup,CUST._desc) AS CustomerName";
        sSQL += " , STAT._desc AS StatusName, FRANDesc";
        sSQL += " , BkRndInfComments, ISNULL(CAF.ProductDescription,ISNULL(TRPD._desc,ProductDesc)) AS ProductDesc, OriginationDate, CustQuoteDueDate, DueDateToBnE, DueDateFromEng";
        sSQL += " , (CASE WHEN QuoteType='0' THEN 'Customer Pre-CQR' ";
        sSQL += "         WHEN QuoteType='1' THEN 'Customer RFQ' ";
        sSQL += "         WHEN QuoteType='2' THEN 'Gate Exit or Internal Review' ";
        sSQL += "         WHEN QuoteType='3' THEN 'Customer ECR' ";
        sSQL += "         ELSE QuoteType      END) AS QuoteType";
        sSQL += " FROM CQR_Header CQ";
        sSQL += " LEFT JOIN USERPROF_UserProfileHeader AMGR ON AMGR.UserId=CQ.AccountMgrId";
        sSQL += " LEFT JOIN USERPROF_UserProfileHeader CBM  ON CBM.UserId =CQ.CommercialBusinessManager";
        sSQL += " LEFT JOIN USERPROF_UserProfileHeader SALE ON SALE.UserId=CQ.SalesAcctDirectorId";
        sSQL += " LEFT JOIN TRS_Header STAT ON STAT.table_name='STATUSFR' AND STAT.table_entry=CQ.Status";
        sSQL += " LEFT JOIN TRS_Header CUST ON CUST.table_name='CQR_OEMGROUP' AND CUST.table_entry=CQ.CustNbr";
        sSQL += " LEFT JOIN TRS_Header TRPD ON TRPD.table_name='CQR_PRODDESC' and TRPD.table_entry=CQ.ProductDesc";
        sSQL += " OUTER APPLY (SELECT TOP 1 * FROM CQR_AntaresFolder CAF WHERE CAF.QueueKey=CQ.QueueKey ORDER BY CQRAntaresFolderId) AS CAF";
        return await _connection.QueryAsync<OpenCQRStatusReportDto>(sSQL, new { });
    }


    public async Task<IEnumerable<DueDateStatusReportDto>> GetSQLDueDateStatusReport()
    {
        var sb = new System.Text.StringBuilder();
        sb.AppendLine(" SELECT DISTINCT CQ.QueueKey,");
        sb.AppendLine(" CAST(CAST(CQ.ProjectNbr AS INTEGER) AS VARCHAR) + '.' + CAST(CAST(CQ.RevNbr AS INTEGER) AS VARCHAR) AS CQRNumber,");
        sb.AppendLine(" QTET._desc AS QuoteType, FranDesc AS QuoteDescription, CustomerJob1 AS SOPDate, GATE._desc AS GateExit, ");
        sb.AppendLine(" STAT._desc AS FolderStatus, RQTT._desc AS RegionalQuoteTeam, IHSData.IHS_SoldFrom AS SoldFrom, IHSData.IHS_FinalAssembly AS FinalAssembly, ");
        sb.AppendLine(" ISNULL(IHSData.OEM,AntaresData.OEM) AS OEMCustomer, ");
        sb.AppendLine(" ISNULL(IHSData.IHS_ProductDescription,AntaresData.ANT_ProductDescription) AS ProductDescription, ");
        sb.AppendLine(" ISNULL(CAST(ORIG.LastName + ', ' + ORIG.FirstName AS VARCHAR), '') AS Originator,");
        sb.AppendLine(" ISNULL(CAST(AMGR.LastName + ', ' + AMGR.FirstName AS VARCHAR), '') AS AccountManager,");
        sb.AppendLine(" ISNULL(CAST(SDIR.LastName + ', ' + SDIR.FirstName AS VARCHAR), '') AS SalesAccountDirector,");
        sb.AppendLine(" ISNULL(CAST(ENGDIR.LastName + ', ' + ENGDIR.FirstName AS VARCHAR), '') AS EngineeringDirector,");
        sb.AppendLine(" ISNULL(CAST(ENGCOORD.LastName + ', ' + ENGCOORD.FirstName AS VARCHAR), '') AS EngineeringCoordinator,");
        sb.AppendLine(" ISNULL(CAST(PURCHCOORD.LastName + ', ' + PURCHCOORD.FirstName AS VARCHAR), '') AS PurchasingCoordinator,");
        sb.AppendLine(" ISNULL(CAST(PMCOORD.LastName + ', ' + PMCOORD.FirstName AS VARCHAR), '') AS PMCoordinator,");
        sb.AppendLine(" ISNULL(CAST(MECOORD.LastName + ', ' + MECOORD.FirstName AS VARCHAR), '') AS MECoordinator,");
        sb.AppendLine(" ISNULL(CAST(COST.LastName + ', ' + COST.FirstName AS VARCHAR), '') AS FinanceCoordinator,");
        sb.AppendLine(" ISNULL(CAST(PRD.LastName + ', ' + PRD.FirstName AS VARCHAR), '') AS DesignEngineer,");
        sb.AppendLine(" ISNULL(CAST(LEDM.LastName + ', ' + LEDM.FirstName AS VARCHAR), '') AS ManufacturingLead,");
        sb.AppendLine(" ISNULL(CAST(LEDP.LastName + ', ' + LEDP.FirstName AS VARCHAR), '') AS PurchasingLead,");
        sb.AppendLine(" ISNULL(CAST(LEDV.LastName + ', ' + LEDV.FirstName AS VARCHAR), '') AS ValidationLead,");
        sb.AppendLine(" TRY_CAST(OriginationDate AS DATETIME) AS CQROriginationDate,");
        sb.AppendLine(" TRY_CAST(RGInit.DoneDate AS DATETIME) AS CQRIssueDate,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(OriginationDate AS DATETIME), TRY_CAST(RGInit.DoneDate AS DATETIME)) AS CQRIssuedDays,");
        sb.AppendLine(" TRY_CAST(TSKCMP.TaskListCompletion AS DATETIME) AS TaskListCompletion,");
        sb.AppendLine(" TRY_CAST(CQ.CustQuoteDueDate AS DATETIME) As QuoteResponseDate, ");
        sb.AppendLine(" FORMAT(TRY_CAST(RGRespRls.DoneDate As DATETIME),'dd-MMM-yyyy') As QuoteResponseCompleteDate, ");
        sb.AppendLine(" DateDiff(dd, TRY_CAST(CQ.CustQuoteDueDate AS DATETIME), TRY_CAST(RGRespRls.DoneDate AS DATETIME)) As QuoteResponseDays,");
        sb.AppendLine(" (CASE WHEN CQ.RevNbr='1' THEN TRY_CAST(RGGate1.DoneDate AS DATETIME) ELSE TRY_CAST(RGInit.DoneDate AS DATETIME) END) AS Gateway1ApprovalDate,");
        sb.AppendLine(" TRY_CAST(RGGate2.DoneDate AS DATETIME) AS Gateway2ApprovalDate, ");

        sb.AppendLine(" CQ.CustQuoteDueDate AS QuoteDueDate, CQ.DueDateFromEng, CQ.QSQuoteDate AS SentToCustomer, CQ.DateWon, ");
        sb.AppendLine(" (CASE WHEN QSQuoteStatus='0' THEN 'Not Quoted' WHEN QSQuoteStatus='1' THEN 'Verbal' WHEN QSQuoteStatus='2' THEN 'Quote Letter' ELSE '' END) AS QuoteStatus, ");
        sb.AppendLine(" (CASE WHEN QSAwardStatus='0' THEN 'Won' WHEN QSAwardStatus='1' THEN 'Lost' WHEN QSAwardStatus='2' THEN 'Other' ELSE '' END) AS AwardStatus, ");

        sb.AppendLine(" CDATQ.CustomerDaysAllowedToQuote, DAYSEL.DaysEarlyLate, ");
        sb.AppendLine(" CASE WHEN CDATQ.CustomerDaysAllowedToQuote='' OR DAYSEL.DaysEarlyLate='' THEN '' ELSE CONVERT(VARCHAR,CONVERT(INT,CDATQ.CustomerDaysAllowedToQuote)+CONVERT(INT,DAYSEL.DaysEarlyLate)) END AS DaysNeededToQuote,");
        sb.AppendLine(" DATEDIFF(d, TRY_CAST(RGInit.DoneDate AS DATETIME), TRY_CAST(RGGate1.DoneDate AS DATETIME)) AS CQRIssuedToGate1Days,");
        sb.AppendLine(" DATEDIFF(d, TRY_CAST((CASE WHEN RevNbr='1' THEN RGGate1.DoneDate ELSE RGInit.DoneDate END) AS DATETIME), TRY_CAST(EPRD.EngineeringPackageReleaseDate AS DATETIME)) As Gate1ToEngineeringDays, ");
        sb.AppendLine(" DATEDIFF(d, TRY_CAST(RGEngCorRls.DoneDate AS DATETIME), TRY_CAST(RGMELeadRls.DoneDate AS DATETIME)) As EngineeringToMEDays, ");
        sb.AppendLine(" DATEDIFF(d, TRY_CAST(RGEngCorRls.DoneDate AS DATETIME), TRY_CAST(RGPRLeadRls.DoneDate AS DATETIME)) As EngineeringToPRDays, ");
        sb.AppendLine(" DATEDIFF(d, TRY_CAST(TSKCMP.TaskListCompletion AS DATETIME), TRY_CAST(RGRespRls.DoneDate AS DATETIME)) As TaskListCompletionToBusinessCase, ");
        // sb.AppendLine(" TSKCMP.TaskListCompletion+'-- >'+CQ.CustQuoteDueDate As TaskListCompletionToBusinessCase, ");
        sb.AppendLine(" DATEDIFF(d, TRY_CAST(CQ.CustQuoteDueDate AS DATETIME), TRY_CAST(RGGate2.DoneDate AS DATETIME)) As BusinessCaseToGateway2, ");
        // sb.AppendLine(" CQ.CustQuoteDueDate+'-- >'+RGGate2.DoneDate As BusinessCaseToGateway2, ");
        sb.AppendLine(" CAST(DATEDIFF(d, TRY_CAST(RGGate2.DoneDate AS DATETIME), TRY_CAST((CASE WHEN CQ.QSQuoteDate='' THEN NULL ELSE CQ.QSQuoteDate END) AS DATETIME)) AS VARCHAR) As Gateway2ToQuoteSent, ");   //--------------------------------------------------

        // DateDiff(d, [CQR Issue Date], [Quote Due Date]) AS [CustDays2Quote]
        sb.AppendLine(" TRY_CAST(CQ.DueDateFromEng AS DATETIME) AS 'Due Date From Engineering',");
        sb.AppendLine(" TRY_CAST(RGEngDirRls.DoneDate AS DATETIME) AS EngineeringDirectorReleaseDate,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGInit.DoneDate AS DATETIME),TRY_CAST(RGEngDirRls.DoneDate AS DATETIME)) AS EngineeringDirectorReleaseDays,");
        sb.AppendLine(" TRY_CAST(RGEngCorRls.DoneDate AS DATETIME) AS EngineeringCoordinatorReleaseDate,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGEngDirRls.DoneDate AS DATETIME),TRY_CAST(RGEngCorRls.DoneDate AS DATETIME)) AS EngineeringCoordinatorReleaseDays,");
        sb.AppendLine(" TRY_CAST(OtherPRD.DoneDate AS DATETIME) AS OtherPRDDate,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGEngCorRls.DoneDate AS DATETIME),TRY_CAST(OtherPRD.DoneDate AS DATETIME)) AS OtherPRDDays,");

        sb.AppendLine(" TRY_CAST(CQ.DueDateToBnE AS DATETIME) AS DueDateToBnE,");
        sb.AppendLine(" TRY_CAST(RGMELeadRls.DoneDate AS DATETIME) AS ManufacturingLeadReleaseDate,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST((SELECT MAX(TaskDate) AS TaskListCompletion FROM (VALUES (OtherPRD.DoneDate),(RGValRls.DoneDate)) AS AllTasks(TaskDate)) AS DATETIME), TRY_CAST(RGMELeadRls.DoneDate AS DATETIME)) AS ManufacturingLeadReleaseDays,");

        sb.AppendLine(" TRY_CAST(RGPRLeadRls.DoneDate AS DATETIME) AS PurchasingLeadReleaseDate,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST((SELECT MAX(TaskDate) AS TaskListCompletion FROM (VALUES (OtherPRD.DoneDate),(RGValRls.DoneDate)) AS AllTasks(TaskDate)) AS DATETIME), TRY_CAST(RGPRLeadRls.DoneDate AS DATETIME)) AS PurchasingLeadReleaseDays,");
        sb.AppendLine(" TRY_CAST(RGMECoordRls.DoneDate AS DATETIME) AS ManufacturingCoordReleaseDate,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGInit.DoneDate AS DATETIME), TRY_CAST(RGMECoordRls.DoneDate AS DATETIME)) AS ManufacturingCoordReleaseDays,");
        sb.AppendLine(" TRY_CAST(RGPRCoordRls.DoneDate AS DATETIME) AS PurchasingCoordReleaseDate,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGInit.DoneDate AS DATETIME), TRY_CAST(RGPRCoordRls.DoneDate AS DATETIME)) AS PurchasingCoordReleaseDays,");
        sb.AppendLine(" TRY_CAST(RGValRls.DoneDate AS DATETIME) AS 'Test Release Date',");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGValRls.DoneDate AS DATETIME), TRY_CAST(CQ.DueDateToBnE AS DATETIME)) AS 'Test Release Days',");
        sb.AppendLine(" DATEADD(dd, - 1, TRY_CAST(CQ.CustQuoteDueDate AS DATETIME)) AS 'Quote Due Date Minus 1',");
        sb.AppendLine(" TRY_CAST(RGRespRls.DoneDate AS DATETIME) AS 'Quote Response Date',");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGRespRls.DoneDate AS DATETIME), TRY_CAST(CQ.CustQuoteDueDate AS DATETIME)) AS 'Quote Response Days',");
        sb.AppendLine(" TRY_CAST(RGSndCust.DoneDate AS DATETIME) AS SentToCustomerDate,");
        sb.AppendLine(" TRY_CAST(RGValRls.DoneDate AS DATETIME) AS ValidationLeadDate, ");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(CQ.CustQuoteDueDate AS DATETIME), TRY_CAST(RGSndCust.DoneDate AS DATETIME)) AS 'Timing Days',");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGInit.DoneDate AS DATETIME), TRY_CAST(CQ.CustQuoteDueDate AS DATETIME)) * - 1 AS 'Issue vs Due Days',");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGInit.DoneDate AS DATETIME), TRY_CAST(RGGate1.DoneDate AS DATETIME)) * - 1 AS 'Gateway 1 Days',");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGGate1.DoneDate AS DATETIME), TRY_CAST(RGEngDirRls.DoneDate AS DATETIME)) * - 1 AS 'EngineeringDirDays',");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGEngDirRls.DoneDate AS DATETIME), TRY_CAST(RGMELeadRls.DoneDate AS DATETIME)) * - 1 AS ManufacturingLeadDays,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGEngDirRls.DoneDate AS DATETIME), TRY_CAST(RGPRLeadRls.DoneDate AS DATETIME)) * - 1 AS PurchasingLeadDays,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGEngDirRls.DoneDate AS DATETIME), TRY_CAST(RGValRls.DoneDate AS DATETIME)) * - 1 AS ValidationLeadDays,");
        sb.AppendLine(" DATEDIFF(dd, DATEADD(dd, - 1, TRY_CAST(RGPRLeadRls.DoneDate AS DATETIME)), TRY_CAST(RGGate2.DoneDate AS DATETIME)) * - 1 AS Gateway2Days,");
        sb.AppendLine(" DATEDIFF(dd, TRY_CAST(RGRespRls.DoneDate AS DATETIME), DATEADD(dd, - 1, TRY_CAST(RGPRLeadRls.DoneDate AS DATETIME))) AS 'Quote Days'");
        //------------------------------------------------ FROM STARTS HERE ------------------------------------------------
        sb.AppendLine(" FROM CQR_Header AS CQ ");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS STAT ON STAT.table_name = 'STATUSFR'  AND STAT.table_entry = CQ.Status ");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS GATE ON GATE.table_name = 'CQR_GateExit'  AND GATE.table_entry = CQ.GateExit ");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS TRPD ON TRPD.table_name = 'CQR_PRODDESC' AND TRPD.table_entry = CQ.ProductDesc ");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS RQTT ON RQTT.table_name = 'CQRRGNLTEAM' AND RQTT.table_entry = CQ.RegionalQuotingTeam ");
        sb.AppendLine(" LEFT OUTER JOIN TRS_Header AS QTET ON QTET.table_name = 'CQR_FLDRTYP' AND QTET.table_entry = CQ.QuoteType ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 UniqueNumber, OEM, OEMGroup, ProductDescription AS ANT_ProductDescription FROM CQR_AntaresFolder WHERE CQR_AntaresFolder.QueueKey=CQ.QueueKey ORDER BY CQRAntaresFolderId) AS AntaresData");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 CoreNameplatePlantMnemonic, OEM, OEMGroup, PROD._desc AS IHS_ProductDescription, SoldFrom AS IHS_SoldFrom, FinalAssembly AS IHS_FinalAssembly FROM CQR_IHSFolder LEFT JOIN TRS_Header PROD ON PROD.table_name='GPN_PRODSEG' AND PROD.table_entry=CQR_IHSFolder.ProductDescription WHERE CQR_IHSFolder.QueueKey=CQ.QueueKey ORDER BY CQRIHSFolderId) AS IHSData");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS ORIG ON ORIG.UserId = CQ.OriginatorId ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS AMGR ON AMGR.UserId = CQ.AccountMgrId ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS SDIR ON SDIR.UserId = CQ.SalesAcctDirectorId ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS CBM ON CBM.UserId = CQ.CommercialBusinessManager ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS ENGDIR ON ENGDIR.userid = CQ.PGMId ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS ENGCOORD ON ENGCOORD.userid = CQ.PETMId ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS PURCHCOORD ON PURCHCOORD.userid = CQ.PurchasingCoordinator ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS MECOORD ON MECOORD.userid = CQ.AMECoordinator ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS PMCOORD ON PMCOORD.userid = CQ.PETMId ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS COST ON COST.UserId = CQ.CostEstimatorId ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS PRD ON PRD.UserId = CQ.PRDId ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS LEDM ON LEDM.UserId = CQ.LeadManufacturing ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS LEDP ON LEDP.UserId = CQ.LeadPurchasing ");
        sb.AppendLine(" LEFT OUTER JOIN USERPROF_UserProfileHeader AS LEDV ON LEDV.UserId = CQ.LeadValidation ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '01' AND RoutingTaskCode = 'BA' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGInit ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '01' AND RoutingTaskCode = 'BQ' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGGate1 ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '01' AND RoutingTaskCode = 'GA' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGEngDirRls ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '10' AND RoutingTaskCode = 'BA' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGEngCorRls ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '42' AND RoutingTaskCode = 'AA' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGGate2 ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '10' AND RoutingTaskCode = 'MM' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGMECoordRls ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '10' AND RoutingTaskCode = 'PP' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGPRCoordRls ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '14' AND RoutingTaskCode = 'MM' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGMELeadRls ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '14' AND RoutingTaskCode = 'PP' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGPRLeadRls ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '14' AND RoutingTaskCode = 'FF' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGValRls ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '22' AND RoutingTaskCode = 'BA' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGRespRls ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader WHERE FolderType = 'FR' AND TaskCode = '28' AND RoutingTaskCode = 'MA' AND FolderNbr = CQ.QueueKey ORDER BY ROUTING_RGHeader.QueueKey DESC) AS RGSndCust ");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate AS EngineeringPackageReleaseDate FROM ROUTING_RGHeader RGEngRls WITH (nolock) WHERE foldertype = 'FR' AND taskcode = '13' AND routingtaskcode = 'PR' AND foldernbr_int = CQ.queuekey AND CQ.status >= '030600FR' ORDER  BY(CASE WHEN Isnull(donedate, '') = '' THEN '********' Else donedate End) DESC) EPRD");
        sb.AppendLine(" OUTER APPLY (SELECT TOP 1 DoneDate FROM ROUTING_RGHeader PRDOther WHERE PRDOther.FolderType = 'FR' AND PRDOther.TaskCode+PRDOther.RoutingTaskCode IN ('13PR','13CA') AND PRDOther.FolderNbr = CQ.QueueKey ORDER BY DoneDate DESC) AS OtherPRD ");
        sb.AppendLine(" OUTER APPLY (SELECT CASE WHEN CQ.CustQuoteDueDate<>'' AND CQ.QSQuoteDate<>'' THEN CONVERT(VARCHAR, DATEDIFF(dd,CONVERT(DATE,CQ.CustQuoteDueDate),CONVERT(DATE,CQ.QSQuoteDate))) ELSE '' END AS DaysEarlyLate) DAYSEL");
        sb.AppendLine(" OUTER APPLY (SELECT CASE WHEN CQ.CustQuoteDueDate<> '' THEN CONVERT(VARCHAR,DATEDIFF(d, TRY_CAST(RGInit.DoneDate AS DATETIME), CQ.CustQuoteDueDate)) ELSE '' END As CustomerDaysAllowedToQuote) CDATQ ");
        sb.AppendLine(" OUTER APPLY (SELECT MAX(TaskDate) AS TaskListCompletion FROM (VALUES (RGMELeadRls.DoneDate),(RGPRLeadRls.DoneDate),(RGValRls.DoneDate)) AS AllTasks(TaskDate)) TSKCMP");

        // sSQL += " WHERE CQ.ProjectNbr IS NOT NULL"
        // sSQL += " ORDER BY 'CQRNumber'"
        // Response.Write("<!--" + sb.ToString() + "-->") // : Response.Flush()
        //sb.ToString();
        var result = await _connection.QueryAsync<DueDateStatusReportDto>(sb.ToString(), new { });
        return result;
    }

    public async Task<IEnumerable<WinLossReportDto>> GetSQLReportWinLoss()
    {
        string sSQL = "";
        sSQL += "SELECT top 100 CQ.QueueKey, CAST(CAST(ProjectNbr AS INTEGER) AS VARCHAR)+'.'+CAST(CAST(RevNbr AS INTEGER) AS VARCHAR) AS CQRNumber";
        sSQL += " , CBM.LastName+', '+CBM.FirstName AS CBMName";
        sSQL += " , SALE.LastName+', '+SALE.FirstName AS SalesDirectorName";
        sSQL += " , AMGR.LastName+', '+AMGR.FirstName AS AccountManagerName";
        sSQL += " , ISNULL(CAF.OEMGroup,CUST._desc) AS CustomerName";
        sSQL += " , STAT._desc AS StatusName, FRANDesc, QSCommentsProceedCancel, BPLN._desc AS BusinessType";
        sSQL += " , MFGS._desc AS ManufacturingSiteName";
        sSQL += " , (CASE WHEN QSAwardStatus='0' THEN (CASE WHEN QuoteType='3' THEN 'Proceed' ELSE 'Win' END) WHEN QSAwardStatus='1' THEN (CASE WHEN QuoteType='3' THEN 'Cancel' ELSE 'Loss' END) WHEN QSAwardStatus='2' THEN 'Other' ELSE '' END) AS AwardStatus";
        sSQL += " , BkRndInfComments, ISNULL(CAF.ProductDescription,ISNULL(TRPD._desc,ProductDesc)) AS ProductDesc, OriginationDate, CustQuoteDueDate, DueDateToBnE";
        sSQL += " , (SELECT TOP 1 CustomData FROM ATTDIR_AttachFileDirHeader AFD WHERE AFD.FolderNbr=CQ.QueueKey AND AFD.FolderTypeCode='QR' ORDER BY QueueKey DESC) AS CustomData";
        sSQL += " FROM CQR_Header CQ";
        sSQL += " LEFT JOIN USERPROF_UserProfileHeader AMGR ON AMGR.UserId=CQ.AccountMgrId";
        sSQL += " LEFT JOIN USERPROF_UserProfileHeader CBM  ON CBM.UserId =CQ.CommercialBusinessManager";
        sSQL += " LEFT JOIN USERPROF_UserProfileHeader SALE ON SALE.UserId=CQ.SalesAcctDirectorId";
        sSQL += " LEFT JOIN TRS_Header CUST ON CUST.table_name='CUSTOMER' AND CUST.table_entry=CQ.CustNbr";
        sSQL += " LEFT JOIN TRS_Header STAT ON STAT.table_name='STATUSFR' AND STAT.table_entry=CQ.Status";
        sSQL += " LEFT JOIN TRS_Header TRPD ON TRPD.table_name='CQR_PRODDESC' and TRPD.table_entry=CQ.ProductDesc";
        sSQL += " LEFT JOIN TRS_Header BPLN ON BPLN.table_name='CQR_BUSPLAN' and BPLN.table_entry=CQ.BusinessPlan";
        sSQL += " LEFT JOIN TRS_Header MFGS ON MFGS.table_name='MFGSITE' and MFGS.table_entry=CQ.ManufacturingSite";
        sSQL += " OUTER APPLY (SELECT TOP 1 * FROM CQR_AntaresFolder CAF WHERE CAF.QueueKey=CQ.QueueKey ORDER BY CQRAntaresFolderId) AS CAF";

        var result = await _connection.QueryAsync<WinLossReportDto>(sSQL, new {  });
        return result;
    }
}
