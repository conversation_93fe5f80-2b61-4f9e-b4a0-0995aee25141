using CQR.Infrastructure.Configurations;
using System.Net.Mail;

namespace CQR.Infrastructure.Utilities;

public class SmtpHelper
{
    private static SmtpSettings _smtpSettings;
    public static void Initialize(SmtpSettings smtpSettings)
    {
        _smtpSettings = smtpSettings;
    }

    public static bool SendEmail(MailMessage mailMessag)
    {
        // 使用 _smtpSettings 來發送郵件
        var smtpClient = new System.Net.Mail.SmtpClient
        {
            Host = _smtpSettings.SMTP_HOST, // Specify your SMTP server here
            Port = _smtpSettings.SMTP_PORT, // Specify the appropriate port for your SMTP server
        };
        try
        {
            smtpClient.Send(mailMessag);
            return true; // Assuming 1 for success, you can modify the return value based on your requirements
        }
        catch (Exception ex)
        {
            return false; // Assuming 0 for failure, you can modify the return value based on your requirements
        }

    }
}
