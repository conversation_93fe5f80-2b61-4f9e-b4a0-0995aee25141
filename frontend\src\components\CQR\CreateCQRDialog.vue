<!-- src/components/CreateCQRDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    title="Create CQR"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form :model="form" label-position="top" label-width="120px">
      <!-- CQR Type -->
      <el-form-item label="CQR Type">
        <el-radio-group v-model="form.cqrType" @change="onCQRTypeChange">
          <el-radio label="New">New</el-radio>
          <el-radio label="Modify">Modify Existing CQR</el-radio>
        </el-radio-group>
        <el-input
          v-model="form.projectNumber"
          placeholder="Enter Project Number"
          style="margin-top: 10px"
          :disabled="form.cqrType !== 'Modify'"
        />
      </el-form-item>

      <!-- Quote Type -->
      <el-form-item label="Quote Type">
        <el-radio-group v-model="form.quoteType" @change="onQuoteTypeChange">
          <el-radio label="0">Customer Pre-CQR</el-radio>
          <el-radio label="1">Customer RFQ</el-radio>
          <el-radio label="3" :disabled="form.cqrType === 'New'"
            >Customer ECR</el-radio
          >
          <el-radio label="2" :disabled="form.cqrType === 'New'"
            >Gate Exit or Internal Review</el-radio
          >
        </el-radio-group>
      </el-form-item>

      <!-- Action Buttons -->
      <el-form-item style="text-align: center">
        <el-button type="primary" @click="handleCreate">Create</el-button>
        <el-button @click="handleCancel">Cancel</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { CreateCQRForm } from "@/types";

const props = defineProps<{
  modelValue: boolean;
  form: CreateCQRForm;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
  (e: "create"): void;
  (e: "changeCQRType"): void;
  (e: "changeQuoteType"): void;
}>();

const visible = ref(props.modelValue);

// 雙向綁定 dialog 顯示
watch(
  () => props.modelValue,
  val => {
    visible.value = val;
  }
);
watch(visible, val => {
  emit("update:modelValue", val);
});

const onCQRTypeChange = () => emit("changeCQRType");
const onQuoteTypeChange = () => emit("changeQuoteType");

const handleCreate = () => {
  emit("create");
};

const handleCancel = () => {
  visible.value = false;
};
</script>
