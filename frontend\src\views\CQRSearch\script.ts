import { defineComponent, onMounted, ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSearchForm } from './composables/useSearchForm'
import { useSearchResults } from './composables/useSearchResults'
import {
  getManufacturingSiteOptions,
  getModelYearOptions,
  getOriginatorOptions,
  getAccountManagerOptions,
  getEstimatorOptions,
  getPDMOptions,
  getPGMOptions,
  getOEMGroupOptions,
  getOEMCustomerOptions,
  exportSearchResults
} from '@/api/search'
import type { SelectOption, UserOption } from '@/api/search'

export default defineComponent({
  name: 'CQRSearch',
  setup() {
    // 使用 composables
    const {
      searchForm,
      advancedForm,
      showAdvanced,
      searchLoading,
      currentPage,
      pageSize,
      hasSearchCriteria,
      validateSearchForm,
      buildSearchQuery,
      clearAllFilters,
      toggleAdvancedSearch,
      formatDate
    } = useSearchForm()

    const {
      searchResults,
      selectedRows,
      selectedRowKeys,
      executeSearch,
      executeQuickSearch,
      handleRowClick,
      handleSelectionChange,
      viewCQRDetails,
      exportResults,
      getSelectionStats,
      clearResults
    } = useSearchResults()

    // 下拉選項資料
    const manufacturingSiteOptions = ref<SelectOption[]>([])
    const modelYearOptions = ref<SelectOption[]>([])
    const originatorOptions = ref<UserOption[]>([])
    const accountManagerOptions = ref<UserOption[]>([])
    const estimatorOptions = ref<UserOption[]>([])
    const pdmOptions = ref<UserOption[]>([])
    const pgmOptions = ref<UserOption[]>([])
    const oemGroupOptions = ref<SelectOption[]>([])
    const oemCustomerOptions = ref<SelectOption[]>([])

    // 載入所有下拉選項
    const loadDropdownOptions = async () => {
      try {
        const promises = [
          getManufacturingSiteOptions(),
          getModelYearOptions(),
          getOriginatorOptions(),
          getAccountManagerOptions(),
          getEstimatorOptions(),
          getPDMOptions(),
          getPGMOptions(),
          getOEMGroupOptions(),
          getOEMCustomerOptions()
        ]

        const [
          manufacturingSites,
          modelYears,
          originators,
          accountManagers,
          estimators,
          pdms,
          pgms,
          oemGroups,
          oemCustomers
        ] = await Promise.allSettled(promises)

        // 處理成功的回應
        if (manufacturingSites.status === 'fulfilled' && manufacturingSites.value.success) {
          manufacturingSiteOptions.value = manufacturingSites.value.data
        }
        if (modelYears.status === 'fulfilled' && modelYears.value.success) {
          modelYearOptions.value = modelYears.value.data
        }
        if (originators.status === 'fulfilled' && originators.value.success) {
          originatorOptions.value = originators.value.data
        }
        if (accountManagers.status === 'fulfilled' && accountManagers.value.success) {
          accountManagerOptions.value = accountManagers.value.data
        }
        if (estimators.status === 'fulfilled' && estimators.value.success) {
          estimatorOptions.value = estimators.value.data
        }
        if (pdms.status === 'fulfilled' && pdms.value.success) {
          pdmOptions.value = pdms.value.data
        }
        if (pgms.status === 'fulfilled' && pgms.value.success) {
          pgmOptions.value = pgms.value.data
        }
        if (oemGroups.status === 'fulfilled' && oemGroups.value.success) {
          oemGroupOptions.value = oemGroups.value.data
        }
        if (oemCustomers.status === 'fulfilled' && oemCustomers.value.success) {
          oemCustomerOptions.value = oemCustomers.value.data
        }

      } catch (error) {
        console.error('載入下拉選項失敗:', error)
        ElMessage.warning('部分下拉選項載入失敗')
      }
    }

    // 執行搜尋
    const handleSearch = async () => {
      if (!validateSearchForm()) {
        return
      }

      // 檢查用戶偏好設定是否需要顯示空搜尋確認彈窗
      if (!hasSearchCriteria.value) {
        const { shouldShowEmptySearchConfirm } = await import('@/composables/useUserPreferences');
        if (shouldShowEmptySearchConfirm()) {
          const confirm = await ElMessageBox.confirm(
            '沒有指定搜尋條件，將顯示前 50 筆記錄。是否繼續？',
            '確認搜尋',
            {
              confirmButtonText: '繼續',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).catch(() => false);

          if (!confirm) return;
        }
      }

      try {
        searchLoading.value = true
        const query = buildSearchQuery()
        await executeSearch(query)
      } catch (error) {
        console.error('搜尋失敗:', error)
      } finally {
        searchLoading.value = false
      }
    }

    // 處理分頁大小變更
    const handleSizeChange = async (newSize: number) => {
      pageSize.value = newSize
      currentPage.value = 1
      
      if (searchResults.totalCount > 0) {
        await handleSearch()
      }
    }

    // 處理當前頁變更
    const handleCurrentChange = async (newPage: number) => {
      currentPage.value = newPage
      
      if (searchResults.totalCount > 0) {
        await handleSearch()
      }
    }

    // 匯出搜尋結果
    const exportResults = async () => {
      if (searchResults.results?.length === 0) {
        ElMessage.warning('沒有搜尋結果可以匯出')
        return
      }

      try {
        const format = await ElMessageBox.prompt(
          '請選擇匯出格式',
          '匯出搜尋結果',
          {
            confirmButtonText: '匯出',
            cancelButtonText: '取消',
            inputPattern: /^(excel|csv)$/i,
            inputErrorMessage: '請輸入 excel 或 csv',
            inputValue: 'excel'
          }
        )

        const query = buildSearchQuery()
        query.pageNumber = 1
        query.pageSize = Number.MAX_SAFE_INTEGER // 匯出所有結果

        await exportResults(query, format.value.toLowerCase() as 'excel' | 'csv')
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('匯出失敗:', error)
        }
      }
    }

    // 快速搜尋（用於初始載入或預設搜尋）
    const performQuickSearch = async (searchText?: string) => {
      try {
        searchLoading.value = true
        await executeQuickSearch(searchText || '', currentPage.value, pageSize.value)
      } catch (error) {
        console.error('快速搜尋失敗:', error)
      } finally {
        searchLoading.value = false
      }
    }

    // 重置搜尋表單和結果
    const resetSearch = () => {
      clearAllFilters()
      clearResults()
      currentPage.value = 1
    }

    // 計算屬性：搜尋摘要文字
    const searchSummaryText = computed(() => {
      if (searchResults.totalCount === 0 && !hasSearchCriteria.value) {
        return '請輸入搜尋條件開始搜尋'
      }
      return searchResults.searchSummary || `找到 ${searchResults.totalCount} 筆記錄`
    })

    // 監聽進階搜尋的 quoteReport 變化
    watch(() => advancedForm.quoteReport, (newValue) => {
      if (!newValue) {
        advancedForm.findLastTwoFolders = false
      }
    })

    // 組件掛載時初始化
    onMounted(async () => {
      await loadDropdownOptions()
      
      // 如果 URL 中有搜尋參數，自動執行搜尋
      const urlParams = new URLSearchParams(window.location.search)
      const autoSearch = urlParams.get('autoSearch')
      const folder = urlParams.get('folder')
      
      if (autoSearch === '1' && folder) {
        const [projectNbr, revNbr] = folder.split('.')
        if (projectNbr) {
          searchForm.cqrFromLeft = projectNbr
          searchForm.cqrToLeft = projectNbr
          if (revNbr) {
            searchForm.cqrFromRight = revNbr
            searchForm.cqrToRight = revNbr
          }
          await handleSearch()
        }
      }
    })

    return {
      // 表單狀態
      searchForm,
      advancedForm,
      showAdvanced,
      searchLoading,
      
      // 分頁狀態
      currentPage,
      pageSize,
      
      // 搜尋結果
      searchResults,
      selectedRows,
      selectedRowKeys,
      
      // 下拉選項
      manufacturingSiteOptions,
      modelYearOptions,
      originatorOptions,
      accountManagerOptions,
      estimatorOptions,
      pdmOptions,
      pgmOptions,
      oemGroupOptions,
      oemCustomerOptions,
      
      // 計算屬性
      hasSearchCriteria,
      searchSummaryText,
      
      // 方法
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      handleRowClick,
      handleSelectionChange,
      viewCQRDetails,
      exportResults,
      clearAllFilters,
      toggleAdvancedSearch,
      formatDate,
      resetSearch,
      performQuickSearch
    }
  }
})