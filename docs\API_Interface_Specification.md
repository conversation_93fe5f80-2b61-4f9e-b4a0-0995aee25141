# CQR 系統 API 接口規格書

## 文檔資訊

| 項目 | 內容 |
|------|------|
| 文檔標題 | CQR 系統 API 接口規格書 |
| 版本 | 2.0 |
| 建立日期 | 2025-07-22 |
| 最後更新 | 2025-07-22 |
| 文檔狀態 | 草案 |
| 作者 | API 開發團隊 |

## 目錄

1. [API 概述](#api-概述)
2. [認證與授權](#認證與授權)
3. [請求響應格式](#請求響應格式)
4. [錯誤處理](#錯誤處理)
5. [CQR 管理 API](#cqr-管理-api)
6. [附件管理 API](#附件管理-api)
7. [用戶管理 API](#用戶管理-api)
8. [報表系統 API](#報表系統-api)
9. [工作流程 API](#工作流程-api)
10. [系統管理 API](#系統管理-api)

---

## API 概述

### 1.1 基本資訊

- **API 版本**: v1.0
- **基礎 URL**: `https://api.cqr.company.com/api/v1`
- **協議**: HTTPS
- **數據格式**: JSON
- **編碼**: UTF-8
- **時區**: UTC

### 1.2 請求標頭

所有 API 請求都需要包含以下標頭：

```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer {access_token}
X-API-Version: 1.0
X-Request-ID: {unique_request_id}
```

### 1.3 API 限流

| 限制類型 | 限制值 | 時間窗口 | 響應標頭 |
|---------|--------|----------|---------|
| IP 限制 | 100 requests | 1 分鐘 | X-RateLimit-Limit-IP |
| 用戶限制 | 500 requests | 1 分鐘 | X-RateLimit-Limit-User |
| 上傳限制 | 50 files | 1 小時 | X-RateLimit-Limit-Upload |

---

## 認證與授權

### 2.1 JWT Token 認證

#### 登入獲取 Token

```http
POST /auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123"
}
```

**響應:**

```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600,
    "tokenType": "Bearer",
    "user": {
      "userId": "john.doe",
      "userName": "John Doe",
      "email": "<EMAIL>",
      "roles": ["AMGR"],
      "permissions": ["CQR_CREATE", "CQR_READ", "CQR_UPDATE"]
    }
  },
  "timestamp": "2025-07-22T10:30:00Z"
}
```

#### 刷新 Token

```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 登出

```http
POST /auth/logout
Authorization: Bearer {access_token}
```

### 2.2 權限系統

#### 角色定義

| 角色代碼 | 角色名稱 | 權限範圍 |
|---------|----------|----------|
| ADMIN | 系統管理員 | 系統全部功能 |
| SDIR | 銷售總監 | 所有 CQR 管理 + 報表 |
| AMGR | 客戶經理 | 創建/編輯自己的 CQR |
| BMGR | 業務經理 | 審核 CQR + 報表查看 |
| CEST | 成本估算師 | CQR 成本分析 |
| EMGR | 工程經理 | CQR 技術評估 |

#### 權限檢查

```http
GET /auth/permissions
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": {
    "userId": "john.doe",
    "roles": ["AMGR"],
    "permissions": [
      "CQR_CREATE",
      "CQR_READ", 
      "CQR_UPDATE",
      "ATTACHMENT_UPLOAD"
    ]
  }
}
```

---

## 請求響應格式

### 3.1 統一響應格式

#### 成功響應

```json
{
  "success": true,
  "data": {
    // 實際數據內容
  },
  "message": "操作成功",
  "timestamp": "2025-07-22T10:30:00Z",
  "traceId": "abc123-def456-789"
}
```

#### 分頁響應

```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalItems": 150,
      "totalPages": 8,
      "hasNextPage": true,
      "hasPreviousPage": false
    }
  },
  "timestamp": "2025-07-22T10:30:00Z"
}
```

#### 錯誤響應

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "請求參數驗證失敗",
    "details": [
      {
        "field": "projectNbr",
        "code": "REQUIRED",
        "message": "項目編號不能為空"
      }
    ]
  },
  "timestamp": "2025-07-22T10:30:00Z",
  "traceId": "abc123-def456-789"
}
```

### 3.2 查詢參數規範

#### 分頁參數

| 參數 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| page | integer | 1 | 頁碼 |
| pageSize | integer | 20 | 每頁數量 (1-100) |

#### 排序參數

| 參數 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| sortBy | string | createdDate | 排序欄位 |
| sortOrder | string | desc | 排序方向 (asc/desc) |

#### 篩選參數

```http
GET /cqrs?status=010100FR,020100FR&custNbr=CUST001&dateFrom=2025-01-01&dateTo=2025-12-31
```

---

## 錯誤處理

### 4.1 HTTP 狀態碼

| 狀態碼 | 說明 | 使用場景 |
|--------|------|----------|
| 200 | OK | 請求成功 |
| 201 | Created | 資源創建成功 |
| 204 | No Content | 刪除成功，無返回內容 |
| 400 | Bad Request | 請求參數錯誤 |
| 401 | Unauthorized | 未認證或 Token 無效 |
| 403 | Forbidden | 權限不足 |
| 404 | Not Found | 資源不存在 |
| 409 | Conflict | 資源衝突（如重複創建） |
| 422 | Unprocessable Entity | 業務邏輯驗證失敗 |
| 429 | Too Many Requests | 請求過於頻繁 |
| 500 | Internal Server Error | 服務器內部錯誤 |

### 4.2 錯誤代碼定義

| 錯誤代碼 | 說明 | 處理建議 |
|---------|------|----------|
| VALIDATION_ERROR | 參數驗證錯誤 | 檢查請求參數格式 |
| AUTHENTICATION_FAILED | 認證失敗 | 重新登入 |
| PERMISSION_DENIED | 權限不足 | 聯繫管理員 |
| RESOURCE_NOT_FOUND | 資源不存在 | 確認資源 ID |
| DUPLICATE_RESOURCE | 資源重複 | 使用其他唯一標識 |
| BUSINESS_RULE_VIOLATION | 業務規則違反 | 檢查業務邏輯 |
| RATE_LIMIT_EXCEEDED | 請求頻率超限 | 降低請求頻率 |
| SERVICE_UNAVAILABLE | 服務不可用 | 稍後重試 |

---

## CQR 管理 API

### 5.1 CQR CRUD 操作

#### 獲取 CQR 列表

```http
GET /cqrs?page=1&pageSize=20&status=010100FR&sortBy=createdDate&sortOrder=desc
Authorization: Bearer {access_token}
```

**查詢參數:**

| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| page | integer | 否 | 頁碼，預設 1 |
| pageSize | integer | 否 | 每頁數量，預設 20 |
| status | string | 否 | 狀態篩選，多個用逗號分隔 |
| custNbr | string | 否 | 客戶編號 |
| projectNbr | string | 否 | 項目編號（模糊搜尋） |
| quoteType | integer | 否 | 報價類型 |
| originatorId | string | 否 | 創建者 ID |
| createdDateFrom | string | 否 | 創建日期起始 (YYYY-MM-DD) |
| createdDateTo | string | 否 | 創建日期結束 (YYYY-MM-DD) |
| volumeMin | number | 否 | 最小年產量 |
| volumeMax | number | 否 | 最大年產量 |
| valueMin | number | 否 | 最小年價值 |
| valueMax | number | 否 | 最大年價值 |
| sortBy | string | 否 | 排序欄位 |
| sortOrder | string | 否 | 排序方向 (asc/desc) |

**響應範例:**

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "queueKey": 12345,
        "projectNbr": "PRJ-2025-001",
        "revNbr": 0,
        "status": "010100FR",
        "statusDesc": "正在初始化",
        "quoteType": 1,
        "custNbr": "CUST001",
        "custName": "ABC Company Ltd.",
        "productDesc": "Automotive Safety System",
        "volumePerAnnum": 50000,
        "approxAnnualValue": 2500000.00,
        "quoteNeededDate": "2025-08-15T00:00:00Z",
        "sopDate": "2026-01-01T00:00:00Z",
        "originatorId": "john.doe",
        "originatorName": "John Doe",
        "createdDate": "2025-07-22T10:30:00Z",
        "modifiedDate": "2025-07-22T15:45:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalItems": 150,
      "totalPages": 8
    }
  }
}
```

#### 獲取單一 CQR

```http
GET /cqrs/{queueKey}
Authorization: Bearer {access_token}
```

**路徑參數:**
- `queueKey` (integer, 必填): CQR 的唯一標識

**響應範例:**

```json
{
  "success": true,
  "data": {
    "queueKey": 12345,
    "projectNbr": "PRJ-2025-001",
    "revNbr": 0,
    "status": "010100FR",
    "statusDesc": "正在初始化",
    "quoteType": 1,
    "customer": {
      "custNbr": "CUST001",
      "custName": "ABC Company Ltd.",
      "buyerName": "Jane Smith",
      "contact": "<EMAIL>",
      "email": "<EMAIL>",
      "phone": "******-0123"
    },
    "product": {
      "description": "Automotive Safety System",
      "vehicle": "Model X SUV",
      "modelYear": "2026",
      "platform": "Electric Vehicle Platform",
      "oemPartNbr": "OEM-12345",
      "customerPartNbr": "CUST-ABC-001",
      "trwPartNbr": "TRW-SS-2025-001"
    },
    "business": {
      "volumePerAnnum": 50000,
      "lifetimeVolume": 250000,
      "approxAnnualValue": 2500000.00,
      "lifetimeValue": ********.00
    },
    "dates": {
      "quoteNeededDate": "2025-08-15T00:00:00Z",
      "sopDate": "2026-01-01T00:00:00Z",
      "eopDate": "2030-12-31T00:00:00Z"
    },
    "team": {
      "originator": {
        "userId": "john.doe",
        "userName": "John Doe"
      },
      "accountManager": {
        "userId": "jane.smith",
        "userName": "Jane Smith"
      }
    },
    "quote": {
      "internalPrice": 50.0000,
      "customerPrice": 65.0000,
      "profitMargin": 15.0000,
      "profitPercentage": 23.08
    },
    "metadata": {
      "createdDate": "2025-07-22T10:30:00Z",
      "createdBy": "john.doe",
      "modifiedDate": "2025-07-22T15:45:00Z",
      "modifiedBy": "john.doe",
      "isActive": true,
      "isLocked": false
    }
  }
}
```

#### 創建新 CQR

```http
POST /cqrs
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "projectNbr": "PRJ-2025-002",
  "quoteType": 1,
  "customer": {
    "custNbr": "CUST002",
    "custName": "XYZ Corporation",
    "buyerName": "Mike Davis",
    "email": "<EMAIL>"
  },
  "product": {
    "description": "Engine Control Module",
    "vehicle": "Compact Car",
    "modelYear": "2026"
  },
  "business": {
    "volumePerAnnum": 25000,
    "approxAnnualValue": 1250000.00
  },
  "dates": {
    "quoteNeededDate": "2025-08-30T00:00:00Z",
    "sopDate": "2026-03-01T00:00:00Z"
  },
  "team": [
    {
      "role": "AccountManager",
      "userId": "jane.smith",
      "userName": "Jane Smith"
    }
  ]
}
```

**響應:**

```json
{
  "success": true,
  "data": {
    "queueKey": 12346,
    "projectNbr": "PRJ-2025-002",
    "status": "010100FR",
    "createdDate": "2025-07-22T11:00:00Z"
  },
  "message": "CQR 創建成功"
}
```

#### 更新 CQR

```http
PUT /cqrs/{queueKey}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "customer": {
    "custNbr": "CUST002",
    "custName": "XYZ Corporation Updated",
    "buyerName": "Mike Davis Jr.",
    "email": "<EMAIL>"
  },
  "product": {
    "description": "Advanced Engine Control Module",
    "vehicle": "Compact Electric Car",
    "modelYear": "2026"
  }
}
```

#### 刪除 CQR

```http
DELETE /cqrs/{queueKey}
Authorization: Bearer {access_token}
```

### 5.2 CQR 狀態管理

#### 更新 CQR 狀態

```http
PUT /cqrs/{queueKey}/status
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "newStatus": "020100FR",
  "comments": "Description phase completed, moving to Background phase"
}
```

#### 獲取狀態歷史

```http
GET /cqrs/{queueKey}/history
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": [
    {
      "changeId": 1,
      "changeDate": "2025-07-22T10:30:00Z",
      "changedBy": "john.doe",
      "changeType": "StatusChange",
      "oldValue": "010100FR",
      "newValue": "020100FR",
      "comments": "Description phase completed"
    }
  ]
}
```

### 5.3 CQR 版本控制

#### 複製 CQR

```http
POST /cqrs/{queueKey}/copy
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "newProjectNbr": "PRJ-2025-003",
  "copyAttachments": true,
  "copyTeam": false,
  "comments": "Created for similar project"
}
```

#### 創建修訂版本

```http
POST /cqrs/{queueKey}/revise
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "revisionReason": "Customer requested specification changes"
}
```

### 5.4 進階搜尋

```http
POST /cqrs/search
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "filters": {
    "status": ["010100FR", "020100FR"],
    "quoteType": [1, 2],
    "custNbr": "CUST001",
    "productDesc": "safety",
    "volumeRange": {
      "min": 10000,
      "max": 100000
    },
    "valueRange": {
      "min": 500000,
      "max": 5000000
    },
    "dateRange": {
      "field": "createdDate",
      "start": "2025-01-01T00:00:00Z",
      "end": "2025-12-31T23:59:59Z"
    },
    "team": {
      "originatorId": "john.doe",
      "accountManagerId": "jane.smith"
    }
  },
  "sorting": {
    "field": "createdDate",
    "order": "desc"
  },
  "pagination": {
    "page": 1,
    "pageSize": 50
  }
}
```

---

## 附件管理 API

### 6.1 文件上傳

#### 上傳附件

```http
POST /attachments/upload
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

file: [binary file data]
queueKey: 12345
category: Quote
description: Initial quote response
```

**請求參數:**

| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| file | file | 是 | 上傳的文件 |
| queueKey | integer | 是 | 關聯的 CQR ID |
| category | string | 否 | 文件分類 |
| description | string | 否 | 文件描述 |

**支援的文件類型:**
- 文檔: `.pdf`, `.doc`, `.docx`, `.txt`
- 電子表格: `.xls`, `.xlsx`, `.xlsm`
- 圖片: `.jpg`, `.jpeg`, `.png`, `.gif`
- 壓縮檔: `.zip`, `.rar`, `.7z`

**文件大小限制:**
- 單文件最大: 50MB
- 批次上傳最大: 500MB

**響應:**

```json
{
  "success": true,
  "data": {
    "attachKey": 67890,
    "fileName": "quote_response_v1.xlsx",
    "originalFileName": "Quote Response Template.xlsx",
    "fileSize": 1048576,
    "fileType": "Spreadsheet",
    "mimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "category": "Quote",
    "version": 1,
    "uploadUrl": "/api/attachments/67890/download",
    "previewUrl": "/api/attachments/67890/preview",
    "thumbnailUrl": "/api/attachments/67890/thumbnail"
  }
}
```

#### 批次上傳

```http
POST /attachments/batch-upload
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

files[]: [binary file data]
files[]: [binary file data]
queueKey: 12345
category: Engineering
```

### 6.2 文件下載與預覽

#### 下載文件

```http
GET /attachments/{attachKey}/download
Authorization: Bearer {access_token}
```

**響應標頭:**

```http
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="quote_response_v1.xlsx"
Content-Length: 1048576
Cache-Control: private, max-age=3600
```

#### 預覽文件

```http
GET /attachments/{attachKey}/preview?width=800&height=600
Authorization: Bearer {access_token}
```

**查詢參數:**

| 參數 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| width | integer | 800 | 預覽寬度 |
| height | integer | 600 | 預覽高度 |
| page | integer | 1 | PDF 頁碼 |

#### 獲取縮圖

```http
GET /attachments/{attachKey}/thumbnail?size=200
Authorization: Bearer {access_token}
```

### 6.3 文件管理

#### 獲取 CQR 附件列表

```http
GET /attachments/cqr/{queueKey}?category=Quote&sortBy=createdDate&sortOrder=desc
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": [
    {
      "attachKey": 67890,
      "fileName": "quote_response_v1.xlsx",
      "originalFileName": "Quote Response Template.xlsx",
      "fileSize": 1048576,
      "fileType": "Spreadsheet",
      "mimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "category": "Quote",
      "description": "Initial quote response",
      "version": 1,
      "isLatestVersion": true,
      "createdBy": "john.doe",
      "createdDate": "2025-07-22T11:00:00Z",
      "downloadUrl": "/api/attachments/67890/download",
      "previewUrl": "/api/attachments/67890/preview",
      "thumbnailUrl": "/api/attachments/67890/thumbnail"
    }
  ]
}
```

#### 更新文件資訊

```http
PUT /attachments/{attachKey}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "category": "Updated Category",
  "description": "Updated description",
  "tags": ["important", "review"]
}
```

#### 刪除文件

```http
DELETE /attachments/{attachKey}
Authorization: Bearer {access_token}
```

### 6.4 版本控制

#### 上傳新版本

```http
POST /attachments/{attachKey}/new-version
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

file: [binary file data]
comments: Updated pricing based on customer feedback
```

#### 獲取版本歷史

```http
GET /attachments/{attachKey}/versions
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": [
    {
      "attachKey": 67890,
      "version": 1,
      "fileName": "quote_response_v1.xlsx",
      "fileSize": 1048576,
      "createdBy": "john.doe",
      "createdDate": "2025-07-22T11:00:00Z",
      "comments": "Initial version",
      "isLatestVersion": false
    },
    {
      "attachKey": 67891,
      "version": 2,
      "fileName": "quote_response_v2.xlsx",
      "fileSize": 1124864,
      "createdBy": "jane.smith",
      "createdDate": "2025-07-23T09:15:00Z",
      "comments": "Updated pricing based on customer feedback",
      "isLatestVersion": true
    }
  ]
}
```

#### 回滾到指定版本

```http
POST /attachments/{attachKey}/rollback/{version}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "comments": "Rollback due to pricing error in latest version"
}
```

---

## 用戶管理 API

### 7.1 用戶資料

#### 獲取當前用戶資料

```http
GET /users/profile
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": {
    "userId": "john.doe",
    "userName": "John Doe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "displayName": "John Doe",
    "department": "Sales",
    "title": "Account Manager",
    "location": "New York Office",
    "phone": "******-0123",
    "mobile": "******-0456",
    "avatar": "/api/users/john.doe/avatar",
    "roles": [
      {
        "roleCode": "AMGR",
        "roleName": "Account Manager",
        "scope": "Global"
      }
    ],
    "permissions": [
      "CQR_CREATE",
      "CQR_READ",
      "CQR_UPDATE",
      "ATTACHMENT_UPLOAD"
    ],
    "preferences": {
      "language": "zh-CN",
      "timezone": "Asia/Shanghai",
      "dateFormat": "yyyy-MM-dd",
      "currency": "USD"
    },
    "lastLoginDate": "2025-07-22T08:30:00Z",
    "createdDate": "2024-01-15T00:00:00Z"
  }
}
```

#### 更新用戶資料

```http
PUT /users/profile
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "phone": "******-0789",
  "mobile": "******-0999",
  "preferences": {
    "language": "en-US",
    "timezone": "America/New_York",
    "dateFormat": "MM/dd/yyyy"
  }
}
```

#### 修改密碼

```http
POST /users/change-password
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "currentPassword": "oldPassword123",
  "newPassword": "newPassword456",
  "confirmPassword": "newPassword456"
}
```

#### 上傳頭像

```http
POST /users/avatar
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

avatar: [image file]
```

### 7.2 用戶搜尋

#### 搜尋用戶

```http
GET /users/search?keyword=john&department=Sales&role=AMGR&page=1&pageSize=20
Authorization: Bearer {access_token}
```

**查詢參數:**

| 參數 | 類型 | 說明 |
|------|------|------|
| keyword | string | 搜尋關鍵字（姓名、郵箱） |
| department | string | 部門篩選 |
| role | string | 角色篩選 |
| isActive | boolean | 是否啟用 |

**響應:**

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "userId": "john.doe",
        "userName": "John Doe",
        "email": "<EMAIL>",
        "department": "Sales",
        "title": "Account Manager",
        "avatar": "/api/users/john.doe/avatar",
        "isActive": true,
        "lastLoginDate": "2025-07-22T08:30:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalItems": 1,
      "totalPages": 1
    }
  }
}
```

#### 獲取用戶詳情

```http
GET /users/{userId}
Authorization: Bearer {access_token}
```

#### 獲取用戶頭像

```http
GET /users/{userId}/avatar
Authorization: Bearer {access_token}
```

---

## 報表系統 API

### 8.1 儀表板統計

#### 獲取儀表板數據

```http
GET /reports/dashboard?dateFrom=2025-01-01&dateTo=2025-12-31
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": {
    "summary": {
      "totalCQRs": 1250,
      "activeCQRs": 89,
      "completedCQRs": 1161,
      "totalValue": ********0.00,
      "averageValue": 100000.00,
      "conversionRate": 65.5
    },
    "statusDistribution": [
      {
        "status": "010100FR",
        "statusDesc": "正在初始化",
        "count": 15,
        "percentage": 16.85,
        "value": 1500000.00
      }
    ],
    "monthlyTrend": [
      {
        "month": "2025-01",
        "newCQRs": 12,
        "completedCQRs": 18,
        "totalValue": 2500000.00,
        "conversionRate": 75.0
      }
    ],
    "topCustomers": [
      {
        "custNbr": "CUST001",
        "custName": "ABC Company",
        "cqrCount": 25,
        "totalValue": 15000000.00,
        "conversionRate": 80.0
      }
    ],
    "performanceMetrics": {
      "averageProcessingTime": 15.5,
      "onTimeDeliveryRate": 92.3,
      "customerSatisfactionScore": 8.7
    }
  }
}
```

### 8.2 CQR 報表

#### CQR 列表報表

```http
GET /reports/cqr-list?format=excel&dateFrom=2025-01-01&dateTo=2025-12-31&status=010100FR,020100FR
Authorization: Bearer {access_token}
```

**查詢參數:**

| 參數 | 類型 | 說明 |
|------|------|------|
| format | string | 匯出格式 (excel/pdf/csv) |
| dateFrom | string | 開始日期 |
| dateTo | string | 結束日期 |
| status | string | 狀態篩選 |
| custNbr | string | 客戶篩選 |
| originatorId | string | 創建者篩選 |

**響應 (Excel 格式):**

```http
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="CQR_List_Report_20250722.xlsx"
```

#### CQR 詳細報表

```http
GET /reports/cqr-detail/{queueKey}?format=pdf&template=standard
Authorization: Bearer {access_token}
```

#### 統計分析報表

```http
POST /reports/analytics
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "reportType": "performance",
  "dateRange": {
    "start": "2025-01-01",
    "end": "2025-12-31"
  },
  "groupBy": ["month", "department"],
  "metrics": ["count", "value", "conversionRate"],
  "filters": {
    "status": ["010100FR", "020100FR"],
    "quoteType": [1, 2]
  },
  "format": "excel"
}
```

### 8.3 自定義報表

#### 創建報表範本

```http
POST /reports/templates
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "templateName": "Monthly Sales Report",
  "description": "Monthly CQR sales performance report",
  "reportType": "analytics",
  "configuration": {
    "dateRange": "month",
    "groupBy": ["customer", "product"],
    "metrics": ["count", "totalValue"],
    "charts": ["bar", "pie"],
    "format": "excel"
  },
  "isPublic": false
}
```

#### 執行自定義報表

```http
POST /reports/templates/{templateId}/execute
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "parameters": {
    "dateFrom": "2025-07-01",
    "dateTo": "2025-07-31",
    "custNbr": "CUST001"
  }
}
```

#### 排程報表

```http
POST /reports/schedules
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "templateId": 123,
  "scheduleName": "Weekly CQR Summary",
  "cronExpression": "0 8 * * MON",
  "recipients": ["<EMAIL>", "<EMAIL>"],
  "parameters": {
    "format": "excel",
    "includeCharts": true
  },
  "isActive": true
}
```

---

## 工作流程 API

### 9.1 任務管理

#### 獲取我的任務

```http
GET /workflow/tasks?status=pending&priority=high&sortBy=dueDate
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": [
    {
      "taskId": "T-12345",
      "queueKey": 12345,
      "projectNbr": "PRJ-2025-001",
      "taskType": "Approval",
      "taskName": "Business Manager Review",
      "assignedTo": "bob.johnson",
      "assignedDate": "2025-07-22T10:00:00Z",
      "dueDate": "2025-07-24T18:00:00Z",
      "priority": "High",
      "status": "Pending",
      "description": "Review project scope and approve to next phase",
      "cqrInfo": {
        "custName": "ABC Company",
        "productDesc": "Safety System",
        "value": 2500000.00
      },
      "actions": [
        {
          "action": "approve",
          "label": "Approve",
          "type": "primary"
        },
        {
          "action": "reject",
          "label": "Reject", 
          "type": "danger"
        }
      ]
    }
  ]
}
```

#### 完成任務

```http
PUT /workflow/tasks/{taskId}/complete
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "decision": "approved",
  "comments": "Project scope is clear and feasible. Approved to proceed to Engineering phase.",
  "nextAssignee": "tom.wilson",
  "attachments": [67890]
}
```

#### 委派任務

```http
POST /workflow/tasks/{taskId}/delegate
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "delegateTo": "alice.brown",
  "reason": "I will be out of office next week",
  "dueDate": "2025-07-25T18:00:00Z"
}
```

### 9.2 工作流程狀態

#### 獲取 CQR 工作流程狀態

```http
GET /workflow/cqr/{queueKey}/status
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": {
    "queueKey": 12345,
    "currentStage": "Engineering Review",
    "status": "020150FR",
    "progress": 35,
    "completedSteps": [
      {
        "stepName": "Initial Review",
        "completedBy": "john.doe",
        "completedDate": "2025-07-22T10:00:00Z",
        "decision": "approved"
      }
    ],
    "pendingSteps": [
      {
        "stepName": "Engineering Assessment",
        "assignedTo": "tom.wilson",
        "dueDate": "2025-07-24T18:00:00Z",
        "priority": "high"
      }
    ],
    "timeline": [
      {
        "date": "2025-07-22T10:00:00Z",
        "action": "CQR Created",
        "actor": "john.doe",
        "status": "010100FR"
      }
    ]
  }
}
```

#### 觸發工作流程事件

```http
POST /workflow/cqr/{queueKey}/trigger
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "event": "status_changed",
  "newStatus": "020100FR",
  "data": {
    "comments": "Moving to background analysis phase",
    "priority": "normal"
  }
}
```

### 9.3 工作流程配置

#### 獲取工作流程定義

```http
GET /workflow/definitions?type=cqr_approval&version=latest
Authorization: Bearer {access_token}
```

#### 創建工作流程定義

```http
POST /workflow/definitions
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "workflowName": "CQR Approval Process v2",
  "description": "Updated CQR approval workflow with parallel review steps",
  "version": "2.0",
  "steps": [
    {
      "stepId": "initial_review",
      "stepName": "Initial Review",
      "assigneeType": "role",
      "assigneeValue": "BMGR",
      "conditions": {
        "value": {"$lt": 500000}
      },
      "timeLimit": 48,
      "actions": ["approve", "reject", "request_info"]
    }
  ],
  "notifications": {
    "onStart": true,
    "onComplete": true,
    "onOverdue": true
  }
}
```

---

## 系統管理 API

### 10.1 系統配置

#### 獲取系統設定

```http
GET /system/config?category=general
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": {
    "general": {
      "systemName": "CQR Management System",
      "version": "2.0.0",
      "environment": "production",
      "timezone": "UTC",
      "dateFormat": "yyyy-MM-dd",
      "currency": "USD"
    },
    "features": {
      "enableNotifications": true,
      "enableFileUpload": true,
      "maxFileSize": 52428800,
      "allowedFileTypes": [".pdf", ".xlsx", ".jpg"]
    },
    "security": {
      "tokenExpiry": 3600,
      "maxLoginAttempts": 3,
      "passwordMinLength": 8
    }
  }
}
```

#### 更新系統設定

```http
PUT /system/config
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "category": "features",
  "settings": {
    "maxFileSize": 104857600,
    "enableAutoSave": true
  }
}
```

### 10.2 系統監控

#### 獲取系統健康狀態

```http
GET /system/health
Authorization: Bearer {access_token}
```

**響應:**

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2025-07-22T10:30:00Z",
    "uptime": 86400,
    "version": "2.0.0",
    "services": {
      "database": {
        "status": "healthy",
        "responseTime": 15,
        "connections": {
          "active": 12,
          "max": 100
        }
      },
      "redis": {
        "status": "healthy",
        "responseTime": 2,
        "memory": {
          "used": "256MB",
          "max": "1GB"
        }
      },
      "sapRFC": {
        "status": "healthy",
        "responseTime": 150,
        "connections": {
          "active": 3,
          "max": 10
        }
      }
    },
    "metrics": {
      "requestsPerMinute": 145,
      "errorRate": 0.02,
      "averageResponseTime": 89
    }
  }
}
```

#### 獲取系統指標

```http
GET /system/metrics?period=24h&metric=performance
Authorization: Bearer {access_token}
```

### 10.3 日誌管理

#### 獲取系統日誌

```http
GET /system/logs?level=error&dateFrom=2025-07-22&page=1&pageSize=50
Authorization: Bearer {access_token}
```

#### 獲取審計日誌

```http
GET /system/audit-logs?userId=john.doe&action=create&resource=cqr&dateFrom=2025-07-01
Authorization: Bearer {access_token}
```

### 10.4 快取管理

#### 清除快取

```http
DELETE /system/cache?pattern=cqr:*
Authorization: Bearer {access_token}
```

#### 獲取快取統計

```http
GET /system/cache/stats
Authorization: Bearer {access_token}
```

---

## 附錄

### A.1 狀態代碼對照表

| 狀態代碼 | 狀態描述 | 階段 |
|---------|----------|------|
| 010100FR | 正在初始化 | 創建 |
| 010150FR | 需要商業經理審核 | 初審 |
| 010200FR | 需要銷售審核 | 銷售審核 |
| 020100FR | 已發布/需要財務協調員 | 發布 |
| 020150FR | 工程評估進行中 | 工程評估 |
| 030100FR | 報價回應階段 | 報價 |
| 040100FR | 銷售審核階段 | 銷售審核 |
| 090100FR | 已結案 | 完成 |

### A.2 錯誤代碼詳細說明

#### 認證相關錯誤

| 錯誤代碼 | HTTP狀態 | 說明 | 解決方案 |
|---------|----------|------|----------|
| AUTH_001 | 401 | Token 已過期 | 使用 refresh token 重新獲取 |
| AUTH_002 | 401 | Token 格式無效 | 檢查 Authorization 標頭格式 |
| AUTH_003 | 401 | 用戶不存在 | 確認用戶帳號 |
| AUTH_004 | 401 | 密碼錯誤 | 確認密碼或重置密碼 |
| AUTH_005 | 403 | 帳號被鎖定 | 聯繫管理員解鎖 |

#### 業務邏輯錯誤

| 錯誤代碼 | HTTP狀態 | 說明 | 解決方案 |
|---------|----------|------|----------|
| CQR_001 | 404 | CQR 不存在 | 確認 QueueKey 是否正確 |
| CQR_002 | 409 | 項目編號重複 | 使用不同的項目編號 |
| CQR_003 | 422 | 狀態轉換無效 | 檢查狀態轉換規則 |
| CQR_004 | 403 | 無權限訪問此 CQR | 檢查用戶權限 |
| CQR_005 | 409 | CQR 被其他用戶鎖定 | 等待解鎖或聯繫鎖定用戶 |

### A.3 API 變更日誌

#### v1.0 (2025-07-22)

**新增:**
- CQR CRUD 操作完整 API
- 附件管理 API
- 用戶管理 API
- 工作流程 API
- 報表系統 API

**修改:**
- 無

**廢棄:**
- 無

### A.4 SDK 和工具

#### JavaScript/TypeScript SDK

```javascript
import { CQRClient } from '@cqr/api-client'

const client = new CQRClient({
  baseURL: 'https://api.cqr.company.com',
  apiKey: 'your-api-key'
})

// 獲取 CQR 列表
const cqrs = await client.cqr.list({
  page: 1,
  pageSize: 20,
  status: ['010100FR']
})

// 創建 CQR
const newCQR = await client.cqr.create({
  projectNbr: 'PRJ-2025-001',
  customer: {
    custNbr: 'CUST001',
    custName: 'ABC Company'
  }
})
```

#### Postman Collection

提供完整的 Postman Collection，包含所有 API 端點的範例請求。

```json
{
  "info": {
    "name": "CQR System API",
    "version": "1.0.0"
  },
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{accessToken}}",
        "type": "string"
      }
    ]
  }
}
```

### A.5 測試環境

#### 測試端點

- **基礎 URL**: `https://api-test.cqr.company.com`
- **測試帳號**: <EMAIL> / test123
- **Swagger 文檔**: `https://api-test.cqr.company.com/swagger`

#### 測試數據

系統提供測試數據集，包含：
- 50+ 測試 CQR 記錄
- 多種狀態的 CQR
- 測試用戶帳號
- 範例附件

### A.6 支援與聯絡

#### 技術支援

- **API 文檔**: https://docs.api.cqr.company.com
- **GitHub Issues**: https://github.com/company/cqr-api/issues
- **電子郵件**: <EMAIL>
- **Slack**: #cqr-api-support

#### SLA 承諾

- **可用性**: 99.9%
- **響應時間**: < 200ms (95th percentile)
- **支援時間**: 9:00-18:00 (工作日)
- **緊急響應**: < 1小時