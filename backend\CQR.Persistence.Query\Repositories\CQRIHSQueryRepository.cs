﻿using CQR.Application.Dto;
using CQR.Application.Dto.Report;
using CQR.Application.Repositories;
using CQR.Persistence.Query.Base;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Query.Implementations;

public class CQRIHSQueryRepository : QueryBaseRepository, IReportQueryRepository
{
    public CQRIHSQueryRepository(IConfiguration configuration) : base(configuration)    {    }

    public Task<IEnumerable<SelectItem>> GetIHSFieldAsync(string fieldName, string valueField)
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<DueDateStatusReportDto>> GetSQLDueDateStatusReport()
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<OpenCQRStatusReportDto>> GetSQLReportOpenCQRStatus()
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<PipelineReportDto>> GetSQLReportPipeline()
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<SalesReportDto>> GetSQLReportSalesReport()
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<WinLossReportDto>> GetSQLReportWinLoss()
    {
        throw new NotImplementedException();
    }
}