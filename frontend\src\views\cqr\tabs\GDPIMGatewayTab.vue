<template>
  <el-card>
    <div>GDPIMGatewayTab</div>
    <!-- Release Gateway Checkbox -->
    <el-form :model="formData" label-width="120px">
      <el-form-item>
        <el-checkbox v-model="formData.releaseGateway" :disabled="true">
          Release?
        </el-checkbox>
      </el-form-item>

      <!-- Basic Info Row -->
      <div class="inline-form">
        <el-form-item label="Business Type:">
          <el-input
            v-model="formData.businessType"
            :readonly="true"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item v-if="showGDPEPCategory" label="GDPEP Category:">
          <el-select
            v-model="formData.category"
            :disabled="true"
            style="width: 150px"
          >
            <el-option label="Category 1" value="1" />
            <el-option label="Category 2" value="2" />
            <el-option label="Category 3" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="Date:">
          <el-input
            v-model="formData.date"
            :readonly="true"
            style="width: 175px"
          />
        </el-form-item>

        <el-form-item label="Sales Director (SDIR):">
          <el-input
            v-model="formData.sdir"
            :readonly="true"
            style="width: 200px"
          />
        </el-form-item>
      </div>
    </el-form>

    <!-- Customer/Platform Details Section -->
    <div class="section-title">1. CUSTOMER / PLATFORM DETAILS</div>

    <el-form :model="formData" label-width="150px">
      <el-form-item label="Product Description:">
        <el-input
          v-model="formData.productDescription"
          :readonly="true"
          style="width: 535px"
        />
      </el-form-item>

      <el-form-item label="Model Year:">
        <el-input
          v-model="formData.modelYear"
          :readonly="true"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item label="Platform:">
        <el-input
          v-model="formData.platform"
          :readonly="true"
          style="width: 200px"
        />
      </el-form-item>

      <!-- Job 1 Info -->
      <div class="inline-form" style="margin: 20px 0">
        <span style="margin-right: 20px">Job 1:</span>
        <el-form-item label="Year:">
          <el-input
            v-model="formData.job1Year"
            :readonly="true"
            style="width: 100px"
          />
        </el-form-item>
        <el-form-item label="Month:">
          <el-input
            v-model="formData.job1Month"
            :readonly="true"
            style="width: 100px"
          />
        </el-form-item>
      </div>
    </el-form>

    <!-- Vehicle Volumes Table -->
    <div class="volume-table">
      <el-table :data="[volumeData]" border style="width: 100%">
        <el-table-column
          prop="description"
          label="VEHICLE VOLUMES (in '000)"
          width="200"
        />
        <el-table-column
          v-for="(year, index) in years"
          :key="index"
          :prop="'year' + (index + 1)"
          :label="year"
          width="100"
        >
          <template #default="scope">
            <el-input
              v-model="scope.row['volume' + (index + 1)]"
              :disabled="true"
              size="small"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- Comments Section -->
    <div class="comments-section">
      <el-form :model="formData" label-width="100px">
        <el-form-item label="Comments:">
          <el-input
            v-model="formData.comments"
            type="textarea"
            :rows="4"
            :readonly="true"
            style="width: 580px"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- Sell Price Section -->
    <div v-if="showSellPrice">
      <el-form label-width="100px">
        <el-form-item label="Sell Price:">
          <div class="price-grid">
            <div
              v-for="i in 10"
              :key="i"
              style="display: flex; align-items: center; gap: 10px"
            >
              <span style="width: 60px">Option {{ i }}:</span>
              <el-input
                v-model="formData.sellPrices['option' + i]"
                :disabled="true"
                style="width: 94px"
              />
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- Objective Section -->
    <div class="section-title">Objective</div>

    <div v-if="showGateway1Objective">
      <p><strong>To Assess:</strong></p>
      <ul>
        <li>Confirm resources to quote</li>
        <li>Manufacturing strategy direction</li>
        <li>Component sourcing strategy direction</li>
        <li>
          Confirm commercial issues (Givebacks, Customer Funding, Target
          Pricing)
        </li>
      </ul>
    </div>

    <div v-if="showGateway2Objective">
      <ul>
        <li>Business Case</li>
        <li>Review Financials and Sales Price</li>
        <li>Define Quoting Strategy</li>
      </ul>
    </div>

    <!-- Approval Section -->
    <div class="section-title">Approval</div>

    <div class="approval-table">
      <el-table :data="approvalData" border style="width: 100%">
        <el-table-column
          prop="role"
          label=""
          width="200"
          class-name="role-cell"
        />
        <el-table-column prop="user" label="User" width="100" />
        <el-table-column label="Required" width="80">
          <template #default="scope">
            <el-checkbox v-model="scope.row.required" :disabled="true" />
          </template>
        </el-table-column>
        <el-table-column label="Approved" width="80">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.approved"
              :disabled="!scope.row.canApprove"
              @change="handleApproval(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="Rejected" width="80">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.rejected"
              :disabled="!scope.row.canReject"
              @change="handleRejection(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="dateTime" label="Date/Time" width="140" />
        <el-table-column label="Comments" min-width="200">
          <template #default="scope">
            <el-input
              v-if="scope.row.showComments"
              v-model="scope.row.comments"
              type="textarea"
              :rows="3"
              :readonly="true"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- Rejection Comments -->
      <div style="margin-top: 20px">
        <el-form label-width="150px">
          <el-form-item label="Rejection Comments:">
            <el-input
              v-model="formData.rejectionComments"
              type="textarea"
              :rows="8"
              :readonly="true"
              style="width: 400px"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- Commercial Business Manager -->
    <div style="margin: 20px 0">
      <el-form label-width="200px">
        <el-form-item label="Commercial Business Manager:">
          <el-select
            v-model="formData.cbm"
            :disabled="true"
            style="width: 200px"
          >
            <el-option label="Manager 1" value="mgr1" />
            <el-option label="Manager 2" value="mgr2" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- Released Business Cases -->
    <div class="section-title">Released Business Cases</div>

    <div class="business-case-table">
      <el-table :data="businessCaseData" border style="width: 100%">
        <el-table-column prop="programName" label="Program Name" width="120" />
        <el-table-column
          prop="startingSellPrice"
          label="Starting Sell Price"
          width="100"
        />
        <el-table-column
          prop="amortizedTools"
          label="Amortized Tools"
          width="100"
        />
        <el-table-column prop="totalSales" label="Total Sales" width="100" />
        <el-table-column
          prop="trwPaidTools"
          label="TRW Paid Tools"
          width="100"
        />
        <el-table-column prop="trwCapital" label="TRW Capital" width="100" />
        <el-table-column
          prop="customerPaidTools"
          label="Customer Paid Tools"
          width="120"
        />
        <el-table-column prop="patPercent" label="PAT % or ROS %" width="100" />
        <el-table-column
          prop="irrPercent"
          label="IRR % or ROAE %"
          width="120"
        />
        <el-table-column prop="matPercent" label="Mat. %" width="80" />
      </el-table>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { onMounted, reactive, ref } from "vue";

// const { createApp, ref, reactive, onMounted } = Vue;
// const { ElMessage } = ElementPlus;

// 定義表單資料介面
const createFormData = () => ({
  phase: "Phase 1",
  releaseGateway: false,
  businessType: "",
  category: "",
  date: "",
  sdir: "",
  productDescription: "",
  modelYear: "",
  platform: "",
  job1Year: "",
  job1Month: "",
  comments: "",
  rejectionComments: "",
  cbm: "",
  sellPrices: {
    option1: "",
    option2: "",
    option3: "",
    option4: "",
    option5: "",
    option6: "",
    option7: "",
    option8: "",
    option9: "",
    option10: ""
  }
});

// 定義車輛量資料介面
const createVolumeData = () => ({
  description: "Customer Vehicle Volumes",
  volume1: "",
  volume2: "",
  volume3: "",
  volume4: "",
  volume5: "",
  volume6: "",
  volume7: ""
});

// 定義審批資料介面
const createApprovalItem = (
  role,
  showComments = false,
  canApprove = false,
  canReject = false
) => ({
  role,
  user: "",
  required: false,
  approved: false,
  rejected: false,
  dateTime: "",
  comments: "",
  canApprove,
  canReject,
  showComments
});

// 定義商業案例資料介面
const createBusinessCaseItem = () => ({
  programName: "Sample Program",
  startingSellPrice: "$100.00",
  amortizedTools: "$50.00",
  totalSales: "$1000.00",
  trwPaidTools: "$200.00",
  trwCapital: "$150.00",
  customerPaidTools: "$300.00",
  patPercent: "15%",
  irrPercent: "12%",
  matPercent: "60%"
});

// 響應式資料
const formData = reactive(createFormData());
const volumeData = reactive(createVolumeData());

const years = ref(["2024", "2025", "2026", "2027", "2028", "2029", "2030"]);

const approvalData = reactive([
  createApprovalItem("CEO"),
  createApprovalItem("CFO"),
  createApprovalItem("CTO"),
  createApprovalItem("CPO", true),
  createApprovalItem("Regional General Manager", true),
  createApprovalItem("Regional Sales Lead", true),
  createApprovalItem("Commercial Change Manager", true, true, true),
  createApprovalItem("BCS Management Team")
]);

const businessCaseData = reactive([createBusinessCaseItem()]);

// 控制顯示的變數
const showGDPEPCategory = ref(true);
const showSellPrice = ref(true);
const showGateway1Objective = ref(true);
const showGateway2Objective = ref(false);

// 方法
const handleApproval = row => {
  if (row.approved) {
    row.rejected = false;
    row.dateTime = new Date().toLocaleString();
    ElMessage.success(`${row.role} approval recorded`);
  }
};

const handleRejection = row => {
  if (row.rejected) {
    row.approved = false;
    row.dateTime = new Date().toLocaleString();
    ElMessage.warning(`${row.role} rejection recorded`);
  }
};

// 初始化資料
const initializeData = () => {
  formData.businessType = "Automotive Parts";
  formData.date = new Date().toLocaleDateString();
  formData.sdir = "John Smith";
  formData.productDescription = "Advanced Safety System Components";
  formData.modelYear = "2025";
  formData.platform = "Platform A";
  formData.job1Year = "2024";
  formData.job1Month = "12";
  formData.comments =
    "Initial gateway review for new automotive safety components.";

  // Sample volume data
  volumeData.volume1 = "100";
  volumeData.volume2 = "150";
  volumeData.volume3 = "200";
  volumeData.volume4 = "180";
  volumeData.volume5 = "160";
  volumeData.volume6 = "140";
  volumeData.volume7 = "120";
};

// 生命週期掛鉤
onMounted(() => {
  initializeData();
});

// 返回需要在模板中使用的資料和方法
// return {
//   formData,
//   volumeData,
//   years,
//   approvalData,
//   businessCaseData,
//   showGDPEPCategory,
//   showSellPrice,
//   showGateway1Objective,
//   showGateway2Objective,
//   handleApproval,
//   handleRejection
// };
</script>

<style lang="scss" scoped></style>
