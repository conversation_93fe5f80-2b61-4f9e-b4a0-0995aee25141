﻿using CQR.Application.Interfaces;
using CQR.Application.Repositories;
using CQR.Application.UseCases.CQR_Headers.Commands.Handler;
using CQR.Domain.IServices;

namespace CQR.Application.Services;

public class ApplicationServices : IApplicationServices
{
    // Command & Query Services
    public ICQRCommandHandler CommandHandler { get; }
    public ICQRQueryService QueryService { get; }
    public ICQRValidationService ValidationService { get; }
    
    // Business Services
    public IAttachmentService AttachmentService { get; }
    // public IProductService ProductService { get; }
    // public ICustomService CustomService { get; }
    
    // Infrastructure Services
    public IExcelService ExcelService { get; }
    
    // External Integration Services
    // public ISqlConnectionService SqlConnectionService { get; }
    // public ISapConnectionService SapConnectionService { get; }
    
    // Cross-Cutting Services
    public ICurrentUserService CurrentUserService { get; }
    
    // Data Access
    public IRepositoryCollection Repositories { get; }

    public ApplicationServices(
        ICQ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> commandHandler,
        ICQRQueryService queryService,
        ICQRValidationService validationService,
        IAttachmentService attachmentService,
        // IProductService productService,
        // ICustomService customService,
        IExcelService excelService,
        // ISqlConnectionService sqlConnectionService,
        // ISapConnectionService sapConnectionService,
        ICurrentUserService currentUserService,
        IRepositoryCollection repos)
    {
        CommandHandler = commandHandler;
        QueryService = queryService;
        ValidationService = validationService;
        AttachmentService = attachmentService;
        // ProductService = productService;
        // CustomService = customService;
        ExcelService = excelService;
        // SqlConnectionService = sqlConnectionService;
        // SapConnectionService = sapConnectionService;
        CurrentUserService = currentUserService;
        Repositories = repos;
    }
}

