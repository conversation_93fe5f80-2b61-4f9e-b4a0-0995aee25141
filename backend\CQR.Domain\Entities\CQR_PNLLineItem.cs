﻿using System.ComponentModel.DataAnnotations;

namespace CQR.Domain.Entities;

public class CQR_PNLLineItem
{
    [Required]
    public int PNLLineItemId { get; set; }

    public int? QueueKey { get; set; }

    public int? PNL_AttachmentKey { get; set; }

    public int? SaveOrder { get; set; }

    [StringLength(50)]
    public string? AwardStatus { get; set; }

    public DateTime? AwardStatusDate { get; set; }

    [StringLength(20)]
    public string? AwardStatusUser { get; set; }

    [StringLength(256)]
    public string? Product { get; set; }

    [StringLength(50)]
    public string? Site { get; set; }

    [StringLength(50)]
    public string? AverageAnnualVolume { get; set; }

    [StringLength(50)]
    public string? SalesPriceInQuoted { get; set; }

    [StringLength(50)]
    public string? QuotedCurrency { get; set; }

    [StringLength(50)]
    public string? SalesBusinessUnit { get; set; }

    [StringLength(50)]
    public string? SalesPriceSOP { get; set; }

    [StringLength(50)]
    public string? AverageAnnualSales { get; set; }

    [StringLength(50)]
    public string? AverageAnnualDVP { get; set; }

    [StringLength(50)]
    public string? AverageAnnualMPBT { get; set; }

    [StringLength(50)]
    public string? EVAFullCost { get; set; }

    [StringLength(50)]
    public string? EVAIncremental { get; set; }

    public DateTime? CreateDate { get; set; }

    [StringLength(1)]
    public char? Marked { get; set; }
}
