# CQR 後端技術規格書

## 文檔資訊

| 項目 | 內容 |
|------|------|
| 文檔標題 | CQR 系統後端技術規格書 |
| 版本 | 2.0 |
| 建立日期 | 2025-07-22 |
| 最後更新 | 2025-07-22 |
| 文檔狀態 | 草案 |
| 作者 | 後端開發團隊 |

## 目錄

1. [架構概述](#架構概述)
2. [專案結構](#專案結構)
3. [領域層設計](#領域層設計)
4. [應用層設計](#應用層設計)
5. [基礎設施層](#基礎設施層)
6. [API 層設計](#api-層設計)
7. [資料存取層](#資料存取層)
8. [認證與授權](#認證與授權)
9. [中介軟體設計](#中介軟體設計)
10. [配置管理](#配置管理)
11. [日誌與監控](#日誌與監控)
12. [錯誤處理](#錯誤處理)
13. [性能優化](#性能優化)
14. [測試策略](#測試策略)

---

## 架構概述

### 1.1 Clean Architecture 實現

CQR 後端系統採用 Clean Architecture 模式，確保業務邏輯與技術實現的分離：

```
┌─────────────────────────────────────────────────────────────────┐
│                    CQR 後端系統架構                              │
├─────────────────────────────────────────────────────────────────┤
│ API 層 (CQR.API)                                                │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ • Controllers (RESTful API 端點)                            │ │
│ │ • Authentication & Authorization                           │ │
│ │ • Middleware (異常處理、日誌、驗證)                           │ │
│ │ • Filters & Attributes                                     │ │
│ │ • Swagger/OpenAPI Documentation                            │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 應用層 (CQR.Application)                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ • Application Services                                     │ │
│ │ • Command & Query Handlers (CQRS)                         │ │
│ │ • DTOs & ViewModels                                        │ │
│ │ • Validators (FluentValidation)                            │ │
│ │ • AutoMapper Profiles                                      │ │
│ │ • Business Workflows                                       │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 領域層 (CQR.Domain)                                             │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ • Domain Entities                                          │ │
│ │ • Value Objects                                            │ │
│ │ • Domain Services                                          │ │
│ │ • Repository Interfaces                                    │ │
│ │ • Domain Events                                            │ │
│ │ • Business Rules & Specifications                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 基礎設施層 (CQR.Infrastructure)                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ • External Services (SAP, SMTP, File Storage)             │ │
│ │ • Configuration Management                                 │ │
│ │ • Caching (Redis)                                          │ │
│ │ • Message Queues                                           │ │
│ │ • Third-party Integrations                                 │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 資料持久層 (CQR.Persistence)                                    │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ • Entity Framework Core (Commands)                         │ │
│ │ • SqlSugar ORM (Queries)                                   │ │
│ │ • Repository Implementations                               │ │
│ │ • Unit of Work Pattern                                     │ │
│ │ • Database Context & Migrations                            │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 CQRS 實現

系統採用命令查詢責任分離 (CQRS) 模式：

**命令端 (Commands)**:
- 使用 Entity Framework Core
- 處理 CUD 操作 (Create, Update, Delete)
- 確保資料一致性和交易完整性

**查詢端 (Queries)**:
- 使用 SqlSugar ORM
- 處理 R 操作 (Read)
- 優化查詢性能，支援複雜查詢

### 1.3 依賴注入容器

使用 ASP.NET Core 內建的 DI 容器，配置如下：

```csharp
// Program.cs
var builder = WebApplication.CreateBuilder(args);

// 註冊服務
builder.Services.AddScoped<IApplicationServices, ApplicationServices>();
builder.Services.AddScoped<ICQRQueryService, CQRQueryService>();
builder.Services.AddScoped<ICQRCommandHandler, CQRCommandHandler>();
builder.Services.AddScoped<IRepositoryCollection, RepositoryCollection>();

// 資料庫服務
builder.Services.AddDbContext<CQRDbContext>(options =>
    options.UseSqlServer(connectionString));
builder.Services.AddScoped<ISqlConnectionService, SqlConnectionService>();

// 業務服務
builder.Services.AddScoped<IAttachmentService, AttachmentService>();
builder.Services.AddScoped<IProductService, ProductService>();
builder.Services.AddScoped<ISapRFCService, SapRFCService>();
```

---

## 專案結構

### 2.1 解決方案結構

```
CQR.Backend/
├── CQR.API/                           # Web API 專案
│   ├── Controllers/                   # API 控制器
│   ├── Middleware/                    # 中介軟體
│   ├── Filters/                       # 過濾器
│   ├── Authentication/                # 認證相關
│   ├── Extensions/                    # 擴展方法
│   └── Program.cs                     # 應用程式入口點
├── CQR.Application/                   # 應用層
│   ├── Services/                      # 應用服務
│   ├── Commands/                      # 命令處理器
│   ├── Queries/                       # 查詢處理器
│   ├── Dto/                           # 資料傳輸物件
│   ├── Validators/                    # 驗證器
│   ├── Mappings/                      # AutoMapper 設定檔
│   └── Interfaces/                    # 介面定義
├── CQR.Domain/                        # 領域層
│   ├── Entities/                      # 領域實體
│   ├── ValueObjects/                  # 值物件
│   ├── Services/                      # 領域服務
│   ├── Interfaces/                    # Repository 介面
│   ├── Events/                        # 領域事件
│   ├── Exceptions/                    # 領域例外
│   └── Constants/                     # 常數定義
├── CQR.Infrastructure/                # 基礎設施層
│   ├── Services/                      # 外部服務
│   ├── Configuration/                 # 配置服務
│   ├── FileStorage/                   # 檔案存儲
│   ├── External/                      # 外部系統整合
│   └── Utilities/                     # 工具類
├── CQR.Persistence.Command/           # 命令資料持久層
│   ├── Context/                       # EF DbContext
│   ├── Configurations/                # 實體配置
│   ├── Migrations/                    # 資料庫遷移
│   ├── Repositories/                  # Repository 實現
│   └── UnitOfWork/                    # 工作單元
├── CQR.Persistence.Query/             # 查詢資料持久層
│   ├── Repositories/                  # 查詢 Repository
│   ├── QueryObjects/                  # 查詢物件
│   └── Extensions/                    # 查詢擴展
└── CQR.Tests/                         # 測試專案
    ├── Unit/                          # 單元測試
    ├── Integration/                   # 整合測試
    ├── Api/                           # API 測試
    └── TestHelpers/                   # 測試輔助類
```

### 2.2 專案依賴關係

```mermaid
graph TD
    API[CQR.API]
    APP[CQR.Application]
    DOMAIN[CQR.Domain]
    INFRA[CQR.Infrastructure]
    PCMD[CQR.Persistence.Command]
    PQRY[CQR.Persistence.Query]
    
    API --> APP
    API --> INFRA
    API --> PCMD
    API --> PQRY
    
    APP --> DOMAIN
    APP --> INFRA
    
    INFRA --> DOMAIN
    
    PCMD --> DOMAIN
    PCMD --> APP
    
    PQRY --> DOMAIN
    PQRY --> APP
```

**依賴規則**:
1. 內層不依賴外層
2. 所有依賴都指向中心 (Domain)
3. 使用介面抽象具體實現

---

## 領域層設計

### 3.1 領域實體

#### 3.1.1 CQRHeader 實體

```csharp
namespace CQR.Domain.Entities
{
    public class CQRHeader : BaseEntity
    {
        // 主鍵
        public int QueueKey { get; private set; }
        
        // 基本資訊
        public ProjectNumber ProjectNbr { get; private set; }
        public int RevNbr { get; private set; }
        public CQRStatus Status { get; private set; }
        public QuoteType QuoteType { get; private set; }
        
        // 客戶資訊
        public CustomerInfo Customer { get; private set; }
        
        // 產品資訊
        public ProductInfo Product { get; private set; }
        
        // 業務資訊
        public BusinessInfo Business { get; private set; }
        
        // 重要日期
        public ProjectDates Dates { get; private set; }
        
        // 團隊成員
        public TeamAssignment Team { get; private set; }
        
        // 報價資訊
        public QuoteInfo Quote { get; private set; }
        
        // 備註資訊
        public ProjectNotes Notes { get; private set; }
        
        // 閘道資訊
        public GateInfo Gates { get; private set; }
        
        // 附件集合
        private readonly List<AttachmentFile> _attachments = new();
        public IReadOnlyCollection<AttachmentFile> Attachments => _attachments.AsReadOnly();
        
        // 建構函式
        protected CQRHeader() { } // EF Core
        
        public CQRHeader(
            ProjectNumber projectNbr,
            QuoteType quoteType,
            CustomerInfo customer,
            ProductInfo product,
            string originatorId)
        {
            ProjectNbr = projectNbr ?? throw new ArgumentNullException(nameof(projectNbr));
            QuoteType = quoteType;
            Customer = customer ?? throw new ArgumentNullException(nameof(customer));
            Product = product ?? throw new ArgumentNullException(nameof(product));
            
            RevNbr = 0;
            Status = CQRStatus.Initializing;
            
            Team = new TeamAssignment(originatorId);
            Business = BusinessInfo.Empty;
            Dates = ProjectDates.Empty;
            Quote = QuoteInfo.Empty;
            Notes = ProjectNotes.Empty;
            Gates = GateInfo.Empty;
            
            // 領域事件
            AddDomainEvent(new CQRCreatedEvent(this));
        }
        
        // 業務方法
        public void UpdateStatus(CQRStatus newStatus, string userId, string comments = null)
        {
            if (!CanTransitionTo(newStatus))
                throw new InvalidStatusTransitionException(Status, newStatus);
                
            var oldStatus = Status;
            Status = newStatus;
            
            AddDomainEvent(new CQRStatusChangedEvent(this, oldStatus, newStatus, userId, comments));
        }
        
        public void AssignTeamMember(TeamRole role, string userId, string userName)
        {
            Team = Team.AssignMember(role, userId, userName);
            AddDomainEvent(new TeamMemberAssignedEvent(QueueKey, role, userId));
        }
        
        public void UpdateQuoteInfo(decimal internalPrice, decimal customerPrice)
        {
            Quote = new QuoteInfo(internalPrice, customerPrice);
            AddDomainEvent(new QuoteUpdatedEvent(QueueKey, internalPrice, customerPrice));
        }
        
        public void AddAttachment(AttachmentFile attachment)
        {
            if (attachment == null) throw new ArgumentNullException(nameof(attachment));
            
            _attachments.Add(attachment);
            AddDomainEvent(new AttachmentAddedEvent(QueueKey, attachment.AttachKey));
        }
        
        public CQRHeader CreateRevision(string userId, string reason)
        {
            var revision = new CQRHeader(
                ProjectNbr,
                QuoteType,
                Customer,
                Product,
                userId)
            {
                RevNbr = this.RevNbr + 1
            };
            
            AddDomainEvent(new CQRRevisedEvent(this.QueueKey, revision.QueueKey, reason));
            
            return revision;
        }
        
        // 私有方法
        private bool CanTransitionTo(CQRStatus newStatus)
        {
            return Status.CanTransitionTo(newStatus);
        }
    }
}
```

#### 3.1.2 值物件設計

```csharp
// 項目編號值物件
public class ProjectNumber : ValueObject
{
    public string Value { get; }
    
    public ProjectNumber(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Project number cannot be null or empty", nameof(value));
            
        if (!IsValidFormat(value))
            throw new ArgumentException("Invalid project number format", nameof(value));
            
        Value = value.ToUpperInvariant();
    }
    
    private static bool IsValidFormat(string value)
    {
        // PRJ-YYYY-### 格式
        var pattern = @"^PRJ-\d{4}-\d{3}$";
        return Regex.IsMatch(value, pattern);
    }
    
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }
    
    public static implicit operator string(ProjectNumber projectNumber) => projectNumber.Value;
    public static implicit operator ProjectNumber(string value) => new(value);
}

// 客戶資訊值物件
public class CustomerInfo : ValueObject
{
    public string CustNbr { get; }
    public string CustName { get; }
    public string BuyerName { get; }
    public string Contact { get; }
    public string Email { get; }
    public string Phone { get; }
    
    public CustomerInfo(
        string custNbr,
        string custName,
        string buyerName = null,
        string contact = null,
        string email = null,
        string phone = null)
    {
        CustNbr = custNbr ?? throw new ArgumentNullException(nameof(custNbr));
        CustName = custName ?? throw new ArgumentNullException(nameof(custName));
        BuyerName = buyerName;
        Contact = contact;
        Email = email;
        Phone = phone;
        
        if (!string.IsNullOrEmpty(email) && !IsValidEmail(email))
            throw new ArgumentException("Invalid email format", nameof(email));
    }
    
    private static bool IsValidEmail(string email)
    {
        return EmailAddress.TryParse(email, out _);
    }
    
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return CustNbr;
        yield return CustName;
        yield return BuyerName;
        yield return Contact;
        yield return Email;
        yield return Phone;
    }
}

// CQR 狀態值物件
public class CQRStatus : ValueObject
{
    public string Code { get; }
    public string Description { get; }
    
    // 預定義狀態
    public static readonly CQRStatus Initializing = new("010100FR", "正在初始化");
    public static readonly CQRStatus NeedsBMGRReview = new("010150FR", "需要商業經理審核");
    public static readonly CQRStatus NeedsSalesReview = new("010200FR", "需要銷售審核");
    public static readonly CQRStatus SalesRejected = new("010300FR", "銷售退回");
    public static readonly CQRStatus Published = new("020100FR", "已發布/需要財務協調員");
    public static readonly CQRStatus EngineeringInProgress = new("020150FR", "工程評估進行中");
    // ... 更多狀態
    
    private static readonly Dictionary<string, List<string>> _allowedTransitions = new()
    {
        ["010100FR"] = new() { "010150FR", "010200FR" },
        ["010150FR"] = new() { "010200FR", "010300FR" },
        ["010200FR"] = new() { "020100FR", "010300FR" },
        // ... 更多轉換規則
    };
    
    public CQRStatus(string code, string description)
    {
        Code = code ?? throw new ArgumentNullException(nameof(code));
        Description = description ?? throw new ArgumentNullException(nameof(description));
    }
    
    public bool CanTransitionTo(CQRStatus newStatus)
    {
        if (_allowedTransitions.TryGetValue(Code, out var allowedNext))
        {
            return allowedNext.Contains(newStatus.Code);
        }
        return false;
    }
    
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Code;
    }
    
    public static implicit operator string(CQRStatus status) => status.Code;
}
```

### 3.2 領域服務

```csharp
// CQR 領域服務
public interface ICQRDomainService
{
    Task<bool> IsProjectNumberUniqueAsync(ProjectNumber projectNbr, int excludeQueueKey = 0);
    Task<ProjectNumber> GenerateNextProjectNumberAsync();
    Task<bool> CanUserCreateCQRAsync(string userId);
    Task<bool> CanUserAccessCQRAsync(string userId, int queueKey);
    Task ValidateBusinessRulesAsync(CQRHeader cqr);
}

public class CQRDomainService : ICQRDomainService
{
    private readonly ICqrHeaderQueryRepository _cqrRepository;
    private readonly IUserRepository _userRepository;
    
    public CQRDomainService(
        ICqrHeaderQueryRepository cqrRepository,
        IUserRepository userRepository)
    {
        _cqrRepository = cqrRepository;
        _userRepository = userRepository;
    }
    
    public async Task<bool> IsProjectNumberUniqueAsync(ProjectNumber projectNbr, int excludeQueueKey = 0)
    {
        var existing = await _cqrRepository.GetByProjectNumberAsync(projectNbr);
        return existing == null || existing.QueueKey == excludeQueueKey;
    }
    
    public async Task<ProjectNumber> GenerateNextProjectNumberAsync()
    {
        var year = DateTime.Now.Year;
        var lastNumber = await _cqrRepository.GetLastProjectNumberForYearAsync(year);
        var nextNumber = lastNumber + 1;
        
        return new ProjectNumber($"PRJ-{year}-{nextNumber:D3}");
    }
    
    public async Task<bool> CanUserCreateCQRAsync(string userId)
    {
        var user = await _userRepository.GetByUserIdAsync(userId);
        if (user == null || !user.IsActive) return false;
        
        var roles = await _userRepository.GetUserRolesAsync(userId);
        return roles.Any(r => r.RoleCode == "AMGR" || r.RoleCode == "SDIR");
    }
    
    public async Task<bool> CanUserAccessCQRAsync(string userId, int queueKey)
    {
        var cqr = await _cqrRepository.GetByIdAsync(queueKey);
        if (cqr == null) return false;
        
        // 創建者總是可以存取
        if (cqr.Team.OriginatorId == userId) return true;
        
        // 檢查團隊成員
        if (cqr.Team.IsTeamMember(userId)) return true;
        
        // 檢查角色權限
        var userRoles = await _userRepository.GetUserRolesAsync(userId);
        return userRoles.Any(r => r.RoleCode == "SDIR" || r.RoleCode == "ADMIN");
    }
    
    public async Task ValidateBusinessRulesAsync(CQRHeader cqr)
    {
        var violations = new List<string>();
        
        // 檢查專案編號唯一性
        if (!await IsProjectNumberUniqueAsync(cqr.ProjectNbr, cqr.QueueKey))
        {
            violations.Add("Project number already exists");
        }
        
        // 檢查必要欄位
        if (cqr.Customer == null)
        {
            violations.Add("Customer information is required");
        }
        
        if (cqr.Product == null)
        {
            violations.Add("Product information is required");
        }
        
        // 檢查日期邏輯
        if (cqr.Dates.SOPDate.HasValue && cqr.Dates.EOPDate.HasValue)
        {
            if (cqr.Dates.SOPDate >= cqr.Dates.EOPDate)
            {
                violations.Add("SOP date must be before EOP date");
            }
        }
        
        // 檢查數值邏輯
        if (cqr.Business.VolumePerAnnum <= 0)
        {
            violations.Add("Volume per annum must be greater than zero");
        }
        
        if (violations.Any())
        {
            throw new BusinessRuleViolationException(violations);
        }
    }
}
```

### 3.3 領域事件

```csharp
// 基底領域事件
public abstract class DomainEvent : IDomainEvent
{
    public Guid EventId { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// CQR 創建事件
public class CQRCreatedEvent : DomainEvent
{
    public int QueueKey { get; }
    public string ProjectNbr { get; }
    public string OriginatorId { get; }
    
    public CQRCreatedEvent(CQRHeader cqr)
    {
        QueueKey = cqr.QueueKey;
        ProjectNbr = cqr.ProjectNbr;
        OriginatorId = cqr.Team.OriginatorId;
    }
}

// 狀態變更事件
public class CQRStatusChangedEvent : DomainEvent
{
    public int QueueKey { get; }
    public string OldStatus { get; }
    public string NewStatus { get; }
    public string ChangedBy { get; }
    public string Comments { get; }
    
    public CQRStatusChangedEvent(
        CQRHeader cqr,
        CQRStatus oldStatus,
        CQRStatus newStatus,
        string changedBy,
        string comments)
    {
        QueueKey = cqr.QueueKey;
        OldStatus = oldStatus;
        NewStatus = newStatus;
        ChangedBy = changedBy;
        Comments = comments;
    }
}

// 領域事件處理器
public class CQRStatusChangedEventHandler : IDomainEventHandler<CQRStatusChangedEvent>
{
    private readonly INotificationService _notificationService;
    private readonly IWorkflowService _workflowService;
    
    public CQRStatusChangedEventHandler(
        INotificationService notificationService,
        IWorkflowService workflowService)
    {
        _notificationService = notificationService;
        _workflowService = workflowService;
    }
    
    public async Task Handle(CQRStatusChangedEvent notification, CancellationToken cancellationToken)
    {
        // 觸發工作流程
        await _workflowService.ProcessStatusChangeAsync(
            notification.QueueKey,
            notification.NewStatus,
            notification.ChangedBy);
        
        // 發送通知
        await _notificationService.NotifyStatusChangeAsync(
            notification.QueueKey,
            notification.OldStatus,
            notification.NewStatus,
            notification.ChangedBy);
    }
}
```

---

## 應用層設計

### 4.1 應用服務模式

採用 Application Services 模式，替代傳統的 MediatR 方式：

```csharp
// 主要應用服務介面
public interface IApplicationServices
{
    ICQRCommandHandler CQRCommands { get; }
    ICQRQueryService CQRQueries { get; }
    ICQRValidationService CQRValidation { get; }
    IAttachmentService Attachments { get; }
    IProductService Products { get; }
    ICustomService Custom { get; }
    IExcelService Excel { get; }
    IDatabaseService Database { get; }
    ISapRFCService SapRFC { get; }
    ISqlConnectionService SqlConnection { get; }
    IRepositoryCollection Repositories { get; }
}

// 應用服務實現
public class ApplicationServices : IApplicationServices
{
    public ICQRCommandHandler CQRCommands { get; }
    public ICQRQueryService CQRQueries { get; }
    public ICQRValidationService CQRValidation { get; }
    public IAttachmentService Attachments { get; }
    public IProductService Products { get; }
    public ICustomService Custom { get; }
    public IExcelService Excel { get; }
    public IDatabaseService Database { get; }
    public ISapRFCService SapRFC { get; }
    public ISqlConnectionService SqlConnection { get; }
    public IRepositoryCollection Repositories { get; }
    
    public ApplicationServices(
        ICQRCommandHandler cqrCommands,
        ICQRQueryService cqrQueries,
        ICQRValidationService cqrValidation,
        IAttachmentService attachments,
        IProductService products,
        ICustomService custom,
        IExcelService excel,
        IDatabaseService database,
        ISapRFCService sapRFC,
        ISqlConnectionService sqlConnection,
        IRepositoryCollection repositories)
    {
        CQRCommands = cqrCommands;
        CQRQueries = cqrQueries;
        CQRValidation = cqrValidation;
        Attachments = attachments;
        Products = products;
        Custom = custom;
        Excel = excel;
        Database = database;
        SapRFC = sapRFC;
        SqlConnection = sqlConnection;
        Repositories = repositories;
    }
}
```

### 4.2 命令處理器

```csharp
// CQR 命令處理器介面
public interface ICQRCommandHandler
{
    Task<int> CreateCQRAsync(CreateCQRCommand command, CancellationToken cancellationToken = default);
    Task UpdateCQRAsync(UpdateCQRCommand command, CancellationToken cancellationToken = default);
    Task DeleteCQRAsync(int queueKey, CancellationToken cancellationToken = default);
    Task UpdateStatusAsync(UpdateStatusCommand command, CancellationToken cancellationToken = default);
    Task AssignTeamMemberAsync(AssignTeamMemberCommand command, CancellationToken cancellationToken = default);
    Task<int> CreateRevisionAsync(CreateRevisionCommand command, CancellationToken cancellationToken = default);
}

// 命令處理器實現
public class CQRCommandHandler : ICQRCommandHandler
{
    private readonly IRepositoryCollection _repositories;
    private readonly ICQRDomainService _domainService;
    private readonly IMapper _mapper;
    private readonly ICurrentUserService _currentUser;
    private readonly ILogger<CQRCommandHandler> _logger;
    
    public CQRCommandHandler(
        IRepositoryCollection repositories,
        ICQRDomainService domainService,
        IMapper mapper,
        ICurrentUserService currentUser,
        ILogger<CQRCommandHandler> logger)
    {
        _repositories = repositories;
        _domainService = domainService;
        _mapper = mapper;
        _currentUser = currentUser;
        _logger = logger;
    }
    
    public async Task<int> CreateCQRAsync(CreateCQRCommand command, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating new CQR with project number: {ProjectNbr}", command.ProjectNbr);
        
        // 驗證使用者權限
        if (!await _domainService.CanUserCreateCQRAsync(_currentUser.UserId))
        {
            throw new UnauthorizedAccessException("User does not have permission to create CQR");
        }
        
        // 產生專案編號（如果未提供）
        var projectNbr = string.IsNullOrEmpty(command.ProjectNbr)
            ? await _domainService.GenerateNextProjectNumberAsync()
            : new ProjectNumber(command.ProjectNbr);
        
        // 檢查專案編號唯一性
        if (!await _domainService.IsProjectNumberUniqueAsync(projectNbr))
        {
            throw new DuplicateProjectNumberException(projectNbr);
        }
        
        // 創建領域實體
        var customer = _mapper.Map<CustomerInfo>(command.Customer);
        var product = _mapper.Map<ProductInfo>(command.Product);
        
        var cqr = new CQRHeader(
            projectNbr,
            command.QuoteType,
            customer,
            product,
            _currentUser.UserId);
        
        // 設定業務資訊
        if (command.Business != null)
        {
            var business = _mapper.Map<BusinessInfo>(command.Business);
            cqr.UpdateBusinessInfo(business);
        }
        
        // 設定重要日期
        if (command.Dates != null)
        {
            var dates = _mapper.Map<ProjectDates>(command.Dates);
            cqr.UpdateProjectDates(dates);
        }
        
        // 分配團隊成員
        if (command.Team != null)
        {
            foreach (var member in command.Team)
            {
                cqr.AssignTeamMember(member.Role, member.UserId, member.UserName);
            }
        }
        
        // 驗證業務規則
        await _domainService.ValidateBusinessRulesAsync(cqr);
        
        // 儲存到資料庫
        await _repositories.CQRHeaders.AddAsync(cqr);
        await _repositories.SaveChangesAsync();
        
        _logger.LogInformation("CQR created successfully with QueueKey: {QueueKey}", cqr.QueueKey);
        
        return cqr.QueueKey;
    }
    
    public async Task UpdateStatusAsync(UpdateStatusCommand command, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Updating CQR status for QueueKey: {QueueKey} to {NewStatus}", 
            command.QueueKey, command.NewStatus);
        
        var cqr = await _repositories.CQRHeaders.GetByIdAsync(command.QueueKey);
        if (cqr == null)
        {
            throw new CQRNotFoundException(command.QueueKey);
        }
        
        // 檢查使用者權限
        if (!await _domainService.CanUserAccessCQRAsync(_currentUser.UserId, command.QueueKey))
        {
            throw new UnauthorizedAccessException("User does not have permission to update this CQR");
        }
        
        // 更新狀態
        cqr.UpdateStatus(command.NewStatus, _currentUser.UserId, command.Comments);
        
        // 儲存變更
        await _repositories.CQRHeaders.UpdateAsync(cqr);
        await _repositories.SaveChangesAsync();
        
        _logger.LogInformation("CQR status updated successfully for QueueKey: {QueueKey}", command.QueueKey);
    }
    
    // 其他命令方法...
}
```

### 4.3 查詢服務

```csharp
// CQR 查詢服務介面
public interface ICQRQueryService
{
    Task<CQRDetailDto> GetByIdAsync(int queueKey, CancellationToken cancellationToken = default);
    Task<PagedResult<CQRSummaryDto>> GetPagedAsync(CQRSearchQuery query, CancellationToken cancellationToken = default);
    Task<List<CQRSummaryDto>> SearchAsync(CQRSearchCriteria criteria, CancellationToken cancellationToken = default);
    Task<CQRStatisticsDto> GetStatisticsAsync(StatisticsQuery query, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(int queueKey, CancellationToken cancellationToken = default);
    Task<List<CQRHistoryDto>> GetHistoryAsync(int queueKey, CancellationToken cancellationToken = default);
}

// 查詢服務實現
public class CQRQueryService : ICQRQueryService
{
    private readonly ICqrHeaderQueryRepository _queryRepository;
    private readonly IMapper _mapper;
    private readonly ICurrentUserService _currentUser;
    private readonly ILogger<CQRQueryService> _logger;
    
    public CQRQueryService(
        ICqrHeaderQueryRepository queryRepository,
        IMapper mapper,
        ICurrentUserService currentUser,
        ILogger<CQRQueryService> logger)
    {
        _queryRepository = queryRepository;
        _mapper = mapper;
        _currentUser = currentUser;
        _logger = logger;
    }
    
    public async Task<CQRDetailDto> GetByIdAsync(int queueKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Fetching CQR details for QueueKey: {QueueKey}", queueKey);
        
        var cqr = await _queryRepository.GetByIdAsync(queueKey);
        if (cqr == null)
        {
            _logger.LogWarning("CQR not found for QueueKey: {QueueKey}", queueKey);
            return null;
        }
        
        // 檢查使用者權限（這裡可以根據需要實現）
        
        var result = _mapper.Map<CQRDetailDto>(cqr);
        
        _logger.LogDebug("CQR details fetched successfully for QueueKey: {QueueKey}", queueKey);
        
        return result;
    }
    
    public async Task<PagedResult<CQRSummaryDto>> GetPagedAsync(CQRSearchQuery query, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Fetching paged CQR list with query: {@Query}", query);
        
        // 建構查詢條件
        var criteria = new CQRSearchCriteria
        {
            Status = query.Status,
            CustNbr = query.CustNbr,
            ProjectNbr = query.ProjectNbr,
            QuoteType = query.QuoteType,
            OriginatorId = query.OriginatorId,
            CreatedDateFrom = query.CreatedDateFrom,
            CreatedDateTo = query.CreatedDateTo,
            VolumeMin = query.VolumeMin,
            VolumeMax = query.VolumeMax,
            ValueMin = query.ValueMin,
            ValueMax = query.ValueMax
        };
        
        // 執行查詢
        var (items, totalCount) = await _queryRepository.GetPagedAsync(
            criteria,
            query.Page,
            query.PageSize,
            query.SortBy,
            query.SortOrder);
        
        // 對映到 DTO
        var dtos = _mapper.Map<List<CQRSummaryDto>>(items);
        
        var result = new PagedResult<CQRSummaryDto>
        {
            Items = dtos,
            TotalCount = totalCount,
            Page = query.Page,
            PageSize = query.PageSize
        };
        
        _logger.LogDebug("Fetched {Count} CQRs out of {Total} total", dtos.Count, totalCount);
        
        return result;
    }
    
    public async Task<CQRStatisticsDto> GetStatisticsAsync(StatisticsQuery query, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Generating CQR statistics for period: {From} - {To}", query.DateFrom, query.DateTo);
        
        var stats = await _queryRepository.GetStatisticsAsync(query.DateFrom, query.DateTo);
        
        return new CQRStatisticsDto
        {
            TotalCQRs = stats.TotalCount,
            ActiveCQRs = stats.ActiveCount,
            CompletedCQRs = stats.CompletedCount,
            TotalValue = stats.TotalValue,
            AverageValue = stats.AverageValue,
            StatusDistribution = stats.StatusDistribution?.ToDictionary(x => x.Key, x => x.Value),
            MonthlyTrend = stats.MonthlyTrend?.Select(x => new MonthlyTrendDto
            {
                Month = x.Month,
                NewCQRs = x.NewCount,
                CompletedCQRs = x.CompletedCount,
                TotalValue = x.TotalValue
            }).ToList()
        };
    }
}
```

### 4.4 資料傳輸物件 (DTOs)

```csharp
// CQR 詳細資訊 DTO
public class CQRDetailDto
{
    public int QueueKey { get; set; }
    public string ProjectNbr { get; set; }
    public int RevNbr { get; set; }
    public string Status { get; set; }
    public string StatusDesc { get; set; }
    public int QuoteType { get; set; }
    
    public CustomerDto Customer { get; set; }
    public ProductDto Product { get; set; }
    public BusinessDto Business { get; set; }
    public ProjectDatesDto Dates { get; set; }
    public TeamAssignmentDto Team { get; set; }
    public QuoteInfoDto Quote { get; set; }
    public ProjectNotesDto Notes { get; set; }
    public GateInfoDto Gates { get; set; }
    
    public List<AttachmentDto> Attachments { get; set; }
    
    public MetadataDto Metadata { get; set; }
}

// CQR 摘要 DTO
public class CQRSummaryDto
{
    public int QueueKey { get; set; }
    public string ProjectNbr { get; set; }
    public int RevNbr { get; set; }
    public string Status { get; set; }
    public string StatusDesc { get; set; }
    public int QuoteType { get; set; }
    
    public string CustNbr { get; set; }
    public string CustName { get; set; }
    public string ProductDesc { get; set; }
    
    public decimal? VolumePerAnnum { get; set; }
    public decimal? ApproxAnnualValue { get; set; }
    
    public DateTime? QuoteNeededDate { get; set; }
    public DateTime? SOPDate { get; set; }
    
    public string OriginatorId { get; set; }
    public string OriginatorName { get; set; }
    
    public DateTime CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
    
    public bool IsLocked { get; set; }
    public string LockedBy { get; set; }
}

// 客戶 DTO
public class CustomerDto
{
    public string CustNbr { get; set; }
    public string CustName { get; set; }
    public string BuyerName { get; set; }
    public string Contact { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
}

// 產品 DTO
public class ProductDto
{
    public string Description { get; set; }
    public string Vehicle { get; set; }
    public string ModelYear { get; set; }
    public string Platform { get; set; }
    public string OEMPartNbr { get; set; }
    public string CustomerPartNbr { get; set; }
    public string TRWPartNbr { get; set; }
}

// 分頁結果 DTO
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}
```

### 4.5 命令與查詢物件

```csharp
// 創建 CQR 命令
public class CreateCQRCommand
{
    public string ProjectNbr { get; set; }
    public QuoteType QuoteType { get; set; }
    
    public CreateCustomerDto Customer { get; set; }
    public CreateProductDto Product { get; set; }
    public CreateBusinessDto Business { get; set; }
    public CreateProjectDatesDto Dates { get; set; }
    
    public List<TeamMemberAssignmentDto> Team { get; set; }
}

public class CreateCustomerDto
{
    [Required]
    public string CustNbr { get; set; }
    
    [Required]
    public string CustName { get; set; }
    
    public string BuyerName { get; set; }
    public string Contact { get; set; }
    
    [EmailAddress]
    public string Email { get; set; }
    
    public string Phone { get; set; }
}

// 更新狀態命令
public class UpdateStatusCommand
{
    [Required]
    public int QueueKey { get; set; }
    
    [Required]
    public CQRStatus NewStatus { get; set; }
    
    public string Comments { get; set; }
}

// CQR 搜尋查詢
public class CQRSearchQuery
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    
    public string Status { get; set; }
    public string CustNbr { get; set; }
    public string ProjectNbr { get; set; }
    public int? QuoteType { get; set; }
    public string OriginatorId { get; set; }
    
    public DateTime? CreatedDateFrom { get; set; }
    public DateTime? CreatedDateTo { get; set; }
    
    public decimal? VolumeMin { get; set; }
    public decimal? VolumeMax { get; set; }
    public decimal? ValueMin { get; set; }
    public decimal? ValueMax { get; set; }
    
    public string SortBy { get; set; } = "CreatedDate";
    public string SortOrder { get; set; } = "desc";
}

// 進階搜尋條件
public class CQRAdvancedSearchQuery
{
    public List<string> Status { get; set; } = new();
    public List<int> QuoteType { get; set; } = new();
    
    public string CustNbr { get; set; }
    public string ProductDesc { get; set; }
    
    public RangeFilter<decimal> VolumeRange { get; set; }
    public RangeFilter<decimal> ValueRange { get; set; }
    public DateRangeFilter DateRange { get; set; }
    
    public TeamFilter Team { get; set; }
    
    public SortOptions Sorting { get; set; }
    public PaginationOptions Pagination { get; set; }
}

public class RangeFilter<T> where T : struct
{
    public T? Min { get; set; }
    public T? Max { get; set; }
}

public class DateRangeFilter
{
    public string Field { get; set; } // CreatedDate, SOPDate, etc.
    public DateTime? Start { get; set; }
    public DateTime? End { get; set; }
}

public class TeamFilter
{
    public string OriginatorId { get; set; }
    public string AccountManagerId { get; set; }
    public string SalesDirectorId { get; set; }
    public string CostEstimatorId { get; set; }
    public string EngineeringManagerId { get; set; }
}
```

### 4.6 驗證器

使用 FluentValidation 進行資料驗證：

```csharp
// 創建 CQR 命令驗證器
public class CreateCQRCommandValidator : AbstractValidator<CreateCQRCommand>
{
    public CreateCQRCommandValidator()
    {
        RuleFor(x => x.QuoteType)
            .IsInEnum()
            .WithMessage("Invalid quote type");
        
        RuleFor(x => x.Customer)
            .NotNull()
            .WithMessage("Customer information is required")
            .SetValidator(new CreateCustomerDtoValidator());
        
        RuleFor(x => x.Product)
            .NotNull()
            .WithMessage("Product information is required")
            .SetValidator(new CreateProductDtoValidator());
        
        When(x => !string.IsNullOrEmpty(x.ProjectNbr), () => {
            RuleFor(x => x.ProjectNbr)
                .Matches(@"^PRJ-\d{4}-\d{3}$")
                .WithMessage("Project number must be in format PRJ-YYYY-###");
        });
    }
}

// 客戶 DTO 驗證器
public class CreateCustomerDtoValidator : AbstractValidator<CreateCustomerDto>
{
    public CreateCustomerDtoValidator()
    {
        RuleFor(x => x.CustNbr)
            .NotEmpty()
            .WithMessage("Customer number is required")
            .MaximumLength(20)
            .WithMessage("Customer number cannot exceed 20 characters");
        
        RuleFor(x => x.CustName)
            .NotEmpty()
            .WithMessage("Customer name is required")
            .MaximumLength(200)
            .WithMessage("Customer name cannot exceed 200 characters");
        
        When(x => !string.IsNullOrEmpty(x.Email), () => {
            RuleFor(x => x.Email)
                .EmailAddress()
                .WithMessage("Invalid email format");
        });
    }
}

// 搜尋查詢驗證器
public class CQRSearchQueryValidator : AbstractValidator<CQRSearchQuery>
{
    public CQRSearchQueryValidator()
    {
        RuleFor(x => x.Page)
            .GreaterThan(0)
            .WithMessage("Page must be greater than 0");
        
        RuleFor(x => x.PageSize)
            .InclusiveBetween(1, 100)
            .WithMessage("Page size must be between 1 and 100");
        
        RuleFor(x => x.SortBy)
            .Must(BeValidSortField)
            .WithMessage("Invalid sort field");
        
        RuleFor(x => x.SortOrder)
            .Must(x => x == "asc" || x == "desc")
            .WithMessage("Sort order must be 'asc' or 'desc'");
        
        When(x => x.VolumeMin.HasValue && x.VolumeMax.HasValue, () => {
            RuleFor(x => x.VolumeMin)
                .LessThanOrEqualTo(x => x.VolumeMax)
                .WithMessage("Volume minimum must be less than or equal to maximum");
        });
        
        When(x => x.ValueMin.HasValue && x.ValueMax.HasValue, () => {
            RuleFor(x => x.ValueMin)
                .LessThanOrEqualTo(x => x.ValueMax)
                .WithMessage("Value minimum must be less than or equal to maximum");
        });
    }
    
    private bool BeValidSortField(string sortBy)
    {
        var validFields = new[] { 
            "CreatedDate", "ModifiedDate", "ProjectNbr", "Status", 
            "CustName", "ProductDesc", "VolumePerAnnum", "ApproxAnnualValue" 
        };
        
        return validFields.Contains(sortBy, StringComparer.OrdinalIgnoreCase);
    }
}
```

### 4.7 AutoMapper 設定檔

```csharp
// CQR 對映設定檔
public class CQRMappingProfile : Profile
{
    public CQRMappingProfile()
    {
        // Entity 到 DTO 對映
        CreateMap<CQRHeader, CQRDetailDto>()
            .ForMember(dest => dest.Customer, opt => opt.MapFrom(src => src.Customer))
            .ForMember(dest => dest.Product, opt => opt.MapFrom(src => src.Product))
            .ForMember(dest => dest.Business, opt => opt.MapFrom(src => src.Business))
            .ForMember(dest => dest.Dates, opt => opt.MapFrom(src => src.Dates))
            .ForMember(dest => dest.Team, opt => opt.MapFrom(src => src.Team))
            .ForMember(dest => dest.Quote, opt => opt.MapFrom(src => src.Quote))
            .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.Notes))
            .ForMember(dest => dest.Gates, opt => opt.MapFrom(src => src.Gates))
            .ForMember(dest => dest.Attachments, opt => opt.MapFrom(src => src.Attachments))
            .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src));
        
        CreateMap<CQRHeader, CQRSummaryDto>();
        
        // 值物件對映
        CreateMap<CustomerInfo, CustomerDto>();
        CreateMap<ProductInfo, ProductDto>();
        CreateMap<BusinessInfo, BusinessDto>();
        CreateMap<ProjectDates, ProjectDatesDto>();
        CreateMap<TeamAssignment, TeamAssignmentDto>();
        CreateMap<QuoteInfo, QuoteInfoDto>();
        CreateMap<ProjectNotes, ProjectNotesDto>();
        CreateMap<GateInfo, GateInfoDto>();
        
        // 命令到值物件對映
        CreateMap<CreateCustomerDto, CustomerInfo>();
        CreateMap<CreateProductDto, ProductInfo>();
        CreateMap<CreateBusinessDto, BusinessInfo>();
        CreateMap<CreateProjectDatesDto, ProjectDates>();
        
        // 附件對映
        CreateMap<AttachmentFile, AttachmentDto>();
        
        // 系統資訊對映
        CreateMap<CQRHeader, MetadataDto>()
            .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => src.CreatedDate))
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.ModifiedDate, opt => opt.MapFrom(src => src.ModifiedDate))
            .ForMember(dest => dest.ModifiedBy, opt => opt.MapFrom(src => src.ModifiedBy))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.IsLocked, opt => opt.MapFrom(src => src.IsLocked))
            .ForMember(dest => dest.LockedBy, opt => opt.MapFrom(src => src.LockedBy))
            .ForMember(dest => dest.LockedDate, opt => opt.MapFrom(src => src.LockedDate));
        
        // 列舉對映
        CreateMap<QuoteType, int>();
        CreateMap<int, QuoteType>();
    }
}
```

---

繼續撰寫剩餘的後端技術規格內容...

### 4.8 應用層服務協調

應用層負責協調不同的領域服務和基礎設施服務：

```csharp
// 複雜業務流程服務
public interface IWorkflowOrchestrationService
{
    Task ProcessCQRCreationAsync(int queueKey);
    Task ProcessStatusTransitionAsync(int queueKey, string newStatus, string userId);
    Task ProcessGateApprovalAsync(int queueKey, int gateNumber, string decision, string comments);
    Task ProcessTeamAssignmentAsync(int queueKey, string role, string userId);
}

public class WorkflowOrchestrationService : IWorkflowOrchestrationService
{
    private readonly IApplicationServices _appServices;
    private readonly INotificationService _notificationService;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly IAuditService _auditService;
    private readonly ILogger<WorkflowOrchestrationService> _logger;
    
    public WorkflowOrchestrationService(
        IApplicationServices appServices,
        INotificationService notificationService,
        IWorkflowEngine workflowEngine,
        IAuditService auditService,
        ILogger<WorkflowOrchestrationService> logger)
    {
        _appServices = appServices;
        _notificationService = notificationService;
        _workflowEngine = workflowEngine;
        _auditService = auditService;
        _logger = logger;
    }
    
    public async Task ProcessCQRCreationAsync(int queueKey)
    {
        _logger.LogInformation("Processing CQR creation workflow for QueueKey: {QueueKey}", queueKey);
        
        try
        {
            var cqr = await _appServices.CQRQueries.GetByIdAsync(queueKey);
            if (cqr == null)
            {
                throw new CQRNotFoundException(queueKey);
            }
            
            // 1. 記錄審計日誌
            await _auditService.LogCQRCreatedAsync(queueKey, cqr.Metadata.CreatedBy);
            
            // 2. 啟動工作流程
            await _workflowEngine.StartWorkflowAsync("CQR_Creation", new
            {
                QueueKey = queueKey,
                ProjectNbr = cqr.ProjectNbr,
                QuoteType = cqr.QuoteType,
                Value = cqr.Business?.ApproxAnnualValue,
                Originator = cqr.Team?.OriginatorId
            });
            
            // 3. 發送通知給相關人員
            await _notificationService.NotifyCQRCreatedAsync(queueKey);
            
            // 4. 根據業務規則自動分配初始審核人員
            await AssignInitialReviewersAsync(cqr);
            
            _logger.LogInformation("CQR creation workflow completed successfully for QueueKey: {QueueKey}", queueKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing CQR creation workflow for QueueKey: {QueueKey}", queueKey);
            throw;
        }
    }
    
    public async Task ProcessStatusTransitionAsync(int queueKey, string newStatus, string userId)
    {
        _logger.LogInformation("Processing status transition for QueueKey: {QueueKey} to status: {NewStatus}", 
            queueKey, newStatus);
        
        var cqr = await _appServices.CQRQueries.GetByIdAsync(queueKey);
        if (cqr == null)
        {
            throw new CQRNotFoundException(queueKey);
        }
        
        // 記錄狀態變更
        await _auditService.LogStatusChangeAsync(queueKey, cqr.Status, newStatus, userId);
        
        // 執行狀態特定的業務邏輯
        await ExecuteStatusSpecificActionsAsync(cqr, newStatus);
        
        // 觸發工作流程事件
        await _workflowEngine.TriggerEventAsync("StatusChanged", new
        {
            QueueKey = queueKey,
            OldStatus = cqr.Status,
            NewStatus = newStatus,
            UserId = userId
        });
        
        // 發送狀態變更通知
        await _notificationService.NotifyStatusChangedAsync(queueKey, cqr.Status, newStatus);
    }
    
    private async Task AssignInitialReviewersAsync(CQRDetailDto cqr)
    {
        // 根據報價類型和金額決定初始審核流程
        switch (cqr.QuoteType)
        {
            case 1: // 客戶 RFQ
                if (cqr.Business?.ApproxAnnualValue > 500000)
                {
                    // 高價值項目需要銷售總監初審
                    await _appServices.CQRCommands.AssignTeamMemberAsync(new AssignTeamMemberCommand
                    {
                        QueueKey = cqr.QueueKey,
                        Role = TeamRole.ReviewerSDir,
                        UserId = await GetSalesDirectorForCustomerAsync(cqr.Customer?.CustNbr)
                    });
                }
                break;
                
            case 2: // 內部或閘道
                // 直接分配給業務經理
                await _appServices.CQRCommands.AssignTeamMemberAsync(new AssignTeamMemberCommand
                {
                    QueueKey = cqr.QueueKey,
                    Role = TeamRole.ReviewerBMgr,
                    UserId = await GetBusinessManagerForProductAsync(cqr.Product?.Description)
                });
                break;
        }
    }
    
    private async Task ExecuteStatusSpecificActionsAsync(CQRDetailDto cqr, string newStatus)
    {
        switch (newStatus)
        {
            case "020100FR": // 已發布/需要財務協調員
                await _appServices.Excel.GenerateInitialQuoteTemplateAsync(cqr.QueueKey);
                break;
                
            case "030100FR": // 報價回應階段
                await _appServices.SapRFC.FetchLatestCostDataAsync(cqr.Product?.OEMPartNbr);
                break;
                
            case "040100FR": // 銷售審核階段  
                await _appServices.Excel.GenerateFinalQuoteReportAsync(cqr.QueueKey);
                break;
                
            case "090100FR": // 已結案
                await ArchiveCQRDataAsync(cqr.QueueKey);
                break;
        }
    }
}
```

---

## 基礎設施層

### 5.1 外部服務整合

#### 5.1.1 SAP RFC 服務

```csharp
// SAP RFC 服務介面
public interface ISapRFCService
{
    Task<SapConnectionResult> TestConnectionAsync();
    Task<List<SapMaterialDto>> GetMaterialDataAsync(string materialNumber);
    Task<List<SapCustomerDto>> GetCustomerDataAsync(string customerNumber);
    Task<SapCostDataDto> GetCostDataAsync(string partNumber, string plant);
    Task<List<SapPricingDto>> GetPricingHistoryAsync(string customerNumber, string materialNumber);
    Task<bool> CreateQuoteInSapAsync(SapQuoteRequestDto request);
}

// SAP RFC 服務實現
public class SapRFCService : ISapRFCService
{
    private readonly SapConfig _config;
    private readonly ILogger<SapRFCService> _logger;
    private readonly ICacheService _cache;
    
    public SapRFCService(
        IOptions<SapConfig> config,
        ILogger<SapRFCService> logger,
        ICacheService cache)
    {
        _config = config.Value;
        _logger = logger;
        _cache = cache;
    }
    
    public async Task<SapConnectionResult> TestConnectionAsync()
    {
        try
        {
            using var destination = RfcDestinationManager.GetDestination(_config.DestinationName);
            using var repository = destination.Repository;
            
            var pingFunction = repository.CreateFunction("RFC_PING");
            pingFunction.Invoke(destination);
            
            return new SapConnectionResult
            {
                IsConnected = true,
                ResponseTime = DateTime.UtcNow,
                SystemInfo = new SapSystemInfoDto
                {
                    SystemId = destination.SystemId,
                    Client = destination.Client,
                    ApplicationServer = destination.ApplicationServer
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SAP connection test failed");
            return new SapConnectionResult
            {
                IsConnected = false,
                Error = ex.Message
            };
        }
    }
    
    public async Task<List<SapMaterialDto>> GetMaterialDataAsync(string materialNumber)
    {
        var cacheKey = $"sap_material_{materialNumber}";
        
        // 嘗試從快取取得
        var cached = await _cache.GetAsync<List<SapMaterialDto>>(cacheKey);
        if (cached != null)
        {
            _logger.LogDebug("Material data retrieved from cache for: {MaterialNumber}", materialNumber);
            return cached;
        }
        
        try
        {
            using var destination = RfcDestinationManager.GetDestination(_config.DestinationName);
            using var repository = destination.Repository;
            
            var function = repository.CreateFunction("BAPI_MATERIAL_GET_DETAIL");
            function.SetValue("MATERIAL", materialNumber);
            function.SetValue("PLANT", _config.DefaultPlant);
            
            function.Invoke(destination);
            
            var materialData = function.GetStructure("MATERIAL_GENERAL_DATA");
            var result = new List<SapMaterialDto>
            {
                new SapMaterialDto
                {
                    MaterialNumber = materialData.GetString("MATERIAL"),
                    Description = materialData.GetString("MATL_DESC"),
                    MaterialType = materialData.GetString("MATL_TYPE"),
                    BaseUnit = materialData.GetString("BASE_UOM"),
                    MaterialGroup = materialData.GetString("MATL_GROUP"),
                    CreatedDate = materialData.GetString("CREATED_ON"),
                    CreatedBy = materialData.GetString("CREATED_BY")
                }
            };
            
            // 快取結果
            await _cache.SetAsync(cacheKey, result, TimeSpan.FromHours(24));
            
            _logger.LogInformation("Retrieved material data from SAP for: {MaterialNumber}", materialNumber);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving material data from SAP for: {MaterialNumber}", materialNumber);
            throw new SapIntegrationException($"Failed to retrieve material data: {ex.Message}", ex);
        }
    }
    
    public async Task<SapCostDataDto> GetCostDataAsync(string partNumber, string plant)
    {
        var cacheKey = $"sap_cost_{partNumber}_{plant}";
        
        var cached = await _cache.GetAsync<SapCostDataDto>(cacheKey);
        if (cached != null && cached.ValidUntil > DateTime.UtcNow)
        {
            return cached;
        }
        
        try
        {
            using var destination = RfcDestinationManager.GetDestination(_config.DestinationName);
            using var repository = destination.Repository;
            
            var function = repository.CreateFunction("CKML_Y_GET_COST_ESTIMATE");
            function.SetValue("MATERIAL", partNumber);
            function.SetValue("PLANT", plant);
            function.SetValue("COSTING_DATE", DateTime.Now.ToString("yyyyMMdd"));
            
            function.Invoke(destination);
            
            var costTable = function.GetTable("ET_COST_COMPONENT");
            
            var result = new SapCostDataDto
            {
                PartNumber = partNumber,
                Plant = plant,
                CostingDate = DateTime.UtcNow,
                ValidUntil = DateTime.UtcNow.AddDays(30),
                TotalCost = 0,
                CostComponents = new List<SapCostComponentDto>()
            };
            
            foreach (var row in costTable)
            {
                var component = new SapCostComponentDto
                {
                    ComponentType = row.GetString("COST_COMP_TYPE"),
                    Description = row.GetString("COST_COMP_DESC"),
                    Amount = row.GetDecimal("AMOUNT"),
                    Currency = row.GetString("CURRENCY"),
                    Percentage = row.GetDecimal("PERCENTAGE")
                };
                
                result.CostComponents.Add(component);
                result.TotalCost += component.Amount;
            }
            
            result.Currency = result.CostComponents.FirstOrDefault()?.Currency ?? "USD";
            
            // 快取成本數據
            await _cache.SetAsync(cacheKey, result, TimeSpan.FromDays(1));
            
            _logger.LogInformation("Retrieved cost data from SAP for part: {PartNumber}", partNumber);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cost data from SAP for part: {PartNumber}", partNumber);
            throw new SapIntegrationException($"Failed to retrieve cost data: {ex.Message}", ex);
        }
    }
}

// SAP 設定類別
public class SapConfig
{
    public string DestinationName { get; set; } = "CQR_SAP";
    public string ApplicationServer { get; set; }
    public string SystemNumber { get; set; }
    public string Client { get; set; }
    public string Username { get; set; }
    public string Password { get; set; }
    public string Language { get; set; } = "EN";
    public string DefaultPlant { get; set; }
    public int ConnectionTimeout { get; set; } = 30000;
    public int MaxConnections { get; set; } = 10;
    public bool EnableTracing { get; set; } = false;
}
```

#### 5.1.2 Excel 服務

```csharp
// Excel 服務介面
public interface IExcelService
{
    Task<byte[]> GenerateQuoteReportAsync(int queueKey, string templateName = "QuoteTemplate");
    Task<byte[]> ExportCQRListAsync(List<CQRSummaryDto> cqrList);
    Task<ExcelValidationResult> ValidateExcelDataAsync(Stream excelStream);
    Task<List<CQRImportDto>> ImportCQRDataAsync(Stream excelStream);
    Task<byte[]> GenerateDashboardReportAsync(DashboardReportRequest request);
}

// Excel 服務實現
public class ExcelService : IExcelService
{
    private readonly IApplicationServices _appServices;
    private readonly IFileStorageService _fileStorage;
    private readonly ILogger<ExcelService> _logger;
    private readonly string _templatePath;
    
    public ExcelService(
        IApplicationServices appServices,
        IFileStorageService fileStorage,
        ILogger<ExcelService> logger,
        IConfiguration configuration)
    {
        _appServices = appServices;
        _fileStorage = fileStorage;
        _logger = logger;
        _templatePath = configuration.GetValue<string>("Excel:TemplatePath");
    }
    
    public async Task<byte[]> GenerateQuoteReportAsync(int queueKey, string templateName = "QuoteTemplate")
    {
        _logger.LogInformation("Generating quote report for CQR: {QueueKey}", queueKey);
        
        try
        {
            // 取得 CQR 資料
            var cqr = await _appServices.CQRQueries.GetByIdAsync(queueKey);
            if (cqr == null)
            {
                throw new CQRNotFoundException(queueKey);
            }
            
            // 載入 Excel 範本
            var templatePath = Path.Combine(_templatePath, $"{templateName}.xlsx");
            if (!File.Exists(templatePath))
            {
                throw new FileNotFoundException($"Template not found: {templateName}");
            }
            
            using var workbook = new XLWorkbook(templatePath);
            var worksheet = workbook.Worksheet(1);
            
            // 填入基本資訊
            PopulateBasicInfo(worksheet, cqr);
            
            // 填入客戶資訊
            PopulateCustomerInfo(worksheet, cqr.Customer);
            
            // 填入產品資訊
            PopulateProductInfo(worksheet, cqr.Product);
            
            // 填入業務資訊
            PopulateBusinessInfo(worksheet, cqr.Business);
            
            // 填入報價資訊
            PopulateQuoteInfo(worksheet, cqr.Quote);
            
            // 填入成本分解（如果有 SAP 資料）
            await PopulateCostBreakdownAsync(worksheet, cqr);
            
            // 產生圖表
            GenerateCharts(worksheet, cqr);
            
            // 儲存為記憶體流
            using var stream = new MemoryStream();
            workbook.SaveAs(stream);
            
            _logger.LogInformation("Quote report generated successfully for CQR: {QueueKey}", queueKey);
            
            return stream.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating quote report for CQR: {QueueKey}", queueKey);
            throw;
        }
    }
    
    private void PopulateBasicInfo(IXLWorksheet worksheet, CQRDetailDto cqr)
    {
        worksheet.Cell("B3").Value = cqr.ProjectNbr;
        worksheet.Cell("B4").Value = cqr.RevNbr;
        worksheet.Cell("B5").Value = cqr.StatusDesc;
        worksheet.Cell("B6").Value = GetQuoteTypeDescription(cqr.QuoteType);
        worksheet.Cell("B7").Value = cqr.Metadata.CreatedDate.ToString("yyyy-MM-dd");
        worksheet.Cell("B8").Value = cqr.Team?.OriginatorName;
    }
    
    private void PopulateCustomerInfo(IXLWorksheet worksheet, CustomerDto customer)
    {
        if (customer == null) return;
        
        worksheet.Cell("E3").Value = customer.CustNbr;
        worksheet.Cell("E4").Value = customer.CustName;
        worksheet.Cell("E5").Value = customer.BuyerName;
        worksheet.Cell("E6").Value = customer.Contact;
        worksheet.Cell("E7").Value = customer.Email;
        worksheet.Cell("E8").Value = customer.Phone;
    }
    
    private void PopulateProductInfo(IXLWorksheet worksheet, ProductDto product)
    {
        if (product == null) return;
        
        worksheet.Cell("B11").Value = product.Description;
        worksheet.Cell("B12").Value = product.Vehicle;
        worksheet.Cell("B13").Value = product.ModelYear;
        worksheet.Cell("B14").Value = product.Platform;
        worksheet.Cell("E11").Value = product.OEMPartNbr;
        worksheet.Cell("E12").Value = product.CustomerPartNbr;
        worksheet.Cell("E13").Value = product.TRWPartNbr;
    }
    
    private async Task PopulateCostBreakdownAsync(IXLWorksheet worksheet, CQRDetailDto cqr)
    {
        try
        {
            if (string.IsNullOrEmpty(cqr.Product?.TRWPartNbr)) return;
            
            var costData = await _appServices.SapRFC.GetCostDataAsync(
                cqr.Product.TRWPartNbr, 
                "P001"); // 預設工廠
            
            if (costData?.CostComponents != null)
            {
                var startRow = 20;
                var row = startRow;
                
                foreach (var component in costData.CostComponents)
                {
                    worksheet.Cell(row, 1).Value = component.Description;
                    worksheet.Cell(row, 2).Value = component.Amount;
                    worksheet.Cell(row, 3).Value = component.Currency;
                    worksheet.Cell(row, 4).Value = component.Percentage;
                    row++;
                }
                
                // 總計
                worksheet.Cell(row + 1, 1).Value = "Total Cost";
                worksheet.Cell(row + 1, 2).Value = costData.TotalCost;
                worksheet.Cell(row + 1, 2).Style.Font.Bold = true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not retrieve cost data for CQR: {QueueKey}", cqr.QueueKey);
            // 不影響報表產生，只記錄警告
        }
    }
    
    private void GenerateCharts(IXLWorksheet worksheet, CQRDetailDto cqr)
    {
        // 使用 ClosedXML 產生圖表
        if (cqr.Quote != null && cqr.Quote.InternalPrice > 0)
        {
            // 利潤分析圓餅圖
            var chartData = new[]
            {
                new { Category = "Cost", Value = cqr.Quote.InternalPrice },
                new { Category = "Profit", Value = cqr.Quote.ProfitMargin ?? 0 }
            };
            
            // 這裡可以擴展更複雜的圖表邏輯
        }
    }
}

// Excel 驗證結果
public class ExcelValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public int TotalRows { get; set; }
    public int ValidRows { get; set; }
}

// Excel 匯入 DTO
public class CQRImportDto
{
    public string ProjectNbr { get; set; }
    public string CustNbr { get; set; }
    public string CustName { get; set; }
    public string ProductDesc { get; set; }
    public decimal? VolumePerAnnum { get; set; }
    public decimal? ApproxAnnualValue { get; set; }
    public DateTime? SOPDate { get; set; }
    public DateTime? EOPDate { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
}
```

#### 5.1.3 檔案存儲服務

```csharp
// 檔案存儲服務介面
public interface IFileStorageService
{
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string category = null);
    Task<Stream> DownloadFileAsync(string filePath);
    Task<bool> DeleteFileAsync(string filePath);
    Task<bool> FileExistsAsync(string filePath);
    Task<FileInfoDto> GetFileInfoAsync(string filePath);
    Task<List<FileInfoDto>> ListFilesAsync(string directory, string searchPattern = "*");
    Task<string> GeneratePreviewAsync(string filePath, PreviewOptions options);
}

// 檔案存儲服務實現
public class FileStorageService : IFileStorageService
{
    private readonly FileStorageConfig _config;
    private readonly ILogger<FileStorageService> _logger;
    private readonly IAntivirusService _antivirusService;
    
    public FileStorageService(
        IOptions<FileStorageConfig> config,
        ILogger<FileStorageService> logger,
        IAntivirusService antivirusService)
    {
        _config = config.Value;
        _logger = logger;
        _antivirusService = antivirusService;
    }
    
    public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string category = null)
    {
        if (fileStream == null) throw new ArgumentNullException(nameof(fileStream));
        if (string.IsNullOrEmpty(fileName)) throw new ArgumentException("File name is required", nameof(fileName));
        
        _logger.LogInformation("Uploading file: {FileName}, Category: {Category}", fileName, category);
        
        try
        {
            // 檢查檔案大小
            if (fileStream.Length > _config.MaxFileSize)
            {
                throw new FileTooLargeException($"File size {fileStream.Length} exceeds maximum allowed size {_config.MaxFileSize}");
            }
            
            // 檢查檔案類型
            var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();
            if (!_config.AllowedExtensions.Contains(fileExtension))
            {
                throw new UnsupportedFileTypeException($"File type {fileExtension} is not allowed");
            }
            
            // 病毒掃描
            fileStream.Seek(0, SeekOrigin.Begin);
            var scanResult = await _antivirusService.ScanAsync(fileStream);
            if (!scanResult.IsClean)
            {
                throw new VirusDetectedException($"Virus detected: {scanResult.ThreatName}");
            }
            
            // 產生儲存路徑
            var storagePath = GenerateStoragePath(fileName, category);
            var fullPath = Path.Combine(_config.BasePath, storagePath);
            
            // 確保目錄存在
            var directory = Path.GetDirectoryName(fullPath);
            Directory.CreateDirectory(directory);
            
            // 儲存檔案
            fileStream.Seek(0, SeekOrigin.Begin);
            using (var fileOutputStream = File.Create(fullPath))
            {
                await fileStream.CopyToAsync(fileOutputStream);
            }
            
            // 產生縮圖（如果是圖片）
            if (IsImageFile(fileExtension))
            {
                await GenerateThumbnailAsync(fullPath);
            }
            
            _logger.LogInformation("File uploaded successfully to: {StoragePath}", storagePath);
            
            return storagePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file: {FileName}", fileName);
            throw;
        }
    }
    
    public async Task<Stream> DownloadFileAsync(string filePath)
    {
        if (string.IsNullOrEmpty(filePath)) throw new ArgumentException("File path is required", nameof(filePath));
        
        var fullPath = Path.Combine(_config.BasePath, filePath);
        
        if (!File.Exists(fullPath))
        {
            throw new FileNotFoundException($"File not found: {filePath}");
        }
        
        _logger.LogDebug("Downloading file: {FilePath}", filePath);
        
        return File.OpenRead(fullPath);
    }
    
    public async Task<string> GeneratePreviewAsync(string filePath, PreviewOptions options)
    {
        var fullPath = Path.Combine(_config.BasePath, filePath);
        
        if (!File.Exists(fullPath))
        {
            throw new FileNotFoundException($"File not found: {filePath}");
        }
        
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        
        switch (extension)
        {
            case ".jpg":
            case ".jpeg":
            case ".png":
            case ".gif":
                return await GenerateImagePreviewAsync(fullPath, options);
                
            case ".pdf":
                return await GeneratePdfPreviewAsync(fullPath, options);
                
            case ".xlsx":
            case ".xls":
                return await GenerateExcelPreviewAsync(fullPath, options);
                
            default:
                throw new UnsupportedPreviewTypeException($"Preview not supported for file type: {extension}");
        }
    }
    
    private string GenerateStoragePath(string fileName, string category)
    {
        var year = DateTime.UtcNow.Year;
        var month = DateTime.UtcNow.Month;
        var day = DateTime.UtcNow.Day;
        
        var safeFileName = Path.GetFileNameWithoutExtension(fileName).Replace(" ", "_");
        var extension = Path.GetExtension(fileName);
        var uniqueId = Guid.NewGuid().ToString("N")[..8];
        
        var categoryPath = string.IsNullOrEmpty(category) ? "general" : category.ToLowerInvariant();
        
        return Path.Combine(categoryPath, year.ToString(), month.ToString("D2"), day.ToString("D2"), $"{safeFileName}_{uniqueId}{extension}");
    }
    
    private async Task GenerateThumbnailAsync(string imagePath)
    {
        try
        {
            var thumbnailPath = Path.ChangeExtension(imagePath, ".thumb.jpg");
            
            using var image = await Image.LoadAsync(imagePath);
            image.Mutate(x => x.Resize(new ResizeOptions
            {
                Size = new Size(200, 200),
                Mode = ResizeMode.Max
            }));
            
            await image.SaveAsJpegAsync(thumbnailPath);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to generate thumbnail for: {ImagePath}", imagePath);
        }
    }
}

// 檔案存儲設定
public class FileStorageConfig
{
    public string BasePath { get; set; } = "wwwroot/uploads";
    public long MaxFileSize { get; set; } = 50 * 1024 * 1024; // 50MB
    public HashSet<string> AllowedExtensions { get; set; } = new()
    {
        ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", 
        ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".zip", ".rar"
    };
    public bool EnableThumbnails { get; set; } = true;
    public bool EnablePreviews { get; set; } = true;
    public int ThumbnailSize { get; set; } = 200;
}
```

### 5.2 快取服務

```csharp
// 快取服務介面
public interface ICacheService
{
    Task<T> GetAsync<T>(string key) where T : class;
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
    Task RemoveAsync(string key);
    Task RemovePatternAsync(string pattern);
    Task<bool> ExistsAsync(string key);
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getItem, TimeSpan? expiration = null) where T : class;
}

// Redis 快取服務實現
public class RedisCacheService : ICacheService
{
    private readonly IDatabase _database;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    
    public RedisCacheService(IConnectionMultiplexer redis, ILogger<RedisCacheService> logger)
    {
        _database = redis.GetDatabase();
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
    }
    
    public async Task<T> GetAsync<T>(string key) where T : class
    {
        try
        {
            var value = await _database.StringGetAsync(key);
            if (!value.HasValue)
                return null;
            
            return JsonSerializer.Deserialize<T>(value, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache value for key: {Key}", key);
            return null;
        }
    }
    
    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
    {
        try
        {
            var json = JsonSerializer.Serialize(value, _jsonOptions);
            await _database.StringSetAsync(key, json, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache value for key: {Key}", key);
        }
    }
    
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getItem, TimeSpan? expiration = null) where T : class
    {
        var cachedValue = await GetAsync<T>(key);
        if (cachedValue != null)
            return cachedValue;
        
        var item = await getItem();
        if (item != null)
            await SetAsync(key, item, expiration);
        
        return item;
    }
    
    // 其他快取方法的實現...
}
```

---

繼續撰寫 API 層和其他重要組件的技術規格...

## API 層設計

### 6.1 控制器基底類別

```csharp
// 基底控制器
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public abstract class BaseController : ControllerBase
{
    protected readonly IApplicationServices AppServices;
    protected readonly ICurrentUserService CurrentUser;
    protected readonly ILogger Logger;
    
    protected BaseController(
        IApplicationServices appServices,
        ICurrentUserService currentUser,
        ILogger logger)
    {
        AppServices = appServices;
        CurrentUser = currentUser;
        Logger = logger;
    }
    
    // 統一的響應格式
    protected IActionResult Ok<T>(T data, string message = "Success")
    {
        return Ok(new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message,
            Timestamp = DateTime.UtcNow,
            TraceId = HttpContext.TraceIdentifier
        });
    }
    
    protected IActionResult Error(string message, object details = null, int statusCode = 400)
    {
        return StatusCode(statusCode, new ApiResponse
        {
            Success = false,
            Error = new ApiError
            {
                Code = GetErrorCode(statusCode),
                Message = message,
                Details = details
            },
            Timestamp = DateTime.UtcNow,
            TraceId = HttpContext.TraceIdentifier
        });
    }
    
    protected IActionResult ValidationError(IEnumerable<ValidationFailure> failures)
    {
        var errors = failures.Select(f => new ValidationErrorDetail
        {
            Field = f.PropertyName,
            Code = f.ErrorCode,
            Message = f.ErrorMessage
        }).ToList();
        
        return BadRequest(new ApiResponse
        {
            Success = false,
            Error = new ApiError
            {
                Code = "VALIDATION_ERROR",
                Message = "Request validation failed",
                Details = errors
            },
            Timestamp = DateTime.UtcNow,
            TraceId = HttpContext.TraceIdentifier
        });
    }
    
    private string GetErrorCode(int statusCode)
    {
        return statusCode switch
        {
            400 => "BAD_REQUEST",
            401 => "UNAUTHORIZED",
            403 => "FORBIDDEN",
            404 => "NOT_FOUND",
            409 => "CONFLICT",
            422 => "UNPROCESSABLE_ENTITY",
            500 => "INTERNAL_SERVER_ERROR",
            _ => "UNKNOWN_ERROR"
        };
    }
}

// API 響應格式
public class ApiResponse
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public ApiError Error { get; set; }
    public DateTime Timestamp { get; set; }
    public string TraceId { get; set; }
}

public class ApiResponse<T> : ApiResponse
{
    public T Data { get; set; }
}

public class ApiError
{
    public string Code { get; set; }
    public string Message { get; set; }
    public object Details { get; set; }
}
```

### 6.2 CQR 控制器

```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
[ApiVersion("1.0")]
public class CQRsController : BaseController
{
    public CQRsController(
        IApplicationServices appServices,
        ICurrentUserService currentUser,
        ILogger<CQRsController> logger) : base(appServices, currentUser, logger)
    {
    }
    
    /// <summary>
    /// 獲取 CQR 列表
    /// </summary>
    /// <param name="query">查詢參數</param>
    /// <returns>CQR 列表</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<CQRSummaryDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetCQRs([FromQuery] CQRSearchQuery query)
    {
        Logger.LogDebug("Getting CQRs with query: {@Query}", query);
        
        // 驗證查詢參數
        var validator = new CQRSearchQueryValidator();
        var validationResult = await validator.ValidateAsync(query);
        
        if (!validationResult.IsValid)
        {
            return ValidationError(validationResult.Errors);
        }
        
        var result = await AppServices.CQRQueries.GetPagedAsync(query);
        
        return Ok(result, $"Retrieved {result.Items.Count} CQRs out of {result.TotalCount} total");
    }
    
    /// <summary>
    /// 根據 ID 獲取單一 CQR
    /// </summary>
    /// <param name="id">CQR ID</param>
    /// <returns>CQR 詳細資訊</returns>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse<CQRDetailDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetCQR(int id)
    {
        Logger.LogDebug("Getting CQR with ID: {Id}", id);
        
        var cqr = await AppServices.CQRQueries.GetByIdAsync(id);
        if (cqr == null)
        {
            return NotFound($"CQR with ID {id} not found");
        }
        
        return Ok(cqr);
    }
    
    /// <summary>
    /// 創建新 CQR
    /// </summary>
    /// <param name="command">創建 CQR 命令</param>
    /// <returns>新創建的 CQR ID</returns>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [Authorize(Policy = "CanCreateCQR")]
    public async Task<IActionResult> CreateCQR([FromBody] CreateCQRCommand command)
    {
        Logger.LogInformation("Creating new CQR for user: {UserId}", CurrentUser.UserId);
        
        // 驗證命令
        var validator = new CreateCQRCommandValidator();
        var validationResult = await validator.ValidateAsync(command);
        
        if (!validationResult.IsValid)
        {
            return ValidationError(validationResult.Errors);
        }
        
        try
        {
            var queueKey = await AppServices.CQRCommands.CreateCQRAsync(command);
            
            var location = Url.Action(nameof(GetCQR), new { id = queueKey });
            
            return Created(location, new ApiResponse<int>
            {
                Success = true,
                Data = queueKey,
                Message = "CQR created successfully",
                Timestamp = DateTime.UtcNow,
                TraceId = HttpContext.TraceIdentifier
            });
        }
        catch (DuplicateProjectNumberException ex)
        {
            return Conflict(ex.Message);
        }
        catch (BusinessRuleViolationException ex)
        {
            return UnprocessableEntity(ex.Message);
        }
    }
    
    /// <summary>
    /// 更新 CQR
    /// </summary>
    /// <param name="id">CQR ID</param>
    /// <param name="command">更新 CQR 命令</param>
    /// <returns>更新結果</returns>
    [HttpPut("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateCQR(int id, [FromBody] UpdateCQRCommand command)
    {
        Logger.LogInformation("Updating CQR {Id} by user: {UserId}", id, CurrentUser.UserId);
        
        if (id != command.QueueKey)
        {
            return BadRequest("ID mismatch between route and command");
        }
        
        var validator = new UpdateCQRCommandValidator();
        var validationResult = await validator.ValidateAsync(command);
        
        if (!validationResult.IsValid)
        {
            return ValidationError(validationResult.Errors);
        }
        
        try
        {
            await AppServices.CQRCommands.UpdateCQRAsync(command);
            return Ok("CQR updated successfully");
        }
        catch (CQRNotFoundException)
        {
            return NotFound($"CQR with ID {id} not found");
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("You don't have permission to update this CQR");
        }
    }
    
    /// <summary>
    /// 更新 CQR 狀態
    /// </summary>
    /// <param name="id">CQR ID</param>
    /// <param name="command">狀態更新命令</param>
    /// <returns>更新結果</returns>
    [HttpPut("{id:int}/status")]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> UpdateStatus(int id, [FromBody] UpdateStatusCommand command)
    {
        Logger.LogInformation("Updating status for CQR {Id} to {NewStatus}", id, command.NewStatus);
        
        if (id != command.QueueKey)
        {
            return BadRequest("ID mismatch between route and command");
        }
        
        try
        {
            await AppServices.CQRCommands.UpdateStatusAsync(command);
            return Ok($"CQR status updated to {command.NewStatus}");
        }
        catch (CQRNotFoundException)
        {
            return NotFound($"CQR with ID {id} not found");
        }
        catch (InvalidStatusTransitionException ex)
        {
            return BadRequest($"Invalid status transition: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 進階搜尋 CQR
    /// </summary>
    /// <param name="query">進階搜尋查詢</param>
    /// <returns>CQR 列表</returns>
    [HttpPost("search")]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<CQRSummaryDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> AdvancedSearch([FromBody] CQRAdvancedSearchQuery query)
    {
        Logger.LogDebug("Performing advanced search with query: {@Query}", query);
        
        var validator = new CQRAdvancedSearchQueryValidator();
        var validationResult = await validator.ValidateAsync(query);
        
        if (!validationResult.IsValid)
        {
            return ValidationError(validationResult.Errors);
        }
        
        // 轉換為內部查詢格式
        var searchQuery = MapToSearchQuery(query);
        var result = await AppServices.CQRQueries.GetPagedAsync(searchQuery);
        
        return Ok(result);
    }
    
    /// <summary>
    /// 複製 CQR
    /// </summary>
    /// <param name="id">來源 CQR ID</param>
    /// <param name="command">複製命令</param>
    /// <returns>新 CQR ID</returns>
    [HttpPost("{id:int}/copy")]
    [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status201Created)]
    [Authorize(Policy = "CanCreateCQR")]
    public async Task<IActionResult> CopyCQR(int id, [FromBody] CopyCQRCommand command)
    {
        Logger.LogInformation("Copying CQR {Id} by user: {UserId}", id, CurrentUser.UserId);
        
        if (id != command.SourceQueueKey)
        {
            return BadRequest("ID mismatch between route and command");
        }
        
        try
        {
            var newQueueKey = await AppServices.CQRCommands.CopyCQRAsync(command);
            
            var location = Url.Action(nameof(GetCQR), new { id = newQueueKey });
            return Created(location, newQueueKey);
        }
        catch (CQRNotFoundException)
        {
            return NotFound($"Source CQR with ID {id} not found");
        }
    }
    
    /// <summary>
    /// 創建 CQR 修訂版本
    /// </summary>
    /// <param name="id">原始 CQR ID</param>
    /// <param name="command">修訂命令</param>
    /// <returns>新修訂版本 ID</returns>
    [HttpPost("{id:int}/revise")]
    [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status201Created)]
    public async Task<IActionResult> ReviseCQR(int id, [FromBody] CreateRevisionCommand command)
    {
        Logger.LogInformation("Creating revision for CQR {Id}", id);
        
        if (id != command.OriginalQueueKey)
        {
            return BadRequest("ID mismatch between route and command");
        }
        
        try
        {
            var revisionQueueKey = await AppServices.CQRCommands.CreateRevisionAsync(command);
            
            var location = Url.Action(nameof(GetCQR), new { id = revisionQueueKey });
            return Created(location, revisionQueueKey);
        }
        catch (CQRNotFoundException)
        {
            return NotFound($"Original CQR with ID {id} not found");
        }
    }
    
    /// <summary>
    /// 獲取 CQR 歷史記錄
    /// </summary>
    /// <param name="id">CQR ID</param>
    /// <returns>歷史記錄列表</returns>
    [HttpGet("{id:int}/history")]
    [ProducesResponseType(typeof(ApiResponse<List<CQRHistoryDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetHistory(int id)
    {
        var history = await AppServices.CQRQueries.GetHistoryAsync(id);
        return Ok(history);
    }
    
    /// <summary>
    /// 刪除 CQR
    /// </summary>
    /// <param name="id">CQR ID</param>
    /// <returns>刪除結果</returns>
    [HttpDelete("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    [Authorize(Policy = "CanDeleteCQR")]
    public async Task<IActionResult> DeleteCQR(int id)
    {
        Logger.LogWarning("Deleting CQR {Id} by user: {UserId}", id, CurrentUser.UserId);
        
        try
        {
            await AppServices.CQRCommands.DeleteCQRAsync(id);
            return Ok("CQR deleted successfully");
        }
        catch (CQRNotFoundException)
        {
            return NotFound($"CQR with ID {id} not found");
        }
    }
    
    // 輔助方法
    private CQRSearchQuery MapToSearchQuery(CQRAdvancedSearchQuery query)
    {
        return new CQRSearchQuery
        {
            Page = query.Pagination?.Page ?? 1,
            PageSize = query.Pagination?.PageSize ?? 20,
            Status = string.Join(",", query.Status ?? new List<string>()),
            CustNbr = query.CustNbr,
            ProductDesc = query.ProductDesc,
            VolumeMin = query.VolumeRange?.Min,
            VolumeMax = query.VolumeRange?.Max,
            ValueMin = query.ValueRange?.Min,
            ValueMax = query.ValueRange?.Max,
            CreatedDateFrom = query.DateRange?.Start,
            CreatedDateTo = query.DateRange?.End,
            OriginatorId = query.Team?.OriginatorId,
            SortBy = query.Sorting?.Field ?? "CreatedDate",
            SortOrder = query.Sorting?.Order ?? "desc"
        };
    }
}
```

### 6.3 附件控制器

```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AttachmentsController : BaseController
{
    public AttachmentsController(
        IApplicationServices appServices,
        ICurrentUserService currentUser,
        ILogger<AttachmentsController> logger) : base(appServices, currentUser, logger)
    {
    }
    
    /// <summary>
    /// 上傳附件
    /// </summary>
    /// <param name="file">檔案</param>
    /// <param name="queueKey">CQR ID</param>
    /// <param name="category">分類</param>
    /// <param name="description">描述</param>
    /// <returns>上傳結果</returns>
    [HttpPost("upload")]
    [ProducesResponseType(typeof(ApiResponse<AttachmentDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [RequestSizeLimit(50 * 1024 * 1024)] // 50MB
    public async Task<IActionResult> UploadFile(
        IFormFile file,
        [FromForm] int queueKey,
        [FromForm] string category = null,
        [FromForm] string description = null)
    {
        if (file == null || file.Length == 0)
        {
            return BadRequest("No file provided");
        }
        
        Logger.LogInformation("Uploading file {FileName} for CQR {QueueKey}", file.FileName, queueKey);
        
        try
        {
            using var stream = file.OpenReadStream();
            
            var uploadRequest = new UploadAttachmentCommand
            {
                QueueKey = queueKey,
                FileName = file.FileName,
                FileStream = stream,
                ContentType = file.ContentType,
                Category = category,
                Description = description
            };
            
            var attachment = await AppServices.Attachments.UploadAsync(uploadRequest);
            
            return Created($"/api/attachments/{attachment.AttachKey}", attachment);
        }
        catch (CQRNotFoundException)
        {
            return NotFound($"CQR with ID {queueKey} not found");
        }
        catch (UnsupportedFileTypeException ex)
        {
            return BadRequest($"Unsupported file type: {ex.Message}");
        }
        catch (FileTooLargeException ex)
        {
            return BadRequest($"File too large: {ex.Message}");
        }
        catch (VirusDetectedException ex)
        {
            return BadRequest($"File contains virus: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 下載附件
    /// </summary>
    /// <param name="id">附件 ID</param>
    /// <returns>檔案內容</returns>
    [HttpGet("{id:int}/download")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DownloadFile(int id)
    {
        Logger.LogDebug("Downloading attachment {Id}", id);
        
        try
        {
            var downloadResult = await AppServices.Attachments.DownloadAsync(id);
            
            return File(
                downloadResult.FileStream,
                downloadResult.ContentType,
                downloadResult.FileName,
                enableRangeProcessing: true);
        }
        catch (AttachmentNotFoundException)
        {
            return NotFound($"Attachment with ID {id} not found");
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("You don't have permission to download this attachment");
        }
    }
    
    /// <summary>
    /// 獲取 CQR 的所有附件
    /// </summary>
    /// <param name="cqrId">CQR ID</param>
    /// <param name="category">篩選分類</param>
    /// <returns>附件列表</returns>
    [HttpGet("cqr/{cqrId:int}")]
    [ProducesResponseType(typeof(ApiResponse<List<AttachmentDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetCQRAttachments(int cqrId, [FromQuery] string category = null)
    {
        var attachments = await AppServices.Attachments.GetByCQRIdAsync(cqrId, category);
        return Ok(attachments);
    }
    
    /// <summary>
    /// 預覽附件
    /// </summary>
    /// <param name="id">附件 ID</param>
    /// <param name="options">預覽選項</param>
    /// <returns>預覽內容</returns>
    [HttpGet("{id:int}/preview")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> PreviewFile(int id, [FromQuery] PreviewOptions options)
    {
        try
        {
            var previewResult = await AppServices.Attachments.GeneratePreviewAsync(id, options);
            
            return File(
                previewResult.Content,
                previewResult.ContentType,
                previewResult.FileName);
        }
        catch (AttachmentNotFoundException)
        {
            return NotFound($"Attachment with ID {id} not found");
        }
        catch (UnsupportedPreviewTypeException ex)
        {
            return BadRequest($"Preview not supported: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 刪除附件
    /// </summary>
    /// <param name="id">附件 ID</param>
    /// <returns>刪除結果</returns>
    [HttpDelete("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    [Authorize(Policy = "CanDeleteAttachment")]
    public async Task<IActionResult> DeleteAttachment(int id)
    {
        Logger.LogWarning("Deleting attachment {Id} by user: {UserId}", id, CurrentUser.UserId);
        
        try
        {
            await AppServices.Attachments.DeleteAsync(id);
            return Ok("Attachment deleted successfully");
        }
        catch (AttachmentNotFoundException)
        {
            return NotFound($"Attachment with ID {id} not found");
        }
    }
    
    /// <summary>
    /// 創建附件新版本
    /// </summary>
    /// <param name="id">原始附件 ID</param>
    /// <param name="file">新檔案</param>
    /// <param name="comments">版本註解</param>
    /// <returns>新版本附件</returns>
    [HttpPost("{id:int}/new-version")]
    [ProducesResponseType(typeof(ApiResponse<AttachmentDto>), StatusCodes.Status201Created)]
    public async Task<IActionResult> CreateNewVersion(
        int id,
        IFormFile file,
        [FromForm] string comments = null)
    {
        if (file == null || file.Length == 0)
        {
            return BadRequest("No file provided");
        }
        
        try
        {
            using var stream = file.OpenReadStream();
            
            var command = new CreateAttachmentVersionCommand
            {
                OriginalAttachKey = id,
                FileName = file.FileName,
                FileStream = stream,
                ContentType = file.ContentType,
                Comments = comments
            };
            
            var newVersion = await AppServices.Attachments.CreateNewVersionAsync(command);
            
            return Created($"/api/attachments/{newVersion.AttachKey}", newVersion);
        }
        catch (AttachmentNotFoundException)
        {
            return NotFound($"Original attachment with ID {id} not found");
        }
    }
    
    /// <summary>
    /// 獲取附件版本歷史
    /// </summary>
    /// <param name="id">附件 ID</param>
    /// <returns>版本列表</returns>
    [HttpGet("{id:int}/versions")]
    [ProducesResponseType(typeof(ApiResponse<List<AttachmentVersionDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetVersions(int id)
    {
        var versions = await AppServices.Attachments.GetVersionsAsync(id);
        return Ok(versions);
    }
}
```

---

好的，現在我創建前端規格文檔：

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"content": "\u5206\u6790\u73fe\u6709\u7cfb\u7d71\u67b6\u69cb\u548c\u4ee3\u78bc\u7d50\u69cb", "status": "completed", "priority": "high", "id": "1"}, {"content": "\u5275\u5efa\u7cfb\u7d71\u67b6\u69cb\u898f\u683c\u66f8", "status": "completed", "priority": "high", "id": "2"}, {"content": "\u7de8\u5beb\u5f8c\u7aef ASP.NET Core API \u898f\u683c", "status": "completed", "priority": "high", "id": "3"}, {"content": "\u7de8\u5beb\u524d\u7aef Vue3 + Pinia \u898f\u683c", "status": "in_progress", "priority": "high", "id": "4"}, {"content": "\u5275\u5efa\u8cc7\u6599\u5eab\u8a2d\u8a08\u898f\u683c", "status": "pending", "priority": "high", "id": "5"}, {"content": "\u7de8\u5beb API \u63a5\u53e3\u898f\u683c", "status": "pending", "priority": "high", "id": "6"}, {"content": "\u5275\u5efa\u90e8\u7f72\u548c\u74b0\u5883\u898f\u683c", "status": "pending", "priority": "high", "id": "7"}]