using CQR.Application.Dto;

namespace CQR.Application.Queries;

public class SearchCQRQuery
{
    public string? ProjectNumber { get; set; }
    public string? Originator { get; set; }
    public string? Status { get; set; }
    public string? ProductDescription { get; set; }
    public string? Customer { get; set; }
    public string? Platform { get; set; }
    public DateTime? DateFrom { get; set; }
    public DateTime? DateTo { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; } = "desc";
    
    // Additional properties from original implementation
    public bool FindLastTwoFolders { get; set; }
    public string? QuoteReport { get; set; }
    public bool SortDescending { get; set; }
    public string? CQRFromLeft { get; set; }
    public string? CQRFromRight { get; set; }
    public string? CQRToLeft { get; set; }
    public string? CQRToRight { get; set; }
    public bool UseWildcard { get; set; }
    public string? ManufacturingSite { get; set; }
    public DateTime? InitialReleaseDateFrom { get; set; }
    public DateTime? DueDateFrom { get; set; }
    public string? CQROriginator { get; set; }
    public string? AccountManager { get; set; }
    
    // Additional search properties for advanced functionality
    public DateTime? InitialReleaseDateTo { get; set; }
    public DateTime? DueDateTo { get; set; }
    public DateTime? WonDateFrom { get; set; }
    public DateTime? WonDateTo { get; set; }
    public DateTime? OpeningMeetingDateFrom { get; set; }
    public DateTime? OpeningMeetingDateTo { get; set; }
    public string? Estimator { get; set; }
    public string? AssignedPDM { get; set; }
    public string? AssignedPGM { get; set; }
    public string? ModelYear { get; set; }
    public string? CustomerRFQNumber { get; set; }
    public string? QuoteType { get; set; }
    public bool OnlyQuoteLettersWon { get; set; }
    public bool OpenCQRFolderOnly { get; set; }
    public string? SearchSummary { get; set; }
}

public class SearchCQRResponse
{
    public List<CQRSearchResultDto> Results { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
    public string? SearchSummary { get; set; }
}