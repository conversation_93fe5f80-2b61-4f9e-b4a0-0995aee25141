using System.ComponentModel.DataAnnotations;

namespace CQR.Domain.Entities;

public class ProductDetail
{
    [Key]
    public int Id { get; set; } // 主键
    public int ProductId { get; set; } // 关联的产品 ID
    public string Description { get; set; } // 产品描述
    public string Specifications { get; set; } // 产品规格
                                               // 其他详细信息属性...

    // public Product Product { get; set; } // 导航属性
}