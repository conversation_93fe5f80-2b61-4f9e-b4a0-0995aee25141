﻿namespace CQR.Domain.DecisionCore;


// decisionCore, Version=1.0.9056.27004, Culture=neutral, PublicKeyToken=null
// decisionCore.clsRouting.T_ROUTING
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

public class T_ROUTING
{
    public string sQueueKey;

    public string sTask;

    public string sTaskAction;

    public string sTaskRouting;

    public string sAssigned;

    public string sAssignedDate;

    public string sAssignedTime;

    public string sDue;

    public string sResult;

    public string sDrawingNbr;

    public bool bCurrentInd;

    public bool bToBeViewedInd;

    public bool bDoneInd;

    public string sDoneDate;

    public string sDoneTime;

    public string sDoneUser;

    public int iSaveFlag;

    public bool bNote;

    public string AssignedDate()
    {
        if (Operators.CompareString(sAssignedDate, "", TextCompare: false) == 0)
        {
            return Conversions.ToString(DateAndTime.Now);
        }
        return modTools.mDateCygnetToScreen(sAssignedDate) + " " + Strings.Left(sAssignedTime, 2) + ":" + Strings.Mid(sAssignedTime, 3);
    }
}
