﻿
// CQR, Version=1.0.9223.23847, Culture=neutral, PublicKeyToken=null
// CQR.clsCQR
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using Microsoft.Office.Interop.Excel;
using Microsoft.Data.SqlClient;

namespace CQR.Domain.DecisionCore;
public class clsCQR
{
    public T_TRSCODE sT_TRSCODE;

    public T_UserRole sT_UserRole;

    public class T_GDPEP_APPROVAL
    {
        public int iGDPEPPhaseId;

        public string sGDPIMCategory;

        public string sReleaseDate;

        public string sComments;

        public string[] sVolume;

        public string[] sSellPrice;

        public string[] sApprovalUser;

        public string[] sRequiredState;

        public string[] sApprovalState;

        public string[] sApprovalStateDbs;

        public string[] sApprovalDate;

        public string[] sApprovalRole;

        public string sReleaseGateway;

        public clsAttachment tAttachment;

        public T_GDPEP_APPROVAL()
        {
            sVolume = new string[13];
            sSellPrice = new string[11];
            sApprovalUser = new string[13];
            sRequiredState = new string[13];
            sApprovalState = new string[13];
            sApprovalStateDbs = new string[13];
            sApprovalDate = new string[13];
            sApprovalRole = new string[15]
            {
                "ZERO", "ODIR", "MDIR", "FDIR", "ENG", "CDEV", "PDIR", "BDIR", "8", "9",
                "10", "11", "12", "13", "14"
            };
            sReleaseGateway = "";
            tAttachment = new clsAttachment();
        }
    }

    public class T_UNITCOST
    {
        public string sDesc;

        public string sCost;

        public string sQty;

        public T_UNITCOST()
        {
            sDesc = "";
            sCost = "";
            sQty = "";
        }
    }

    public class clsPNL
    {
        public string sQAFKey;

        public string sQafName;

        public string sQafStatus;

        public string sQafViewable;

        public bool bCanAdvance;

        public bool bRelease;

        public bool bReplaced;

        public int iReplacedIndex;

        public string sStatusDate;

        public string sTargetSellPrice;

        public string sStartingSellPrice;

        public string sTotalVolume;

        public string sTotalSales;

        public string sAmortizedTools;

        public string sTRWPaidTools;

        public string sTRWCapital;

        public string sCustomerPaidTools;

        public string sPATPercent;

        public string sPATDollars;

        public string sIRRPercent;

        public string sIRRDollars;

        public string sMaterialPercent;

        public string sMaterialDollars;

        public string sSGA;

        public string sEngDollars;

        public string sEngPercent;

        public string sPayback;

        public string sAvgAnnualSPReduction;

        public string sEngSite;

        public string sFOBPoint;

        public bool bAttachmentFound;

        public bool bSave;

        public bool bCreateOnSave;

        public bool bDeleteOnSave;

        public string bChecked;

        public string sOldQafKey;

        public PNLLineItem[] tPNLLineItem;

        public bool bIsNewFormat;

        public clsPNL()
        {
            bChecked = "";
            tPNLLineItem = new PNLLineItem[0];
        }
    }

    public struct PNLLineItem
    {
        public int iPNLLineItemId;

        public string sProduct;

        public string sSite;

        public string sAverageAnnualVolume;

        public string sSalesPriceInQuoted;

        public string sSalesBusinessUnit;

        public string sQuotedCurrency;

        public string sSalesPriceSOP;

        public string sAverageAnnualSales;

        public string sAverageAnnualDVP;

        public string sAverageAnnualMPBT;

        public string sEVAFullCost;

        public string sEVAIncremental;

        public string sAwardStatus;

        public string sAwardStatusDbs;

        public string sAwardStatusDate;

        public string sAwardStatusUser;

        public string sCreateDate;
    }

    public struct Antares
    {
        public string sUniqueNumber;

        public string sVB_ID;

        public string sOEMGroup;

        public string sOEMCustomer;

        public string sPlatform;

        public string sProgram;

        public string sNameplate;

        public string sCountry;

        public string sRegion;

        public string sNewBusinessCategory;

        public string sStatus;

        public string sProductId;

        public string sProductDescription;

        public string sProductGrouping;

        public string sSOP;

        public string sEOP;

        public string sSoldFrom;

        public string sFinalAssembly;

        public string sUsedByOtherCQR;

        public bool bArchived;
    }

    public struct IHS
    {
        public string sCoreNameplatePlantMnemonic;

        public string sOEMGroup;

        public string sOEMCustomer;

        public string sPlatform;

        public string sProgram;

        public string sNameplate;

        public string sRegion;

        public string sCountry;

        public string sSOP;

        public string sEOP;

        public string sProductDescription;

        public string sProductGrouping;

        public string sSoldFrom;

        public string sFinalAssembly;

        public bool bArchived;
    }

    public class clsHandoverPackageAttachments : clsAttachment
    {
        private clsCQR tCqr;

        public clsHandoverPackageAttachments(clsCQR _tCqr)
        {
            tCqr = _tCqr;
        }

        //public override bool CustomColumnsHeader(coreTable tbl)
        //{
        //    TableCell docCell = new TableCell();
        //    docCell.Text = "Document Type";
        //    tbl.LastRow().Cells.AddAt(1, docCell);
        //    return true;
        //}

        //public override bool CustomColumnsInto(int attIndex, int rowNumber, coreTable tbl, bool bReadOnly)
        //{
        //    TableCell docCell = new TableCell();
        //    DropDownList cmb = new DropDownList();
        //    string customData = tTab.tAttInfo[attIndex].sCustomData;
        //    string[] custSplit = Strings.Split(customData + "~", "~");
        //    cmb.ID = "cmbSalesDoc_" + Conversions.ToString(attIndex);
        //    string flag = Conversions.ToString(Interaction.IIf(Operators.CompareString(custSplit[1], "on", TextCompare: false) == 0, "checked", ""));
        //    if (!((Operators.CompareString(tTab.tAttInfo[attIndex].sQueueKey, "", TextCompare: false) == 0) | (Strings.Len(tTab.tAttInfo[attIndex].sCustomData) < 2)) && (!tCqr.CanEditSalesCloseout() | !tCqr.tUserData.bIsAMgr))
        //    {
        //        cmb.Enabled = false;
        //        flag += " disabled";
        //    }
        //    modTRS.mTRSPopulateControl("CQR_SALESDOC", cmb, custSplit[0], abClearBox: false);
        //    docCell.Controls.Add(cmb);
        //    tbl.LastRow().Cells.AddAt(1, docCell);
        //    return true;
        //}

        public override bool CustomColumnsFrom(int attIndex)
        {
            if (tCqr.CanEditSalesCloseout() | tCqr.CanApproveHandover() | tCqr.tUserData.bIsAMgr)
            {
                //string docSel = HttpContext.Current.Request["cmbSalesDoc_" + Conversions.ToString(attIndex)];
                //string appSel = HttpContext.Current.Request["chkSalesApproved_" + Conversions.ToString(attIndex)];
                //tTab.tAttInfo[attIndex].sCustomData = docSel + "~" + appSel;
            }
            return true;
        }
    }



    public const int ddcFail = -1;

    public const int ddcSuccess = 0;

    public const int trwSuccess = 0;

    public const int GDPEP_ROLE_COUNT_LEGACY = 7;

    public const int GDPEP_ROLE_COUNT = 12;

    public const int HANDOVER_APPROVAL_COUNT = 4;

    public const string REC_CQR_HDR_FLD_KEY = "QueueKey";

    public const string REC_CQR_HDR_FLD_PROJECT_NBR = "ProjectNbr";

    public const string REC_CQR_HDR_FLD_REV_NBR = "RevNbr";

    public const string REC_CQR_HDR_FLD_STATUS = "Status";

    public const string REC_CQR_HDR_FLD_STATUS_DATE = "StatusDate";

    public const string REC_CQR_HDR_FLD_CUST_NBR = "CustNbr";

    public const string REC_CQR_HDR_FLD_CUST_BUYER_NAME = "CustBuyerName";

    public const string REC_CQR_HDR_FLD_CUST_ENGINEER_NAME = "CustEngineerName";

    public const string REC_CQR_HDR_FLD_VEHICLE = "Vehicle";

    public const string REC_CQR_HDR_FLD_RFQ_REF_NBR = "RFQRefNbr";

    public const string REC_CQR_HDR_FLD_RFQ_RCVD_DATE = "RFQRcvdDate";

    public const string REC_CQR_HDR_FLD_VOLUME_PER_ANNUM = "VolumePerAnnum";

    public const string REC_CQR_HDR_FLD_ORIG_PRODUCT_LIFE = "OriginalProductLife";

    public const string REC_CQR_HDR_FLD_REM_PRODUCT_LIFE = "RemainingProductLife";

    public const string REC_CQR_HDR_FLD_QUOTE_RESPONSE_DUE_DATE = "QuoteResponseDueDate";

    public const string REC_CQR_HDR_FLD_FRAN_ISSUE_DATE = "FRANIssueDate";

    public const string REC_CQR_HDR_FLD_NEW_MOD_REPL = "NewModRepl";

    public const string REC_CQR_HDR_FLD_IN_FORECAST_IND = "InForecastInd";

    public const string REC_CQR_HDR_FLD_APPROX_ANNUAL_VALUE = "ApproxAnnualValue";

    public const string REC_CQR_HDR_FLD_QUOTE_TYPE = "QuoteType";

    public const string REC_CQR_HDR_FLD_IF_CHECKED_IND = "IfCheckedInd";

    public const string REC_CQR_HDR_FLD_QS9000_CONSIDERED = "IfCheckedDoneInd";

    public const string REC_CQR_HDR_FLD_PRODUCTION_REQD_IND = "ProductionReqdInd";

    public const string REC_CQR_HDR_FLD_PROTOTYPE_REQD_IND = "PrototypeReqdInd";

    public const string REC_CQR_HDR_FLD_PROTOTYPE_QTY = "PrototypeQty";

    public const string REC_CQR_HDR_FLD_PIECE_PRICE_REQD_IND = "PiecePriceReqdInd";

    public const string REC_CQR_HDR_FLD_TOOLING_REQD_IND = "ToolingReqdInd";

    public const string REC_CQR_HDR_FLD_DESIGN_PROP_REQD_IND = "DesignPropReqdInd";

    public const string REC_CQR_HDR_FLD_TIMING_PLAN_REQD_IND = "TimingPlanReqdInd";

    public const string REC_CQR_HDR_FLD_MILESTONE_1_DATE = "Milestone1Date";

    public const string REC_CQR_HDR_FLD_MILESTONE_2_DATE = "Milestone2Date";

    public const string REC_CQR_HDR_FLD_MILESTONE_3_DATE = "Milestone3Date";

    public const string REC_CQR_HDR_FLD_MILESTONE_4_DATE = "Milestone4Date";

    public const string REC_CQR_HDR_FLD_MILESTONE_5_DATE = "Milestone5Date";

    public const string REC_CQR_HDR_FLD_OBSOLESCENCE_REQD_IND = "ObsolescenceReqdInd";

    public const string REC_CQR_HDR_FLD_COMMISSION_REQD_IND = "CommissionReqdInd";

    public const string REC_CQR_HDR_FLD_COMMISSION_PERCENT = "CommissionPercent";

    public const string REC_CQR_HDR_FLD_MFG_REP_COMPANY = "MfgRepCompany";

    public const string REC_CQR_HDR_FLD_MFG_REP_INDIVIDUAL = "MfgRepIndividual";

    public const string REC_CQR_HDR_FLD_ORIGINATOR_ID = "OriginatorId";

    public const string REC_CQR_HDR_FLD_ORIGINATOR_DATE = "OriginationDate";

    public const string REC_CQR_HDR_FLD_ACCOUNT_MGR_ID = "AccountMgrId";

    public const string REC_CQR_HDR_FLD_COST_ESTIMATOR_ID = "CostEstimatorId";

    public const string REC_CQR_HDR_FLD_PGM_ID = "PGMId";

    public const string REC_CQR_HDR_FLD_PSM_ID = "PSMId";

    public const string REC_CQR_HDR_FLD_PA_ID = "PAId";

    public const string REC_CQR_HDR_FLD_PETM_ID = "PETMId";

    public const string REC_CQR_HDR_FLD_PRD_ID = "PRDId";

    public const string REC_CQR_HDR_FLD_TIME_FRAME_OK_IND = "TimeframeOKInd";

    public const string REC_CQR_HDR_FLD_INFORMATION_OK_IND = "InformationOKInd";

    public const string REC_CQR_HDR_FLD_WORK_PROCEED_OK_IND = "WorkProceedOKInd";

    public const string REC_CQR_HDR_FLD_DUE_DATE_FROM_ENG = "DueDateFromEng";

    public const string REC_CQR_HDR_FLD_DUE_DATE_TO_BNE = "DueDateToBnE";

    public const string REC_CQR_HDR_FLD_PSR = "PSR";

    public const string REC_CQR_HDR_FLD_UNUSED2 = "Unused2";

    public const string REC_CQR_HDR_FLD_RCT_INITIATED = "RctInit";

    public const string REC_CQR_HDR_FLD_UNUSED3 = "Unused3";

    public const string REC_CQR_HDR_FLD_QUOTE_COMPLETED_SENT = "QuoteCompletedSent";

    public const string REC_CQR_HDR_FLD_QUOTE_RESPONSE_RCVD = "QuoteResponseRcvd";

    public const string REC_CQR_HDR_FLD_UNUSED1 = "Unused1";

    public const string REC_CQR_HDR_FLD_QR_UNIT_PRICE = "QRUnitPrice";

    public const string REC_CQR_HDR_FLD_QR_QUANTITY = "QRQuantity";

    public const string REC_CQR_HDR_FLD_QR_FOB = "QRFOB";

    public const string REC_CQR_HDR_FLD_QR_TOOL_LEAD_TIME = "QRToolLeadTime";

    public const string REC_CQR_HDR_FLD_QR_MATERIAL_DATE = "QRMaterialDate";

    public const string REC_CQR_HDR_FLD_QR_LABOR_DATE = "QRLaborDate";

    public const string REC_CQR_HDR_FLD_QR_TOOL_CAP = "QRToolingCapacity";

    public const string REC_CQR_HDR_FLD_QR_PROG_CLASS = "QRProgramClass";

    public const string REC_CQR_HDR_FLD_FRAN_DESC = "FranDesc";

    public const string REC_CQR_HDR_FLD_HEALTH_IND = "HealthAndSafetyInd";

    public const string REC_CQR_HDR_FLD_NO_RCT_FOLDERS = "NoRctFolders";

    public const string REC_CQR_HDR_FLD_MOD_NEEDS_COST_IND = "ModNeedsCostInd";

    public const string REC_CQR_HDR_FLD_QUOTE_DATE = "QSQuoteDate";

    public const string REC_CQR_HDR_FLD_CUST_RESPONSE = "QSCustomerResp";

    public const string REC_CQR_HDR_FLD_CUST_PO = "QSCustomerPO";

    public const string REC_CQR_HDR_FLD_CUST_AUTH = "QSCustomerAuth";

    public const string REC_CQR_HDR_FLD_CUST_FRAN = "QSCustomerFran";

    public const string REC_CQR_HDR_VPAComments = "VPAComments";

    public const string REC_CQR_HDR_IntPricComments = "IntPricComments";

    public const string REC_CQR_HDR_BkRndInfComments = "BkRndInfComments";

    public const string REC_CQR_HDR_ActionComments = "ActionComments";

    public const string REC_CQR_HDR_QAFComments = "QAFComments";

    public const string REC_CQR_HDR_EngPkgComments = "EngPkgComments";

    public const string REC_CQR_HDR_OtherInfoComments = "OtherInfo";

    public const string REC_CQR_HDR_CustProdComments = "CustProdComments";

    public const string REC_CQR_HDR_QRToolingComments = "QRToolingComments";

    public const string REC_CQR_HDR_FLD_RESP_LEVEL = "ResponseLevel";

    public const string REC_CQR_HDR_FLD_BUS_CLASS = "BusinessClassif";

    public const string REC_CQR_HDR_FLD_PROD_CAT = "ProductCategory";

    public const string REC_CQR_HDR_FLD_PROD_LINE = "ProductLine";

    public const string REC_CQR_HDR_FLD_WARRANTY = "WarrantyRqmts";

    public const string REC_CQR_HDR_FLD_PERF_RQMT = "PerformanceRqmts";

    public const string REC_CQR_HDR_FLD_CUR_SUPP = "CurrentSupplier";

    public const string REC_CQR_HDR_FLD_LOC_SALES = "LocationSales";

    public const string REC_CQR_HDR_FLD_LOC_ENG = "LocationEngineer";

    public const string REC_CQR_HDR_FLD_LOC_SHIP = "LocationShipping";

    public const string REC_CQR_HDR_FLD_TIMING1 = "CustomerTiming1";

    public const string REC_CQR_HDR_FLD_TIMING2 = "CustomerTiming2";

    public const string REC_CQR_HDR_FLD_TIMING3 = "CustomerTiming3";

    public const string REC_CQR_HDR_FLD_TIMING4 = "CustomerTiming4";

    public const string REC_CQR_HDR_FLD_TIMING5 = "CustomerTiming5";

    public const string REC_CQR_HDR_FLD_TIMING6 = "CustomerTiming6";

    public const string REC_CQR_HDR_FLD_TIMING7 = "CustomerTiming7";

    public const string REC_CQR_HDR_FLD_TIMING8 = "CustomerTiming8";

    public const string REC_CQR_HDR_FLD_TIMING9 = "CustomerTiming9";

    public const string REC_CQR_HDR_FLD_ECON_YEAR1 = "EconomicYear1";

    public const string REC_CQR_HDR_FLD_ECON_YEAR2 = "EconomicYear2";

    public const string REC_CQR_HDR_FLD_ECON_YEAR3 = "EconomicYear3";

    public const string REC_CQR_HDR_FLD_ECON_YEAR4 = "EconomicYear4";

    public const string REC_CQR_HDR_FLD_ECON_YEAR5 = "EconomicYear5";

    public const string REC_CQR_HDR_FLD_ECON_YEAR6 = "EconomicYear6";

    public const string REC_CQR_HDR_FLD_ECON_YEAR7 = "EconomicYear7";

    public const string REC_CQR_HDR_FLD_ECON_YEAR8 = "EconomicYear8";

    public const string REC_CQR_HDR_FLD_ECON_VOL1 = "EconomicVolume1";

    public const string REC_CQR_HDR_FLD_ECON_VOL2 = "EconomicVolume2";

    public const string REC_CQR_HDR_FLD_ECON_VOL3 = "EconomicVolume3";

    public const string REC_CQR_HDR_FLD_ECON_VOL4 = "EconomicVolume4";

    public const string REC_CQR_HDR_FLD_ECON_VOL5 = "EconomicVolume5";

    public const string REC_CQR_HDR_FLD_ECON_VOL6 = "EconomicVolume6";

    public const string REC_CQR_HDR_FLD_ECON_VOL7 = "EconomicVolume7";

    public const string REC_CQR_HDR_FLD_ECON_VOL8 = "EconomicVolume8";

    public const string REC_CQR_HDR_FLD_ECON_TGT1 = "EconomicTarget1";

    public const string REC_CQR_HDR_FLD_ECON_TGT2 = "EconomicTarget2";

    public const string REC_CQR_HDR_FLD_ECON_TGT3 = "EconomicTarget3";

    public const string REC_CQR_HDR_FLD_ECON_TGT4 = "EconomicTarget4";

    public const string REC_CQR_HDR_FLD_ECON_TGT5 = "EconomicTarget5";

    public const string REC_CQR_HDR_FLD_ECON_TGT6 = "EconomicTarget6";

    public const string REC_CQR_HDR_FLD_ECON_TGT7 = "EconomicTarget7";

    public const string REC_CQR_HDR_FLD_ECON_TGT8 = "EconomicTarget8";

    public const string REC_CQR_CPN_FLD_CUST_PART_NBR = "CustPartNbr";

    public const string REC_CQR_LPN_FLD_MRP_COMPANY_CODE = "MRPCompanyCode";

    public const string REC_CQR_LPN_FLD_PART_NBR = "PartNbr";

    public const string REC_CQR_FLD_SEG_UNIT_COST_DESC = "UnitCostDesc";

    public const string REC_CQR_FLD_SEG_UNIT_COST = "UnitCost_001";

    public const string REC_CQR_FLD_SEG_UNIT_QUANTITY = "UnitQuantity";

    public const int partsCustomer = 1;

    public const int partsLucas = 2;

    public const string REC_RG_HDR_FLD_FOLDER_TYPE = "FolderType";

    public const string REC_RG_HDR_FLD_FOLDER_NBR = "FolderNbr";

    public const string REC_RG_HDR_FLD_TASK_CODE = "TaskCode";

    public const string REC_RG_HDR_FLD_PART_DWG_NBR = "PartDwgNbr";

    public const string REC_RG_HDR_FLD_ACTION_TASK_CODE = "ActionTaskCode";

    public const string REC_RG_HDR_FLD_ROUTING_TASK_CODE = "RoutingTaskCode";

    public const string REC_RG_HDR_FLD_ASSIGNED_TO = "AssignedTo";

    public const string REC_RG_HDR_FLD_SORT_DATE = "SortDate";

    public const string REC_RG_HDR_FLD_SORT_TIME = "SortTime";

    public const string REC_RG_HDR_FLD_ASSIGN_DATE = "AssignedDate";

    public const string REC_RG_HDR_FLD_ASSIGN_TIME = "AsignedTime";

    public const string REC_RG_HDR_FLD_DUE_DATE = "DueDate";

    public const string REC_RG_HDR_FLD_DONE_IND = "DoneInd";

    public const string REC_RG_HDR_FLD_RESULT = "Result";

    public const string REC_RG_HDR_FLD_DONE_DATE = "DoneDate";

    public const string REC_RG_HDR_FLD_DONE_BY_NAME = "DoneByName";

    public const string REC_RG_HDR_FLD_CURRENT_TASK_IND = "CurrentTaskInd";

    public const string REC_RG_HDR_FLD_VIEW_IND = "ToBeViewedInd";

    public const string REC_RG_HDR_FLD_NOTE_IND = "NotificationInd";

    public const string REC_RG_HDR_FLD_EMAIL_IND = "EMailInd";

    public const string REC_RG_HDR_FLD_DONE_TIME = "DoneTm";

    private bool bCreate;

    private bool bModify;

    private string sLockedKey;

    private string sLockedEntry;

    private string sLockedNumber;

    public bool bUserIsOrig;

    public bool bUserHasOrigRole;

    public bool bOrigIsPgm;

    public bool bOrigIsPetm;

    public bool bOrigIsAMgr;

    public bool bOrigIsSDir;

    public bool bOrigIsCost;

    public bool bOrigIsMbne;

    public bool bDualRelease;

    public bool bHasChecked;

    public string sIndTask;

    public T_UserRole tUserData;

    public long iQueueKey;

    public int iOpenMode;

    public bool bFolderWasLocked;

    public string sStatus;

    public string sStatusDate;

    public string sOriginator;

    public string sOriginationDate;

    public bool bNewCQR;

    public int iSaveMode;

    public bool bSuperUser;

    public string sFolderNum;

    public string sProjectNum;

    public string sRevision;

    public string sCQRIssueDate;

    public string sFranNewModRepl;

    public string sQuoteType;

    public string sUserStamp;

    public string sSecurityMode;

    public string sCommentsHeld;

    public string sComment;

    public string sNewComment;

    public string sOrgComment;

    public string sCQRDesc;

    public string sProductDescription;

    public string sVehicleBuildId;

    public string sOEMGroup;

    public string sCustomerBuyer;

    public string sCustomerEngineer;

    public string sPlatform;

    public string sNameplate;

    public string sModelYear;

    public string sRFQRefNum;

    public string sRFQRecDate;

    public string sVolumePerAnnum;

    public string sOrigProductLife;

    public string sOpeningMeetingDate;

    public string sRemProductLife;

    public string sGateway;

    public string sCustomerQuoteDueDate;

    public string sCustomerQuoteDueDateLast;

    public string sCustomerQuoteDueDateOrig;

    public string sCustomerQuoteDueDateChangeComment;

    public string sQuoteRespDueDate;

    public string sQuoteRespDueDateDbs;

    public string sManufacturingSite;

    public string sManufacturingSiteDbs;

    public string sPIMSite;

    public string sPIMSiteDbs;

    public string sEngineeringSite;

    public string sEngineeringSiteDbs;

    public string sApproxAnnualValue;

    public string sObsolescenceRequiredInd;

    public string sCommissionRequiredInd;

    public string sCommissionPercentage;

    public string sManufacturerRepCompany;

    public string sManufacturerRepIndividual;

    public string sAwardQuarter;

    public string sAwardYear;

    public string sGateExit;

    public string sCustomerGroup;

    public string sProductCategory;

    public string sBusinessPlan;

    public string sIfCheckedInd;

    public string sProductionInd;

    public string sPrototypeInd;

    public string sPrototypeQuantity;

    public string sPiecePriceInd;

    public string sToolingInd;

    public string sDesignProposalInd;

    public string sTimingPlanInd;

    public string sMilestone1Date;

    public string sMilestone2Date;

    public string sMilestone3Date;

    public string sMilestone4Date;

    public string sMilestone5Date;

    public string sCommercialManager;

    public string sCommercialBusinessManager;

    public string sAccountManager;

    public string sSalesAccountDirector;

    public string sCostEstimator;

    public string sPGM_aka_CoC;

    public string sPDM_aka_ProgramManager;

    public string sAMECoordinator;

    public string sPurchasingCoordinator;

    public string sPA;

    public string sPSM;

    public string sTDRDate;

    public string sTDRNoInput;

    public string sCommunication;

    public string sASILLevel;

    public string sAUTOSAR;

    public string sCybersecurity;

    public string sOtherRelevantFeatures;

    public string sEngineeringManager;

    public string sEngineeringManagerDbs;

    public string sCalculationCurrency;

    public string sCommercialManagerDbs;

    public string sAccountManagerDbs;

    public string sSalesAccountDirectorDbs;

    public string sCostEstimatorDbs;

    public string sPGM_aka_CoCDbs;

    public string sPDM_aka_ProgramManagerDbs;

    public string sAMECoordinatorDbs;

    public string sPurchasingCoordinatorDbs;

    public string sTimeFrameOkInd;

    public string sInformationOkInd;

    public string sWorkProceedOkInd;

    public string sHealthInd;

    public string sModNeedsCostInd;

    public string[] sElecInputReqdInd;

    public string[] sElecPRD;

    public string[] sElecPRDdbs;

    public string[] sElecPRDNoInput;

    public string sEngineerAssigned;

    public string sEngineerAssignedDbs;

    public string sLeadManufacturing;

    public string sLeadPurchasing;

    public string sLeadValidation;

    public string sLeadManufacturingNoCostImpact;

    public string sLeadPurchasingNoCostImpact;

    public string sLeadValidationNoCostImpact;

    public string sLeadManufacturingDbs;

    public string sLeadPurchasingDbs;

    public string sLeadValidationDbs;

    public string sQS9000Considered;

    public string sDateOfCSR;

    public string sDueDateFromEngineering;

    public string sDueDateFromEngineeringDbs;

    public string sDueDateToBidsAndEstimating;

    public string sDueDateToBidsAndEstimatingDbs;

    public string sGDPEPPhase1Required;

    public string sGDPEPPhase1RequiredDbs;

    public string sGDPEPPhase2Required;

    public string sGDPEPPhase2RequiredDbs;

    public string sNoRCTFolders;

    public int iRCTCount;

    public string[] sRCTItem;

    public string[] sRCTCost;

    public string[] sRCTPartName;

    public int iEPLCount;

    public string[] sEPLItem;

    public string[] sEPLPartName;

    public string sLogComment;

    public string sQRFOB;

    public string sQRLeadTime;

    public string sQRMaterialDate;

    public string sQRLaborDate;

    public string sQRToolCap;

    public string sQRProgClass;

    public string sQRReviewAvailable;

    public string sQRCompletedAndSent;

    public string sQSQuoteStatus;

    public string sQSQuoteDate;

    public string sQSDateWon;

    public string sQSSalesAdministrator;

    public string sQSAwardStatus;

    public string sQSComments;

    public string sNewQSComments;

    public string sOrgQSComments;

    public string sQSCommentsProceedCancel;

    public string sNewQSCommentsProceedCancel;

    public string sOrgQSCommentsProceedCancel;

    public int iCustPartCount;

    public string[] sCustPartList;

    public int iLucasPartCount;

    public string[] sLucasPartList;

    public string sCustPartListString;

    public string sLucasPartListString;

    public string sDbsCommentsVolumePerAnnum;

    public string sNewCommentsVolumePerAnnum;

    public string sOrgCommentsVolumePerAnnum;

    public string sDbsCommentsTargetPricing;

    public string sDbsCommentsBackgroundInfo;

    public string sDbsCommentsOtherInfo;

    public string sDbsCommentsAction;

    public string sDbsCommentsQRTooling;

    public string sDbsCommentsCustomerProductivity;

    public string sDbsCommentsQAF;

    public string sDbsCommentsGDPEPRejection;

    public string sNewCommentsTargetPricing;

    public string sNewCommentsBackgroundInfo;

    public string sNewCommentsOtherInfo;

    public string sNewCommentsAction;

    public string sNewCommentsQRTooling;

    public string sNewCommentsCustomerProductivity;

    public string sNewCommentsQAF;

    public string sNewCommentsGDPEPRejection;

    public string sOrgCommentsTargetPricing;

    public string sOrgCommentsBackgroundInfo;

    public string sOrgCommentsOtherInfo;

    public string sOrgCommentsAction;

    public string sOrgCommentsQRTooling;

    public string sOrgCommentsCustomerProductivity;

    public string sOrgCommentsQAF;

    public string sOrgCommentsGDPEPRejection;

    public string sCommentsEP;

    public string sNewCommentsEP;

    public string sOrgCommentsEP;

    public string sResponseLevel;

    public string sBusinessClass;

    public string sCustomerSOP;

    public string sRegionalQuotingTeam;

    public string sWarrantyRqmt;

    public string sPerformRqmt;

    public string sCurrentSupp;

    public string sProductLine;

    public string sLocationSales;

    public string sLocationEng;

    public string sLocationShip;

    public string sTiming1Date;

    public string sTiming2Date;

    public string sTiming3Date;

    public string sTiming4Date;

    public string sTiming5Date;

    public string sTiming6Date;

    public string sTiming7Date;

    public string sTiming8Date;

    public string sTiming9Date;

    public string sEconYear1;

    public string sEconYear2;

    public string sEconYear3;

    public string sEconYear4;

    public string sEconYear5;

    public string sEconYear6;

    public string sEconYear7;

    public string sEconYear8;

    public string sEconVolume1;

    public string sEconVolume2;

    public string sEconVolume3;

    public string sEconVolume4;

    public string sEconVolume5;

    public string sEconVolume6;

    public string sEconVolume7;

    public string sEconVolume8;

    public string sEconTarget1;

    public string sEconTarget2;

    public string sEconTarget3;

    public string sEconTarget4;

    public string sEconTarget5;

    public string sEconTarget6;

    public string sEconTarget7;

    public string sEconTarget8;

    public T_GDPEP_APPROVAL[] tGateway;

    public string sHandoverComment;

    public string[] sHandoverApprovalRole;

    public bool[] bHandoverApprovalRole;

    public string[] sHandoverApprovalTask;

    public int iUnitCostCount;

    public T_UNITCOST[] tUnitCost;

    public clsAttachment tAttachmentResponse;

    public clsHandoverPackageAttachments tHandoverPackage;

    public clsAttachment tTasklist;

    public clsAttachment tQuoteResponseFCM;

    public clsAttachment tQuoteResponseECR;

    public clsAttachment tPNLAttach;

    public clsRouting tRouting;

    public int printCurrentQRSelected;

    public int iQAFCount;

    public clsPNL[] tPNL;

    public string sMode;

    private int saveCount;

    public string rejectTo;

    public string sUserId;

    public Antares[] antaresRecords;

    public string sAntaresArchiveOk;

    public IHS[] ihsRecords;

    public string sIHSArchiveOk;

    public string UserId
    {
        get
        {
            return sUserId;
        }
        set
        {
            //modUserProf.UserProfTranslateUserId(new corePage(), value, ref sUserId);
        }
    }

    public clsCQR()
    {
        bHasChecked = false;
        tUserData = new T_UserRole();
        sGateway = "";
        sElecInputReqdInd = new string[9];
        sElecPRD = new string[9];
        sElecPRDdbs = new string[9];
        sElecPRDNoInput = new string[9];
        sLogComment = "";
        tGateway = new T_GDPEP_APPROVAL[3]
        {
            new T_GDPEP_APPROVAL(),
            new T_GDPEP_APPROVAL(),
            new T_GDPEP_APPROVAL()
        };
        sHandoverApprovalRole = new string[5] { "", "AMGR", "SDIR", "FR00", "PDM" };
        bHandoverApprovalRole = new bool[5];
        sHandoverApprovalTask = new string[5] { "", "AA", "DD", "GG", "LL" };
        iUnitCostCount = 8;
        tUnitCost = new T_UNITCOST[9];
        tAttachmentResponse = new clsAttachment();
        tHandoverPackage = new clsHandoverPackageAttachments(this);
        tTasklist = new clsAttachment();
        tQuoteResponseFCM = new clsAttachment();
        tQuoteResponseECR = new clsAttachment();
        tPNLAttach = new clsAttachment();
        tRouting = new clsRouting();
        printCurrentQRSelected = -1;
        iQAFCount = 0;
        saveCount = 0;
        rejectTo = "";
    }

    public void StoreInSession(object tPage)
    {
        NewLateBinding.LateSet(tPage, null, "Session", new object[2]
        {
            "CQR." + Conversions.ToString(iQueueKey),
            this
        }, null, null);
    }

    public bool LoadUserRoles()
    {
        //corePage tPage = new corePage();
        tUserData = new T_UserRole();
        tUserData.bIsPetmOnly = true;
        string sql = "SELECT RLS.RoleCode";
        sql += " FROM USERPROF_UserProfileHeader HDR INNER JOIN";
        sql += " USERPROF_UserRoles RLS ON HDR.QueueKey = RLS.QueueKey";
        sql = sql + " WHERE HDR.UserID = '" + UserId + "'";
        SqlDataReader cIndex = null;//tPage.cnExecute(sql);
        checked
        {
            int iLoop;
            while (cIndex.Read())
            {
                string sRole = "";//Strings.UCase(Strings.Trim(tPage.rstString(ref cIndex, "RoleCode")));
                switch (sRole)
                {
                    case "SAFR":
                        //if (Strings.LCase(HttpContext.Current.Request.ServerVariables["PATH_INFO"]).Contains("_staging"))
                        //{
                        //    tUserData.bIsSAFR = true;
                        //}
                        break;
                    case "AMGR":
                        tUserData.bIsAMgr = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "CQRHRCOST":
                        tUserData.bIsCost = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "CQRHRPGM":
                        tUserData.bIsPgm = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "CQRHRPM":
                        tUserData.bIsPDM = true;
                        break;
                    case "CQRHRAME":
                        tUserData.bIsAME_Coord = true;
                        break;
                    case "CQRHRPUR":
                        tUserData.bIsPUR_Coord = true;
                        break;
                    case "FRAN":
                        tUserData.bIsFran = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "FR00":
                        tUserData.bIsFr00 = true;
                        break;
                    case "MB&E":
                        tUserData.bIsMBnE = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "CQGWNFY":
                        tUserData.bIsCQGWNFY = true;
                        break;
                    case "PETM":
                        tUserData.bIsPetm = true;
                        break;
                    case "PRD":
                        tUserData.bIsPrd = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "PSR":
                        tUserData.bIsPsr = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "PSM":
                        tUserData.bIsPsr = true;
                        tUserData.bIsPsm = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "SDIR":
                        tUserData.bIsSDir = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "BUM":
                        tUserData.bIsBum = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "FIN":
                        tUserData.bIsFin = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "PRES":
                        tUserData.bIsPres = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "UKGM":
                        tUserData.bIsUkGm = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "UKDM":
                        tUserData.bIsUkDm = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "CMGR":
                        tUserData.bIsCmgr = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "BEB":
                        tUserData.bIsBeb = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "MEPL":
                        tUserData.bIsMEPL = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "AIME":
                        tUserData.bIsAIME = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "AIPR":
                        tUserData.bIsAIPR = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "CTLC":
                        tUserData.bIsCTLC = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                    case "PA":
                        tUserData.bIsPA = true;
                        tUserData.bIsPetmOnly = false;
                        break;
                }
                iLoop = 1;
                do
                {
                    if (Operators.CompareString(tGateway[1].sApprovalRole[iLoop], sRole, TextCompare: false) == 0)
                    {
                        tUserData.bIsGateway[iLoop] = true;
                    }
                    iLoop++;
                }
                while (iLoop <= 12);
            }
            tUserData.sManagerFin = modUserProf.mUPGetManager("FIN");
            tUserData.sManagerPres = modUserProf.mUPGetManager("PRES");
            tUserData.sManagerFran = modUserProf.mUPGetManager("FRAN");
            tUserData.sManagerBids = modUserProf.mUPGetManager("MB&E");
            tUserData.sManagerBeb = modUserProf.mUPGetManager("BEB");
            tUserData.sManagerCmgr = modUserProf.mUPGetManager("CMGR");
            iLoop = 1;
            do
            {
                tUserData.sManagerGateway[iLoop] = modUserProf.mUPGetManager(tGateway[1].sApprovalRole[iLoop]);
                iLoop++;
            }
            while (iLoop <= 12);
            bSuperUser = Operators.CompareString(Strings.LCase(UserId), "super", TextCompare: false) == 0;
            //cIndex.Close();
            //tPage.cnClose();
            bool LoadUserRoles = default(bool);
            return LoadUserRoles;
        }
    }

    public bool LoadData(corePage tPage, int aiQueueKey, bool abEditMode, string modifiedProjNbr = "-1", string quoteType = null, string modifiedRevNbr = ""
       )
    {

        string userLocalo = "";
        iQueueKey = aiQueueKey;
        LoadUserRoles();
        if (aiQueueKey != 0)
        {
            if (aiQueueKey == -1)
            {
                bCreate = true;
                bNewCQR = true;
                if ((Conversions.ToDouble(modifiedProjNbr) == -1.0) | (Conversions.ToDouble(modifiedProjNbr) == -2.0))
                {
                    bModify = false;
                }
                else
                {
                    bModify = true;
                }
            }
            else
            {
                iQueueKey = aiQueueKey;
                bNewCQR = false;
            }
            if (!bNewCQR)
            {
                goto IL_022a;
            }
            if (!bModify)
            {
                if (z_CreateNewCQR(tPage))
                {
                    modTRS.mTRSGetNextNumber("FRANPROJ", "003000", ref sProjectNum);
                    sRevision = "0001";
                    if (Conversions.ToDouble(modifiedProjNbr) == -1.0)
                    {
                        sFranNewModRepl = Conversions.ToString(1);
                    }
                    else if (Conversions.ToDouble(modifiedProjNbr) == -2.0)
                    {
                        sFranNewModRepl = Conversions.ToString(3);
                    }
                    sIfCheckedInd = "1";
                    antaresRecords = new Antares[0];
                    ihsRecords = new IHS[0];
                    goto IL_01e0;
                }
            }
            else
            {
                sProjectNum = Strings.Right("000000" + modifiedProjNbr, 6);
                if (Conversions.ToDouble(mLockNextRevision(tPage, "FRAN" + sProjectNum, "0002", ref sRevision)) == 0.0)
                {
                    if (z_CreateNewCQR(tPage))
                    {
                        sFranNewModRepl = Conversions.ToString(2);
                        sIfCheckedInd = "";
                        goto IL_01e0;
                    }
                }
                else
                {
                    //bIsLocked = true;
                }
            }
        }
        bool LoadData = default(bool);
        return LoadData;
    IL_022a:
        iOpenMode = 2;
        if (abEditMode)
        {
            if (tPage.FolderCanBeEdited("CQR_Header", "QueueKey", Conversions.ToString(iQueueKey), UserId))
            {
                tPage.FolderLock("CQR_Header", "QueueKey", Conversions.ToString(iQueueKey), UserId);
                iOpenMode = 1;
            }
            else
            {
                bFolderWasLocked = true;
            }
        }
        string previousRevisionQueueKey = "";
        if (bModify)
        {
            //previousRevisionQueueKey = tPage.cnExecuteForSingleValue(Conversions.ToString(Operators.ConcatenateObject("SELECT QueueKey FROM CQR_Header CH WHERE CH.ProjectNbr = '" + Strings.Right("000000" + sProjectNum, 6) + "' and CH.RevNbr = ", Interaction.IIf(Conversions.ToDouble(modifiedRevNbr) == -1.0, Conversions.ToDouble(sRevision) - 1.0, modifiedRevNbr))));
            if (Operators.CompareString(previousRevisionQueueKey, "", TextCompare: false) == 0)
            {
                previousRevisionQueueKey = Conversions.ToString(0);
            }
        }
        string sSQL = Conversions.ToString(Operators.ConcatenateObject("SELECT CH.* FROM CQR_Header CH WHERE CH.QueueKey = ", Interaction.IIf(bModify, previousRevisionQueueKey, iQueueKey)));
        SqlDataReader drRecVals = tPage.cnExecute(sSQL);
        checked
        {
            if (drRecVals.Read())
            {
                sFolderNum = Conversions.ToString(iQueueKey);
                if (bModify)
                {
                    LoadPartialData(tPage, drRecVals);
                    if (!modUserProf.mUserHasRole(sPDM_aka_ProgramManager, "PDM"))
                    {
                        sPDM_aka_ProgramManager = "";
                    }
                }
                if (!bCreate)
                {
                    sGateway = tPage.rstString(ref drRecVals, "Gateway");
                    sProjectNum = tPage.rstString(ref drRecVals, "ProjectNbr");
                    sRevision = tPage.rstString(ref drRecVals, "RevNbr");
                    sFranNewModRepl = tPage.rstString(ref drRecVals, "NewModRepl");
                    sQuoteType = tPage.rstString(ref drRecVals, "QuoteType");
                    sIfCheckedInd = tPage.rstString(ref drRecVals, "IfCheckedInd");
                    sOriginator = tPage.rstString(ref drRecVals, "OriginatorId");
                    sOriginationDate = tPage.rstString(ref drRecVals, "OriginationDate");
                    sCQRIssueDate = tPage.rstString(ref drRecVals, "FRANIssueDate");
                    sStatus = tPage.rstString(ref drRecVals, "Status");
                    sStatusDate = tPage.rstString(ref drRecVals, "StatusDate");
                    LoadPartialData(tPage, drRecVals);
                    sRFQRefNum = tPage.rstString(ref drRecVals, "RFQRefNbr");
                    sRFQRecDate = tPage.rstString(ref drRecVals, "RFQRcvdDate");
                    sBusinessClass = tPage.rstString(ref drRecVals, "BusinessClassif");
                    sProductLine = tPage.rstString(ref drRecVals, "ProductLine");
                    sResponseLevel = tPage.rstString(ref drRecVals, "ResponseLevel");
                    sWarrantyRqmt = tPage.rstString(ref drRecVals, "WarrantyRqmts");
                    sPerformRqmt = tPage.rstString(ref drRecVals, "PerformanceRqmts");
                    sCurrentSupp = tPage.rstString(ref drRecVals, "CurrentSupplier");
                    sLocationSales = tPage.rstString(ref drRecVals, "LocationSales");
                    sLocationEng = tPage.rstString(ref drRecVals, "LocationEngineer");
                    sLocationShip = tPage.rstString(ref drRecVals, "LocationShipping");
                    sCustomerQuoteDueDate = tPage.rstString(ref drRecVals, "CustQuoteDueDate");
                    sCustomerQuoteDueDateOrig = tPage.rstString(ref drRecVals, "CustQuoteDueDateOrig");
                    sCustomerQuoteDueDateChangeComment = tPage.rstString(ref drRecVals, "CustQuoteDueDateChangeComment");
                    if ((Operators.CompareString(sCustomerQuoteDueDateOrig, "", TextCompare: false) == 0) & (Operators.CompareString(sStatus, "010100FR", TextCompare: false) > 0))
                    {
                        sCustomerQuoteDueDateOrig = sCustomerQuoteDueDate;
                    }
                    sCustomerQuoteDueDateLast = sCustomerQuoteDueDate;
                    sQuoteRespDueDate = tPage.rstString(ref drRecVals, "QuoteResponseDueDate");
                    sCustomerGroup = tPage.rstString(ref drRecVals, "Customergroup");
                    sProductCategory = tPage.rstString(ref drRecVals, "ProductCategory2");
                    sBusinessPlan = tPage.rstString(ref drRecVals, "BusinessPlan");
                    sObsolescenceRequiredInd = tPage.rstString(ref drRecVals, "ObsolescenceReqdInd");
                    sGDPEPPhase1Required = tPage.rstString(ref drRecVals, "GDPIMPhase1Required");
                    sGDPEPPhase1RequiredDbs = sGDPEPPhase1Required;
                    sGDPEPPhase2Required = tPage.rstString(ref drRecVals, "GDPIMPhase2Required");
                    sGDPEPPhase2RequiredDbs = sGDPEPPhase2Required;
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "EngPkgComments"), ref sCommentsEP, ref sNewCommentsEP, ref sOrgCommentsEP, bSuperUser);
                    sNoRCTFolders = tPage.rstString(ref drRecVals, "NoRctFolders");
                    sQRLeadTime = tPage.rstString(ref drRecVals, "QRToolLeadTime");
                    sQRFOB = tPage.rstString(ref drRecVals, "QRFOB");
                    sQRMaterialDate = tPage.rstString(ref drRecVals, "QRMaterialDate");
                    sQRLaborDate = tPage.rstString(ref drRecVals, "QRLaborDate");
                    sQRToolCap = tPage.rstString(ref drRecVals, "QRToolingCapacity");
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "QRToolingComments"), ref sDbsCommentsQRTooling, ref sNewCommentsQRTooling, ref sOrgCommentsQRTooling, bSuperUser);
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "QAFComments"), ref sDbsCommentsQAF, ref sNewCommentsQAF, ref sOrgCommentsQAF, bSuperUser);
                    sPiecePriceInd = tPage.rstString(ref drRecVals, "PiecePriceReqdInd");
                    sToolingInd = tPage.rstString(ref drRecVals, "ToolingReqdInd");
                    sQRCompletedAndSent = tPage.rstString(ref drRecVals, "QuoteCompletedSent");
                    sQRReviewAvailable = tPage.rstString(ref drRecVals, "QuoteSendForReview");
                    sGateExit = tPage.rstString(ref drRecVals, "GateExit");
                    sQSQuoteStatus = tPage.rstString(ref drRecVals, "QSQuoteStatus");
                    sQSAwardStatus = tPage.rstString(ref drRecVals, "QSAwardStatus");
                    sQSQuoteDate = tPage.rstString(ref drRecVals, "QSQuoteDate");
                    sQSDateWon = tPage.rstString(ref drRecVals, "DateWon");
                    sQSSalesAdministrator = tPage.rstString(ref drRecVals, "SalesAdministrator");
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "QSComments"), ref sQSComments, ref sNewQSComments, ref sOrgQSComments, bSuperUser);
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "QSCommentsProceedCancel"), ref sQSCommentsProceedCancel, ref sNewQSCommentsProceedCancel, ref sOrgQSCommentsProceedCancel, bSuperUser);
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "ActionComments"), ref sDbsCommentsAction, ref sNewCommentsAction, ref sOrgCommentsAction, bSuperUser);
                    sQRProgClass = tPage.rstString(ref drRecVals, "QRProgramClass");
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "GDPIMRejectionComments"), ref sDbsCommentsGDPEPRejection, ref sNewCommentsGDPEPRejection, ref sOrgCommentsGDPEPRejection, bSuperUser);
                    sProductDescription = tPage.rstString(ref drRecVals, "ProductDesc");
                    sVehicleBuildId = tPage.rstString(ref drRecVals, "VehicleBuildId");
                    sCustomerSOP = tPage.rstString(ref drRecVals, "CustomerJob1");
                    sRegionalQuotingTeam = tPage.rstString(ref drRecVals, "RegionalQuotingTeam");
                    sCQRDesc = tPage.rstString(ref drRecVals, "FranDesc");
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "VPAComments"), ref sDbsCommentsVolumePerAnnum, ref sNewCommentsVolumePerAnnum, ref sOrgCommentsVolumePerAnnum, bSuperUser);
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "CustProdComments"), ref sDbsCommentsCustomerProductivity, ref sNewCommentsCustomerProductivity, ref sOrgCommentsCustomerProductivity, bSuperUser);
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "IntPricComments"), ref sDbsCommentsTargetPricing, ref sNewCommentsTargetPricing, ref sOrgCommentsTargetPricing, bSuperUser);
                    modTools.CommentSplit(tPage.rstString(ref drRecVals, "BkRndInfComments"), ref sComment, ref sNewComment, ref sOrgComment, bSuperUser);
                    sLeadManufacturingNoCostImpact = tPage.rstString(ref drRecVals, "LeadManufacturingNoCostImpact");
                    sLeadPurchasingNoCostImpact = tPage.rstString(ref drRecVals, "LeadPurchasingNoCostImpact");
                    sLeadValidationNoCostImpact = tPage.rstString(ref drRecVals, "LeadValidationNoCostImpact");
                    LoadHandover(drRecVals);
                }
                if (!bCreate | (Operators.CompareString(sQuoteType, "2", TextCompare: false) == 0))
                {
                    sTimeFrameOkInd = tPage.rstString(ref drRecVals, "TimeframeOKInd");
                    sInformationOkInd = tPage.rstString(ref drRecVals, "InformationOKInd");
                    sWorkProceedOkInd = tPage.rstString(ref drRecVals, "WorkProceedOKInd");
                    sHealthInd = tPage.rstString(ref drRecVals, "HealthAndSafetyInd");
                    sModNeedsCostInd = tPage.rstString(ref drRecVals, "ModNeedsCostInd");
                    sQS9000Considered = tPage.rstString(ref drRecVals, "IfCheckedDoneInd");
                    sDueDateToBidsAndEstimating = tPage.rstString(ref drRecVals, "DueDateToBnE");
                    sDueDateFromEngineering = tPage.rstString(ref drRecVals, "DueDateFromEng");
                    sDateOfCSR = tPage.rstString(ref drRecVals, "DateOfCSR");
                    sEngineerAssigned = tPage.rstString(ref drRecVals, "PRDId");
                    sEngineerAssignedDbs = sEngineerAssigned;
                    sLeadManufacturing = tPage.rstString(ref drRecVals, "LeadManufacturing");
                    sLeadPurchasing = tPage.rstString(ref drRecVals, "LeadPurchasing");
                    sLeadValidation = tPage.rstString(ref drRecVals, "LeadValidation");
                    sLeadManufacturingDbs = sLeadManufacturing;
                    sLeadPurchasingDbs = sLeadPurchasing;
                    sLeadValidationDbs = sLeadValidation;
                    int prdLoop = 1;
                    do
                    {
                        sElecInputReqdInd[prdLoop] = tPage.rstString(ref drRecVals, Conversions.ToString(Operators.ConcatenateObject("ElecInputReqdInd", Interaction.IIf(prdLoop == 1, "", prdLoop))));
                        sElecPRD[prdLoop] = tPage.rstString(ref drRecVals, Conversions.ToString(Operators.ConcatenateObject("ElecPETMId", Interaction.IIf(prdLoop == 1, "", prdLoop))));
                        sElecPRDdbs[prdLoop] = sElecPRD[prdLoop];
                        sElecPRDNoInput[prdLoop] = tPage.rstString(ref drRecVals, "ElecPETMId" + Conversions.ToString(prdLoop) + "NoInput");
                        prdLoop++;
                    }
                    while (prdLoop <= 8);
                }
                sAwardQuarter = tPage.rstString(ref drRecVals, "AwardQuarter");
                sAwardYear = tPage.rstString(ref drRecVals, "AwardYear");
                //if (Conversions.ToBoolean(bCreate && Conversions.ToBoolean(Operators.OrObject(Operators.CompareObjectEqual(_ HttpContext.Current.Session["UserActual"], "ED", TextCompare: false), Operators.CompareString(Environment.MachineName, "ED10", TextCompare: false) == 0))))
                //if (Conversions.ToBoolean(bCreate && Conversions.ToBoolean(Operators.OrObject(Operators.CompareObjectEqual(_ HttpContext.Current.Session["UserActual"], "ED", TextCompare: false), Operators.CompareString(Environment.MachineName, "ED10", TextCompare: false) == 0))))
                {
                    if (Operators.CompareString(sQuoteType, "2", TextCompare: false) != 0)
                    {
                        sNewQSComments = "NewQSComments";
                        sNewCommentsQRTooling = "NewCommentsQRTooling";
                        sNewCommentsQAF = "NewCommentsQAF";
                        sNewCommentsEP = "NewCommentsEP";
                        sModelYear = "000040";
                        sVolumePerAnnum = "90";
                        sRemProductLife = "95";
                        sApproxAnnualValue = "100";
                        sCustomerSOP = Strings.Format(DateAndTime.Now, "yyyyMMdd");
                        sDueDateFromEngineering = Strings.Format(DateAndTime.Now, "yyyyMMdd");
                        sDueDateToBidsAndEstimating = Strings.Format(DateAndTime.Now, "yyyyMMdd");
                        sOpeningMeetingDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
                        sCommercialManager = GetRandomUserForRole("CMGR");
                        sEngineeringManager = GetRandomUserForRole("PETM");
                        sAccountManager = GetRandomUserForRole("AMGR");
                        sSalesAccountDirector = GetRandomUserForRole("SDIR");
                        sLeadValidationNoCostImpact = "1";
                        sLeadValidation = GetRandomUserForRole("CTLC");
                        sLeadManufacturing = GetRandomUserForRole("AIME");
                        sLeadPurchasing = GetRandomUserForRole("AIPR");
                        sCostEstimator = GetRandomUserForRole("QRHRCOST");
                        sEngineerAssigned = GetRandomUserForRole("PRD");
                        sPDM_aka_ProgramManager = GetRandomUserForRole("CQRHRPM");
                        sPGM_aka_CoC = GetRandomUserForRole("CQRHRPGM");
                        sAMECoordinator = GetRandomUserForRole("CQRHRAME");
                        sPurchasingCoordinator = GetRandomUserForRole("CQRHRPUR");
                        sRegionalQuotingTeam = "001000";
                        sAwardQuarter = "Q3";
                        sAwardYear = "2025";
                        sProductCategory = "********";
                        sBusinessPlan = "00000003";
                        sEngineeringSite = "20";
                        sCalculationCurrency = "BRL";
                        sTDRNoInput = "1";
                        int num = Information.UBound(sElecPRDNoInput);
                        for (int iLoop = 0; iLoop <= num; iLoop++)
                        {
                            sElecPRDNoInput[iLoop] = Conversions.ToString(0);
                        }
                        sCommunication = "00003";
                        sASILLevel = "00003";
                        sASILLevel = "00003";
                        sAUTOSAR = "Yes";
                        sCybersecurity = "No";
                    }
                    sCQRDesc = "CQR Desc - " + Conversions.ToString(DateAndTime.Now);
                    sNewCommentsVolumePerAnnum = "NewCommentsVolumePerAnnum";
                    sNewCommentsCustomerProductivity = "NewCommentsCustomerProductivity";
                    sNewCommentsTargetPricing = "NewCommentsTargetPricing";
                    sNewComment = "NewBackgroundInformation (comment)";
                    sGateExit = "00003";
                    sGateway = "1";
                    sCustomerQuoteDueDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
                    sObsolescenceRequiredInd = "0";
                }
            }
            drRecVals.Close();
            mGetUserDataFolder();
            UpdateDefaultDueDateBnE();
            LoadGDPEP(1);
            LoadGDPEP(2);
            LoadPartsList(1, tPage);
            LoadPartsList(2, tPage);
            LoadUnitCost(tPage);
            tRouting.mInitialize("FR", UserId);
            if (bCreate)
            {
                tRouting.mCreateTask("01", "BA", "BA", UserId);
            }
            else
            {
                tRouting.mLoadData(sFolderNum, "FolderType, FolderNbr, (Case When TaskCode='42' AND ActionTaskCode='AA' THEN '26' ELSE TaskCode END), PartDwgNbr, ActionTaskCode, RoutingTaskCode, AssignedTo");
            }
            tAttachmentResponse.mInitialize(UserId, "FR", Strings.Right("**********" + Conversions.ToString(iQueueKey), 10), "ATTACH_CQR");
            tHandoverPackage.mInitialize(UserId, "HP", Strings.Right("**********" + Conversions.ToString(iQueueKey), 10), "ATTACH_CQR_HP");
            tAttachmentResponse.LoadData();
            tHandoverPackage.LoadData();
            tAttachmentResponse.sReAttachText = "replace";
            tTasklist.mInitialize(UserId, "FT", Strings.Right("**********" + Conversions.ToString(iQueueKey), 10), "ATTACH_TASKLIST");
        }
        if (modTools.isLocalhost_() && false)
        {
            tTasklist.mInitializeAceOffix("CQR");
        }
        tTasklist.LoadData();
        tTasklist.sReAttachText = "replace";
        tQuoteResponseFCM.mInitialize(UserId, "QR", Strings.Right("**********" + Conversions.ToString(iQueueKey), 10), "ATTACH_QUOTERESPONSEFCM");
        tQuoteResponseFCM.LoadData();
        tQuoteResponseECR.mInitialize(UserId, "QS", Strings.Right("**********" + Conversions.ToString(iQueueKey), 10), "ATTACH_QUOTERESPONSEECR");
        tQuoteResponseECR.LoadData();
        if (isLegacy())
        {
            ((clsCQRLegacy)this).LoadPNL();
        }
        return true;
    IL_01e0:
        sQuoteType = quoteType;
        sOriginator = UserId;
        sOriginationDate = modTools.mDateScreenToCygnet(Conversions.ToString(DateAndTime.Now));
        sStatus = "010100FR";
        sStatusDate = modTools.mDateScreenToCygnet(Conversions.ToString(DateAndTime.Now));
        goto IL_022a;
    }

    private string GetRandomUserForRole(string roleCode)
    {
        throw new NotImplementedException();
        //corePage tPage = new corePage();
        //return tPage.cnExecuteForSingleValue("SELECT TOP 1 UserId FROM USERPROF_UserProfileHeader UPH INNER JOIN USERPROF_UserRoles UR ON UPH.QueueKey=UR.QueueKey AND UR.RoleCode='" + roleCode + "'");

    }

    private void LoadAntares(int antaresQueueKey, string antaresIdList = "~")
    {
        if (modCQR.isABS())
        {
            return;
        }
        //corePage tPage = new corePage();
        string antSql = "SELECT CAF.*, ";
        antSql += "      (SELECT TOP 1  CAST(ProjectNbr AS INT) FROM CQR_Header WHERE QueueKey = ";
        antSql = antSql + "       (SELECT TOP 1 QueueKey FROM CQR_AntaresFolder WHERE CQR_AntaresFolder.UniqueNumber=CAF.UniqueNumber AND CQR_AntaresFolder.QueueKey=CQR_Header.QueueKey AND ISNULL(Archived,0)=0 AND CQR_Header.ProjectNbr <> '" + sProjectNum + "')";
        antSql += "      ) AS UsedByOtherCQR";
        antSql = antSql + " FROM CQR_AntaresFolder CAF WHERE QueueKey = " + Conversions.ToString(iQueueKey);
        if (Operators.CompareString(antaresIdList, "~", TextCompare: false) != 0)
        {
            antSql = "SELECT *, 0 AS Archived, '' AS UsedByOtherCQR FROM CQR_Antares WHERE UniqueNumber IN (0" + antaresIdList + ")";
        }
        else if (CanEditAntares() & !bSuperUser)
        {
            antSql = "SELECT *, 0 AS Archived, '' AS UsedByOtherCQR FROM CQR_Antares WHERE UniqueNumber IN (SELECT UniqueNumber FROM CQR_AntaresFolder WHERE ISNULL(Archived,0)=0 AND QueueKey=" + Conversions.ToString(antaresQueueKey) + ")";
        }
        SqlDataReader ant = null;//tPage.cnExecute(antSql);
        antaresRecords = new Antares[0];
        while (ant.Read())
        {
            ref Antares[] reference = ref antaresRecords;
            reference = (Antares[])Utils.CopyArray(reference, new Antares[checked(Information.UBound(antaresRecords) + 1 + 1)]);
            ref Antares reference2 = ref antaresRecords[Information.UBound(antaresRecords)];
            //reference2.sUniqueNumber = tPage.rstString(ref ant, "UniqueNumber");
            //reference2.sVB_ID = tPage.rstString(ref ant, "VB_ID");
            //reference2.sOEMGroup = tPage.rstString(ref ant, "OEMGroup");
            //reference2.sOEMCustomer = tPage.rstString(ref ant, "OEM");
            //reference2.sPlatform = tPage.rstString(ref ant, "Platform");
            //reference2.sProgram = tPage.rstString(ref ant, "Program");
            //reference2.sNameplate = tPage.rstString(ref ant, "Nameplate");
            //reference2.sCountry = tPage.rstString(ref ant, "Country");
            //reference2.sRegion = tPage.rstString(ref ant, "Region");
            //reference2.sNewBusinessCategory = tPage.rstString(ref ant, "NewBusinessCategory");
            //reference2.sStatus = tPage.rstString(ref ant, "Status");
            //reference2.sProductId = tPage.rstString(ref ant, "ProductId");
            //reference2.sProductDescription = tPage.rstString(ref ant, "ProductDescription");
            //reference2.sProductGrouping = tPage.rstString(ref ant, "ProductGrouping");
            //reference2.sSOP = tPage.rstString(ref ant, "SOP");
            //reference2.sEOP = tPage.rstString(ref ant, "EOP");
            //reference2.sSoldFrom = tPage.rstString(ref ant, "SoldFrom");
            //reference2.sFinalAssembly = tPage.rstString(ref ant, "FinalAssembly");
            //reference2.sUsedByOtherCQR = tPage.rstString(ref ant, "UsedByOtherCQR");
            //reference2.bArchived = tPage.rstInt(ref ant, "Archived") != 0;
            if (Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0)
            {
                string strSql = "";
                strSql += "SELECT TOP 1 ProjectNbr FROM CQR_Header ";
                strSql += " INNER JOIN CQR_AntaresFolder ON CQR_AntaresFolder.QueueKey=CQR_Header.QueueKey AND ISNULL(CQR_AntaresFolder.Archived,0)=0";
                strSql = strSql + " WHERE CQR_Header.Status>'010100FR' AND CAST(CQR_Header.ProjectNbr AS Int) <> " + Conversions.ToString(Conversion.Val(sProjectNum)) + " AND CQR_AntaresFolder.UniqueNumber=" + reference2.sUniqueNumber;
                //reference2.sUsedByOtherCQR = tPage.cnExecuteForSingleValue(strSql);
            }
        }
        //ant.Close();
        //tPage.cnClose();
    }

    private void LoadIHS(int IHSQueueKey, string IHSIdList = "???")
    {
        //corePage tPage = new corePage();
        string IHSSql = "SELECT CAF.* FROM CQR_IHSFolder CAF WHERE QueueKey = " + Conversions.ToString(iQueueKey);
        string[] ihsArray = Strings.Split(IHSIdList, ",");
        string thisList = "'fake_entry'";
        if (Operators.CompareString(IHSIdList, "???", TextCompare: false) != 0)
        {
            string[] array = ihsArray;
            foreach (string thisItem in array)
            {
                if (Operators.CompareString(thisItem, "", TextCompare: false) != 0)
                {
                    thisList = thisList + "," + Conversions.ToString(Conversion.Val(thisItem));
                }
            }
            IHSSql = "SELECT *, 0 AS Archived FROM CQR_IHS WHERE CoreNameplatePlantMnemonic IN (" + thisList + ")";
        }
        else if (CanEditIHS() & !bSuperUser)
        {
            IHSSql = "SELECT CQR_IHS.*, ExtraInfo.*, 0 AS Archived FROM CQR_IHS ";
            IHSSql = IHSSql + " OUTER APPLY (SELECT ProductDescription, SoldFrom, FinalAssembly FROM CQR_IHSFolder WHERE QueueKey=" + Conversions.ToString(IHSQueueKey) + " AND CQR_IHS.CoreNameplatePlantMnemonic=CQR_IHSFolder.CoreNameplatePlantMnemonic) ExtraInfo ";
            IHSSql = IHSSql + " WHERE CoreNameplatePlantMnemonic In (Select CoreNameplatePlantMnemonic FROM CQR_IHSFolder WHERE ISNULL(Archived, 0)=0 And QueueKey=" + Conversions.ToString(IHSQueueKey) + ")";
        }
        SqlDataReader IHS = null;// tPage.cnExecute(IHSSql);
        ihsRecords = new IHS[0];
        checked
        {
            while (IHS.Read())
            {
                ref IHS[] reference = ref ihsRecords;
                reference = (IHS[])Utils.CopyArray(reference, new IHS[Information.UBound(ihsRecords) + 1 + 1]);
                ref IHS reference2 = ref ihsRecords[Information.UBound(ihsRecords)];
                //reference2.sCoreNameplatePlantMnemonic = tPage.rstString(ref IHS, "CoreNameplatePlantMnemonic");
                //reference2.sOEMGroup = tPage.rstString(ref IHS, "OEMGroup");
                //reference2.sOEMCustomer = tPage.rstString(ref IHS, "OEM");
                //reference2.sPlatform = tPage.rstString(ref IHS, "Platform");
                //reference2.sProgram = tPage.rstString(ref IHS, "Program");
                //reference2.sNameplate = tPage.rstString(ref IHS, "ProductionNameplate");
                //reference2.sCountry = tPage.rstString(ref IHS, "Country");
                //reference2.sRegion = tPage.rstString(ref IHS, "Region");
                //reference2.sSOP = tPage.rstString(ref IHS, "StartOfProduction");
                //reference2.sEOP = tPage.rstString(ref IHS, "EndOfProduction");
                //reference2.sProductDescription = tPage.rstString(ref IHS, "ProductDescription");
                //reference2.sSoldFrom = tPage.rstString(ref IHS, "SoldFrom");
                //reference2.sFinalAssembly = tPage.rstString(ref IHS, "FinalAssembly");
                if (Operators.CompareString(IHSIdList, "???", TextCompare: false) != 0)
                {
                    int num = Information.UBound(ihsArray);
                    for (int ihsLoop = 0; ihsLoop <= num; ihsLoop++)
                    {
                        if (Conversion.Val(ihsArray[ihsLoop]) == Conversion.Val(reference2.sCoreNameplatePlantMnemonic))
                        {
                            string[] theInfo = Strings.Split(ihsArray[ihsLoop] + "~~~", "~");
                            reference2.sProductDescription = theInfo[1];
                            reference2.sSoldFrom = theInfo[2];
                            reference2.sFinalAssembly = theInfo[3];
                        }
                    }
                }
                //reference2.bArchived = tPage.rstInt(ref IHS, "Archived") != 0;
            }
            //IHS.Close();
            //tPage.cnClose();
        }
    }

    private void LoadGDPEP(int iPhase)
    {
        string strSql = "SELECT * FROM CQR_GDPIMPhase WHERE QueueKey = " + Conversions.ToString(iQueueKey);
        strSql = strSql + " AND GDPIMPhase = " + Conversions.ToString(iPhase);
        //corePage tPage = new corePage();
        SqlDataReader rst = null;//tPage.cnExecute(strSql);
        checked
        {
            if (rst.Read())
            {
                T_GDPEP_APPROVAL t_GDPEP_APPROVAL = tGateway[iPhase];
                //t_GDPEP_APPROVAL.iGDPEPPhaseId = tPage.rstInt(ref rst, "GDPIMPhaseId");
                //t_GDPEP_APPROVAL.sGDPIMCategory = tPage.rstString(ref rst, "Category");
                //t_GDPEP_APPROVAL.sReleaseDate = tPage.rstString(ref rst, "ReleaseDate");
                int iLoop = 1;
                do
                {
                    //t_GDPEP_APPROVAL.sVolume[iLoop] = tPage.rstString(ref rst, "Volume" + Conversions.ToString(iLoop));
                    iLoop++;
                }
                while (iLoop <= 7);
                iLoop = 1;
                do
                {
                    //t_GDPEP_APPROVAL.sSellPrice[iLoop] = tPage.rstString(ref rst, "SellPrice" + Conversions.ToString(iLoop));
                    iLoop++;
                }
                while (iLoop <= 10);
                iLoop = 1;
                do
                {
                    //t_GDPEP_APPROVAL.sApprovalUser[iLoop] = tPage.rstString(ref rst, "ApprovalUser" + Conversions.ToString(iLoop));
                    //t_GDPEP_APPROVAL.sApprovalDate[iLoop] = tPage.rstString(ref rst, "ApprovalDate" + Conversions.ToString(iLoop));
                    //t_GDPEP_APPROVAL.sRequiredState[iLoop] = tPage.rstString(ref rst, "RequiredState" + Conversions.ToString(iLoop));
                    //t_GDPEP_APPROVAL.sApprovalState[iLoop] = tPage.rstString(ref rst, "ApprovalState" + Conversions.ToString(iLoop));
                    t_GDPEP_APPROVAL.sApprovalStateDbs[iLoop] = t_GDPEP_APPROVAL.sApprovalState[iLoop];
                    iLoop++;
                }
                while (iLoop <= 12);
                //t_GDPEP_APPROVAL.sComments = tPage.rstString(ref rst, "Comment");
                t_GDPEP_APPROVAL.tAttachment.mInitialize(UserId, "CQR_GW" + Conversions.ToString(iPhase), Strings.Right("**********" + Conversions.ToString(iQueueKey), 10), "ATTACH_GW" + Conversions.ToString(iPhase));
                t_GDPEP_APPROVAL.tAttachment.LoadData();
                t_GDPEP_APPROVAL = null;
            }
            rst.Close();
            //tPage.cnClose();
        }
    }

    private void SaveGDPEP(int iPhase)
    {
        //corePage tPage = new corePage();
        T_GDPEP_APPROVAL t_GDPEP_APPROVAL = tGateway[iPhase];
        string strSql;
        if (t_GDPEP_APPROVAL.iGDPEPPhaseId <= 0)
        {
            strSql = "INSERT INTO CQR_GDPIMPhase (QueueKey, GDPIMPhase) VALUES(" + Conversions.ToString(iQueueKey) + "," + Conversions.ToString(iPhase) + ")";
            //t_GDPEP_APPROVAL.iGDPEPPhaseId = tPage.cnExecuteGetIdentity(strSql);
        }
        strSql = "UPDATE CQR_GDPIMPhase SET ";
        strSql += z_AddUpdateField("Comment", t_GDPEP_APPROVAL.sComments);
        int iLoop = 1;
        checked
        {
            do
            {
                strSql += z_AddUpdateField("Volume" + Conversions.ToString(iLoop), t_GDPEP_APPROVAL.sVolume[iLoop]);
                iLoop++;
            }
            while (iLoop <= 7);
            iLoop = 1;
            do
            {
                strSql += z_AddUpdateField("SellPrice" + Conversions.ToString(iLoop), t_GDPEP_APPROVAL.sSellPrice[iLoop]);
                iLoop++;
            }
            while (iLoop <= 10);
            iLoop = 1;
            do
            {
                strSql += z_AddUpdateField("ApprovalUser" + Conversions.ToString(iLoop), t_GDPEP_APPROVAL.sApprovalUser[iLoop]);
                strSql += z_AddUpdateFieldDate("ApprovalDate" + Conversions.ToString(iLoop), t_GDPEP_APPROVAL.sApprovalDate[iLoop]);
                strSql += z_AddUpdateField("ApprovalState" + Conversions.ToString(iLoop), t_GDPEP_APPROVAL.sApprovalState[iLoop]);
                strSql += z_AddUpdateField("RequiredState" + Conversions.ToString(iLoop), t_GDPEP_APPROVAL.sRequiredState[iLoop]);
                t_GDPEP_APPROVAL.sApprovalStateDbs[iLoop] = t_GDPEP_APPROVAL.sApprovalState[iLoop];
                iLoop++;
            }
            while (iLoop <= 12);
            strSql += z_AddUpdateFieldDate("ReleaseDate", t_GDPEP_APPROVAL.sReleaseDate);
            strSql = strSql + "Category = '" + t_GDPEP_APPROVAL.sGDPIMCategory + "'";
            //t_GDPEP_APPROVAL.tAttachment.SaveData(tPage, sStatus);
            t_GDPEP_APPROVAL = null;
            strSql = strSql + "WHERE QueueKey = " + Conversions.ToString(iQueueKey) + " AND GDPIMPhase = " + Conversions.ToString(iPhase);
            //tPage.cnExecuteNonQuery(strSql);
        }
    }

    public void LoadHandoverRoles()
    {
        bHandoverApprovalRole[1] = tUserData.bIsAMgr;
        bHandoverApprovalRole[2] = tUserData.bIsSDir;
        bHandoverApprovalRole[3] = tUserData.bIsFr00;
        bHandoverApprovalRole[4] = tUserData.bIsPDM;
    }

    private void LoadHandover(SqlDataReader rst)
    {
        //corePage tPage = new corePage();
        LoadHandoverRoles();
        //sHandoverComment = tPage.rstString(ref rst, "HandoverComment");
    }

    private string SaveHandover()
    {
        return "";
    }

    private void LoadPartialData(corePage tPage, SqlDataReader drRecVals)
    {
        sVehicleBuildId = tPage.rstString(ref drRecVals, "VehicleBuildId");
        sProductDescription = tPage.rstString(ref drRecVals, "ProductDesc");
        sOEMGroup = tPage.rstString(ref drRecVals, "CustNbr");
        sPlatform = tPage.rstString(ref drRecVals, "Vehicle");
        sNameplate = tPage.rstString(ref drRecVals, "Nameplate");
        sProductCategory = tPage.rstString(ref drRecVals, "ProductCategory2");
        sBusinessPlan = tPage.rstString(ref drRecVals, "BusinessPlan");
        sCustomerGroup = tPage.rstString(ref drRecVals, "Customergroup");
        sCustomerSOP = tPage.rstString(ref drRecVals, "CustomerJob1");
        sRegionalQuotingTeam = tPage.rstString(ref drRecVals, "RegionalQuotingTeam");
        sModelYear = tPage.rstString(ref drRecVals, "ModelYear");
        sVolumePerAnnum = tPage.rstString(ref drRecVals, "VolumePerAnnum");
        sOrigProductLife = tPage.rstString(ref drRecVals, "OriginalProductLife");
        sRemProductLife = tPage.rstString(ref drRecVals, "RemainingProductLife");
        sApproxAnnualValue = tPage.rstString(ref drRecVals, "ApproxAnnualValue");
        sOpeningMeetingDate = tPage.rstString(ref drRecVals, "OpeningMeetingDate");
        sCustomerBuyer = tPage.rstString(ref drRecVals, "CustBuyerName");
        sCustomerEngineer = tPage.rstString(ref drRecVals, "CustEngineerName");
        sAccountManager = tPage.rstString(ref drRecVals, "AccountMgrId");
        sAccountManagerDbs = sAccountManager;
        sCommercialManager = tPage.rstString(ref drRecVals, "CommercialManager");
        sCommercialManagerDbs = sCommercialManager;
        sCommercialBusinessManager = tPage.rstString(ref drRecVals, "CommercialBusinessManager");
        sSalesAccountDirector = tPage.rstString(ref drRecVals, "SalesAcctDirectorId");
        sSalesAccountDirectorDbs = sSalesAccountDirector;
        sCostEstimator = tPage.rstString(ref drRecVals, "CostEstimatorId");
        sCostEstimatorDbs = sCostEstimator;
        sPGM_aka_CoC = tPage.rstString(ref drRecVals, "PGMId");
        sPGM_aka_CoCDbs = sPGM_aka_CoC;
        sPDM_aka_ProgramManager = tPage.rstString(ref drRecVals, "PETMId");
        sPDM_aka_ProgramManagerDbs = sPDM_aka_ProgramManager;
        sAMECoordinator = tPage.rstString(ref drRecVals, "AMECoordinator");
        sAMECoordinatorDbs = sAMECoordinator;
        sPurchasingCoordinator = tPage.rstString(ref drRecVals, "PurchasingCoordinator");
        sPurchasingCoordinatorDbs = sPurchasingCoordinator;
        sTDRDate = tPage.rstString(ref drRecVals, "TDRDate");
        sTDRNoInput = tPage.rstString(ref drRecVals, "TDRNoInput");
        sCommunication = tPage.rstString(ref drRecVals, "Communication");
        sASILLevel = tPage.rstString(ref drRecVals, "ASILLevel");
        sAUTOSAR = tPage.rstString(ref drRecVals, "AUTOSAR");
        sCybersecurity = tPage.rstString(ref drRecVals, "Cybersecurity");
        sOtherRelevantFeatures = tPage.rstString(ref drRecVals, "OtherRelevantFeatures");
        sPA = tPage.rstString(ref drRecVals, "PAId");
        sPSM = tPage.rstString(ref drRecVals, "PSMId");
        sEngineeringManager = tPage.rstString(ref drRecVals, "EngineeringManagerId");
        sEngineeringManagerDbs = sEngineeringManager;
        sCalculationCurrency = tPage.rstString(ref drRecVals, "CalculationCurrency");
        sManufacturingSite = tPage.rstString(ref drRecVals, "ManufacturingSite");
        sEngineeringSite = tPage.rstString(ref drRecVals, "EngineeringSite");
        sEngineeringSiteDbs = sEngineeringSite;
        sPIMSite = tPage.rstString(ref drRecVals, "PIMSite");
        sPIMSiteDbs = sPIMSite;
        string sTemp = modTRS.mTRSGetSingleDesc("MFGSITE", sManufacturingSite);
        if (((Operators.CompareString(sTemp, "", TextCompare: false) == 0) | (Operators.CompareString(sTemp, sManufacturingSite, TextCompare: false) == 0)) & (Operators.CompareString(sManufacturingSite, "", TextCompare: false) != 0))
        {
            sTemp = modTRS.mTRSGetSingleDesc("MFGSITE", "00000" + sManufacturingSite);
            if (Operators.CompareString(sTemp, "", TextCompare: false) != 0)
            {
                sManufacturingSite = "00000" + sManufacturingSite;
            }
        }
        sManufacturingSiteDbs = sManufacturingSite;
        sQRProgClass = tPage.rstString(ref drRecVals, "QRProgramClass");
        LoadAntares(tPage.rstInt(ref drRecVals, "QueueKey"));
        LoadIHS(tPage.rstInt(ref drRecVals, "QueueKey"));
    }

    public void UpdateDefaultDueDateBnE()
    {
        if (Operators.CompareString(sCustomerQuoteDueDateLast, sCustomerQuoteDueDate, TextCompare: false) != 0)
        {
            sCustomerQuoteDueDateLast = sCustomerQuoteDueDate;
            if (Information.IsDate(sCustomerQuoteDueDate) && Information.IsDate(sDueDateToBidsAndEstimating))
            {
                mTRSDateDecrement(sCustomerQuoteDueDate, ref sDueDateToBidsAndEstimating, Conversions.ToInteger(Interaction.IIf(Conversion.Val(sRevision) == 1.0, 4, 2)));
            }
        }
    }

    public string GetAntaresList()
    {
        string lstAnt = "0";
        Antares[] array = antaresRecords;
        for (int i = 0; i < array.Length; i = checked(i + 1))
        {
            Antares antItem = array[i];
            lstAnt = lstAnt + "," + antItem.sUniqueNumber;
        }
        return lstAnt;
    }

    public string GetIHSList()
    {
        string lstIHS = "0";
        IHS[] array = ihsRecords;
        for (int i = 0; i < array.Length; i = checked(i + 1))
        {
            IHS IHSItem = array[i];
            lstIHS = lstIHS + "," + IHSItem.sCoreNameplatePlantMnemonic;
        }
        return lstIHS;
    }

    public bool SaveData(corePage tPage, bool bExit, bool bRelease = false)
    {
        if (iQueueKey != 0)
        {
            string sSQL = "UPDATE CQR_Header SET ";
            sSQL += z_AddUpdateField("ProjectNbr", sProjectNum);
            sSQL += z_AddUpdateField("RevNbr", sRevision);
            sSQL += z_AddUpdateField("NewModRepl", sFranNewModRepl);
            sSQL += z_AddUpdateField("QuoteType", sQuoteType);
            sSQL += z_AddUpdateField("Status", sStatus);
            sSQL += z_AddUpdateField("StatusDate", sStatusDate);
            sSQL += z_AddUpdateField("OriginatorId", sOriginator);
            sSQL += z_AddUpdateField("OriginationDate", sOriginationDate);
            sSQL += z_AddUpdateField("FRANIssueDate", sCQRIssueDate);
            sSQL += z_AddUpdateField("FranDesc", sCQRDesc);
            sSQL += z_AddUpdateField("AwardQuarter", sAwardQuarter);
            sSQL += z_AddUpdateField("AwardYear", sAwardYear);
            sSQL += z_AddUpdateField("GateExit", sGateExit);
            sSQL += z_AddUpdateField("ProductDesc", sProductDescription);
            sSQL += z_AddUpdateField("VehicleBuildId", sVehicleBuildId);
            sSQL += z_AddUpdateField("CustNbr", sOEMGroup);
            sSQL += z_AddUpdateField("Vehicle", sPlatform);
            sSQL += z_AddUpdateField("Nameplate", sNameplate);
            sSQL += z_AddUpdateField("VolumePerAnnum", sVolumePerAnnum);
            sSQL += z_AddUpdateField("RemainingProductLife", sRemProductLife);
            sSQL += z_AddUpdateField("Gateway", sGateway);
            string sCommentString = modTools.CommentStamp(sDbsCommentsVolumePerAnnum, ref sNewCommentsVolumePerAnnum, ref sOrgCommentsVolumePerAnnum, abReleasing: true, UserId, bSuperUser: false);
            sSQL += z_AddUpdateField("VPAComments", sCommentString);
            sSQL += z_AddUpdateField("ApproxAnnualValue", sApproxAnnualValue);
            sSQL += z_AddUpdateField("OpeningMeetingDate", modTools.mDateScreenToCygnet(sOpeningMeetingDate));
            sSQL += z_AddUpdateField("CustomerJob1", modTools.mDateScreenToCygnet(sCustomerSOP));
            sSQL += z_AddUpdateField("RegionalQuotingTeam", modTools.mDateScreenToCygnet(sRegionalQuotingTeam));
            sCommentString = modTools.CommentStamp(sDbsCommentsTargetPricing, ref sNewCommentsTargetPricing, ref sOrgCommentsTargetPricing, abReleasing: true, UserId, bSuperUser: false);
            sSQL += z_AddUpdateField("IntPricComments", sCommentString);
            sSQL += z_AddUpdateField("CustQuoteDueDate", modTools.mDateScreenToCygnet(sCustomerQuoteDueDate));
            sSQL += z_AddUpdateField("CustQuoteDueDateOrig", modTools.mDateScreenToCygnet(sCustomerQuoteDueDateOrig));
            sSQL += z_AddUpdateField("CustQuoteDueDateChangeComment", modTools.mDateScreenToCygnet(sCustomerQuoteDueDateChangeComment));
            sSQL += z_AddUpdateField("ModelYear", sModelYear);
            sCommentString = modTools.CommentStamp(sComment, ref sNewComment, ref sOrgComment, abReleasing: true, UserId, bSuperUser: false);
            sSQL += z_AddUpdateField("BkRndInfComments", sCommentString);
            sSQL += z_AddUpdateField("CustBuyerName", sCustomerBuyer);
            sSQL += z_AddUpdateField("CustEngineerName", sCustomerEngineer);
            sSQL += z_AddUpdateField("ObsolescenceReqdInd", sObsolescenceRequiredInd);
            sCommentString = modTools.CommentStamp(sDbsCommentsCustomerProductivity, ref sNewCommentsCustomerProductivity, ref sOrgCommentsCustomerProductivity, abReleasing: true, UserId, bSuperUser: false);
            sSQL += z_AddUpdateField("CustProdComments", sCommentString);
            sSQL += z_AddUpdateField("AccountMgrId", sAccountManager);
            sSQL += z_AddUpdateField("CommercialManager", sCommercialManager);
            sSQL += z_AddUpdateField("CommercialBusinessManager", sCommercialBusinessManager);
            sSQL += z_AddUpdateField("SalesAcctDirectorId", sSalesAccountDirector);
            sSQL += z_AddUpdateField("CostEstimatorId", sCostEstimator);
            sSQL += z_AddUpdateField("PGMId", sPGM_aka_CoC);
            sSQL += z_AddUpdateField("PETMId", sPDM_aka_ProgramManager);
            sSQL += z_AddUpdateField("AMECoordinator", sAMECoordinator);
            sSQL += z_AddUpdateField("PurchasingCoordinator", sPurchasingCoordinator);
            sSQL += z_AddUpdateField("TDRDate", sTDRDate);
            sSQL += z_AddUpdateField("TDRNoInput", sTDRNoInput);
            sSQL += z_AddUpdateField("Communication", sCommunication);
            sSQL += z_AddUpdateField("ASILLevel", sASILLevel);
            sSQL += z_AddUpdateField("AUTOSAR", sAUTOSAR);
            sSQL += z_AddUpdateField("Cybersecurity", sCybersecurity);
            sSQL += z_AddUpdateField("OtherRelevantFeatures", sOtherRelevantFeatures);
            sSQL += z_AddUpdateField("PAId", sPA);
            sSQL += z_AddUpdateField("PSMId", sPSM);
            sSQL += z_AddUpdateField("EngineeringManagerId", sEngineeringManager);
            sSQL += z_AddUpdateField("CalculationCurrency", sCalculationCurrency);
            sSQL += z_AddUpdateField("Customergroup", sCustomerGroup);
            sSQL += z_AddUpdateField("ProductCategory2", sProductCategory);
            sSQL += z_AddUpdateField("BusinessPlan", sBusinessPlan);
            sSQL += z_AddUpdateField("TimeframeOKInd", sTimeFrameOkInd);
            sSQL += z_AddUpdateField("InformationOKInd", sInformationOkInd);
            sSQL += z_AddUpdateField("WorkProceedOKInd", sWorkProceedOkInd);
            sSQL += z_AddUpdateField("HealthAndSafetyInd", sHealthInd);
            sSQL += z_AddUpdateField("ModNeedsCostInd", sModNeedsCostInd);
            sSQL += z_AddUpdateField("IfCheckedDoneInd", sQS9000Considered);
            sSQL += z_AddUpdateField("DueDateToBnE", modTools.mDateScreenToCygnet(sDueDateToBidsAndEstimating));
            sSQL += z_AddUpdateField("DueDateFromEng", modTools.mDateScreenToCygnet(sDueDateFromEngineering));
            sSQL += z_AddUpdateField("DateOfCSR", modTools.mDateScreenToCygnet(sDateOfCSR));
            sSQL += z_AddUpdateField("PRDId", sEngineerAssigned);
            sSQL += z_AddUpdateField("LeadManufacturing", sLeadManufacturing);
            sSQL += z_AddUpdateField("LeadPurchasing", sLeadPurchasing);
            sSQL += z_AddUpdateField("LeadValidation", sLeadValidation);
            sSQL += z_AddUpdateField("LeadManufacturingNoCostImpact", sLeadManufacturingNoCostImpact);
            sSQL += z_AddUpdateField("LeadPurchasingNoCostImpact", sLeadPurchasingNoCostImpact);
            sSQL += z_AddUpdateField("LeadValidationNoCostImpact", sLeadValidationNoCostImpact);
            int prdLoop = 1;
            bool addedTask;
            checked
            {
                do
                {
                    sSQL += z_AddUpdateField(Conversions.ToString(Operators.ConcatenateObject("ElecInputReqdInd", Interaction.IIf(prdLoop == 1, "", prdLoop))), sElecInputReqdInd[prdLoop]);
                    sSQL += z_AddUpdateField(Conversions.ToString(Operators.ConcatenateObject("ElecPETMId", Interaction.IIf(prdLoop == 1, "", prdLoop))), sElecPRD[prdLoop]);
                    sSQL += z_AddUpdateField("ElecPETMId" + Conversions.ToString(prdLoop) + "NoInput", sElecPRDNoInput[prdLoop]);
                    sElecPRDdbs[prdLoop] = sElecPRD[prdLoop];
                    prdLoop++;
                }
                while (prdLoop <= 8);
                sSQL += z_AddUpdateField("ManufacturingSite", Strings.Right(sManufacturingSite, 3));
                sSQL += z_AddUpdateField("EngineeringSite", sEngineeringSite);
                sSQL += z_AddUpdateField("PIMSite", sPIMSite);
                sCommentString = modTools.CommentStamp(sCommentsEP, ref sNewCommentsEP, ref sOrgCommentsEP, abReleasing: true, UserId, bSuperUser: false);
                sSQL += z_AddUpdateField("EngPkgComments", sCommentString);
                sSQL += z_AddUpdateField("NoRctFolders", sNoRCTFolders);
                sSQL += z_AddUpdateField("GDPIMPhase1Required", sGDPEPPhase1Required);
                sSQL += z_AddUpdateField("GDPIMPhase2Required", sGDPEPPhase2Required);
                sSQL += z_AddUpdateField("QRToolLeadTime", sQRLeadTime);
                sSQL += z_AddUpdateField("QRFOB", sQRFOB);
                string sTemp = Conversions.ToString(Interaction.IIf(Operators.CompareString(sQRMaterialDate, "N/A", TextCompare: false) != 0, sQRMaterialDate, ""));
                sSQL += z_AddUpdateField("QRMaterialDate", modTools.mDateScreenToCygnet(sTemp));
                sTemp = Conversions.ToString(Interaction.IIf(Operators.CompareString(sQRLaborDate, "N/A", TextCompare: false) != 0, sQRLaborDate, ""));
                sSQL += z_AddUpdateField("QRLaborDate", modTools.mDateScreenToCygnet(sTemp));
                sSQL += z_AddUpdateField("QRToolingCapacity", sQRToolCap);
                sCommentString = modTools.CommentStamp(sDbsCommentsQRTooling, ref sNewCommentsQRTooling, ref sOrgCommentsQRTooling, abReleasing: true, UserId, bSuperUser: false);
                sSQL += z_AddUpdateField("QRToolingComments", sCommentString);
                sCommentString = modTools.CommentStamp(sDbsCommentsQAF, ref sNewCommentsQAF, ref sOrgCommentsQAF, abReleasing: true, UserId, bSuperUser: false);
                sSQL += z_AddUpdateField("QAFComments", sCommentString);
                sSQL += z_AddUpdateField("QRProgramClass", sQRProgClass);
                sSQL += z_AddUpdateField("QuoteCompletedSent", sQRCompletedAndSent);
                sSQL += z_AddUpdateField("QuoteSendForReview", sQRReviewAvailable);
                sSQL += z_AddUpdateField("QSQuoteStatus", sQSQuoteStatus);
                sSQL += z_AddUpdateField("QSAwardStatus", sQSAwardStatus);
                sSQL += z_AddUpdateField("QSQuoteDate", modTools.mDateScreenToCygnet(sQSQuoteDate));
                sSQL += z_AddUpdateField("DateWon", modTools.mDateScreenToCygnet(sQSDateWon));
                sSQL += z_AddUpdateField("SalesAdministrator", modTools.mDateScreenToCygnet(sQSSalesAdministrator));
                sCommentString = modTools.CommentStamp(sQSComments, ref sNewQSComments, ref sOrgQSComments, abReleasing: true, UserId, bSuperUser: false);
                sSQL += z_AddUpdateField("QSComments", sCommentString);
                sCommentString = modTools.CommentStamp(sQSCommentsProceedCancel, ref sNewQSCommentsProceedCancel, ref sOrgQSCommentsProceedCancel, abReleasing: true, UserId, bSuperUser: false);
                sSQL += z_AddUpdateField("QSCommentsProceedCancel", sCommentString);
                sCommentString = modTools.CommentStamp(sDbsCommentsAction, ref sNewCommentsAction, ref sOrgCommentsAction, abReleasing: true, UserId, bSuperUser: false);
                sSQL += z_AddUpdateField("ActionComments", sCommentString);
                sCommentString = modTools.CommentStamp(sDbsCommentsGDPEPRejection, ref sNewCommentsGDPEPRejection, ref sOrgCommentsGDPEPRejection, abReleasing: true, UserId, bSuperUser: false);
                sSQL += z_AddUpdateField("GDPIMRejectionComments", sCommentString);
                sSQL += SaveHandover();
                if (bExit)
                {
                    sSQL += " lock_userid = NULL, lock_time = NULL,";
                }
                sSQL = sSQL + " LastUpdatedDate = '" + modTools.mDateScreenToCygnet(DateAndTime.Today.ToString()) + "', ";
                sSQL = sSQL + " LastUpdatedTime = '" + Strings.Format(DateAndTime.Now, "HHmm") + "', ";
                sSQL += " LastUpdatedVersion = 'v2.2.01i', ";
                sSQL = sSQL + " LastUpdatedBy = '" + UserId + "'";
                sSQL = sSQL + " WHERE QueueKey = " + Conversions.ToString(iQueueKey);
                tPage.cnExecuteNonQuery(sSQL);
                if (CanEditAntares())
                {
                    string antaresRecordList = "0";
                    Antares[] array = antaresRecords;
                    for (int i = 0; i < array.Length; i++)
                    {
                        Antares antRec = array[i];
                        antaresRecordList = antaresRecordList + "," + antRec.sUniqueNumber;
                    }
                    string antaresFields = "UniqueNumber, DateAdded, DateUpdated, VB_ID, OEMGroup, OEM, Platform, ";
                    antaresFields += " Program, Nameplate, Country, Region, NewBusinessCategory, Status, ProductId, ProductDescription, SoldFrom, SOP, EOP, FinalAssembly, ProductGrouping ";
                    sSQL = "DELETE FROM CQR_AntaresFolder WHERE ISNULL(Archived,0)=0 AND QueueKey = " + Conversions.ToString(iQueueKey) + ";";
                    sSQL = sSQL + "INSERT INTO CQR_AntaresFolder (QueueKey, " + antaresFields + ")";
                    sSQL = sSQL + " (SELECT " + Conversions.ToString(iQueueKey) + "," + antaresFields + " FROM CQR_Antares WHERE UniqueNumber IN (0" + antaresRecordList + "))";
                    if (bSuperUser)
                    {
                        sSQL = sSQL + "DELETE FROM CQR_AntaresFolder WHERE ISNULL(Archived,0)=1 AND UniqueNumber NOT IN (0" + antaresRecordList + ") AND QueueKey = " + Conversions.ToString(iQueueKey) + ";";
                    }
                    tPage.cnExecuteNonQuery(sSQL);
                }
                if (!modCQR.isABS())
                {
                    string lstAnt = GetAntaresList();
                    string sqlAnt2 = "UPDATE CQR_AntaresFolder SET Archived=null, ArchivedByQueueKey=null, ArchivedDate=null, ArchivedByUser=null";
                    sqlAnt2 = sqlAnt2 + " WHERE UniqueNumber NOT IN (" + lstAnt + ") AND ArchivedByQueueKey=" + Conversions.ToString(iQueueKey);
                    tPage.cnExecuteNonQuery(sqlAnt2);
                }
                if (CanEditIHS())
                {
                    string IHSRecordList = "";
                    IHS[] array2 = ihsRecords;
                    for (int j = 0; j < array2.Length; j++)
                    {
                        IHS IHSRec = array2[j];
                        IHSRecordList = Conversions.ToString(Operators.ConcatenateObject(IHSRecordList, Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(IHSRecordList, "", TextCompare: false) == 0, "", ","), IHSRec.sCoreNameplatePlantMnemonic)));
                    }
                    string IHSFields = "CoreNameplatePlantMnemonic, Region, Country, Platform, Program, ProductionNameplate, StartOfProduction, EndOfProduction, OEMGroup, OEM  ";
                    sSQL = "DELETE FROM CQR_IHSFolder WHERE ISNULL(Archived,0)=0 AND QueueKey = " + Conversions.ToString(iQueueKey) + ";";
                    if (Operators.CompareString(IHSRecordList, "", TextCompare: false) != 0)
                    {
                        sSQL = sSQL + "INSERT INTO CQR_IHSFolder (QueueKey, " + IHSFields + ")";
                        sSQL = sSQL + " (SELECT " + Conversions.ToString(iQueueKey) + "," + IHSFields + " FROM CQR_IHS WHERE CoreNameplatePlantMnemonic IN (" + IHSRecordList + "))";
                        if (bSuperUser)
                        {
                            sSQL = sSQL + "DELETE FROM CQR_IHSFolder WHERE ISNULL(Archived,0)=1 AND CoreNameplatePlantMnemonic NOT IN (" + IHSRecordList + ") AND QueueKey = " + Conversions.ToString(iQueueKey) + ";";
                        }
                    }
                    IHS[] array3 = ihsRecords;
                    for (int k = 0; k < array3.Length; k++)
                    {
                        IHS IHSRec = array3[k];
                        sSQL = sSQL + "UPDATE CQR_IHSFolder SET ProductDescription='" + Strings.Replace(IHSRec.sProductDescription, "'", "''") + "', SoldFrom='" + Strings.Replace(IHSRec.sSoldFrom, "'", "''") + "', FinalAssembly='" + Strings.Replace(IHSRec.sFinalAssembly, "'", "''") + "'";
                        sSQL = sSQL + " WHERE QueueKey=" + Conversions.ToString(iQueueKey) + " AND CoreNameplatePlantMnemonic='" + IHSRec.sCoreNameplatePlantMnemonic + "';\r\n";
                    }
                    tPage.cnExecuteNonQuery(sSQL);
                }
                string lstIHS = GetIHSList();
                string sqlIHS2 = "UPDATE CQR_IHSFolder Set Archived=null, ArchivedByQueueKey = null, ArchivedDate = null, ArchivedByUser = null";
                sqlIHS2 = sqlIHS2 + " WHERE CoreNameplatePlantMnemonic Not In (" + lstIHS + ") And ArchivedByQueueKey = " + Conversions.ToString(iQueueKey);
                tPage.cnExecuteNonQuery(sqlIHS2);
                sEngineerAssignedDbs = sEngineerAssigned;
                sDueDateFromEngineeringDbs = sDueDateFromEngineering;
                sDueDateToBidsAndEstimatingDbs = sDueDateToBidsAndEstimating;
                sGDPEPPhase1RequiredDbs = sGDPEPPhase1Required;
                sGDPEPPhase2RequiredDbs = sGDPEPPhase2Required;
                sCostEstimatorDbs = sCostEstimator;
                sPGM_aka_CoCDbs = sPGM_aka_CoC;
                sPDM_aka_ProgramManagerDbs = sPDM_aka_ProgramManager;
                sAMECoordinatorDbs = sAMECoordinator;
                sPurchasingCoordinatorDbs = sPurchasingCoordinator;
                sSalesAccountDirectorDbs = sSalesAccountDirector;
                sAccountManagerDbs = sAccountManager;
                sCommercialManagerDbs = sCommercialManager;
                sEngineeringManagerDbs = sEngineeringManager;
                sEngineeringSiteDbs = sEngineeringSite;
                sPIMSiteDbs = sPIMSite;
                sManufacturingSiteDbs = sManufacturingSite;
                sQuoteRespDueDateDbs = sQuoteRespDueDate;
                sLeadManufacturingDbs = sLeadManufacturing;
                sLeadPurchasingDbs = sLeadPurchasing;
                sLeadValidationDbs = sLeadValidation;
                tPage.cnClose();
                saveCount++;
                SaveGDPEP(1);
                SaveGDPEP(2);
                tAttachmentResponse.SaveData(tPage, sStatus);
                tHandoverPackage.SaveData(tPage, sStatus);
                addedTask = false;
                if (tUserData.bIsPrd & (Operators.CompareString(sStatus, "030200FR", TextCompare: false) > 0))
                {
                    int num = tTasklist.tTab.iAttCount - 1;
                    for (int attLoop = 0; attLoop <= num; attLoop++)
                    {
                        if (((Operators.CompareString(tTasklist.tTab.tAttInfo[attLoop].sCheckoutUser, "", TextCompare: false) != 0) & (Operators.CompareString(tTasklist.tTab.tAttInfo[attLoop].sCheckoutUserDbs, "", TextCompare: false) == 0)) && !tRouting.mHasToBeViewedTask("13", "N1", sCostEstimator))
                        {
                            addedTask = true;
                            tRouting.mCreateTask("13", "N1", "N1", sCostEstimator, "", abNote: true);
                        }
                    }
                }
            }
            if (tTasklist.HasChangesToSave() && !addedTask && !tRouting.mHasToBeViewedTask("43", "N1", sCostEstimator))
            {
                tRouting.mCreateTask("43", "N1", "N1", sCostEstimator, "", abNote: true);
                if (Operators.CompareString(UserId, sCostEstimator, TextCompare: false) == 0)
                {
                    tRouting.mCloseTask("43", "N1", "N1", sCostEstimator, "Acknowledged");
                }
            }
            tTasklist.SaveData(tPage, sStatus);
            if (tQuoteResponseFCM.HasChangesToSave() && !tRouting.mHasToBeViewedTask("43", "N3", sCostEstimator))
            {
                tRouting.mCreateTask("43", "N3", "N3", sCostEstimator, "", abNote: true);
                if (Operators.CompareString(UserId, sCostEstimator, TextCompare: false) == 0)
                {
                    tRouting.mCloseTask("43", "N3", "N3", sCostEstimator, "Acknowledged");
                }
            }
            tQuoteResponseFCM.SaveData(tPage, sStatus);
            if (tQuoteResponseECR.HasChangesToSave() && !tRouting.mHasToBeViewedTask("43", "N5", sCostEstimator))
            {
                tRouting.mCreateTask("43", "N5", "N5", sCostEstimator, "", abNote: true);
                if (Operators.CompareString(UserId, sCostEstimator, TextCompare: false) == 0)
                {
                    tRouting.mCloseTask("43", "N5", "N5", sCostEstimator, "Acknowledged");
                }
            }
            tQuoteResponseECR.SaveData(tPage, sStatus);
            if (isLegacy())
            {
                ((clsCQRLegacy)this).SavePNL(Conversions.ToString(iQueueKey), tPage);
            }
            tRouting.mSaveData(Conversions.ToString(iQueueKey));
            return true;
        }
        bool SaveData = default(bool);
        return SaveData;
    }

    public void savePNLAttachments(corePage tpage)
    {
        tPNLAttach.SaveData(tpage, sStatus);
        checked
        {
            int num = iQAFCount - 1;
            for (int iLoop = 0; iLoop <= num; iLoop++)
            {
                if (Conversion.Val(tPNL[iLoop].sQAFKey) < 0.0)
                {
                    tPNL[iLoop].sQAFKey = tPNLAttach.tTab.tAttInfo[(int)Math.Round(Conversion.Val(tPNL[iLoop].sQAFKey))].sQueueKey;
                }
            }
        }
    }

    public bool CheckSave(corePage tPage, bool abForceSave = false)
    {
        bool bSave = abForceSave;
        bool bRelease = false;
        bool bExit = false;
        string sLink = "";
        string sRqd = "";
        string sTab = "";
        int releaseCount = 0;
        //sLink = ((Operators.CompareString(tPage.Request["Link"], "", TextCompare: false) == 0) ? tPage.Request["__EVENTTARGET"] : tPage.Request["Link"]);
        //if ((Operators.CompareString(tPage.Request["__EVENTTARGET"], "saveLink", TextCompare: false) == 0) | (Operators.CompareString(tPage.Request["__EVENTTARGET"], "saveexitLink", TextCompare: false) == 0) | (Operators.CompareString(tPage.Request["__EVENTTARGET"], "releaseOptionalLink", TextCompare: false) == 0) | (Operators.CompareString(tPage.Request["__EVENTTARGET"], "approveOptionalLink", TextCompare: false) == 0) | (Operators.CompareString(tPage.Request["__EVENTTARGET"], "cancelLink", TextCompare: false) == 0))
        //{
        //    sLink = tPage.Request["__EVENTTARGET"];
        //}
        switch (sLink)
        {
            case "saveLink":
                bSave = true;
                break;
            case "saveexitLink":
                bExit = true;
                bSave = true;
                break;
            case "rejectNoCommentLink":
                if (isLegacy())
                {
                    sRqd = ((clsCQRLegacy)this).RequiredFieldsReject();
                }
                if (((Operators.CompareString(sRqd, "", TextCompare: false) == 0) & isLegacy()) && ((clsCQRLegacy)this).mQafCheckRelease(tPage))
                {
                    bExit = true;
                    bRelease = true;
                    bSave = true;
                }
                break;
            case "terminateLink":
                sMode = "Terminate";
                bExit = true;
                bSave = true;
                bRelease = true;
                break;
            case "releaseLink":
            case "releaseOptionalLink":
            case "releasesortsLink":
            case "approveLink":
            case "approveOptionalLink":
            case "rejectLink":
            case "NCTVoidLink":
            case "releaseGDPEPLink":
                switch (sLink)
                {
                    case "releaseLink":
                    case "releaseOptionalLink":
                    case "releasesortsLink":
                    case "releaseGDPEPLink":
                        sMode = "Released";
                        break;
                    case "approveLink":
                    case "approveOptionalLink":
                        sMode = "Approved";
                        break;
                    case "rejectLink":
                    case "rejectNoCommentLink":
                        sMode = "Rejected";
                        break;
                }
                if (mQafCheckRelease_(tPage) & mCheckRelease_(tPage, sLink))
                {
                    bExit = true;
                    bSave = true;
                    bRelease = true;
                }
                else
                {
                    bExit = false;
                    bSave = false;
                    bRelease = false;
                }
                break;
            case "cancelLink":
                bExit = true;
                break;
            default:
                if (Operators.CompareString(Strings.Left(sLink, 10), "saveAttach", TextCompare: false) == 0)
                {
                    HttpResponse response = tPage.Response;
                    bSave = true;
                    //response.Write("\r\n<script>");
                    //response.Write("\r\n  var win;");
                    //response.Write("\r\n  win = window.open('../_coreForm/frmAttachment.aspx?Create=1&QueueKey=" + Strings.Mid(tPage.Request["__EVENTTARGET"], 11) + "', '_createAttachment', 'left=300,top=200,height=185,width=475,status=yes,toolbar=no,scrollbars=yes,menubar=no,location=no,titlebar=no');");
                    //response.Write("\r\n  win.focus();");
                    //response.Write("\r\n</script>");
                    response = null;
                }
                break;
        }
        if (bSave && modTRS.RequiredLogComment(sUserId, sLogComment))
        {
            bExit = false;
            bSave = false;
            bRelease = false;
            modCQR.AlertMessage(tPage, "Please enter the log comment in the Routing tab", "Comment Missing");
        }
        bDualRelease = true;
        if ((bRelease & isLegacy()) && ((Strings.InStr(sSecurityMode, "C") > 0) & tUserData.bIsPetm) && ((Operators.CompareString(UserId, sPDM_aka_ProgramManager, TextCompare: false) != 0) & tUserData.bIsPgm & tRouting.mHasCurrentTask("10", "BA", "BA", "*")))
        {
            //if (Operators.CompareString(tPage.Request["DualRelease"], "", TextCompare: false) != 0)
            //{
            //    //bDualRelease = Conversions.ToBoolean(Interaction.IIf(Conversions.ToDouble(tPage.Request["DualRelease"]) == 1.0, true, false));
            //    bHasChecked = bDualRelease;
            //    //if (Conversions.ToDouble(tPage.Request["DualRelease"]) == -1.0)
            //    //{
            //    //    bRelease = false;
            //    //    bSave = false;
            //    //}
            //}
            //else
            //{
            //    bDualRelease = mReleaseTask("10BA", sEngineeringManager, tPage);
            //    bExit = false;
            //    bRelease = false;
            //    bSave = false;
            //}
        }
        if (((Operators.CompareString(sLink, "terminateLink", TextCompare: false) != 0) & (Operators.CompareString(sLink, "rejectNoCommentLink", TextCompare: false) != 0)) && (bRelease || bSave))
        {
            //sRqd = ((!isLegacy()) ? ((clsCQRCurrent)this).RequiredFields(sLink) : ((clsCQRLegacy)this).RequiredFields(sLink));
        }
        if ((Operators.CompareString(Strings.Left(sRqd, 8), "Optional", TextCompare: false) == 0) & ((Operators.CompareString(sLink, "releaseOptionalLink", TextCompare: false) == 0) | (Operators.CompareString(sLink, "approveOptionalLink", TextCompare: false) == 0)))
        {
            sRqd = "";
        }
        else if (Operators.CompareString(sRqd, "", TextCompare: false) != 0)
        {
            bExit = false;
            bSave = false;
            bRelease = false;
            if (Operators.CompareString(Strings.Left(sRqd, 8), "Optional", TextCompare: false) == 0)
            {
                modCQR.OptionalMessage(tPage, sRqd, Conversions.ToString(Interaction.IIf(Operators.CompareString(sLink, "approveLink", TextCompare: false) == 0, "approveOptionalLink", "releaseOptionalLink")));
            }
            else if (Operators.CompareString(Strings.Left(sRqd, 1), "@", TextCompare: false) == 0)
            {
                modCQR.AlertMessage(tPage, Strings.Mid(sRqd, 2), "Required Fields");
            }
            else
            {
                modCQR.AlertMessage(tPage, "One or more required fields have not been entered or have an invalid value" + sTab + ":" + sRqd, "Required Fields");
            }
        }
        if (bSave)
        {
            if (isLegacy())
            {
                ((clsCQRLegacy)this).CheckSaveAdvanceGDPEP(bRelease);
            }
            else
            {
                if (tHandoverPackage.HasChangesToSave() & tUserData.bIsAMgr)
                {
                    //((clsCQRCurrent)this).z_RevertHandover();
                }
                if (mRerouteTasks())
                {
                    bRelease = false;
                }
            }
            if (Operators.CompareString(sRqd, "", TextCompare: false) == 0)
            {
                SaveData(tPage, bExit: false);
                modTRS.logAddRecord(tPage, "CQR", sUserId, modTRS.getButtonType(sLink), Conversions.ToString(iQueueKey), bSuperUser, "", sLogComment, sStatus);
            }
        }
        if (bRelease && Operators.CompareString(sRqd, "", TextCompare: false) == 0)
        {
            string PerformTask = "";//tPage.Request["PerformTask"];
            bool shouldAdvance = false;
            if (Operators.CompareString(PerformTask, "", TextCompare: false) == 0)
            {
                if (!bHasChecked)
                {
                    if (!checkCurrentTask(tPage))
                    {
                        bExit = false;
                        shouldAdvance = false;
                    }
                    else
                    {
                        savePNLAttachments(tPage);
                        shouldAdvance = true;
                        bHasChecked = false;
                    }
                }
                else
                {
                    savePNLAttachments(tPage);
                    shouldAdvance = true;
                    bHasChecked = false;
                }
            }
            else
            {
                //if (Operators.CompareString(tPage.Request["PerformTask"], "0", TextCompare: false) == 0)
                //{
                //    sIndTask = "";
                //}
                savePNLAttachments(tPage);
                shouldAdvance = true;
                bHasChecked = false;
            }
            if (shouldAdvance)
            {
                if (isLegacy())
                {
                    ((clsCQRLegacy)this).z_AdvanceStatus(sLink);
                }
                else
                {
                    //((clsCQRCurrent)this).z_AdvanceStatus(sLink);
                }
            }
        }
        if (bSave)
        {
            if (isLegacy())
            {
                ((clsCQRLegacy)this).mNotifyManufacturingSite();
            }
            if (Operators.CompareString(sRqd, "", TextCompare: false) == 0)
            {
                SaveData(tPage, bExit, bRelease);
            }
        }
        if (bExit)
        {
            if (bNewCQR & bModify)
            {
                mTRSNextNumberUnlock(0 - (bSave ? 1 : 0), tPage);
            }
            tPage.FolderUnlock("CQR_Header", "QueueKey", Conversions.ToString(iQueueKey), UserId);
            if (!bSave & bNewCQR & (saveCount == 0))
            {
                DeleteNewCQR(tPage);
            }
            //tPage.Response.Redirect(modCQR.getRedirectPage());
        }
        return bExit;
    }

    public bool checkCurrentTask(corePage atPage)
    {
        bool checkCurrentTask = true;
        sIndTask = "";
        switch (sStatus)
        {
            case "010150FR":
                sIndTask = "3";
                if (((Operators.CompareString(UserId, sCommercialManager, TextCompare: false) != 0) & tRouting.mHasCurrentTask("01", "BQ", "BQ", "*")) && (tUserData.bIsCmgr | tUserData.bIsCost))
                {
                    checkCurrentTask = updateCheckCurrentTask(atPage, sCommercialManager);
                    sIndTask = "3";
                }
                break;
            case "020100FR":
            case "020150FR":
            case "020200FR":
            case "020300FR":
            case "020400FR":
                {
                    string currentPSM = mGetCurrentPSM();
                    if ((Operators.CompareString(UserId, tUserData.sManagerBids, TextCompare: false) != 0) & tRouting.mHasCurrentTask("07", "BA", "BA", "*"))
                    {
                        if (tUserData.bIsMBnE)
                        {
                            checkCurrentTask = updateCheckCurrentTask(atPage, tUserData.sManagerBids);
                        }
                        sIndTask = "1";
                    }
                    if ((Operators.CompareString(UserId, sPGM_aka_CoC, TextCompare: false) != 0) & tRouting.mHasCurrentTask("01", "GA", "GA", "*"))
                    {
                        if (tUserData.bIsPgm)
                        {
                            checkCurrentTask = updateCheckCurrentTask(atPage, sPGM_aka_CoC);
                        }
                        sIndTask = "2";
                    }
                    break;
                }
            case "030200FR":
            case "030600FR":
                {
                    string currentPSM = mGetCurrentPSM();
                    if (!(!tRouting.mHasCurrentTask("08", "BA", "BA", "*") & !tRouting.mHasCurrentTask("07", "BA", "BA", "*")) || !tUserData.bIsPrd)
                    {
                        break;
                    }
                    string userList = "";
                    if (tRouting.mHasCurrentTask("13", "CA", "CA", sEngineerAssigned))
                    {
                        userList = userList + "," + sEngineerAssigned;
                    }
                    int prdLoop = 1;
                    do
                    {
                        if (tRouting.mHasCurrentTask("13", "PR", "PR", sElecPRD[prdLoop]))
                        {
                            userList = userList + "," + sElecPRD[prdLoop];
                        }
                        prdLoop = checked(prdLoop + 1);
                    }
                    while (prdLoop <= 8);
                    if (Operators.CompareString(userList, "", TextCompare: false) != 0 && userList.IndexOf(UserId) <= 0)
                    {
                        mUserPerformTask(atPage, "one of these users?", Strings.Mid(userList, 2));
                        checkCurrentTask = false;
                    }
                    break;
                }
            case "040100FR":
            case "040125FR":
                if (!tRouting.mHasCurrentTask("08", "BA", "BA", "*") & !tRouting.mHasCurrentTask("07", "BA", "BA", "*"))
                {
                    if (tUserData.bIsCost & (Operators.CompareString(tGateway[2].sReleaseGateway, "1", TextCompare: false) != 0))
                    {
                        checkCurrentTask = updateCheckCurrentTask(atPage, sCostEstimator);
                    }
                    sIndTask = "4";
                }
                break;
            case "040200FR":
                if ((!tRouting.mHasCurrentTask("08", "BA", "BA", "*") & !tRouting.mHasCurrentTask("07", "BA", "BA", "*")) && tUserData.bIsAMgr)
                {
                    checkCurrentTask = updateCheckCurrentTask(atPage, sAccountManager);
                }
                break;
        }
        return checkCurrentTask;
    }

    public string mGetCurrentPSM()
    {
        if (Operators.CompareString(sLocationEng, "000002", TextCompare: false) == 0)
        {
            return "ASEIBERT";
        }
        return modUserProf.mUPGetManager("PSM");
    }

    public bool updateCheckCurrentTask(corePage atPage, string assignedUserId)
    {
        bool updateCheckCurrentTask = true;
        if (Operators.CompareString(UserId, assignedUserId, TextCompare: false) != 0)
        {
            mUserPerformTask(atPage, assignedUserId);
            updateCheckCurrentTask = false;
        }
        return updateCheckCurrentTask;
    }

    public bool mUserPerformTask(corePage atpage, string asUser, string releaseUsers = "")
    {
        string sTemp = Conversions.ToString(Interaction.IIf(Operators.CompareString(releaseUsers, "", TextCompare: false) == 0, modUserProf.UserNameFromId(asUser), asUser));
        AlertUserBox(atpage, "Do you want to perform the work for " + sTemp, releaseUsers);
        return false;
    }

    public void AlertUserBox(corePage atPage, string msgText, string releaseUsers = "")
    {
        string bDualReleaseChecked = "";// Conversions.ToString(Interaction.IIf(Information.IsNothing(atPage.Request["DualRelease"]), 0, atPage.Request["DualRelease"]));
        string releaseHTML = "";
        if (Operators.CompareString(releaseUsers, "", TextCompare: false) != 0)
        {
            string[] userList = Strings.Split(releaseUsers, ",");
            int num = Information.UBound(userList);
            for (int userLoop = 0; userLoop <= num; userLoop = checked(userLoop + 1))
            {
                if (Operators.CompareString(userList[userLoop], "", TextCompare: false) != 0)
                {
                    releaseHTML = releaseHTML + "<input id=\"optAlertUser" + Conversions.ToString(userLoop) + "\" type=\"radio\" name=\"optAlertUser\" value=\"" + userList[userLoop] + "\">&nbsp;" + modUserProf.UserNameFromId(userList[userLoop]) + "<br>";
                }
            }
        }
        StringBuilder sb = new StringBuilder();
        sb.AppendLine("<div id='confirmDual' style='display:none'>");
        sb.AppendLine(msgText);
        if (Operators.CompareString(releaseHTML, "", TextCompare: false) != 0)
        {
            sb.AppendLine("<br>" + releaseHTML);
        }
        sb.AppendLine("<button class='btn btn-default' type='button' onclick='goReleaseForOther()'>yes</button>");
        sb.AppendLine("<button class='btn btn-default' type='button' onclick='UnTip();'>no</button>");
        sb.AppendLine("</div>");
        sb.AppendLine("<script type=\"text/javascript\">");
        sb.AppendLine("  function goReleaseForOther() {");
        sb.AppendLine("    var selVal = $(\"input[name=optAlertUser]:checked\").val();");
        sb.AppendLine("    if(selVal == '') {");
        sb.AppendLine("      alert('Please select a user');");
        sb.AppendLine("      return;");
        sb.AppendLine("    }");
        //sb.AppendLine(Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject("    szUrl = '" + DescriptionURL() + "?QueueKey=" + Conversions.ToString(iQueueKey) + "&DualRelease=" + bDualReleaseChecked + "&Link=", Interaction.IIf(Information.IsNothing(atPage.Request["__EVENTTARGET"]), atPage.Request["Link"], atPage.Request["__EVENTTARGET"])), "&ReleaseNoPNL="), atPage.Request["ReleaseNoPNL"]), "&PerformTask=1&forUser='+selVal;")));
        sb.AppendLine("    forceSkip = true;");
        sb.AppendLine("    document.location = szUrl;");
        sb.AppendLine("  }");
        sb.AppendLine("  Tip(document.getElementById('confirmDual').innerHTML, FOLLOWMOUSE, false, FIX, ['pageContent_navCol_lnkSaveExit', 0, 0]);");
        sb.AppendLine("</script>");
        //atPage.FindControl("pageContent" + Conversions.ToString(atPage.IdSeparator) + "phMessage").Controls.Add(new LiteralControl(sb.ToString()));
    }

    public void ControlsFromPrevious(object tPage)
    {
        int printCQR = Conversions.ToInteger(NewLateBinding.LateGet(NewLateBinding.LateGet(tPage, null, "Request", new object[0], null, null, null), null, "Form", new object[1] { "printCQR" }, null, null, null));
        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "hdnRejectTo", ref rejectTo);
        if (printCQR == 1)
        {
            printCurrentQRSelected = Conversions.ToInteger(Interaction.IIf(NewLateBinding.LateGet(tPage, null, "Request", new object[1] { "hdnCurrentQRSelected" }, null, null, null) == null, -1, Conversion.Val(RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(tPage, null, "Request", new object[1] { "hdnCurrentQRSelected" }, null, null, null)))));
            NewLateBinding.LateCall(NewLateBinding.LateGet(tPage, null, "Response", new object[0], null, null, null), null, "Write", new object[1] { "<script>\r\n" }, null, null, null, IgnoreReturn: true);
            NewLateBinding.LateCall(NewLateBinding.LateGet(tPage, null, "Response", new object[0], null, null, null), null, "Write", new object[1] { "var win;\r\n" }, null, null, null, IgnoreReturn: true);
            NewLateBinding.LateCall(NewLateBinding.LateGet(tPage, null, "Response", new object[0], null, null, null), null, "Write", new object[1] { "win = window.open('Print.aspx?QueueKey=" + Conversions.ToString(iQueueKey) + "','_CQRPrint','left=0,top=0,height=800,width=800,status=0,toolbar=0,scrollbars=yes,menubar=yes,location=0,titlebar=0');\r\n" }, null, null, null, IgnoreReturn: true);
            NewLateBinding.LateCall(NewLateBinding.LateGet(tPage, null, "Response", new object[0], null, null, null), null, "Write", new object[1] { "win.focus();\r\n" }, null, null, null, IgnoreReturn: true);
            NewLateBinding.LateCall(NewLateBinding.LateGet(tPage, null, "Response", new object[0], null, null, null), null, "Write", new object[1] { "</script>\r\n" }, null, null, null, IgnoreReturn: true);
        }
        string sTemp = "";
        checked
        {
            switch (Conversions.ToString(NewLateBinding.LateGet(NewLateBinding.LateGet(tPage, null, "Request", new object[0], null, null, null), null, "Form", new object[1] { "tabName" }, null, null, null)))
            {
                default:
                    return;
                case "ccDescription":
                case "ccDescriptionLegacy":
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtCQRDesc", ref sCQRDesc);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbAwardQuarter", ref sAwardQuarter);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbAwardYear", ref sAwardYear);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbGateExit", ref sGateExit);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbModelYear", ref sModelYear);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtRFQRefNum", ref sRFQRefNum);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtRFQRecievedDate", ref sRFQRecDate, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtVolPerAnnum", ref sVolumePerAnnum);
                    sVolumePerAnnum = modCQR.mDecommifyDollarAmount(sVolumePerAnnum);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtOrgProdLife", ref sOrigProductLife);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtRemainingProdLife", ref sRemProductLife);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optGateway", ref sGateway, abCheckBox: false, abYESNORadioButton: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtOpeningMeetingDate", ref sOpeningMeetingDate, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtApproxAnnualVal", ref sApproxAnnualValue);
                    sApproxAnnualValue = modCQR.mDecommifyDollarAmount(sApproxAnnualValue);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtCustQuote", ref sCustomerQuoteDueDate, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtCustomerQuoteDueDateChangeComment", ref sCustomerQuoteDueDateChangeComment, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtQuoteResponse", ref sQuoteRespDueDate, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbBusClassification", ref sBusinessClass);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbProdLine", ref sProductLine);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optRespLv", ref sResponseLevel);
                    if (Operators.CompareString(sResponseLevel, "optRespLvFirm", TextCompare: false) == 0)
                    {
                        sResponseLevel = "0";
                    }
                    if (Operators.CompareString(sResponseLevel, "optRespLvBallpark", TextCompare: false) == 0)
                    {
                        sResponseLevel = "1";
                    }
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtStartOfProd", ref sCustomerSOP, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbRegionalQuotingTeam", ref sRegionalQuotingTeam, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtWarrantyReq", ref sWarrantyRqmt);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtPerformanceReq", ref sPerformRqmt);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtCurrSupplier", ref sCurrentSupp);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbSalesLoc", ref sLocationSales);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbEngLoc", ref sLocationEng);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbTRWShipPt", ref sLocationShip);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtCustPartNum", ref sCustPartListString);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtTRWPartNum", ref sLucasPartListString);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtCustBuyer", ref sCustomerBuyer);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtCustEngineer", ref sCustomerEngineer);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optEngineeringSite", ref sEngineeringSite);
                    sEngineeringSite = Strings.Right(sEngineeringSite, 1);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optObsolReq", ref sObsolescenceRequiredInd, abCheckBox: false, abYESNORadioButton: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbEngineeringSite", ref sEngineeringSite);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbManufacturingSite", ref sManufacturingSite);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbCMGR", ref sCommercialManager);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbAMGR", ref sAccountManager);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbSDIR", ref sSalesAccountDirector);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbCOST", ref sCostEstimator);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbPGM", ref sPGM_aka_CoC);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbPDM", ref sPDM_aka_ProgramManager);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbAMECoordinator", ref sAMECoordinator);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbPurchasingCoordinator", ref sPurchasingCoordinator);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbPSM", ref sPSM);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbEngineeringManager", ref sEngineeringManager);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbPA", ref sPA);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtTDRDate", ref sTDRDate);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkTDRNoInput", ref sTDRNoInput, abCheckBox: true);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbCommunication", ref sCommunication);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbASILLevel", ref sASILLevel);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbAUTOSAR", ref sAUTOSAR);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbCybersecurity", ref sCybersecurity);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtOtherRelevantFeatures", ref sOtherRelevantFeatures);
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbCalculationCurrency", ref sCalculationCurrency);
                    UpdateDefaultDueDateBnE();
                    if (isIHSMode())
                    {
                        //LoadIHS((int)iQueueKey, HttpContext.Current.Request["hdnIHSUniqueIdList"]);
                    }
                    else
                    {
                        //LoadAntares((int)iQueueKey, HttpContext.Current.Request["hdnAntaresUniqueIdList"]);
                    }
                    if (Operators.CompareString(Conversions.ToString(NewLateBinding.LateGet(NewLateBinding.LateGet(tPage, null, "Request", new object[0], null, null, null), null, "Form", new object[1] { "tabName" }, null, null, null)), "ccDescriptionLegacy", TextCompare: false) == 0)
                    {
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbOEMGroup", ref sOEMGroup);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbPlatform", ref sPlatform);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbNameplate", ref sNameplate);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbCustomergroup", ref sCustomerGroup);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbProductCategory", ref sProductCategory);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbBusinessPlan", ref sBusinessPlan);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbProductDescription", ref sProductDescription);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtVehicleBuildId", ref sVehicleBuildId);
                    }
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkAntaresArchive", ref sAntaresArchiveOk, abCheckBox: true);
                    break;
                case "ccEngineering":
                    {
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optIsCQRAchievable", ref sTimeFrameOkInd);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optIsCQRAchievable", ref sTimeFrameOkInd, abCheckBox: false, abYESNORadioButton: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optIsSuffInfo", ref sInformationOkInd);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optIsSuffInfo", ref sInformationOkInd, abCheckBox: false, abYESNORadioButton: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optCanWorkProceed", ref sWorkProceedOkInd);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optCanWorkProceed", ref sWorkProceedOkInd, abCheckBox: false, abYESNORadioButton: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optIsSafetyConsidered", ref sHealthInd);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optIsSafetyConsidered", ref sHealthInd, abCheckBox: false, abYESNORadioButton: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkTS16949Considered", ref sQS9000Considered, abCheckBox: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtDateOfCSR", ref sDateOfCSR, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtDueDateBid", ref sDueDateToBidsAndEstimating, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtDueDateDesign", ref sDueDateFromEngineering, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbDesignResp", ref sEngineerAssigned);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbLeadManufacturing", ref sLeadManufacturing);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbLeadPurchasing", ref sLeadPurchasing);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbLeadValidation", ref sLeadValidation);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkLeadManufacturingNoCostImpact", ref sLeadManufacturingNoCostImpact, abCheckBox: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkLeadPurchasingNoCostImpact", ref sLeadPurchasingNoCostImpact, abCheckBox: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkLeadValidationNoCostImpact", ref sLeadValidationNoCostImpact, abCheckBox: true);
                        int prdLoop = 1;
                        do
                        {
                            sElecPRD[prdLoop] = "";
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optIsDesignNeeded" + Conversions.ToString(prdLoop), ref sElecInputReqdInd[prdLoop], abCheckBox: false, abYESNORadioButton: true);
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbElecPRD" + Conversions.ToString(prdLoop), ref sElecPRD[prdLoop]);
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkElecPRD" + Conversions.ToString(prdLoop), ref sElecPRDNoInput[prdLoop], abCheckBox: true);
                            prdLoop++;
                        }
                        while (prdLoop <= 8);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optPIMSite", ref sTemp);
                        if (Operators.CompareString(sTemp, "", TextCompare: false) != 0)
                        {
                            sPIMSite = Strings.Right(sTemp, 1);
                        }
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkNoRCTFolders", ref sNoRCTFolders, abCheckBox: true);
                        if (isLegacy())
                        {
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optEngineeringSite", ref sEngineeringSite);
                            sEngineeringSite = Strings.Right(sEngineeringSite, 1);
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbMfgSite", ref sManufacturingSite);
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optGateway1", ref sGDPEPPhase1Required);
                            sGDPEPPhase1Required = Strings.Right(sGDPEPPhase1Required, 1);
                            if (Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) == 0)
                            {
                                sGDPEPPhase2Required = "1";
                                break;
                            }
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optGateway2", ref sGDPEPPhase2Required);
                            sGDPEPPhase2Required = Strings.Right(sGDPEPPhase2Required, 1);
                        }
                        break;
                    }
                case "ccQuoteResponse":
                    {
                        if (isLegacy())
                        {
                            int num3 = iUnitCostCount - 1;
                            for (int iLoop = 0; iLoop <= num3; iLoop++)
                            {
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtUnitCostDesc_" + Conversions.ToString(iLoop), ref tUnitCost[iLoop].sDesc);
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtUnitCostCost_" + Conversions.ToString(iLoop), ref tUnitCost[iLoop].sCost);
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtUnitCostQty_" + Conversions.ToString(iLoop), ref tUnitCost[iLoop].sQty);
                            }
                        }
                        else
                        {
                            int num4 = tQuoteResponseECR.tTab.iAttCount - 1;
                            for (int iLoop = 0; iLoop <= num4; iLoop++)
                            {
                                string customData = "";
                                sTemp = "";
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtUnitCostDesc_" + Conversions.ToString(iLoop), ref sTemp);
                                customData += sTemp;
                                sTemp = "";
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtUnitCostCost_" + Conversions.ToString(iLoop), ref sTemp);
                                customData = customData + "@#@" + sTemp;
                                sTemp = "";
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtUnitCostQty_" + Conversions.ToString(iLoop), ref sTemp);
                                customData = customData + "@#@" + sTemp;
                                tQuoteResponseECR.tTab.tAttInfo[iLoop].sCustomData = customData;
                            }
                        }
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtToolLeadTime", ref sQRLeadTime);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbFOB", ref sQRFOB);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbProgClassification", ref sQRProgClass);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtToolingCapacity", ref sQRToolCap);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkEconLvDateMaterialNA", ref sTemp, abCheckBox: true);
                        if (Operators.CompareString(sTemp, "", TextCompare: false) != 0)
                        {
                            sQRMaterialDate = "N/A";
                        }
                        else
                        {
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtEconLvDateMaterial", ref sQRMaterialDate, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                        }
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkEconLvDateLaborNA", ref sTemp, abCheckBox: true);
                        if (Operators.CompareString(sTemp, "", TextCompare: false) != 0)
                        {
                            sQRLaborDate = "N/A";
                        }
                        else
                        {
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtEconLvDateLabor", ref sQRLaborDate, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                        }
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkQuoteCompleted", ref sQRCompletedAndSent, abCheckBox: true);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkQuoteReviewAvailable", ref sQRReviewAvailable, abCheckBox: true);
                        sPiecePriceInd = "Net 30";
                        sToolingInd = "Net 30";
                        if (isLegacy())
                        {
                            for (int iCount = 0; iCount < iQAFCount; iCount++)
                            {
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkBox_" + Conversions.ToString(iCount), ref tPNL[iCount].bChecked);
                            }
                            break;
                        }
                        int num5 = tQuoteResponseFCM.tTab.iAttCount - 1;
                        for (int iCount = 0; iCount <= num5; iCount++)
                        {
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtCustom_xxxQuoteResponse_" + Conversions.ToString(iCount), ref tQuoteResponseFCM.tTab.tAttInfo[iCount].sCustomData);
                        }
                        break;
                    }
                case "ccGDPEPGateway":
                    {
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbCBM", ref sCommercialBusinessManager);
                        T_GDPEP_APPROVAL t_GDPEP_APPROVAL = tGateway[(int)Math.Round(Conversion.Val(RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(tPage, null, "Request", new object[1] { "hdnPhase" }, null, null, null))))];
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbCategory", ref t_GDPEP_APPROVAL.sGDPIMCategory);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkReleaseGateway", ref t_GDPEP_APPROVAL.sReleaseGateway, abCheckBox: true);
                        int iLoop = 1;
                        do
                        {
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtVolume" + Conversions.ToString(iLoop), ref t_GDPEP_APPROVAL.sVolume[iLoop]);
                            iLoop++;
                        }
                        while (iLoop <= 7);
                        iLoop = 1;
                        do
                        {
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtSellPrice" + Conversions.ToString(iLoop), ref t_GDPEP_APPROVAL.sSellPrice[iLoop]);
                            iLoop++;
                        }
                        while (iLoop <= 10);
                        string sTemp1 = "";
                        string sTemp2 = "";
                        iLoop = 1;
                        do
                        {
                            if (NewLateBinding.LateGet(tPage, null, "FindControl", new object[1] { "chkApproval" + Conversions.ToString(iLoop) }, null, null, null) != null)
                            {
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkApproval" + Conversions.ToString(iLoop), ref sTemp1, abCheckBox: true);
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkReject" + Conversions.ToString(iLoop), ref sTemp2, abCheckBox: true);
                                if (Operators.CompareString(sTemp1, "1", TextCompare: false) == 0)
                                {
                                    t_GDPEP_APPROVAL.sApprovalState[iLoop] = "1";
                                }
                                else if (Operators.CompareString(sTemp2, "1", TextCompare: false) == 0)
                                {
                                    t_GDPEP_APPROVAL.sApprovalState[iLoop] = "0";
                                }
                                else
                                {
                                    t_GDPEP_APPROVAL.sApprovalState[iLoop] = "";
                                }
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkRequired" + Conversions.ToString(iLoop), ref sTemp1, abCheckBox: true);
                                if (Operators.CompareString(sTemp1, "", TextCompare: false) != 0)
                                {
                                    t_GDPEP_APPROVAL.sRequiredState[iLoop] = sTemp1;
                                }
                            }
                            iLoop++;
                        }
                        while (iLoop <= 12);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtComments", ref t_GDPEP_APPROVAL.sComments);
                        t_GDPEP_APPROVAL = null;
                        break;
                    }
                case "ccSalesCloseout":
                    {
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optQuoteStatus", ref sQSQuoteStatus, abCheckBox: false, abYESNORadioButton: false, abDate: true);
                        if (Operators.CompareString(sQSQuoteStatus, "optQuoteStatusNotQuoted", TextCompare: false) == 0)
                        {
                            sQSQuoteStatus = "0";
                        }
                        if (Operators.CompareString(sQSQuoteStatus, "optQuoteStatusVerbal", TextCompare: false) == 0)
                        {
                            sQSQuoteStatus = "1";
                        }
                        if (Operators.CompareString(sQSQuoteStatus, "optQuoteStatusLetter", TextCompare: false) == 0)
                        {
                            sQSQuoteStatus = "2";
                        }
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtQuoteDate", ref sQSQuoteDate);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtDateWon", ref sQSDateWon);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbSalesAdministrator", ref sQSSalesAdministrator);
                        z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "optAwardStatus", ref sQSAwardStatus);
                        if (Operators.CompareString(sQSAwardStatus, "optAwardStatusWon", TextCompare: false) == 0)
                        {
                            sQSAwardStatus = "0";
                        }
                        if (Operators.CompareString(sQSAwardStatus, "optAwardStatusLost", TextCompare: false) == 0)
                        {
                            sQSAwardStatus = "1";
                        }
                        if (Operators.CompareString(sQSAwardStatus, "optAwardStatusOther", TextCompare: false) == 0)
                        {
                            sQSAwardStatus = "2";
                        }
                        int num = iQAFCount - 1;
                        int iLoop;
                        for (iLoop = 0; iLoop <= num; iLoop++)
                        {
                            int num2 = Information.UBound(tPNL[iLoop].tPNLLineItem);
                            for (int jLoop = 0; jLoop <= num2; jLoop++)
                            {
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "cmbAward_" + Conversions.ToString(iLoop) + "_" + Conversions.ToString(jLoop), ref tPNL[iLoop].tPNLLineItem[jLoop].sAwardStatus);
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtQuoted_" + Conversions.ToString(iLoop) + "_" + Conversions.ToString(jLoop), ref tPNL[iLoop].tPNLLineItem[jLoop].sQuotedCurrency);
                                z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtSBU_" + Conversions.ToString(iLoop) + "_" + Conversions.ToString(jLoop), ref tPNL[iLoop].tPNLLineItem[jLoop].sSalesBusinessUnit);
                            }
                        }
                        tHandoverPackage.ControlsFromPreviousComments();
                        string sTemp1 = "";
                        string sTemp2 = "";
                        iLoop = 1;
                        do
                        {
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkApproval" + Conversions.ToString(iLoop), ref sTemp1, abCheckBox: true);
                            z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "chkReject" + Conversions.ToString(iLoop), ref sTemp2, abCheckBox: true);
                            iLoop++;
                        }
                        while (iLoop <= 4);
                        break;
                    }
                case "ccRouting":
                    z_FromPage(RuntimeHelpers.GetObjectValue(tPage), "txtLogComment", ref sLogComment);
                    break;
                case "ccAttachment":
                    tTasklist.ControlsFromPreviousComments();
                    break;
            }
            StoreInSession(RuntimeHelpers.GetObjectValue(tPage));
        }
    }

    public void SetHeaderInfo()
    //public void SetHeaderInfo(pageHeader pageHdr)
    {
        //if (modUserProf.mUserHasRole(UserId, "CQRBLOCK"))
        //{
        //    modUserProf.AccessDeniedMessage();
        //}
        string sTemp = "";
        string left = Strings.Trim(sFranNewModRepl);
        if (Operators.CompareString(left, Conversions.ToString(1), TextCompare: false) == 0)
        {
            sTemp = "New";
        }
        else if (Operators.CompareString(left, Conversions.ToString(2), TextCompare: false) == 0)
        {
            sTemp = "Mod";
        }
        else if (Operators.CompareString(left, Conversions.ToString(3), TextCompare: false) == 0)
        {
            sTemp = "Repl";
        }
        //pageHdr.propNumber = Conversions.ToString(Conversions.ToDouble(sProjectNum)) + "." + Conversions.ToString(Conversions.ToDouble(sRevision)) + " (" + sTemp + ")";
        //pageHdr.propStatus = Conversions.ToString(Operators.ConcatenateObject(modTRS.mTRSGetSingleDesc("STATUSFR", sStatus), Interaction.IIf(modTools.isLocalhost_(), " (" + sStatus + ")", "")));
        //pageHdr.propOriginator = Conversions.ToString(Operators.ConcatenateObject(modUserProf.UserNameFromId(sOriginator), Interaction.IIf(modTools.isLocalhost_(), " (" + sUserId + ")", "")));
        //pageHdr.propOriginationDate = modTools.mDateCygnetToScreen(sOriginationDate);
        if (Information.UBound(antaresRecords) >= 0)
        {
            //pageHdr.propCustomer = antaresRecords[0].sOEMGroup;
        }
        else if (Information.UBound(ihsRecords) >= 0)
        {
            //pageHdr.propCustomer = ihsRecords[0].sOEMGroup;
        }
        else
        {
            //pageHdr.propCustomer = modTRS.mTRSGetSingleDesc(CustomerTRSTable(), sOEMGroup);
        }
        //pageHdr.propQuoteType = QuoteTypeDescription();
    }

    public string QuoteTypeDescription()
    {
        return Strings.Trim(sQuoteType) switch
        {
            "0" => "Customer Pre-CQR",
            "1" => "Customer RFQ",
            "2" => "Gate Exit or Internal Review",
            "3" => "Customer ECR",
            _ => "",
        };
    }

    public void z_AddToRequired(ref string sRqd, string sMsg)
    {
        sRqd = sRqd + "\\n\\t" + sMsg;
    }

    public bool checkHolidays(string dateToCompare)
    {
        string sTempDate = "";
        bool checkHolidays = true;
        if (Operators.CompareString(dateToCompare, "", TextCompare: false) != 0)
        {
            mTRSDateDecrement(dateToCompare, ref sTempDate, 1);
            mTRSDateDecrement(sTempDate, ref sTempDate, -1);
            if (Operators.CompareString(modTools.mDateScreenToCygnet(sTempDate), modTools.mDateScreenToCygnet(dateToCompare), TextCompare: false) != 0)
            {
                checkHolidays = false;
            }
        }
        return checkHolidays;
    }

    public void SetSecurity(corePage tPage)
    //public void SetSecurity(corePage tPage, navColumn pageCol)
    {

        string sSecMode = "";
        checked
        {
            bool bRelease = default(bool);
            bool bReleaseGDPEP = default(bool);
            if (isLegacy())
            {
                int iLoop = 1;
                do
                {
                    if (tUserData.bIsGateway[iLoop])
                    {
                        if ((Operators.CompareString(tGateway[1].sApprovalUser[iLoop], "", TextCompare: false) != 0) & tRouting.mHasTask("41", "QA", "", tGateway[1].sApprovalUser[iLoop]))
                        {
                            bRelease = true;
                            //pageCol.lnkSave.Enabled = false;
                            //pageCol.lnkSaveExit.Enabled = false;
                        }
                        if ((Operators.CompareString(tGateway[2].sApprovalUser[iLoop], "", TextCompare: false) != 0) & tRouting.mHasTask("42", "QA", "", tGateway[2].sApprovalUser[iLoop]))
                        {
                            bRelease = true;
                            //pageCol.lnkSave.Enabled = false;
                            //pageCol.lnkSaveExit.Enabled = false;
                        }
                    }
                    iLoop++;
                }
                while (iLoop <= 12);
                if ((tUserData.bIsCmgr & (iOpenMode != 2)) && !tRouting.mHasCurrentTask("01", "BQ", "BQ", "*"))
                {
                    if (!tRouting.mHasTask("41", "AA") & (Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) == 0) & (Operators.CompareString(sGDPEPPhase1RequiredDbs, sGDPEPPhase1Required, TextCompare: false) != 0))
                    {
                        bRelease = true;
                    }
                    if (tRouting.mHasCurrentTask("41", "AA", "AA", "*"))
                    {
                        bReleaseGDPEP = true;
                    }
                    if (tRouting.mHasCurrentTask("42", "AA", "AA", "*"))
                    {
                        bReleaseGDPEP = true;
                    }
                }
            }
            else if ((tUserData.bIsCmgr & (iOpenMode != 2)) && !tRouting.mHasCurrentTask("01", "BQ", "BQ", "*") && tRouting.mHasCurrentTask("42", "AA", "AA", "*"))
            {
                bRelease = true;
            }
            object tTemp = tPage;
            //tTemp = tPage.FindControl("pageContent");
            tTemp = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(tTemp, null, "FindControl", new object[1] { "navCol" }, null, null, null));
            tTemp = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(tTemp, null, "FindControl", new object[1] { "lnkReject" }, null, null, null));
            if ((Operators.CompareString(sStatus, "040150FR", TextCompare: false) == 0) & (Operators.CompareString(sNewCommentsQAF, sDbsCommentsQAF, TextCompare: false) != 0))
            {
                NewLateBinding.LateSet(tTemp, null, "NavigateURL", new object[1] { "javascript:fnLink('rejectNoComment')" }, null, null);
            }
            if (((Operators.CompareString(sStatus, "040200FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "040300FR", TextCompare: false) == 0)) & (Operators.CompareString(sNewCommentsQRTooling, "", TextCompare: false) != 0))
            {
                NewLateBinding.LateSet(tTemp, null, "NavigateURL", new object[1] { "javascript:fnLink('rejectNoComment')" }, null, null);
            }
            if ((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "040125FR", TextCompare: false) == 0) | (((Operators.CompareString(sStatus, "040190FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "030600FR", TextCompare: false) == 0)) & tUserData.bIsCost))
            {
                NewLateBinding.LateSet(tTemp, null, "NavigateURL", new object[1] { "javascript:fnLink2('rejectComments')" }, null, null);
            }
            if (iOpenMode == 2)
            {
                //pageCol.lnkSave.Enabled = false;
                //pageCol.lnkSaveExit.Enabled = false;
                //pageCol.divReleaseGDPEP.Visible = false;
                return;
            }
            string sCaseStatus = sStatus;
            bool bApprove = default(bool);
            bool bReject = default(bool);
            if (tUserData.bIsPgm && tRouting.mHasCurrentTask("01", "GA", "GA", "*"))
            {
                bApprove = true;
                bReject = true;
                sSecMode = "BC";
            }
            if (CanEditPSM())
            {
                bRelease = true;
            }
            if (CanEditLeadManufacturing())
            {
                bRelease = true;
            }
            if (CanEditLeadPurchasing())
            {
                bRelease = true;
            }
            if (CanEditReviewRandD())
            {
                bRelease = true;
            }
            if ((Operators.CompareString(UserId, tUserData.sManagerBids, TextCompare: false) == 0) & tRouting.mHasCurrentTask("07", "BA", "BA", "*"))
            {
                bRelease = true;
            }
            switch (sCaseStatus)
            {
                case "010100FR":
                    if (bUserHasOrigRole)
                    {
                        if (bOrigIsPgm)
                        {
                            bApprove = true;
                        }
                        else
                        {
                            bRelease = true;
                        }
                        sSecMode = ((!(bOrigIsPetm | bOrigIsPgm)) ? "A" : "ABC");
                    }
                    break;
                case "010150FR":
                    bApprove = tUserData.bIsCmgr;
                    bReject = bApprove;
                    break;
                case "010200FR":
                    if (tUserData.bIsAMgr | tUserData.bIsSDir)
                    {
                        bApprove = true;
                        bReject = true;
                        sSecMode = "A";
                    }
                    break;
                case "020100FR":
                case "020150FR":
                case "020200FR":
                case "020300FR":
                case "020400FR":
                    if ((!bApprove & tUserData.bIsPetm) && tRouting.mHasCurrentTask("10", "BA", "BA", "*"))
                    {
                        bRelease = true;
                        sSecMode += "BC";
                    }
                    if (tUserData.bIsMBnE && tRouting.mHasCurrentTask("07", "BA", "BA", "*"))
                    {
                        if (Operators.CompareString(sUserId, tUserData.sManagerBids, TextCompare: false) == 0)
                        {
                            bRelease = true;
                        }
                        sSecMode += "1";
                    }
                    break;
                case "020500FR":
                case "030100FR":
                    if (bUserHasOrigRole)
                    {
                        bRelease = true;
                        sSecMode = "A";
                    }
                    break;
                case "030200FR":
                    if (tUserData.bIsPrd)
                    {
                        bRelease = true;
                        sSecMode = "D";
                    }
                    if (tUserData.bIsCTLC & tRouting.mHasCurrentTask("14", "FF", "FF", "*"))
                    {
                        bRelease = true;
                    }
                    break;
                case "030300FR":
                    if (tUserData.bIsPrd)
                    {
                        if (tRouting.mHasCurrentTask("13", "CA", "CA", "*"))
                        {
                            bRelease = true;
                            sSecMode = "D";
                        }
                        if (tRouting.mHasCurrentTask("13", "PR", "PR", "*"))
                        {
                            bRelease = true;
                        }
                        if (tUserData.bIsCost && !tRouting.mHasCurrentTask("14", "FF", "FF", "*"))
                        {
                            bReject = true;
                        }
                    }
                    break;
                case "030400FR":
                    if (tUserData.bIsPrd)
                    {
                        bRelease = true;
                        sSecMode = "2";
                    }
                    break;
                case "030600FR":
                    if (tUserData.bIsCTLC & tRouting.mHasCurrentTask("14", "FF", "FF", "*"))
                    {
                        bRelease = true;
                    }
                    if (tUserData.bIsPrd & tRouting.mHasCurrentTask("13", "PR", "PR", "*"))
                    {
                        bRelease = true;
                    }
                    if (tUserData.bIsCost)
                    {
                        if (!tRouting.mHasCurrentTask("14", "MM", "MM", "*"))
                        {
                            bReject = true;
                        }
                        if (!tRouting.mHasCurrentTask("14", "PP", "PP", "*"))
                        {
                            bReject = true;
                        }
                        if (!tRouting.mHasCurrentTask("14", "FF", "FF", "*"))
                        {
                            bReject = true;
                        }
                    }
                    break;
                case "040100FR":
                case "040125FR":
                    if (tUserData.bIsCost)
                    {
                        bRelease = true;
                        sSecMode = "F";
                    }
                    break;
                case "040190FR":
                    bReject = true;
                    break;
                case "040200FR":
                case "040400FR":
                    if (bUserHasOrigRole | tUserData.bIsAMgr)
                    {
                        if (!tRouting.mHasTask("24", "DA") & isLegacy())
                        {
                            bApprove = true;
                            bReject = true;
                        }
                        else
                        {
                            bRelease = true;
                        }
                    }
                    break;
                case "040250FR":
                    if (bUserHasOrigRole)
                    {
                        bRelease = true;
                    }
                    break;
                case "040300FR":
                    if (tUserData.bIsAMgr)
                    {
                        bRelease = true;
                    }
                    break;
                case "040700FR":
                    if (tUserData.bIsPDM)
                    {
                        bRelease = true;
                    }
                    break;
            }
            if ((Operators.CompareString(sStatus, "040150FR", TextCompare: false) > 0) & tUserData.bIsCmgr)
            {
                bRelease = true;
                if (isLegacy())
                {
                    //pageCol.lnkRelease.Text = "Reopen P&L";
                }
            }
            if (CanReleaseHandover())
            {
                bRelease = true;
            }
            if (CanApproveHandover())
            {
                bApprove = true;
                bReject = true;
            }
            if (bUserHasOrigRole && tRouting.mHasCurrentTask("10", "CA", "CA", "*"))
            {
                sSecMode = ((!(bOrigIsPetm | bOrigIsPgm)) ? (sSecMode + "Y") : (sSecMode + "YBC"));
            }
            if ((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "040125FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "040150FR", TextCompare: false) == 0))
            {
                if (tUserData.bIsCost)
                {
                    bRelease |= tRouting.mHasCurrentTask("24", "CA", "CA", sCostEstimator);
                }
                if (tUserData.bIsPDM)
                {
                    bApprove |= tRouting.mHasCurrentTask("24", "DA", "DA", sPDM_aka_ProgramManager);
                }
                if (tUserData.bIsPgm)
                {
                    bApprove |= tRouting.mHasCurrentTask("24", "DA", "DA", sPGM_aka_CoC);
                }
                if (tUserData.bIsBeb)
                {
                    bApprove |= tRouting.mHasCurrentTask("24", "DA", "DA", tUserData.sManagerBeb);
                }
                if (tUserData.bIsSDir | bUserIsOrig | (tUserData.bIsAMgr & bOrigIsAMgr))
                {
                    bApprove |= tRouting.mHasCurrentTask("24", "DA", "DA", mQafSalesActionGuy());
                }
                if (tUserData.bIsBum)
                {
                    bApprove |= tRouting.mHasCurrentTask("24", "EB", "EB", "*");
                }
                if (tUserData.bIsFin)
                {
                    bApprove |= tRouting.mHasCurrentTask("24", "EA", "EA", tUserData.sManagerFin);
                }
                if (tUserData.bIsPres)
                {
                    bApprove |= tRouting.mHasCurrentTask("24", "EA", "EA", tUserData.sManagerPres);
                }
                if (tUserData.bIsCmgr)
                {
                    bApprove |= tRouting.mHasCurrentTask("24", "EA", "EA", "*");
                }
                if (tUserData.bIsCmgr)
                {
                    bApprove |= tRouting.mHasCurrentTask("24", "DA", "DA", "*");
                }
                bReject = bApprove;
                if (((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "040125FR", TextCompare: false) == 0)) & tUserData.bIsCost)
                {
                    bReject = true;
                }
            }
            if (bSuperUser)
            {
                sSecMode = "";
                int iLoop = 65;
                do
                {
                    sSecMode += Conversions.ToString(Strings.Chr(iLoop));
                    iLoop++;
                }
                while (iLoop <= 90);
                iLoop = 49;
                do
                {
                    sSecMode += Conversions.ToString(Strings.Chr(iLoop));
                    iLoop++;
                }
                while (iLoop <= 57);
                bRelease = false;
                bReleaseGDPEP = false;
                bApprove = false;
                bReject = false;
            }
            sSecurityMode = sSecMode;
            //pageCol.lnkTerminate.Enabled = Conversions.ToBoolean(Interaction.IIf(bUserIsOrig & (Operators.CompareString(sStatus, "090100FR", TextCompare: false) < 0), true, false));
            //pageCol.lnkRelease.Enabled = bRelease;
            //pageCol.divReleaseGDPEP.Visible = bReleaseGDPEP & isLegacy();
            //pageCol.lnkApprove.Enabled = bApprove;
            //pageCol.lnkReject.Enabled = bReject;
            //if (Operators.CompareString(sStatus, "090100FR", TextCompare: false) >= 0)
            //{
            //    pageCol.lnkSave.Enabled = true;
            //    pageCol.lnkSaveExit.Enabled = true;
            //}
            //pageCol.lnkTerminate.Enabled = Conversions.ToBoolean(Interaction.IIf(bUserIsOrig & (Operators.CompareString(sStatus, "090100FR", TextCompare: false) < 0), true, false));
            //tPage.Response.Write("<script>\r\n");
            //tPage.Response.Write("var refreshLockTimer;\r\n");
            //tPage.Response.Write("var elapsedTime=0;\r\n");
            //tPage.Response.Write("function refreshLock() {\r\n");
            //tPage.Response.Write("  var win;\r\n");
            string revisionQueueKey = "";
            if ((Operators.CompareString(sLockedKey, "New", TextCompare: false) != 0) & (Operators.CompareString(sLockedKey, "", TextCompare: false) != 0))
            {
                revisionQueueKey = "&revQueueKey=" + sLockedKey;
            }
            //tPage.Response.Write("  win = window.open('RefreshLock.aspx?QueueKey=" + Conversions.ToString(iQueueKey) + revisionQueueKey + "','_blank','height=200,width=400,status=yes,toolbar=no,scrollbars=yes,menubar=no,location=no,titlebar=no');\r\n");
            //tPage.Response.Write("  win.close();\r\n");
            //tPage.Response.Write("}\r\n\r\n");
            //tPage.Response.Write("refreshLockTimer = window.setInterval('refreshLock()',900000); //15 minutes\r\n");
            //tPage.Response.Write("</script>\r\n");
            //if (!modTools.isLocalhost_())
            //{
            //    tPage.Response.Write("<script>\r\n");
            //    tPage.Response.Write("var timer;\r\n");
            //    tPage.Response.Write("var elapsedTime=0;\r\n");
            //    tPage.Response.Write("function timerAlert() {\r\n");
            //    tPage.Response.Write("\telapsedTime+=25;\r\n");
            //    tPage.Response.Write("\talert(elapsedTime + ' minutes have elapsed without saving your data.\\nIf you have made changes, press save now, or risk possible data loss due to session timeout.');\r\n");
            //    tPage.Response.Write("}\r\n\r\n");
            //    tPage.Response.Write("timer = window.setInterval('timerAlert()',1500000); //25 minutes\r\n");
            //    tPage.Response.Write("</script>\r\n");
            //}
        }
    }

    public bool DeleteNewCQR(corePage atPage)
    {
        atPage.cnExecuteNonQuery("DELETE FROM CQR_Header WHERE CONVERT(int, QueueKey) = " + Conversions.ToString(iQueueKey));
        return true;
    }

    public bool CheckDelete(corePage atPage)
    {
        bool CheckDelete = default(bool);
        return CheckDelete;
    }

    public bool CheckCopy(corePage atPage)
    {
        bool CheckCopy = default(bool);
        return CheckCopy;
    }

    public void CommentsSet(string asSource, ref string sTextNew)
    {
        switch (asSource)
        {
            case "txtVolPerAnnumComments":
                sNewCommentsVolumePerAnnum = sTextNew;
                break;
            case "txtTargetPricingComment":
                sNewCommentsTargetPricing = sTextNew;
                break;
            case "txtBgInfo":
                sNewComment = sTextNew;
                break;
            case "txtCustProdTargets":
                sNewCommentsCustomerProductivity = sTextNew;
                break;
            case "txtEngPackageComments":
                sNewCommentsEP = sTextNew;
                break;
            case "txtQuoteRespComments":
                sNewCommentsQRTooling = sTextNew;
                break;
            case "txtQSComments":
                sNewQSComments = sTextNew;
                break;
            case "txtQSCommentsProceedCancel":
                sNewQSCommentsProceedCancel = sTextNew;
                break;
            case "txtActionComments":
                sNewCommentsAction = sTextNew;
                break;
            case "txtQuoteComments":
                sNewCommentsQAF = sTextNew;
                break;
            case "txtRejectionComments":
                sNewCommentsGDPEPRejection = sTextNew;
                break;
        }
    }

    public void CommentsGet(string asSource, ref string sOld, ref string sNew)
    {
        switch (asSource)
        {
            case "txtVolPerAnnumComments":
                sOld = sDbsCommentsVolumePerAnnum;
                sNew = sNewCommentsVolumePerAnnum;
                break;
            case "txtTargetPricingComment":
                sOld = sDbsCommentsTargetPricing;
                sNew = sNewCommentsTargetPricing;
                break;
            case "txtBgInfo":
                sOld = sComment;
                sNew = sNewComment;
                break;
            case "txtCustProdTargets":
                sOld = sDbsCommentsCustomerProductivity;
                sNew = sNewCommentsCustomerProductivity;
                break;
            case "txtEngPackageComments":
                sOld = sCommentsEP;
                sNew = sNewCommentsEP;
                break;
            case "txtQuoteRespComments":
                sOld = sDbsCommentsQRTooling;
                sNew = sNewCommentsQRTooling;
                break;
            case "txtQSComments":
                sOld = sQSComments;
                sNew = sNewQSComments;
                break;
            case "txtQSCommentsProceedCancel":
                sOld = sQSCommentsProceedCancel;
                sNew = sNewQSCommentsProceedCancel;
                break;
            case "txtActionComments":
                sOld = sDbsCommentsAction;
                sNew = sNewCommentsAction;
                break;
            case "txtQuoteComments":
                sOld = sDbsCommentsQAF;
                sNew = sNewCommentsQAF;
                break;
            case "txtRejectionComments":
                sOld = sDbsCommentsGDPEPRejection;
                sNew = sNewCommentsGDPEPRejection;
                break;
        }
    }

    public void AlertBox(corePage atPage, string sMsg)
    {
        //if (!new corePage().isLocalhost())
        //{
        //    atPage.Response.Write("<Script language='VBScript'>\r\n");
        //    atPage.Response.Write("   answer = MsgBox(\"" + sMsg + "\", vbYesNo)\r\n");
        //    atPage.Response.Write("</Script>\r\n");
        //}
        //else
        //{
        //    atPage.Response.Write("<script language='JavaScript'>\r\n");
        //    atPage.Response.Write("  alert('BYPASSING - " + sMsg + "');\r\n");
        //    atPage.Response.Write("  var answer = 7;\r\n");
        //    atPage.Response.Write("</script>\r\n");
        //}
        //atPage.Response.Write("<Script language='JavaScript'>\r\n");
        //atPage.Response.Write("   if (answer == 6){\r\n");
        //atPage.Response.Write("       document.location='" + DescriptionURL() + "?QueueKey=" + Conversions.ToString(iQueueKey) + "&DualRelease=1&Link=" + atPage.Request["__EVENTTARGET"] + "&ReleaseNoPNL=" + atPage.Request["ReleaseNoPNL"] + "';}\r\n");
        //atPage.Response.Write("   else if (answer == 7){\r\n");
        //atPage.Response.Write("       document.location='" + DescriptionURL() + "?QueueKey=" + Conversions.ToString(iQueueKey) + "&DualRelease=0&Link=" + atPage.Request["__EVENTTARGET"] + "&ReleaseNoPNL=" + atPage.Request["ReleaseNoPNL"] + "';}\r\n");
        //atPage.Response.Write("   else{\r\n");
        //atPage.Response.Write("       document.location='" + DescriptionURL() + "?QueueKey=" + Conversions.ToString(iQueueKey) + "&DualRelease=-1&Link=" + atPage.Request["__EVENTTARGET"] + "&ReleaseNoPNL=" + atPage.Request["ReleaseNoPNL"] + "';}\r\n");
        //atPage.Response.Write("</Script>\r\n");
    }

    public void AlertNoPNLBox(corePage atPage, string sMsg)
    {
        //atPage.Response.Write("<Script>");
        //atPage.Response.Write("   var answer = confirm(\"" + sMsg + "\");");
        //atPage.Response.Write("   if (answer == true){");
        //atPage.Response.Write("       document.location='QuoteResponse.aspx?QueueKey=" + Conversions.ToString(iQueueKey) + "&DualRelease=" + atPage.Request["DualRelease"] + "&Link=" + atPage.Request["__EVENTTARGET"] + "&ReleaseNoPNL=1';}");
        //atPage.Response.Write("   else{");
        //atPage.Response.Write("       document.location='QuoteResponse.aspx?QueueKey=" + Conversions.ToString(iQueueKey) + "&DualRelease=" + atPage.Request["DualRelease"] + "';}");
        //atPage.Response.Write("</Script>");
    }

    public void AlertAndRedirect(corePage atPage, string sMsg)
    {
        //atPage.Response.Write("<Script>");
        //atPage.Response.Write("   alert(\"" + sMsg + "\");");
        //atPage.Response.Write("   document.location = '" + modCQR.getRedirectPage() + "';");
        //atPage.Response.Write("</Script>");
    }

    private bool z_CreateNewCQR(corePage tPage)
    {
        string sSQL = " CQR_Header WHERE OriginatorId = '" + UserId + "' AND LastUpdatedBy = 'Create'";
        tPage.cnExecuteNonQuery("DELETE FROM" + sSQL);
        tPage.cnExecuteNonQuery("INSERT INTO CQR_Header (OriginatorId, LastUpdatedBy) VALUES ('" + UserId + "', 'Create')");
        iQueueKey = checked((long)Math.Round(Conversion.Val(tPage.cnExecuteForSingleValue("SELECT QueueKey FROM " + sSQL))));
        sStatus = "010100FR";
        sStatusDate = Conversions.ToString(DateAndTime.Now);
        sOriginator = UserId;
        sOriginationDate = Conversions.ToString(DateAndTime.Now);
        return iQueueKey > 0;
    }

    private string z_AddInsertValueDate(string asValue)
    {
        return Conversions.ToString(Operators.ConcatenateObject(", ", Interaction.IIf(Operators.CompareString(asValue, "", TextCompare: false) == 0, "null", "'" + asValue + "'")));
    }

    public string z_AddInsertValue(string asValue)
    {
        return ", '" + Strings.Replace(asValue, "'", "''") + "'";
    }

    public string z_AddUpdateField(string asField, string asValue)
    {
        return asField + " = '" + Strings.Replace(asValue, "'", "''") + "', ";
    }

    private string z_AddUpdateFieldDate(string asField, string asValue)
    {
        if (Operators.CompareString(asValue, "", TextCompare: false) != 0)
        {
            return asField + " = '" + Strings.Replace(asValue, "'", "''") + "', ";
        }
        return asField + " = Null, ";
    }

    public void z_UpdateStatus(string asNewStatus)
    {
        sStatus = asNewStatus;
        sStatusDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
        if (((Operators.CompareString(sStatus, "090100FR", TextCompare: false) == 0) & isLegacy()) && !tRouting.mHasTask("31", "NA"))
        {
            tRouting.mCreateTask("31", "NA", "NA", sAccountManager, "", abNote: true);
        }
    }

    private void z_FromPage(object tPage, string asField, ref string asValue, bool abCheckBox = false, bool abYESNORadioButton = false, bool abDate = false)
    {
        object[] array;
        bool[] array2;
        object obj3;
        if (abCheckBox)
        {
            object[] obj = new object[1] { asField };
            array = obj;
            bool[] obj2 = new bool[1] { true };
            array2 = obj2;
            obj3 = NewLateBinding.LateGet(tPage, null, "FindControl", obj, null, null, obj2);
            if (array2[0])
            {
                asField = (string)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[0]), typeof(string));
            }
            //System.Web.UI.WebControls.CheckBox tCheckbox = (System.Web.UI.WebControls.CheckBox)obj3;
            //if (tCheckbox != null)
            //{
            //    asValue = Conversions.ToString(Interaction.IIf(tCheckbox.Checked, "1", "0"));
            //}
            return;
        }
        if (abYESNORadioButton)
        {
            Type typeFromHandle = typeof(Strings);
            object[] array3 = new object[1];
            obj3 = tPage;
            object instance = obj3;
            object[] array4 = new object[1];
            string text = (string)(array4[0] = asField);
            object[] array5 = array4;
            bool[] obj4 = new bool[1] { true };
            bool[] array6 = obj4;
            object obj5 = NewLateBinding.LateGet(instance, null, "Request", array4, null, null, obj4);
            if (array6[0])
            {
                asField = (string)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array5[0]), typeof(string));
            }
            array3[0] = obj5;
            array = array3;
            object obj6 = NewLateBinding.LateGet(null, typeFromHandle, "UCase", array3, null, null, array2 = new bool[1] { true });
            if (array2[0])
            {
                NewLateBinding.LateSetComplex(obj3, null, "Request", new object[2]
                {
                    text,
                    array[0]
                }, null, null, OptimisticSet: true, RValueBase: false);
            }
            string sTemp = Conversions.ToString(obj6);
            if (Operators.CompareString(Strings.Right(sTemp, 3), "YES", TextCompare: false) == 0)
            {
                asValue = "1";
            }
            if (Operators.CompareString(Strings.Right(sTemp, 2), "NO", TextCompare: false) == 0)
            {
                asValue = "0";
            }
            return;
        }
        object[] obj7 = new object[1] { asField };
        array = obj7;
        bool[] obj8 = new bool[1] { true };
        array2 = obj8;
        obj3 = NewLateBinding.LateGet(tPage, null, "Request", obj7, null, null, obj8);
        if (array2[0])
        {
            asField = (string)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[0]), typeof(string));
        }
        if (obj3 is string)
        {
            object[] obj9 = new object[1] { asField };
            array = obj9;
            bool[] obj10 = new bool[1] { true };
            array2 = obj10;
            obj3 = NewLateBinding.LateGet(tPage, null, "Request", obj9, null, null, obj10);
            if (array2[0])
            {
                asField = (string)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[0]), typeof(string));
            }
            string sTemp = Conversions.ToString(obj3);
            asValue = Strings.RTrim(sTemp);
            if (abDate)
            {
                asValue = modTools.mDateScreenToCygnet(asValue);
            }
        }
    }

    public void mGetUserDataFolder()
    {
        //corePage tPage = new corePage();
        bOrigIsPgm = false;
        bOrigIsPetm = false;
        bOrigIsSDir = false;
        bOrigIsAMgr = false;
        bOrigIsCost = false;
        bOrigIsMbne = false;
        bUserIsOrig = Operators.CompareString(sOriginator, UserId, TextCompare: false) == 0;
        bUserHasOrigRole = mUserHasCommonRole(sOriginator);
        string sql = "SELECT UR.RoleCode FROM USERPROF_UserRoles UR, USERPROF_UserProfileHeader UH WHERE UR.QueueKey = UH.QueueKey AND UH.UserId = '" + sOriginator + "'";
        SqlDataReader cIndex = null;//tPage.cnExecute(sql);
        //while (cIndex.Read())
        //{
        //    switch (Strings.Trim(tPage.rstString(ref cIndex, "RoleCode")))
        //    {
        //        case "PGM":
        //            bOrigIsPgm = true;
        //            break;
        //        case "PETM":
        //            bOrigIsPetm = true;
        //            break;
        //        case "SDIR":
        //            bOrigIsSDir = true;
        //            break;
        //        case "AMGR":
        //            bOrigIsAMgr = true;
        //            break;
        //        case "COST":
        //            bOrigIsCost = true;
        //            break;
        //        case "MBNE":
        //            bOrigIsMbne = true;
        //            break;
        //    }
        //}
        cIndex.Close();
        //tPage.cnClose();
    }

    public void z_NotifyComma(string asText, string asTask, string asAction)
    {
        string sTemp = asText;
        checked
        {
            while (Operators.CompareString(sTemp, "", TextCompare: false) != 0)
            {
                int iTemp = Strings.InStr(sTemp, ",");
                if (iTemp == 0)
                {
                    iTemp = Strings.Len(sTemp) + 1;
                }
                tRouting.mCreateTask(asTask, asAction, asAction, Strings.UCase(Strings.Left(sTemp, iTemp - 1)), "", abNote: true);
                sTemp = Strings.Mid(sTemp, iTemp + 1);
            }
        }
    }

    public bool mUserHasCommonRole(string asWithUser)
    {
        //corePage tPage = new corePage();
        string sRoles = "";
        bool mUserHasCommonRole = true;
        if (Operators.CompareString(asWithUser, UserId, TextCompare: false) != 0)
        {
            string sSQL = "SELECT RoleCode FROM USERPROF_UserProfileHeader UH, USERPROF_UserRoles UR";
            sSQL += " WHERE UH.QueueKey = UR.QueueKey";
            sSQL += " AND UH.UserId = '";
            SqlDataReader cIndex = null;//tPage.cnExecute(sSQL + asWithUser + "'");
            while (cIndex.Read())
            {
                //if (Operators.CompareString(tPage.rstString(ref cIndex, "RoleCode"), "MAIL", TextCompare: false) != 0)
                //{
                //    sRoles = sRoles + tPage.rstString(ref cIndex, "RoleCode") + ",";
                //}
            }
            cIndex.Close();
            //tPage.cnClose();
            if (Operators.CompareString(sRoles, "", TextCompare: false) == 0)
            {
                mUserHasCommonRole = false;
            }
            else
            {
                //cIndex = tPage.cnExecute(sSQL + UserId + "'");
                //while (cIndex.Read() && Strings.InStr(sRoles, tPage.rstString(ref cIndex, "RoleCode")) <= 0)
                //{
                //}
                mUserHasCommonRole = Conversions.ToBoolean(Interaction.IIf(!cIndex.NextResult() | (Operators.CompareString(sRoles, "", TextCompare: false) == 0), false, true));
                cIndex.Close();
            }
        }
        return mUserHasCommonRole;
    }

    public bool mHasCloseOutRole()
    {
        if (bOrigIsAMgr | bOrigIsSDir | (Operators.CompareString(sQuoteType, "1", TextCompare: false) == 0))
        {
            return tUserData.bIsAMgr | tUserData.bIsSDir;
        }
        return bUserHasOrigRole;
    }

    public void z_CloseQuoteResponseTask(string sMode)
    {
        tRouting.mCloseTask("22", "BA", "BA", "*", sMode);
        tRouting.mCloseTask("22", "CA", "CA", "*", sMode);
        tRouting.mCloseTask("24", "CA", "CA", "*", sMode);
    }

    private bool needToLoad()
    {
        checked
        {
            int num = iQAFCount - 1;
            for (int iLoop = 0; iLoop <= num; iLoop++)
            {
                if ((Operators.CompareString(tPNL[iLoop].sQafStatus, "010100QF", TextCompare: false) > 0) & (Information.UBound(tPNL[iLoop].tPNLLineItem) < 0))
                {
                    return true;
                }
            }
            bool needToLoad = default(bool);
            return needToLoad;
        }
    }

    private void pullLineItemData(Worksheet tExcelSheet, int iIndex)
    {
        int iRow = 16;
        tPNL[iIndex].tPNLLineItem = new PNLLineItem[0];
        checked
        {
            for (; Operators.ConditionalCompareObjectNotEqual(Operators.ConcatenateObject(((_Worksheet)tExcelSheet).get_Range((object)("B" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)), ""), "0", TextCompare: false) && Operators.CompareString(Strings.Trim(Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("B" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)))) ?? "", "", TextCompare: false) != 0; iRow++)
            {
                object[] array;
                //Range range;
                object value;
                bool[] array2;
                //object left = NewLateBinding.LateGet(null, typeof(Strings), "LCase", array = new object[1] { (range = ((_Worksheet)tExcelSheet).get_Range((object)("B" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value))).get_Value(RuntimeHelpers.GetObjectValue(value = Missing.Value)) }, null, null, array2 = new bool[1] { true });
                //if (array2[0])
                //{
                //    range.set_Value(RuntimeHelpers.GetObjectValue(value), RuntimeHelpers.GetObjectValue(RuntimeHelpers.GetObjectValue(array[0])));
                //}
                //if (Operators.ConditionalCompareObjectEqual(Operators.ConcatenateObject(left, ""), "total", TextCompare: false))
                //{
                //    break;
                //}
                ref PNLLineItem[] tPNLLineItem = ref tPNL[iIndex].tPNLLineItem;
                tPNLLineItem = (PNLLineItem[])Utils.CopyArray(tPNLLineItem, new PNLLineItem[Information.UBound(tPNL[iIndex].tPNLLineItem) + 1 + 1]);
                ref PNLLineItem reference = ref tPNL[iIndex].tPNLLineItem[Information.UBound(tPNL[iIndex].tPNLLineItem)];
                reference.sProduct = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("B" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sSite = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("C" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAverageAnnualVolume = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("F" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sSalesPriceInQuoted = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("D" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sQuotedCurrency = "US Dollars";
                reference.sSalesBusinessUnit = "BCSNA";
                reference.sSalesPriceSOP = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("D" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAverageAnnualSales = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("G" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAverageAnnualDVP = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("H" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAverageAnnualMPBT = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("I" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sEVAFullCost = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("S" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sEVAIncremental = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("T" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAwardStatus = "";
                reference.sCreateDate = Conversions.ToString(DateAndTime.Now);
            }
        }
    }

    private void pullLineItemDataNewFormat(Worksheet tExcelSheet, int iIndex)
    {
        int iRow = 18;
        tPNL[iIndex].tPNLLineItem = new PNLLineItem[0];
        checked
        {
            for (; Operators.ConditionalCompareObjectNotEqual(Operators.ConcatenateObject(((_Worksheet)tExcelSheet).get_Range((object)("B" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)), ""), "0", TextCompare: false) && Operators.CompareString(Strings.Trim(Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("B" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)))) ?? "", "", TextCompare: false) != 0; iRow++)
            {
                object[] array;
                //Range range;
                object value;
                bool[] array2;
                //object left = NewLateBinding.LateGet(null, typeof(Strings), "LCase", array = new object[1] { (range = ((_Worksheet)tExcelSheet).get_Range((object)("B" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value))).get_Value(RuntimeHelpers.GetObjectValue(value = Missing.Value)) }, null, null, array2 = new bool[1] { true });
                //if (array2[0])
                //{
                //    //range.set_Value(RuntimeHelpers.GetObjectValue(value), RuntimeHelpers.GetObjectValue(RuntimeHelpers.GetObjectValue(array[0])));
                //}
                //if (Operators.ConditionalCompareObjectEqual(Operators.ConcatenateObject(left, ""), "total", TextCompare: false))
                //{
                //    break;
                //}
                ref PNLLineItem[] tPNLLineItem = ref tPNL[iIndex].tPNLLineItem;
                tPNLLineItem = (PNLLineItem[])Utils.CopyArray(tPNLLineItem, new PNLLineItem[Information.UBound(tPNL[iIndex].tPNLLineItem) + 1 + 1]);
                ref PNLLineItem reference = ref tPNL[iIndex].tPNLLineItem[Information.UBound(tPNL[iIndex].tPNLLineItem)];
                reference.sProduct = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("B" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sSite = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("C" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAverageAnnualVolume = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("F" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sSalesPriceInQuoted = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("D" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sQuotedCurrency = "US Dollars";
                reference.sSalesBusinessUnit = "BCSNA";
                reference.sSalesPriceSOP = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("D" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAverageAnnualSales = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("H" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAverageAnnualDVP = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("I" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAverageAnnualMPBT = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("J" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sEVAFullCost = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("N" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sEVAIncremental = Conversions.ToString(((_Worksheet)tExcelSheet).get_Range((object)("S" + Conversions.ToString(iRow)), RuntimeHelpers.GetObjectValue(Missing.Value)).get_Value(RuntimeHelpers.GetObjectValue(Missing.Value)));
                reference.sAwardStatus = "";
                reference.sCreateDate = Conversions.ToString(DateAndTime.Now);
            }
        }
    }

    public void retrievePNLGridData()
    {
        if (needToLoad())
        {
            bool flag = true;
        }
    }

    public void mNotifyUserArray(string[] sUsers, int iUserCount, string asTask, string asAction)
    {
        checked
        {
            int num = iUserCount - 1;
            for (int iLoop = 0; iLoop <= num; iLoop++)
            {
                if (Operators.CompareString(sUsers[iLoop], "", TextCompare: false) != 0 && !tRouting.mHasCurrentTask(asTask, asAction, asAction, sUsers[iLoop]))
                {
                    tRouting.mCreateTask(asTask, asAction, asAction, sUsers[iLoop], "", abNote: true);
                }
            }
        }
    }

    public bool mHasQRCostTask()
    {
        bool mHasQRCostTask = true;
        if (!tRouting.mHasCurrentTask("22", "BA", "BA", "*") && !tRouting.mHasCurrentTask("22", "CA", "CA", "*") && !tRouting.mHasCurrentTask("24", "CA", "CA", "*"))
        {
            mHasQRCostTask = false;
        }
        return mHasQRCostTask;
    }

    public bool LoadPartsList(int aiPartList, corePage atPage)
    {
        int try0001_dispatch = -1;
        bool LoadPartsList;
        int num = default(int);
        int num2 = default(int);
        while (true)
        {
            try
            {
                /*Note: ILSpy has introduced the following switch to emulate a goto from catch-block to try-block*/
                ;
                switch (try0001_dispatch)
                {
                    default:
                        LoadPartsList = true;
                        goto end_IL_0001;
                    case 13:
                        num = -1;
                        switch (num2)
                        {
                            case 2:
                                LoadPartsList = false;
                                goto end_IL_0001;
                        }
                        break;
                }
            }
            catch (Exception e)
            {

            }
            //catch (object obj) when (obj is Exception && num2 != 0 && num == 0)
            //{
            //    ProjectData.SetProjectError((Exception)obj);
            //    try0001_dispatch = 13;
            //    continue;
            //}
            //throw ProjectData.CreateProjectError(-2146828237);
            continue;
        end_IL_0001:
            break;
        }
        if (num != 0)
        {
            ProjectData.ClearProjectError();
        }
        return LoadPartsList;
    }

    public void mRedimLucasParts(int aiSize)
    {
        ref string[] reference = ref sLucasPartList;
        reference = (string[])Utils.CopyArray(reference, new string[checked(aiSize + 1)]);
    }

    public void mRedimCustomerParts(int aiSize)
    {
        ref string[] reference = ref sCustPartList;
        reference = (string[])Utils.CopyArray(reference, new string[checked(aiSize + 1)]);
    }

    public void LoadUnitCost(corePage atPage)
    {
        checked
        {
            if (!modCQR.isABS())
            {
                string sSQL = "SELECT * FROM CQR_QRUnitCost WHERE QueueKey = " + Conversions.ToString(iQueueKey);
                SqlDataReader drRecVals = atPage.cnExecute(sSQL);
                T_UNITCOST tBlank = new T_UNITCOST();
                int iCount;
                for (iCount = 0; drRecVals.Read() & (iCount < iUnitCostCount); iCount++)
                {
                    tUnitCost[iCount] = new T_UNITCOST();
                    tUnitCost[iCount].sDesc = atPage.rstString(ref drRecVals, "UnitCostDesc");
                    tUnitCost[iCount].sCost = atPage.rstString(ref drRecVals, "UnitCost_001");
                    tUnitCost[iCount].sQty = atPage.rstString(ref drRecVals, "UnitQuantity");
                }
                atPage.cnClose();
                int num = iCount;
                int num2 = iUnitCostCount - 1;
                for (iCount = num; iCount <= num2; iCount++)
                {
                    tUnitCost[iCount] = tBlank;
                }
            }
        }
    }

    public string mUnitCostGet(int aiIndex)
    {
        string mUnitCostGet = "";
        if (!(aiIndex >= iUnitCostCount || aiIndex < 0))
        {
            T_UNITCOST t_UNITCOST = tUnitCost[aiIndex];
            if ((Operators.CompareString(t_UNITCOST.sDesc, "", TextCompare: false) != 0) | (Operators.CompareString(t_UNITCOST.sCost, "", TextCompare: false) != 0) | (Operators.CompareString(t_UNITCOST.sQty, "", TextCompare: false) != 0))
            {
                mUnitCostGet = t_UNITCOST.sDesc + "\t" + t_UNITCOST.sCost + "\t" + t_UNITCOST.sQty + "\t";
            }
            t_UNITCOST = null;
        }
        return mUnitCostGet;
    }

    public void mUnitCostLet(int aiIndex, string sDesc, string sCost, string sQty)
    {
        if (!(aiIndex >= iUnitCostCount || aiIndex < 0))
        {
            tUnitCost[aiIndex].sDesc = sDesc;
            tUnitCost[aiIndex].sCost = sCost;
            tUnitCost[aiIndex].sQty = sQty;
        }
    }

    public string mLockNextRevision(corePage atPage, string asKey, string asDefault, ref string asRevision)
    {
        return Conversions.ToString(mTRSNextNumberLock(atPage, asKey, asDefault, ref asRevision));
    }

    public int mTRSNextNumberLock(corePage atPage, string asEntry, string asDefault, ref string asNewNumber, string asQueueKey = "0")
    {
        int try0001_dispatch = -1;
        int num2 = default(int);
        int mTRSNextNumberLock;
        int num = default(int);
        try
        {
            /*Note: ILSpy has introduced the following switch to emulate a goto from catch-block to try-block*/

         
                        //ProjectData.ClearProjectError();
                        num2 = 2;
                        mTRSNextNumberLock = -1;
                        string sSQL = "SELECT * FROM TRS_Header";
                        sSQL += " WHERE table_name = 'NUMBERS'";
                        sSQL = sSQL + " AND table_entry = '" + asEntry + "'";
                        SqlDataReader drRecVals = atPage.cnExecute(sSQL);
                        sLockedEntry = asEntry;
                        if (!drRecVals.HasRows)
                        {
                            string maxRevNumber = "";//atPage.cnExecuteForSingleValue("SELECT MAX(RevNbr) From CQR_Header where ProjectNbr = " + sProjectNum);
                            maxRevNumber = Strings.Right("**********" + maxRevNumber, Strings.Len(asDefault));
                            atPage.cnExecuteNonQuery("INSERT INTO TRS_Header (table_name, table_entry, _desc) VALUES ('NUMBERS','" + sLockedEntry + "', '0001')");
                        }
                        drRecVals.Close();
                        atPage.cnClose();
                        drRecVals = atPage.cnExecute(sSQL);
                        if (!drRecVals.HasRows)
                        {
                            sLockedKey = "NEW";
                            sLockedNumber = asDefault;
                        }
                        else
                        {
                            while (drRecVals.Read())
                            {
                                sLockedKey = atPage.rstString(ref drRecVals, "QueueKey");
                                sLockedNumber = Conversions.ToString(Conversions.ToDouble(atPage.rstString(ref drRecVals, "_desc")) + 1.0);
                                sLockedNumber = Strings.Right("**********" + sLockedNumber, Strings.Len(asDefault));
                            }
                            atPage.cnClose();
                            if (!atPage.FolderCanBeEdited("TRS_Header", "QueueKey", sLockedKey, UserId))
                            {
                                //goto end_IL_0001;
                            }
                            atPage.FolderLock("TRS_Header", "QueueKey", sLockedKey, UserId);
                        }
                        sLockedNumber = Conversions.ToString(Conversions.ToDouble(atPage.cnExecuteForSingleValue("SELECT MAX(RevNbr) From CQR_Header where ProjectNbr = " + sProjectNum)) + 1.0);
                        sLockedNumber = Strings.Right("**********" + sLockedNumber, Strings.Len(asDefault));
                        atPage.cnClose();
                        asNewNumber = sLockedNumber;
                        mTRSNextNumberLock = 0;
                        //goto end_IL_0001;
                
        }
        catch (Exception e)
        {

        }
        //catch (object obj) when (obj is Exception && num2 != 0 && num == 0)
        //{
        //    ProjectData.SetProjectError((Exception)obj);
        //    try0001_dispatch = 543;
        //    continue;
        //}
        //throw ProjectData.CreateProjectError(-2146828237);
        //continue;
        //end_IL_0001:
        //return mTRSNextNumberLock;
        return 0;
    }

    public int mTRSNextNumberUnlock(int abSave, corePage atpage)
    {
        string desc = "";
        if (abSave != 0 && Operators.CompareString(sLockedKey, "", TextCompare: false) != 0)
        {
            if (Operators.CompareString(sLockedKey, "NEW", TextCompare: false) == 0)
            {
                desc = null;//atpage.cnExecuteForSingleValue("SELECT _desc from TRS_Header where table_entry = '" + sLockedEntry + "'");
                if (Operators.CompareString(desc, "", TextCompare: false) != 0)
                {
                    sRevision = Strings.Right("0000" + Conversions.ToString(Conversions.ToDouble(desc) + 1.0), 4);
                    atpage.cnExecuteNonQuery("UPDATE TRS_Header SET _desc = '" + sRevision + "' WHERE table_entry = '" + sLockedEntry + "'");
                    SaveData(atpage, bExit: true);
                }
                else
                {
                    atpage.cnExecuteNonQuery("INSERT INTO TRS_Header (table_name, table_entry, _desc) VALUES ('NUMBERS', '" + sLockedEntry + "', '" + sLockedNumber + "')");
                }
            }
            else
            {
                atpage.cnExecuteNonQuery("UPDATE TRS_Header SET _desc = '" + sLockedNumber + "' WHERE QueueKey = " + sLockedKey);
            }
        }
        atpage.FolderUnlock("TRS_Header", "QueueKey", sLockedKey, UserId);
        sLockedKey = "";
        sLockedEntry = "";
        sLockedNumber = "";
        int mTRSNextNumberUnlock = default(int);
        return mTRSNextNumberUnlock;
    }

    public bool mReleaseTask(string asTask, string asUser, corePage atPage, string asMoreInfo = "")
    {
        string sTemp = modUserProf.UserNameFromId(asUser);
        AlertBox(atPage, "You are not the Engineer Assigned on this folder. Do you want to perform the work for the named Engineer Assigned (" + sTemp + ")");
        return false;
    }

    public string mQafSalesActionGuy()
    {
        if (bOrigIsSDir | bOrigIsAMgr | (Operators.CompareString(Strings.Trim(sSalesAccountDirector), "", TextCompare: false) == 0))
        {
            return sOriginator;
        }
        return sSalesAccountDirector;
    }

    public void z_AdvanceQaf(int iIndex, string sQaf, string asNewStatus)
    {
        tPNL[iIndex].sQafStatus = asNewStatus;
        tPNL[iIndex].sStatusDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
    }

    public void mSendNotification(string originator, string recipient, string asTask, string asAction, string asRtg, string asUser, string asDue = "", bool abNote = false, string asDwgNbr = "")
    {
        if (Operators.CompareString(originator, recipient, TextCompare: false) != 0)
        {
            tRouting.mCreateTask(asTask, asAction, asRtg, asUser, asDue, abNote, asDwgNbr);
        }
    }

    public string mRCTFillList(coreTable acSheet)
    //public string mRCTFillList(coreTable acSheet, System.Web.UI.WebControls.Label acControl = null)
    {
        long lTotalObs = 0L;
        //corePage atPage = new corePage();
        int num = iRCTCount;
        checked
        {
            //for (int iLoop = 1; iLoop <= num; iLoop++)
            //{
            //    acSheet.AddRow();
            //    HyperLink rctLink = new HyperLink();
            //    rctLink.NavigateUrl = "javascript:openSelectedRCT('" + sRCTItem[iLoop - 1] + "')";
            //    rctLink.Text = sRCTItem[iLoop - 1];
            //    acSheet.AddCell().Controls.Add(rctLink);
            //    acSheet.AddCell(sRCTPartName[iLoop - 1]);
            //    TableCell tableCell = acSheet.AddCell("$" + Strings.Format(Conversion.Val(sRCTCost[iLoop - 1]), "###,###,##0"));
            //    tableCell.HorizontalAlign = HorizontalAlign.Right;
            //    tableCell = null;
            //    lTotalObs = (long)Math.Round((double)lTotalObs + Conversion.Val(sRCTCost[iLoop - 1]));
            //}
            //if (acControl != null)
            //{
            //    acControl.Text = Strings.Format(Conversion.Val(lTotalObs), "$###,###,##0");
            //}
            return Strings.Format(Conversion.Val(lTotalObs), "$###,###,##0");
        }
    }

    public void mEPLDetails(coreTable acSheet)
    {
        long lTotalMat = 0L;
        long lTotalTool = 0L;
        long lTotalCapital = 0L;
        //corePage atPage = new corePage();
        SqlDataReader drRecVals = null;//atPage.cnExecute(getEPLDetailQuery());
        //Table tTable = new Table();
        //TableRow tRow = new TableRow();
        //TableCell tCell = new TableCell();
        //System.Web.UI.WebControls.CheckBox tCheckBox = new System.Web.UI.WebControls.CheckBox();
        checked
        {
            //while (drRecVals.Read())
            //{
            //    acSheet.AddRow();
            //    acSheet.AddCell(atPage.rstString(ref drRecVals, "QueueKey"));
            //    acSheet.AddCell(atPage.rstString(ref drRecVals, "PartNumber"));
            //    acSheet.AddCell(atPage.rstString(ref drRecVals, "PartName"));
            //    TableCell tableCell = acSheet.AddCell(atPage.rstString(ref drRecVals, "QtyPer"));
            //    tableCell.HorizontalAlign = HorizontalAlign.Right;
            //    tableCell = null;
            //    TableCell tableCell2 = acSheet.AddCell("$" + modCQR.mCommifyDollarAmount(Conversions.ToString(Conversion.Val(atPage.rstString(ref drRecVals, "MatCost"))), 4));
            //    tableCell2.HorizontalAlign = HorizontalAlign.Right;
            //    tableCell2 = null;
            //    TableCell tableCell3 = acSheet.AddCell("$" + modCQR.mCommifyDollarAmount(Conversions.ToString(Conversion.Val(atPage.rstString(ref drRecVals, "Tooling"))), 4));
            //    tableCell3.HorizontalAlign = HorizontalAlign.Right;
            //    tableCell3 = null;
            //    TableCell tableCell4 = acSheet.AddCell("$" + modCQR.mCommifyDollarAmount(Conversions.ToString(Conversion.Val(atPage.rstString(ref drRecVals, "Capital"))), 4));
            //    tableCell4.HorizontalAlign = HorizontalAlign.Right;
            //    tableCell4 = null;
            //    lTotalMat = (long)Math.Round((double)lTotalMat + Conversion.Val(atPage.rstString(ref drRecVals, "MatCost")));
            //    lTotalCapital = (long)Math.Round((double)lTotalCapital + Conversion.Val(atPage.rstString(ref drRecVals, "Capital")));
            //    lTotalTool = (long)Math.Round((double)lTotalTool + Conversion.Val(atPage.rstString(ref drRecVals, "Tooling")));
            //}
            //acSheet.AddRow();
            //TableCell tableCell5 = acSheet.AddCell("Totals");
            //tableCell5.ColumnSpan = 4;
            //tableCell5.HorizontalAlign = HorizontalAlign.Right;
            //tableCell5.CssClass = "tableHeader";
            //tableCell5 = null;
            //TableCell tableCell6 = acSheet.AddCell("$" + modCQR.mCommifyDollarAmount(Conversions.ToString(Conversion.Val(lTotalMat)), 4));
            //tableCell6.HorizontalAlign = HorizontalAlign.Right;
            //tableCell6.CssClass = "tableHeader";
            //tableCell6 = null;
            //TableCell tableCell7 = acSheet.AddCell("$" + modCQR.mCommifyDollarAmount(Conversions.ToString(Conversion.Val(lTotalTool)), 4));
            //tableCell7.HorizontalAlign = HorizontalAlign.Right;
            //tableCell7.CssClass = "tableHeader";
            //tableCell7 = null;
            //TableCell tableCell8 = acSheet.AddCell("$" + modCQR.mCommifyDollarAmount(Conversions.ToString(Conversion.Val(lTotalCapital)), 4));
            //tableCell8.HorizontalAlign = HorizontalAlign.Right;
            //tableCell8.CssClass = "tableHeader";
            //tableCell8 = null;
            //drRecVals.Close();
        }
    }

    public string getEPLDetailQuery()
    {
        string sSQL = "";
        sSQL = "SELECT EP.Queuekey, EP.DisplayOrder, EP.PartNumber, EP.PartName, EP.QtyPer, EP.MatCost,EP.DirLabor, EP.OHD, EP.Tooling, EP.Capital ";
        sSQL = sSQL + "FROM EPL_PARTS EP, EPL_HEADER EH WHERE EP.QueueKey = EH.QueueKey and EH.CQRNUMBER = '" + Conversions.ToString(Conversion.Val(sProjectNum)) + "." + Conversions.ToString(Conversion.Val(sRevision)) + "'";
        return sSQL + " ORDER BY EP.QUEUEKEY, EP.DISPLAYORDER";
    }

    public void LoadEPLList()
    {
        //corePage atPage = new corePage();
        SqlDataReader drRecVals = null;//atPage.cnExecute("SELECT QueueKey, Subject FROM EPL_HEADER WHERE CQRNUMBER = '" + Conversions.ToString(Conversion.Val(sProjectNum)) + "." + Conversions.ToString(Conversion.Val(sRevision)) + "'");
        iEPLCount = 0;
        checked
        {
            while (drRecVals.Read())
            {
                iEPLCount++;
                ref string[] reference = ref sEPLItem;
                reference = (string[])Utils.CopyArray(reference, new string[iEPLCount + 1]);
                ref string[] reference2 = ref sEPLPartName;
                reference2 = (string[])Utils.CopyArray(reference2, new string[iEPLCount + 1]);
                //sEPLItem[iEPLCount - 1] = Conversions.ToString(atPage.rstInt(ref drRecVals, "QueueKey"));
                //sEPLPartName[iEPLCount - 1] = atPage.rstString(ref drRecVals, "Subject");
            }
            drRecVals.Close();
        }
    }

    //public int mEPLFillList(coreTable acSheet, System.Web.UI.WebControls.Label acControl = null, bool abAddCheck = false)
    //{
    //    int num = iEPLCount;
    //    checked
    //    {
    //        for (int iLoop = 1; iLoop <= num; iLoop++)
    //        {
    //            acSheet.AddRow();
    //            if (abAddCheck)
    //            {
    //                System.Web.UI.WebControls.CheckBox tCheck = new System.Web.UI.WebControls.CheckBox();
    //                tCheck.ID = "chkECM_" + Conversions.ToString(iLoop);
    //                tCheck.Attributes.Add("ecmNum", sEPLItem[iLoop - 1]);
    //                acSheet.AddCellEx("", HorizontalAlign.Center).Controls.Add(tCheck);
    //            }
    //            HyperLink eplLink = new HyperLink();
    //            eplLink.NavigateUrl = "javascript:openSelectedEPL('" + sEPLItem[iLoop - 1] + "')";
    //            eplLink.Text = sEPLItem[iLoop - 1];
    //            acSheet.AddCell().Controls.Add(eplLink);
    //            acSheet.AddCell(sEPLPartName[iLoop - 1]);
    //        }
    //        return 0;
    //    }
    //}

    public string mTRSDateDecrement(string asDate, ref string asDecDate, int aiHowMuch)
    {
        string mTRSDateDecrement = "";
        checked
        {
            if (Operators.CompareString(asDate, "", TextCompare: false) == 0)
            {
                asDecDate = "";
            }
            else
            {
                string sTempDate = asDate;
                string startDate = modTools.mDateCygnetToScreen(asDate);
                int num = Math.Abs(aiHowMuch) - 1;
                for (int iLoop = 0; iLoop <= num; iLoop++)
                {
                    sTempDate = Conversions.ToString(DateAndTime.DateAdd("d", Conversions.ToDouble(Operators.MultiplyObject(-1, Interaction.IIf(aiHowMuch < 0, -1, 1))), modTools.mDateCygnetToScreen(sTempDate))) ?? "";
                    if ((DateAndTime.Weekday(Conversions.ToDate(sTempDate)) == 7) | (DateAndTime.Weekday(Conversions.ToDate(sTempDate)) == 1))
                    {
                        iLoop--;
                    }
                }
                string endDate = modTools.mDateCygnetToScreen(sTempDate);
                int num2 = getHolidayCount(startDate, endDate) - 1;
                for (int iLoop = 0; iLoop <= num2; iLoop++)
                {
                    sTempDate = Conversions.ToString(DateAndTime.DateAdd("d", Conversions.ToDouble(Operators.MultiplyObject(-1, Interaction.IIf(aiHowMuch < 0, -1, 1))), modTools.mDateCygnetToScreen(sTempDate))) ?? "";
                }
                while ((DateAndTime.Weekday(Conversions.ToDate(sTempDate)) == 7) | (DateAndTime.Weekday(Conversions.ToDate(sTempDate)) == 1) | (getHolidayCount(sTempDate, sTempDate) > 0))
                {
                    sTempDate = Conversions.ToString(DateAndTime.DateAdd("d", Conversions.ToDouble(Operators.MultiplyObject(-1, Interaction.IIf(aiHowMuch < 0, -1, 1))), modTools.mDateCygnetToScreen(sTempDate))) ?? "";
                }
                mTRSDateDecrement = (asDecDate = Strings.Format(Conversions.ToDate(sTempDate), "yyyyMMdd"));
            }
            return mTRSDateDecrement;
        }
    }

    private int getHolidayCount(string startDate, string decDate)
    {
        string sSQL = "";
        //corePage atPage = new corePage();
        sSQL = "SELECT COUNT(1) AS Expr1 ";
        sSQL += "FROM TRS_Header ";
        sSQL += "WHERE (table_name = 'HOLIDAY') AND (CAST(SUBSTRING(table_entry, 5, 2) + '/' + SUBSTRING(table_entry, 7, 2) + '/' + LEFT(table_entry, 4) AS smalldatetime) ";
        sSQL = sSQL + "BETWEEN '" + decDate + "' AND '" + startDate + "')";
        //return checked((int)Math.Round(Conversion.Val(atPage.cnExecuteForSingleValue(sSQL))));
        return 0;
    }

    public int pnl_GetAttachmentIndex(string sQafKey)
    {
        int pnl_GetAttachmentIndex = -1;
        checked
        {
            int num = tPNLAttach.tTab.iAttCount - 1;
            for (int iLoop = 0; iLoop <= num; iLoop++)
            {
                if (Conversion.Val(tPNLAttach.tTab.tAttInfo[iLoop].sQueueKey) == Conversion.Val(sQafKey))
                {
                    pnl_GetAttachmentIndex = iLoop;
                }
            }
            return pnl_GetAttachmentIndex;
        }
    }

    public int getQAFCount()
    {
        checked
        {
            int getQAFCount = default(int);
            if (tPNL != null)
            {
                int num = Information.UBound(tPNL);
                for (int iLoop = 0; iLoop <= num; iLoop++)
                {
                    if (tPNL[iLoop] != null && !tPNL[iLoop].bDeleteOnSave)
                    {
                        getQAFCount++;
                    }
                }
            }
            return getQAFCount;
        }
    }

    public string mGetAttachmentQueueKey(corePage atPage, int aiIndex, bool bMap = true)
    {
        string mGetAttachmentQueueKey = "";
        if (tPNL != null && aiIndex <= Information.UBound(tPNL))
        {
            int iAttIndex = aiIndex;
            if (bMap)
            {
                iAttIndex = pnl_GetAttachmentIndex(tPNL[aiIndex].sQAFKey);
            }
            string sSQL = "Select QueueKey from ATTDIR_AttachFileDirHeader where FolderTypeCode = 'FQ'";
            sSQL = sSQL + "  and FolderNbr = '" + Strings.Right("**********" + Conversions.ToString(iQueueKey), 10) + "' and  filelocation = '" + tPNLAttach.tTab.tAttInfo[iAttIndex].sSrcFile + "'";
            mGetAttachmentQueueKey = Conversions.ToString(Conversion.Val(atPage.cnExecuteForSingleValue(sSQL)));
        }
        return mGetAttachmentQueueKey;
    }

    public bool mCheckFormAttached(string keyWord)
    {
        bool mCheckFormAttached = false;
        checked
        {
            int num = tAttachmentResponse.tTab.iAttCount - 1;
            for (int iIndex = 0; iIndex <= num; iIndex++)
            {
                if (Strings.InStr(Strings.LCase(tAttachmentResponse.tTab.tAttInfo[iIndex].sAttachTitle), keyWord) > 0)
                {
                    mCheckFormAttached = true;
                    break;
                }
            }
            return mCheckFormAttached;
        }
    }

    public bool mRerouteTasks()
    {
        if (Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0)
        {
            return false;
        }
        tRouting.countRerouted = 0;
        if (Operators.CompareString(sCommercialManager, sCommercialManagerDbs, TextCompare: false) != 0)
        {
            tRouting.mRerouteTask("01", "BQ", "BQ", sCommercialManagerDbs, sCommercialManager);
        }
        if ((Operators.CompareString(sPIMSite, sPIMSiteDbs, TextCompare: false) != 0) & (Operators.CompareString(sPIMSiteDbs, "", TextCompare: false) != 0))
        {
            if (Operators.CompareString(sPIMSite, "1", TextCompare: false) == 0)
            {
                tRouting.mRerouteTask("08", "BA", "BA", "ASEIBERT", modUserProf.mUPGetManager("PSM"));
            }
            else
            {
                tRouting.mRerouteTask("08", "BA", "BA", modUserProf.mUPGetManager("PSM"), "ASEIBERT");
            }
        }
        if ((Operators.CompareString(sManufacturingSiteDbs, sManufacturingSite, TextCompare: false) != 0) & (Operators.CompareString(sStatus, "010150FR", TextCompare: false) >= 0))
        {
        }
        if ((Operators.CompareString(Strings.Trim(sAccountManagerDbs), "", TextCompare: false) != 0) & (Operators.CompareString(Strings.Trim(sAccountManager), "", TextCompare: false) != 0) & (Operators.CompareString(sAccountManagerDbs, sAccountManager, TextCompare: false) != 0))
        {
            tRouting.mRerouteTask("01", "CA", "CA", sAccountManagerDbs, sAccountManager);
            tRouting.mRerouteTask("28", "MA", "MA", sAccountManagerDbs, sAccountManager);
            tRouting.mRerouteTask("28", "PA", "PA", sAccountManagerDbs, sAccountManager);
            tRouting.mRerouteTask("28", "SA", "SA", sAccountManagerDbs, sAccountManager);
            tRouting.mRerouteTask("53", "AA", "AA", sAccountManagerDbs, sAccountManager);
        }
        if ((Operators.CompareString(Strings.Trim(sSalesAccountDirectorDbs), "", TextCompare: false) != 0) & (Operators.CompareString(Strings.Trim(sSalesAccountDirector), "", TextCompare: false) != 0) & (Operators.CompareString(sSalesAccountDirectorDbs, sSalesAccountDirector, TextCompare: false) != 0))
        {
            tRouting.mRerouteTask("01", "CA", "CA", sSalesAccountDirectorDbs, sSalesAccountDirector);
        }
        if ((Operators.CompareString(Strings.Trim(sCostEstimatorDbs), "", TextCompare: false) != 0) & (Operators.CompareString(Strings.Trim(sCostEstimator), "", TextCompare: false) != 0) & (Operators.CompareString(sCostEstimatorDbs, sCostEstimator, TextCompare: false) != 0))
        {
            tRouting.mRerouteTask("01", "HA", "HA", sCostEstimatorDbs, sCostEstimator);
            tRouting.mRerouteTask("22", "BA", "BA", sCostEstimatorDbs, sCostEstimator);
            tRouting.mRerouteTask("22", "CA", "CA", sCostEstimatorDbs, sCostEstimator);
            tRouting.mRerouteTask("24", "CA", "CA", sCostEstimatorDbs, sCostEstimator);
        }
        if ((Operators.CompareString(Strings.Trim(sPGM_aka_CoCDbs), "", TextCompare: false) != 0) & (Operators.CompareString(Strings.Trim(sPGM_aka_CoC), "", TextCompare: false) != 0) & (Operators.CompareString(sPGM_aka_CoCDbs, sPGM_aka_CoC, TextCompare: false) != 0))
        {
            tRouting.mRerouteTask("01", "GA", "GA", sPGM_aka_CoCDbs, sPGM_aka_CoC);
            tRouting.mRerouteTask("14", "RR", "RR", sPGM_aka_CoCDbs, sPGM_aka_CoC);
        }
        if ((Operators.CompareString(Strings.Trim(sEngineeringManagerDbs), "", TextCompare: false) != 0) & (Operators.CompareString(Strings.Trim(sEngineeringManager), "", TextCompare: false) != 0) & (Operators.CompareString(sEngineeringManagerDbs, sEngineeringManager, TextCompare: false) != 0))
        {
            tRouting.mRerouteTask("10", "BA", "BA", sEngineeringManagerDbs, sEngineeringManager);
        }
        if (Operators.CompareString(Strings.Trim(sQuoteRespDueDate), Strings.Trim(sQuoteRespDueDateDbs), TextCompare: false) != 0)
        {
        }
        int prdLoop = 1;
        do
        {
            if (Operators.CompareString(sElecPRD[prdLoop], sElecPRDdbs[prdLoop], TextCompare: false) != 0)
            {
                if ((Operators.CompareString(sElecPRDdbs[prdLoop], "", TextCompare: false) == 0) & (Operators.CompareString(sStatus, "030200FR", TextCompare: false) >= 0))
                {
                    tRouting.mCreateTask("13", "PR", "PR", sElecPRD[prdLoop]);
                }
                else
                {
                    tRouting.mRerouteTask("13", "PR", "PR", sElecPRDdbs[prdLoop], sElecPRD[prdLoop]);
                }
            }
            prdLoop = checked(prdLoop + 1);
        }
        while (prdLoop <= 8);
        if (Operators.CompareString(sAMECoordinator, sAMECoordinatorDbs, TextCompare: false) != 0)
        {
            tRouting.mRerouteTask("10", "MM", "MM", sAMECoordinatorDbs, sAMECoordinator);
        }
        if (Operators.CompareString(sPurchasingCoordinator, sPurchasingCoordinatorDbs, TextCompare: false) != 0)
        {
            tRouting.mRerouteTask("10", "PP", "PP", sPurchasingCoordinatorDbs, sPurchasingCoordinator);
        }
        if (Operators.CompareString(sLeadManufacturing, sLeadManufacturingDbs, TextCompare: false) != 0)
        {
            tRouting.mRerouteTask("14", "MM", "MM", sLeadManufacturingDbs, sLeadManufacturing);
        }
        if (Operators.CompareString(sLeadPurchasing, sLeadPurchasingDbs, TextCompare: false) != 0)
        {
            tRouting.mRerouteTask("14", "PP", "PP", sLeadPurchasingDbs, sLeadPurchasing);
        }
        if (Operators.CompareString(sLeadValidation, sLeadValidationDbs, TextCompare: false) != 0)
        {
            tRouting.mRerouteTask("14", "FF", "FF", sLeadValidationDbs, sLeadValidation);
        }
        if (Operators.CompareString(Strings.Trim(sEngineerAssigned), Strings.Trim(sEngineerAssignedDbs), TextCompare: false) != 0)
        {
            tRouting.mRerouteTask("13", "CA", "CA", sEngineerAssignedDbs, sEngineerAssigned);
        }
        if (Operators.CompareString(Strings.Trim(sDueDateFromEngineering), Strings.Trim(sDueDateFromEngineeringDbs), TextCompare: false) != 0)
        {
        }
        return tRouting.countRerouted > 0;
    }

    public string getLooney()
    {
        string getLooney = modTRS.mTRSGetSingleDesc("CQRNOTE", "SDIRASST");
        if (Operators.CompareString(getLooney, "", TextCompare: false) == 0)
        {
            getLooney = "SDIRASST";
        }
        return getLooney;
    }

    public bool mIsDesignProposalOnly()
    {
        if ((Operators.CompareString(sDesignProposalInd, "1", TextCompare: false) == 0) & (Operators.CompareString(sProductionInd, "1", TextCompare: false) != 0) & (Operators.CompareString(sPrototypeInd, "1", TextCompare: false) != 0) & (Operators.CompareString(sPiecePriceInd, "1", TextCompare: false) != 0) & (Operators.CompareString(sToolingInd, "1", TextCompare: false) != 0) & (Operators.CompareString(sTimingPlanInd, "1", TextCompare: false) != 0))
        {
            return true;
        }
        return false;
    }

    public bool isGatewayWorthy()
    {
        if (tUserData.bIsCost)
        {
            return true;
        }
        if (tUserData.bIsCmgr)
        {
            return true;
        }
        if (tUserData.bIsMBnE)
        {
            return true;
        }
        if (tUserData.bIsCQGWNFY)
        {
            return true;
        }
        if (tUserData.bIsAMgr)
        {
            return true;
        }
        if (tUserData.bIsSDir)
        {
            return true;
        }
        if (tUserData.bIsPgm)
        {
            return true;
        }
        return false;
    }

    public bool isDirector()
    {
        int iIndex = 1;
        do
        {
            if (tUserData.bIsGateway[iIndex])
            {
                return true;
            }
            iIndex = checked(iIndex + 1);
        }
        while (iIndex <= 12);
        return false;
    }

    private bool mQafCheckRelease_(corePage tPage)
    {
        if (isLegacy())
        {
            return ((clsCQRLegacy)this).mQafCheckRelease(tPage);
        }
        return true;
    }

    private bool mCheckRelease_(corePage tPage, string sLink)
    {
        if (isLegacy())
        {
            return ((clsCQRLegacy)this).mCheckRelease(tPage, sLink);
        }
        //return ((clsCQRCurrent)this).mCheckRelease(tPage, sLink);
        return false;
    }

    public bool CanEditEngineeringCoordinator()
    {
        if (bSuperUser)
        {
            return true;
        }
        return (Operators.CompareString(sStatus, "020100FR", TextCompare: false) == 0) & tRouting.mHasCurrentTask("01", "GA", "GA", "*") & tUserData.bIsPgm;
    }

    public bool CanEditPSM()
    {
        if (bSuperUser)
        {
            return true;
        }
        return tRouting.mHasCurrentTask("08", "BA", "BA", "*") & (tUserData.bIsPsm | (Operators.CompareString(UserId, "ASEIBERT", TextCompare: false) == 0) | (Operators.CompareString(UserId, "AWAN", TextCompare: false) == 0));
    }

    public bool CanEditIHS()
    {
        if (bSuperUser)
        {
            return true;
        }
        return Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0;
    }

    public bool CanEditAntares()
    {
        return CanEditIHS() & !modCQR.isABS();
    }

    public bool CanReleaseHandover()
    {
        return tUserData.bIsAMgr & (tRouting.mHasCurrentTask("53", "AA", "AA", "*") | tRouting.mHasCurrentTask("28", "SA", "SA", "*"));
    }

    public bool CanApproveHandover()
    {
        int iLoop = 2;
        do
        {
            if (bHandoverApprovalRole[iLoop] & tRouting.mHasCurrentTask("53", sHandoverApprovalTask[iLoop], sHandoverApprovalTask[iLoop], "*"))
            {
                return true;
            }
            iLoop = checked(iLoop + 1);
        }
        while (iLoop <= 4);
        return false;
    }

    public bool CanCheckoutTasklist()
    {
        if (bSuperUser)
        {
            return true;
        }
        if (Operators.CompareString(sStatus, "040100FR", TextCompare: false) < 0)
        {
            return true;
        }
        if ((Operators.CompareString(sStatus, "040100FR", TextCompare: false) >= 0) & tUserData.bIsCost)
        {
            return true;
        }
        return false;
    }

    public bool CanAddTasklist()
    {
        if (bSuperUser)
        {
            return true;
        }
        if (bUserIsOrig & (Operators.CompareString(sStatus, "040100FR", TextCompare: false) < 0))
        {
            return true;
        }
        return false;
    }

    public bool CanEditTasklistAttachmentsXXX()
    {
        if (bSuperUser)
        {
            return true;
        }
        if ((Operators.CompareString(sStatus, "030200FR", TextCompare: false) == 0) & tUserData.bIsPrd)
        {
            return true;
        }
        if ((Operators.CompareString(sStatus, "030600FR", TextCompare: false) > 0) & !tUserData.bIsCost)
        {
            return false;
        }
        return true;
    }

    public bool CanEditCostEstimator()
    {
        if (bSuperUser)
        {
            return true;
        }
        return tRouting.mHasCurrentTask("07", "BA", "BA", "*") & tUserData.bIsMBnE;
    }

    public bool CanEditEngResourcesAndResponsibilities()
    {
        if (bSuperUser)
        {
            return true;
        }
        if ((Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0) & (Operators.CompareString(sQuoteType, "2", TextCompare: false) == 0))
        {
            return true;
        }
        return ((Operators.CompareString(sStatus, "020100FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "020150FR", TextCompare: false) == 0)) & tRouting.mHasCurrentTask("10", "BA", "BA", "*") & tUserData.bIsPetm;
    }

    public bool CanEditReviewRandD()
    {
        if (bSuperUser)
        {
            return true;
        }
        if (tRouting.mHasCurrentTask("14", "RR", "RR", "*") & tUserData.bIsPgm)
        {
            return true;
        }
        return false;
    }

    public bool CanEditLeadManufacturing()
    {
        if (bSuperUser)
        {
            return true;
        }
        if ((Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0) & (Operators.CompareString(sQuoteType, "2", TextCompare: false) == 0))
        {
            return true;
        }
        if (tRouting.mHasCurrentTask("10", "MM", "MM", "*") & tUserData.bIsAME_Coord)
        {
            return true;
        }
        if (tRouting.mHasCurrentTask("14", "MM", "MM", "*") & tUserData.bIsAIME)
        {
            return true;
        }
        return false;
    }

    public bool CanEditLeadPurchasing()
    {
        if (bSuperUser)
        {
            return true;
        }
        if ((Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0) & (Operators.CompareString(sQuoteType, "2", TextCompare: false) == 0))
        {
            return true;
        }
        if (tRouting.mHasCurrentTask("10", "PP", "PP", "*") & tUserData.bIsPUR_Coord)
        {
            return true;
        }
        if (tRouting.mHasCurrentTask("14", "PP", "PP", "*") & tUserData.bIsAIPR)
        {
            return true;
        }
        return false;
    }

    public bool CanEditEngineeringPackage()
    {
        if (bSuperUser)
        {
            return true;
        }
        if ((Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0) & (Operators.CompareString(sQuoteType, "2", TextCompare: false) == 0))
        {
            return true;
        }
        return (Operators.CompareString(sStatus, "030200FR", TextCompare: false) == 0) & tRouting.mHasCurrentTask("13", "CA", "CA", "*") & tUserData.bIsPrd;
    }

    public bool CanEditQuoteResponse()
    {
        if (bSuperUser)
        {
            return true;
        }
        return ((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "040125FR", TextCompare: false) == 0)) & tRouting.mHasCurrentTask("22", "BA", "BA", "*") & tUserData.bIsCost;
    }

    public bool CanEditSalesCloseout()
    {
        if (bSuperUser)
        {
            return true;
        }
        return tUserData.bIsAMgr & (Operators.CompareString(sStatus, "040300FR", TextCompare: false) == 0);
    }

    public string Gateway2Approvals()
    {
        string gwApprovals = "";
        if (Operators.CompareString(tGateway[2].sApprovalState[1], "1", TextCompare: false) == 0)
        {
            gwApprovals = Conversions.ToString(Operators.ConcatenateObject(gwApprovals, Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(gwApprovals, "", TextCompare: false) == 0, "", ", "), "CEO")));
        }
        if (Operators.CompareString(tGateway[2].sApprovalState[2], "1", TextCompare: false) == 0)
        {
            gwApprovals = Conversions.ToString(Operators.ConcatenateObject(gwApprovals, Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(gwApprovals, "", TextCompare: false) == 0, "", ", "), "CTO")));
        }
        if (Operators.CompareString(tGateway[2].sApprovalState[3], "1", TextCompare: false) == 0)
        {
            gwApprovals = Conversions.ToString(Operators.ConcatenateObject(gwApprovals, Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(gwApprovals, "", TextCompare: false) == 0, "", ", "), "CFO")));
        }
        if (Operators.CompareString(tGateway[2].sApprovalState[12], "1", TextCompare: false) == 0)
        {
            gwApprovals = Conversions.ToString(Operators.ConcatenateObject(gwApprovals, Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(gwApprovals, "", TextCompare: false) == 0, "", ", "), "CPO")));
        }
        if (Operators.CompareString(tGateway[2].sApprovalState[8], "1", TextCompare: false) == 0)
        {
            gwApprovals = Conversions.ToString(Operators.ConcatenateObject(gwApprovals, Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(gwApprovals, "", TextCompare: false) == 0, "", ", "), "Commercial Change Manager")));
        }
        if (Operators.CompareString(tGateway[2].sApprovalState[9], "1", TextCompare: false) == 0)
        {
            gwApprovals = Conversions.ToString(Operators.ConcatenateObject(gwApprovals, Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(gwApprovals, "", TextCompare: false) == 0, "", ", "), "Regional General Manager")));
        }
        if (Operators.CompareString(tGateway[2].sApprovalState[10], "1", TextCompare: false) == 0)
        {
            gwApprovals = Conversions.ToString(Operators.ConcatenateObject(gwApprovals, Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(gwApprovals, "", TextCompare: false) == 0, "", ", "), "Regional Sales Lead")));
        }
        if (Operators.CompareString(tGateway[2].sApprovalState[11], "1", TextCompare: false) == 0)
        {
            gwApprovals = Conversions.ToString(Operators.ConcatenateObject(gwApprovals, Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(gwApprovals, "", TextCompare: false) == 0, "", ", "), "BCS Management Team")));
        }
        return gwApprovals;
    }

    public bool isProceedCancel()
    {
        if ((tQuoteResponseFCM.getAttachCount() == 0) & (tQuoteResponseECR.getAttachCount() > 0))
        {
            return true;
        }
        if (Operators.CompareString(sQuoteType, "3", TextCompare: false) == 0)
        {
            return true;
        }
        return false;
    }

    public bool isInternalCQR()
    {
        if ((Operators.CompareString(sQuoteType, "2", TextCompare: false) == 0) | (Operators.CompareString(sQSQuoteStatus, "0", TextCompare: false) == 0))
        {
            return true;
        }
        return false;
    }

    public string CustomerTRSTable()
    {
        if (isLegacy())
        {
            return "Customer";
        }
        return "CQR_OEMGROUP";
    }

    //public void GetAwardStatus(ref string titleText, ref string awardStatus)
    //{
    //    if (isProceedCancel())
    //    {
    //        titleText = "Change Status";
    //        awardStatus = Conversions.ToString(Interaction.IIf(Operators.CompareString(sQSAwardStatus, "0", TextCompare: false) == 0, "Proceed", RuntimeHelpers.GetObjectValue(Interaction.IIf(Operators.CompareString(sQSAwardStatus, "1", TextCompare: false) == 0, "Cancel", RuntimeHelpers.GetObjectValue(Interaction.IIf(Operators.CompareString(sQSAwardStatus, "2", TextCompare: false) == 0, "Other", ""))))));
    //    }
    //    else
    //    {
    //        titleText = "Award Status";
    //        awardStatus = Conversions.ToString(Interaction.IIf(Operators.CompareString(sQSAwardStatus, "0", TextCompare: false) == 0, "Won", RuntimeHelpers.GetObjectValue(Interaction.IIf(Operators.CompareString(sQSAwardStatus, "1", TextCompare: false) == 0, "Lost", RuntimeHelpers.GetObjectValue(Interaction.IIf(Operators.CompareString(sQSAwardStatus, "2", TextCompare: false) == 0, "Other", ""))))));
    //    }
    //}

    public string DescriptionURL()
    {
        if (isLegacyDescriptionTab())
        {
            return "DescriptionLegacy.aspx";
        }
        return "Description.aspx";
    }

    public bool isLegacyDescriptionTab()
    {
        return false;
    }

    public bool isLegacy()
    {
        return Operators.CompareString(sOriginationDate, "20150614", TextCompare: false) < 0;
    }

    public bool NeedsReviewRnDCost()
    {
        return (Operators.CompareString(sQuoteType, "1", TextCompare: false) == 0) | (Operators.CompareString(sQuoteType, "3", TextCompare: false) == 0);
    }

    public bool NeedsOpeningMeetingDate()
    {
        return (Operators.CompareString(sQuoteType, "0", TextCompare: false) == 0) | (Operators.CompareString(sQuoteType, "1", TextCompare: false) == 0) | (Operators.CompareString(sQuoteType, "3", TextCompare: false) == 0);
    }

    public bool isIHSMode()
    {
        return Information.UBound(antaresRecords) < 0;
    }

    public bool isAntaresMode()
    {
        return Information.UBound(antaresRecords) >= 0;
    }
}
