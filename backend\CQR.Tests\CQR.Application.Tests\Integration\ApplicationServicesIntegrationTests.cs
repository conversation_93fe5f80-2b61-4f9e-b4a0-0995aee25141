using CQR.Application.Tests.TestBase;
using CQR.Application.Tests.TestHelpers;
using Xunit;

namespace CQR.Application.Tests.Integration;

public class ApplicationServicesIntegrationTests : TestBase
{
    [Fact]
    public async Task ApplicationServices_ShouldWorkTogether_ForCQRCollectionFlow()
    {
        // Arrange
        var queueKey = 12345;
        var testCQRHeader = MockDataBuilder.CreateTestCQRHeader(queueKey);
        var testRoles = MockDataBuilder.CreateTestUserRolesList();
        var testIHSFolders = MockDataBuilder.CreateTestIHSFolderList();
        var testRoutingTasks = MockDataBuilder.CreateTestRoutingTaskList();

        // Setup mocks
        MockUserRoleQueryRepository.Setup(x => x.GetRolesNames("IBERLANG"))
            .ReturnsAsync(testRoles);
        MockCQRHeaderRepository.Setup(x => x.GetByIdAsync(queueKey))
            .ReturnsAsync(testCQRHeader);
        MockCQRIHSFolderRepository.Setup(x => x.GetByQueueKeyAsync(queueKey))
            .ReturnsAsync(testIHSFolders);
        MockCqrHeaderQueryRepository.Setup(x => x.GetRoutingTaskByQueuekey(queueKey, "FR"))
            .ReturnsAsync(testRoutingTasks);
        MockUserRoleQueryRepository.Setup(x => x.GetManagerByRole(It.IsAny<string>()))
            .Returns("TestManager");

        var queryService = CreateCQRQueryService();
        MockQueryService.Setup(x => x.GetCollectionAsync(queueKey))
            .Returns(queryService.GetCollectionAsync(queueKey));

        var applicationServices = CreateApplicationServices();

        // Act
        var result = await applicationServices.QueryService.GetCollectionAsync(queueKey);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(testCQRHeader, result.iCQRHeader);
        Assert.NotNull(result.cUserRoles);
        Assert.True(result.cUserRoles.bIsAMgr); // AMGR role should be set
        Assert.True(result.cUserRoles.bIsCost); // CQRHRCOST role should be set
        Assert.True(result.cUserRoles.bIsFran); // FRAN role should be set
        Assert.True(result.cUserRoles.bIsPsr); // PSR role should be set
        Assert.Equal(testIHSFolders, result.ihsFolderRecords);
        Assert.Equal(testRoutingTasks, result.routingTasks);

        // Verify all expected calls were made
        MockUserRoleQueryRepository.Verify(x => x.GetRolesNames("IBERLANG"), Times.Once);
        MockCQRHeaderRepository.Verify(x => x.GetByIdAsync(queueKey), Times.Once);
        MockCQRIHSFolderRepository.Verify(x => x.GetByQueueKeyAsync(queueKey), Times.Once);
        MockCqrHeaderQueryRepository.Verify(x => x.GetRoutingTaskByQueuekey(queueKey, "FR"), Times.Once);
    }

    [Fact]
    public void ApplicationServices_ShouldProvideAllRequiredServices()
    {
        // Arrange & Act
        var applicationServices = CreateApplicationServices();

        // Assert - Verify all services are properly wired
        Assert.NotNull(applicationServices.CommandHandler);
        Assert.NotNull(applicationServices.QueryService);
        Assert.NotNull(applicationServices.ValidationService);
        Assert.NotNull(applicationServices.AttachmentService);
        Assert.NotNull(applicationServices.ProductService);
        Assert.NotNull(applicationServices.CustomService);
        Assert.NotNull(applicationServices.ExcelService);
        Assert.NotNull(applicationServices.DatabaseService);
        Assert.NotNull(applicationServices.SapRFCService);
        Assert.NotNull(applicationServices.SqlConnectionService);
        Assert.NotNull(applicationServices.SapConnectionService);
        Assert.NotNull(applicationServices.CurrentUserService);
        Assert.NotNull(applicationServices.Repositories);
    }

    [Fact]
    public async Task CQRQueryService_ShouldHandleComplexUserRoleMapping()
    {
        // Arrange
        var queueKey = 12345;
        var complexRolesList = new List<string> 
        { 
            "AMGR", "CQRHRCOST", "FRAN", "PSR", "PSM", "SDIR", "BUM" 
        };
        
        MockUserRoleQueryRepository.Setup(x => x.GetRolesNames("IBERLANG"))
            .ReturnsAsync(complexRolesList);
        MockCQRHeaderRepository.Setup(x => x.GetByIdAsync(queueKey))
            .ReturnsAsync(MockDataBuilder.CreateTestCQRHeader(queueKey));
        MockCQRIHSFolderRepository.Setup(x => x.GetByQueueKeyAsync(queueKey))
            .ReturnsAsync(new List<CQRLIB.CQR_IHSFolders.CQR_IHSFolder>());
        MockCqrHeaderQueryRepository.Setup(x => x.GetRoutingTaskByQueuekey(queueKey, "FR"))
            .ReturnsAsync(new List<CQR.Application.Dto.RoutingTaskDto>());
        MockUserRoleQueryRepository.Setup(x => x.GetManagerByRole(It.IsAny<string>()))
            .Returns("TestManager");

        var queryService = CreateCQRQueryService();

        // Act
        var result = await queryService.GetCollectionAsync(queueKey);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.cUserRoles);
        
        // Verify complex role mappings
        Assert.True(result.cUserRoles.bIsAMgr);      // AMGR
        Assert.True(result.cUserRoles.bIsCost);     // CQRHRCOST
        Assert.True(result.cUserRoles.bIsFran);     // FRAN
        Assert.True(result.cUserRoles.bIsPsr);      // PSR
        Assert.True(result.cUserRoles.bIsPsm);      // PSM
        Assert.True(result.cUserRoles.bIsSDir);     // SDIR
        Assert.True(result.cUserRoles.bIsBum);      // BUM
        
        // PSR should also set bIsPA
        Assert.True(result.cUserRoles.bIsPA);
        
        // PSM should also set bIsPsr (inheritance)
        Assert.True(result.cUserRoles.bIsPsr);
        
        // Verify bIsPetmOnly is false for all these roles
        Assert.False(result.cUserRoles.bIsPetmOnly);
    }
}