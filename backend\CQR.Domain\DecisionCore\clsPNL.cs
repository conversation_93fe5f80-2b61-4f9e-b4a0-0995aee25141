﻿using CQR.Domain.DecisionCore;

namespace CQR.Core.DecisionCore;

// CQR, Version=1.0.9223.23847, Culture=neutral, PublicKeyToken=null
// CQR.clsCQR.clsPNL
public class clsPNL
{
    public string sQAFKey;

    public string sQafName;

    public string sQafStatus;

    public string sQafViewable;

    public bool bCanAdvance;

    public bool bRelease;

    public bool bReplaced;

    public int iReplacedIndex;

    public string sStatusDate;

    public string sTargetSellPrice;

    public string sStartingSellPrice;

    public string sTotalVolume;

    public string sTotalSales;

    public string sAmortizedTools;

    public string sTRWPaidTools;

    public string sTRWCapital;

    public string sCustomerPaidTools;

    public string sPATPercent;

    public string sPATDollars;

    public string sIRRPercent;

    public string sIRRDollars;

    public string sMaterialPercent;

    public string sMaterialDollars;

    public string sSGA;

    public string sEngDollars;

    public string sEngPercent;

    public string sPayback;

    public string sAvgAnnualSPReduction;

    public string sEngSite;

    public string sFOBPoint;

    public bool bAttachmentFound;

    public bool bSave;

    public bool bCreateOnSave;

    public bool bDeleteOnSave;

    public string bChecked;

    public string sOldQafKey;

    public PNLLineItem[] tPNLLineItem;

    public bool bIsNewFormat;

    public clsPNL()
    {
        bChecked = "";
        tPNLLineItem = new PNLLineItem[0];
    }
}

