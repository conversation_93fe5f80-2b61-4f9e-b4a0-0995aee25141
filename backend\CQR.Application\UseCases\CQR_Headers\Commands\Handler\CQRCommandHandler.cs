﻿using CQR.Application.Dto;
using CQR.Application.Dto.CQRHead;
using CQR.Application.Helpers;
using CQR.Application.Repositories;
using CQR.Domain.Constants;
using CQR.Domain.CQRHeaders;
using CQR.Domain.Enum;
using CQR.Domain.Interfaces.Repository;
using Microsoft.Extensions.Logging;

namespace CQR.Application.UseCases.CQR_Headers.Commands.Handler;

public class CQRCommandHandler : ICQRCommandHandler 
{
    private readonly IRepositoryCollection _repos;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CQRCommandHandler> _logger;

    public CQRCommandHandler(IRepositoryCollection repos, IUnitOfWork unitOfWork, ILogger<CQRCommandHandler> logger)
    {
        _repos = repos;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async  Task<int> CreateNewCQR(CreateCQRCommand command)
    {
        var entity = new CQR_Header
        {
            OriginatorId = command.UserId,
            LastUpdatedBy = "Create"
        };

        //// 透過 Repository 執行資料邏輯
        //await _unitOfWork.CQRHeaderRepository.AddAsync(new CQR_Header
        //{
        //    OriginatorId = command.UserId,
        //    LastUpdatedBy = "Create"
        //});


        //await _unitOfWork.SaveChangesAsync();
        await _unitOfWork.CQRHeaderRepository.AddAsync(entity);
        await _unitOfWork.SaveChangesAsync();

        // 取得新建立的 QueueKey
        //var newQueueKey = await _unitOfWork.CQRHeaderRepository.GetQueueKeyByUserAsync(command.UserId);
        //return newQueueKey;
        // ✅ SaveChangesAsync() 後，EF Core 會自動填回主鍵
        return entity.QueueKey;

        //return -1;


        //////throw new NotImplementedException();
        //// 1. 刪除舊資料
        //var existing = await _repos.cqr.FindAllAsync(h => h.OriginatorId == command.UserId && h.LastUpdatedBy == "Create");
        //// Replace the following line:
        //var existing = await _repos.CQRHeaderRepository.FindAllAsync(h => h.OriginatorId == command.UserId && h.LastUpdatedBy == "Create");

        //// With this corrected line:
        //var existing = await _repos.CQRHeaderRepository.FindAsync(h => h.OriginatorId == command.UserId && h.LastUpdatedBy == "Create");
        //_context.CQR_Header.RemoveRange(existing);

        //// 2. 插入新資料
        //var header = new CQR_Header
        //{
        //    OriginatorId = command.UserId,
        //    LastUpdatedBy = "Create",
        //    Status = "010100FR", // status010100FR
        //    StatusDate = DateTime.Now,
        //    OriginationDate = DateTime.Now
        //};

        //_context.CQR_Header.Add(header);
        //await _context.SaveChangesAsync();

        //return header.QueueKey; // Identity Key
    }

    public async Task<CQRHeaderCollection> GetCQRCollection(SaveGDPIMPhaseCommand command)
    {
        // TODO: Implement GetCQRCollection logic
        throw new NotImplementedException("GetCQRCollection not implemented yet");
    }

    //private readonly ICQRCommandHandler _repository;

    //public async Task Handle(SaveGDPIMPhaseCommand command)
    //{
    //    var phase = await _repository.GetByQueueAndPhase(command.QueueKey, command.Phase);
    //    if (phase == null)
    //    {
    //        phase = GDPIMPhase.Create(command.QueueKey, command.Phase);
    //    }

    //    phase.Update(...); // 用 command 的欄位
    //    await _repository.SaveAsync(phase);
    //}

    public Task<bool> Handle(SaveGDPIMPhaseCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<bool> SaveCQRHeaderCollectionAsync(CQRHeaderCollection cqrHeaderData, string userId)
    {
        try
        {
            _logger.LogInformation("Processing SaveCQRHeaderCollection for User: {UserId}, QueueKey: {QueueKey}", 
                userId, cqrHeaderData.iQueueKey);

            // 1. Domain validation
            if (cqrHeaderData.iCQRHeader == null)
            {
                _logger.LogWarning("CQR Header is null for request from user: {UserId}", userId);
                return false;
            }

            // 2. Get existing entity or create new one
            var existingHeader = await _repos.CQRHeaderRepository.GetByIdAsync((int)cqrHeaderData.iQueueKey);
            
            if (existingHeader == null)
            {
                _logger.LogWarning("CQR Header not found with QueueKey: {QueueKey}", cqrHeaderData.iQueueKey);
                return false;
            }

            // 3. Update domain entity with business rules
            UpdateHeaderFromDto(existingHeader, cqrHeaderData, userId);

            // 4. Save changes using Unit of Work pattern
            await _unitOfWork.SaveChangesAsync();

            // 5. Handle related operations
            await SaveRelatedEntities(cqrHeaderData);

            _logger.LogInformation("Successfully saved CQR Header Collection for QueueKey: {QueueKey}", 
                cqrHeaderData.iQueueKey);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving CQR Header Collection for User: {UserId}, QueueKey: {QueueKey}", 
                userId, cqrHeaderData.iQueueKey);
            throw;
        }
    }

    private void UpdateHeaderFromDto(CQR_Header header, CQRHeaderCollection dto, string userId)
    {
        // Domain logic: Update entity properties
        header.LastUpdatedBy = userId;
        header.LastUpdatedDate = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
        
        // Map DTO properties to domain entity
        if (dto.iCQRHeader != null)
        {
            header.Status = dto.sStatus ?? header.Status;
            header.ProductDesc = dto.sProductDescription ?? header.ProductDesc;
            // Add other property mappings as needed
        }
    }

    private async Task SaveRelatedEntities(CQRHeaderCollection dto)
    {
        // TODO: Implement related entity saving as per business requirements
        // Example for GDPEP phases:
        if (dto.tGateway != null)
        {
            for (int i = 0; i < dto.tGateway.Length; i++)
            {
                if (dto.tGateway[i] != null)
                {
                    // Save GDPEP phase data
                    await SaveGDPEPPhase(dto.iQueueKey, i + 1, dto.tGateway[i]);
                }
            }
        }
    }

    private async Task SaveGDPEPPhase(long queueKey, int phase, GDPEP_APPROVAL approval)
    {
        // Implementation for saving GDPEP phase
        _logger.LogDebug("Saving GDPEP Phase {Phase} for QueueKey: {QueueKey}", phase, queueKey);
        
        // This would interact with your GDPEP repository when implemented
        await Task.CompletedTask; // Placeholder
    }
}
