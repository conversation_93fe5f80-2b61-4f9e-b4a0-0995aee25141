using CQR.Domain.IServices;
using Microsoft.AspNetCore.Mvc;

namespace MyWebAPI.Controllers;

[Route("api/[controller]")]
[ApiController]
public class MyController : ControllerBase
{
    private readonly ICustomService _customService;

    // ?��?構造函?�注??CustomService
    public MyController(ICustomService customService)
    {
        _customService = customService;
    }

    [HttpGet("message")]
    public IActionResult GetMessage()
    {
        var message = _customService.GetMessage();
        return Ok(message);
    }

    [HttpGet("Product")]
    public ActionResult<IEnumerable<string>> Get()
    {
        return new string[] { "Product1", "Product2", "Product3" };
    }
}
