﻿using CQR.Application.Dto;
using CQR.Application.Dto.RoleUser;
using CQR.Application.Repositories;
using CQR.Persistence.Query.Base;
using Dapper;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Query.Repositories;

public class UserQueryRepository : QueryBaseRepository, IUserQueryRepository
{
    public UserQueryRepository(IConfiguration configuration) : base(configuration)
    {
    }
    public async Task<List<UserRoleDto>> GetUsersWithRolesAsync(string[]? roles = null, string[]? locationCodes = null)
    {
        var rolesCsv = roles != null && roles.Length > 0
       ? string.Join(",", roles)
       : null;

        var locationCsv = locationCodes != null && locationCodes.Length > 0
            ? string.Join(",", locationCodes)
            : null;

        //AND(@Roles IS NULL OR UR.RoleCode IN(SELECT Value FROM @Roles))
        //            AND(@Locations IS NULL OR UP.LocationCode IN(SELECT Value FROM @Locations))
        var sql = @"
                SELECT DISTINCT 
                    UPPER(UP.UserId) AS UserId,
                    (UP.LastName + ', ' + UP.FirstName) AS FullName,
                    UR.RoleCode AS Role
                FROM 
                    USERPROF_UserProfileHeader UP
                INNER JOIN 
                    USERPROF_UserRoles UR ON UP.QueueKey = UR.QueueKey
                WHERE 
                   ISNULL(UP.Inactive, 0) = 0
                 
                ORDER BY 
                    FullName";

        var result = await _connection.QueryAsync<UserRoleDto>(sql,
       new
       {
           //Roles = rolesCsv,
           //Locations = locationCsv
       });

        return result.ToList();
    }

    public IEnumerable<UserFullNameDto> GetUsers(string role, string selectCode = "", string locationFilter = "", string topEntryText = "", string valueField = "UserId")
    {
        //throw new NotImplementedException();
        var sql = $@"
            SELECT DISTINCT 
                LastName + ', ' + FirstName AS FullName, 
                UPPER({valueField}) AS UserId 
            FROM USERPROF_UserProfileHeader UP
            {(string.IsNullOrWhiteSpace(role) ? "" : "INNER JOIN USERPROF_UserRoles UR ON UP.QueueKey = UR.QueueKey")}
            WHERE
                (ISNULL(Inactive, 0) = 0)
                {(string.IsNullOrWhiteSpace(role) ? "" : $" AND RoleCode IN ({FormatInClause(role)})")}
                {(string.IsNullOrWhiteSpace(locationFilter) ? "" : $" AND LocationCode IN ({FormatInClause(locationFilter)})")}
                {(string.IsNullOrWhiteSpace(selectCode) ? "" : $" OR UserId = @SelectCode")}
                {(topEntryText.StartsWith("OR") ? " " + topEntryText : "")}
            ORDER BY FullName";

        return _connection.Query<UserFullNameDto>(sql, new { SelectCode = selectCode });
    }

    private string FormatInClause(string commaSeparatedValues)
    {
        var values = commaSeparatedValues.Split(',').Select(v => $"'{v.Trim()}'");
        return string.Join(", ", values);
    }
}
