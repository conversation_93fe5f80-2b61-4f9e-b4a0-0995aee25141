﻿using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.VisualBasic;
using System.Text;


namespace CQR.Domain.DecisionCore;
public class modTRS
{
    public T_TRSCODE sT_TRSCODE;

    public T_TRSLIST sT_TRSLIST;

    public T_TRS_CACHE sT_TRS_CACHE;

    public const string TRS_CC_Customer = "CUSTOMER";

    public const string TRS_CC_Platform = "CONCPLAT";

    public const string TRS_CC_TRWPlantLocation = "CCTRWPLT";

    public const string TRS_CTL_TRWPlantLocation = "CLTRWPLT";

    public const string TRS_CC_CustomerPlantLocation = "CCCSTPLT";

    public const string TRS_CC_Closure = "CCCLOSE";

    public const string TRS_CC_NonConformanceStatus = "CCNONCNF";

    public const string TRS_CC_NonConformanceCommodityCode = "CC_CommodCode";

    public const string TRS_CC_Severity = "CCSEVTY";

    public const string TRS_CC_IssueNotifer = "CCISNTFR";

    public const string TRS_CC_ShipMethod = "CCSHPMET";

    public static T_TRS_CACHE tTRSCache;


    private readonly IHttpContextAccessor _httpContextAccessor;

    public modTRS(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    private static int mFaultTable(string asTableName, int aiListIndex)
    {
        //corePage corePage2 = new corePage();
        asTableName = Strings.UCase(asTableName);
        string text = "SELECT * FROM TRS_Header";
        text = text + " WHERE table_name = '" + asTableName + "'";
        //text = ((Operators.CompareString(asTableName, "NRECODES", TextCompare: false) != 0) ? (text + " ORDER BY _desc") : (text + " ORDER BY table_entry"));
        SqlDataReader rst = null;//corePage2.cnExecute(text);
        //SqlDataReader rst = corePage2.cnExecute(text);
        ref T_TRSLIST reference = ref tTRSCache.tTableList[aiListIndex];
        tTRSCache.tTableList[aiListIndex].sTableName = asTableName;
        reference.iEntryCount = 0;
        checked
        {
            while (rst.Read())
            {
                reference.iEntryCount++;
                ref T_TRSCODE[] tCodes = ref reference.tCodes;
                tCodes = (T_TRSCODE[])Utils.CopyArray(tCodes, new T_TRSCODE[reference.iEntryCount - 1 + 1]);
                //reference.tCodes[reference.iEntryCount - 1].sQueueKey = Strings.Trim(Conversions.ToString(rst["QueueKey"]));
                //reference.tCodes[reference.iEntryCount - 1].sCode = Strings.Trim(corePage2.rstString(ref rst, "table_entry"));
                //reference.tCodes[reference.iEntryCount - 1].sDesc = Strings.RTrim(corePage2.rstString(ref rst, "_desc"));
                //reference.tCodes[reference.iEntryCount - 1].sFormattedData = Strings.Trim(corePage2.rstString(ref rst, "format_data"));
                //reference.tCodes[reference.iEntryCount - 1].bInactive = corePage2.rstInt(ref rst, "Inactive") == 1;
                //reference.tCodes[reference.iEntryCount - 1].bRestricted = corePage2.rstInt(ref rst, "Restricted") == 1;
            }
            rst.Close();
            rst = null;
            //corePage2.cnClose();
            int result = default(int);
            return result;
        }
    }

    public static int mGetNameIndex(string asTableName)
    {
        string text = Strings.UCase(asTableName);
        //if (Operators.CompareString(HttpContext.Current.Request["NoCache"], "", TextCompare: false) != 0)
        //{
        //    tTRSCache.iTableCount = 0;
        //    tTRSCache.tTableList = new T_TRSLIST[0];
        //}
        checked
        {
            int num = tTRSCache.iTableCount - 1;
            int i;
            for (i = 0; i <= num; i++)
            {
                //if (Operators.CompareString(Strings.UCase(Strings.Trim(tTRSCache.tTableList[i].sTableName)), Strings.Trim(text), TextCompare: false) == 0)
                //{
                //    return i;
                //}
            }
            tTRSCache.iTableCount++;
            ref T_TRSLIST[] tTableList = ref tTRSCache.tTableList;
            tTableList = (T_TRSLIST[])Utils.CopyArray(tTableList, new T_TRSLIST[tTRSCache.iTableCount + 1]);
            mFaultTable(text, tTRSCache.iTableCount - 1);
            return i;
        }
    }

    public static int mRefreshTableCache(string asTableName)
    {
        bool flag = false;
        checked
        {
            int num = tTRSCache.iTableCount - 1;
            int i;
            for (i = 0; i <= num; i++)
            {
                //if (Operators.CompareString(tTRSCache.tTableList[i].sTableName, Strings.UCase(asTableName), TextCompare: false) == 0)
                if (string.Equals(tTRSCache.tTableList[i].sTableName, asTableName, StringComparison.OrdinalIgnoreCase))
                {
                    flag = true;
                    break;
                }
            }
            if (flag)
            {
                mFaultTable(asTableName, i);
            }
            int result = default(int);
            return result;
        }
    }

    public static int mTRSGetCodeArray(string asTableName, ref T_TRSCODE[] asArray, ref int aiCount)
    {
        int num = mGetNameIndex(asTableName);
        aiCount = tTRSCache.tTableList[num].iEntryCount;
        checked
        {
            asArray = new T_TRSCODE[aiCount + 1];
            int num2 = aiCount - 1;
            for (int i = 0; i <= num2; i++)
            {
                asArray[i] = tTRSCache.tTableList[num].tCodes[i];
            }
            int result = default(int);
            return result;
        }
    }

    //public static void mTRSPutAddLinkJavascript(HttpResponse tResponse)
    //{
    //    tResponse.Write("<script>");
    //    tResponse.Write("\tfunction trsAddCode(codeType, tableName, formatString, itemId, mode) {\r\n");
    //    tResponse.Write("\tvar win;\r\n");
    //    tResponse.Write("\tvar hght = (formatString == 'EDIT' ? 210 : 160);\r\n");
    //    tResponse.Write("\tvar table_entry = '';\r\n");
    //    tResponse.Write("\tif(mode == 'edit') {\r\n");
    //    tResponse.Write("\t  var cmb = document.getElementById(itemId);\r\n");
    //    tResponse.Write("\t  table_entry = cmb.options[cmb.selectedIndex].value;\r\n");
    //    tResponse.Write("\t  if(table_entry == '') {\r\n");
    //    tResponse.Write("\t    alert('Please selected an item to edit.');\r\n");
    //    tResponse.Write("\t    return;\r\n");
    //    tResponse.Write("\t  }\r\n");
    //    tResponse.Write("\t}\r\n");
    //    tResponse.Write("\twin = window.open('../_coreForm/frmTRSNewCode.aspx?CodeType=' + codeType + '&TableName=' + tableName + '&table_entry=' + table_entry + '&mode=' + mode + '&FormatString=' + formatString + '&ControlId=' + itemId,'_TRSNewCode','height='+hght+',width=480,status=yes,toolbar=no,scrollbars=yes,menubar=no,location=no,titlebar=no');\r\n");
    //    tResponse.Write("\twin.focus();\r\n");
    //    tResponse.Write("\t}\r\n");
    //    tResponse.Write("</script>");
    //}

    public static void mTRSPutAddLink(object phTRSHolder, string asHeader, string asTable, string asFormat, string asControlName, bool bEdit, string linkText = "add")
    {
        //HyperLink hyperLink = new HyperLink();
        //hyperLink.ID = "add" + asControlName;
        //hyperLink.Text = linkText;
        //hyperLink.NavigateUrl = "javascript:trsAddCode('" + asHeader + "', '" + asTable + "', '" + asFormat + "', '" + asControlName + "','add');";
        //object instance = NewLateBinding.LateGet(phTRSHolder, null, "Controls", new object[0], null, null, null);
        //object[] obj = new object[1] { hyperLink };
        //object[] array = obj;
        bool[] obj2 = new bool[1] { true };
        bool[] array2 = obj2;
        //NewLateBinding.LateCall(instance, null, "Add", obj, null, null, obj2, IgnoreReturn: true);
        if (array2[0])
        {
            //hyperLink = (HyperLink)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[0]), typeof(HyperLink));
        }
        if (bEdit)
        {
            //Label label = new Label();
            //label.Text = "&nbsp;&nbsp;";
            //object instance2 = NewLateBinding.LateGet(phTRSHolder, null, "Controls", new object[0], null, null, null);
            //object[] obj3 = new object[1] { label };
            //array = obj3;
            bool[] obj4 = new bool[1] { true };
            array2 = obj4;
            //NewLateBinding.LateCall(instance2, null, "Add", obj3, null, null, obj4, IgnoreReturn: true);
            if (array2[0])
            {
                //label = (Label)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[0]), typeof(Label));
            }
            //hyperLink = new HyperLink();
            //hyperLink.ID = "edit" + asControlName;
            //hyperLink.Text = "edit";
            //hyperLink.NavigateUrl = "javascript:trsAddCode('" + asHeader + "', '" + asTable + "', '" + asFormat + "', '" + asControlName + "','edit');";
            //NewLateBinding.LateCall(NewLateBinding.LateGet(phTRSHolder, null, "Controls", new object[0], null, null, null), null, "Add", array = new object[1] { hyperLink }, null, null, array2 = new bool[1] { true }, IgnoreReturn: true);
            if (array2[0])
            {
                //hyperLink = (HyperLink)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[0]), typeof(HyperLink));
            }
        }
    }

    public static int mTRSGetNextNumber(string asEntry, string asDefault, ref string asNewNumber)
    {
        //corePage corePage2 = new corePage();
        string text = "SELECT _desc FROM TRS_Header";
        text += " WHERE table_name = 'NUMBERS'";
        text = text + " AND table_entry = '" + Strings.Replace(asEntry, "'", "''") + "'";
        //string text2 = corePage2.cnExecuteForSingleValue(text);
        string text2 = "";
        //if (Operators.CompareString(text2, "", TextCompare: false) == 0)
        if (string.IsNullOrEmpty(text2))
        {
            //    text = "SELECT TOP 1 table_entry FROM TRS_Header";
            //    text = text + " WHERE table_name = '" + Strings.Replace(asEntry, "'", "''") + "'";
            //    text += " ORDER BY table_entry DESC";
            //    //text2 = corePage2.cnExecuteForSingleValue(text);
            //    text2 = !string.IsNullOrEmpty(text2)
            //? (double.TryParse(text2, out double value) ? (value + 1.0).ToString() : text2)
            //: asDefault;
            //text2 = ((Operators.CompareString(text2, "", TextCompare: false) != 0) ? (Conversion.Val(text2) + 1.0).ToString() : asDefault);
            //text2 = Strings.Right("0000000000000000000000000000" + text2, int.Parse(Interaction.IIf(Strings.Len(text2) > Strings.Len(asDefault), Strings.Len(text2), Strings.Len(asDefault))));


            // 填充 0 到足夠長度
            string zeros = "0000000000000000000000000000";
            // 決定最終字串長度 (取 text2 和 asDefault 中較長者)
            //int finalLength = Math.Max(text2?.Length ?? 0, asDefault?.Length ?? 0);
            //// 將 zeros + text2 從右側截取 finalLength 個字符
            //text2 = (zeros + text2).Substring(Math.Max(0, (zeros + text2).Length - finalLength));

            text = "INSERT INTO TRS_Header (table_name, table_entry, _desc, child, format_data) VALUES (";
            text += "'NUMBERS', ";
            text = text + "'" + asEntry + "', ";
            //text = text + "'" + text2 + "', ";
            text += "'','')";
        }
        else
        {
            text2 = (Conversion.Val(text2) + 1.0).ToString();
            //text2 = Strings.Right("0000000000000000000000000000" + text2, int.Parse(Interaction.IIf(Strings.Len(text2) > Strings.Len(asDefault), Strings.Len(text2), Strings.Len(asDefault))));


            int maxLength = Math.Max(
                string.IsNullOrEmpty(text2) ? 0 : text2.Length,
                string.IsNullOrEmpty(asDefault) ? 0 : asDefault.Length
            );

            // 填充 0 並從右側取字符
            string paddedText = "0000000000000000000000000000" + text2;
            text2 = paddedText.Substring(paddedText.Length - maxLength);
            text = "UPDATE TRS_Header SET _desc = '" + text2 + "' WHERE table_name = 'NUMBERS' AND table_entry = '" + Strings.Replace(asEntry, "'", "''") + "'";
        }
        //corePage2.cnExecuteNonQuery(text);
        asNewNumber = text2;
        return -1;
    }

    public static void PopulateDistinctTRS(string trsTable, string sqlTableName, string fieldToUseForJoin, string asSelectCode, bool addBlank = true)
    //public static void PopulateDistinctTRS(DropDownList dd, string trsTable, string sqlTableName, string fieldToUseForJoin, string asSelectCode, bool addBlank = true)
    {
        //AddTip(dd, "MAIN TABLE: " + trsTable);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.AppendLine("SELECT DISTINCT ISNULL(_desc," + fieldToUseForJoin + ") AS FieldText, ISNULL(table_entry," + fieldToUseForJoin + ") AS FieldValue");
        stringBuilder.AppendLine(" FROM " + sqlTableName);
        stringBuilder.AppendLine(" LEFT JOIN TRS_Header ON table_name='" + trsTable + "' AND table_entry=" + fieldToUseForJoin);
        stringBuilder.AppendLine(" WHERE ISNULL(ISNULL(_desc," + fieldToUseForJoin + "),'') <> ''");
        stringBuilder.AppendLine(" ORDER BY ISNULL(_desc," + fieldToUseForJoin + ")");
        //corePage obj = new corePage();
        ////SqlDataReader sqlDataReader = obj.cnExecute(stringBuilder.ToString());
        //if (sqlDataReader != null)
        //{
        //    //dd.DataSource = sqlDataReader;
        //    //dd.DataValueField = "FieldValue";
        //    //dd.DataTextField = "FieldText";
        //    //dd.DataBind();
        //    if (addBlank)
        //    {
        //        //dd.Items.Insert(0, new ListItem(""));
        //    }
        //    //sqlDataReader.Close();
        //}
        //obj.cnClose();
        //obj.cnClose2();
        //checked
        //{
        //    int num = dd.Items.Count - 1;
        //    int i;
        //    for (i = 0; i <= num; i++)
        //    {
        //        if (Operators.CompareString(dd.Items[i].Value, asSelectCode, TextCompare: false) == 0)
        //        {
        //            dd.SelectedIndex = i;
        //            break;
        //        }
        //    }
        //    if ((i == dd.Items.Count) & (Operators.CompareString(asSelectCode, "", TextCompare: false) != 0) & (Operators.CompareString(asSelectCode, "0", TextCompare: false) != 0))
        //    {
        //        dd.Items.Insert(1, asSelectCode);
        //        dd.SelectedIndex = 1;
        //    }
        //}
    }

    public static void mTRSPopulateControl(string asTbl, string asSelectCode, bool abClearBox, bool abItemData = false, string asField = "_desc", string asTopEntryText = "", string asOrderBy = "", int abForceEntryNumeric = 0, string asEntryField = "table_entry")
    //public static void mTRSPopulateControl(string asTbl, DropDownList acControl, string asSelectCode, bool abClearBox, bool abItemData = false, string asField = "_desc", string asTopEntryText = "", string asOrderBy = "", int abForceEntryNumeric = 0, string asEntryField = "table_entry")
    {
        if (abClearBox)
        {
            //acControl.Items.Clear();
        }
        //corePage corePage2 = new corePage();
        string text = asEntryField + " AS Entry";
        if (abForceEntryNumeric != 0)
        {
            text = "CAST(table_entry AS INT) AS Entry";
        }
        string text2 = "select " + text + "," + asField + " AS Description, format_data, inactive FROM TRS_Header ";
        text2 = text2 + " WHERE table_name LIKE '" + asTbl + "'";
        //if (Operators.CompareString(asTopEntryText, "LOADINACTIVE", TextCompare: false) == 0)
        if (asTopEntryText == "LOADINACTIVE")

        {
            asTopEntryText = "";
        }
        else
        {
            text2 = text2 + " AND (ISNULL(inactive,0) <> '1' or table_entry ='" + asSelectCode + " ') ";
        }
        string orderBy = string.IsNullOrEmpty(asOrderBy) ? asField : asOrderBy;
        text2 += " ORDER BY " + orderBy;
        //SqlDataReader rst = corePage2.cnExecute(text2);

        //text2 = Conversions.ToString(Operators.ConcatenateObject(text2, Operators.ConcatenateObject(" ORDER BY ", Interaction.IIf(Operators.CompareString(asOrderBy, "", TextCompare: false) == 0, asField, asOrderBy))));
        //SqlDataReader rst = corePage2.cnExecute(text2);
        //while (rst.Read())
        //{
            ////ListItem listItem = new ListItem(corePage2.rstString(ref rst, "Description"), corePage2.rstString(ref rst, "Entry"));
            //if (abItemData)
            //{
            //    listItem.Attributes.Add("format_data", corePage2.rstString(ref rst, "format_data"));
            //}
            //if (corePage2.rstInt(ref rst, "inactive") == 1)
            //{
            //    listItem.Attributes.Add("inactive", Conversions.ToString(1));
            //}
            //acControl.Items.Add(listItem);
        //}
        //if (Operators.CompareString(asTopEntryText, "@", TextCompare: false) != 0)
        //{
        //    acControl.Items.Insert(0, new ListItem(asTopEntryText, ""));
        //}
        //rst.Close();
        //corePage2.cnClose();
        checked
        {
            //int num = acControl.Items.Count - 1;
            //int i;
            //for (i = 0; i <= num; i++)
            //{
            //    if (Operators.CompareString(acControl.Items[i].Value, asSelectCode, TextCompare: false) == 0)
            //    {
            //        acControl.SelectedIndex = i;
            //        break;
            //    }
            //}
            //if ((i == acControl.Items.Count) & (Operators.CompareString(asSelectCode, "", TextCompare: false) != 0) & (Operators.CompareString(asSelectCode, "0", TextCompare: false) != 0))
            //{
            //    acControl.Items.Insert(1, asSelectCode);
            //    acControl.SelectedIndex = 1;
            //}
            //AddTip(acControl, "TRS: " + asTbl);
        }
    }

    //public static void AddTip(object acControl, string asTbl)
    //{
    //    if (acControl != null && NewLateBinding.LateGet(acControl, null, "page", new object[0], null, null, null) != null && Conversions.ToBoolean(Operators.OrObject(Operators.OrObject(Operators.CompareObjectNotEqual(NewLateBinding.LateGet(NewLateBinding.LateGet(acControl, null, "Page", new object[0], null, null, null), null, "Request", new object[1] { "spm" }, null, null, null), "", TextCompare: false), Operators.CompareObjectEqual(HttpContext.Current.Session["spm"], "1", TextCompare: false)), isDecisionDesignUser(Conversions.ToString(HttpContext.Current.Session["userActual"])))))
    //    {
    //        object instance = NewLateBinding.LateGet(acControl, null, "Attributes", new object[0], null, null, null);
    //        object[] obj = new object[2] { "title", asTbl };
    //        object[] array = obj;
    //        bool[] obj2 = new bool[2] { false, true };
    //        bool[] array2 = obj2;
    //        NewLateBinding.LateCall(instance, null, "Add", obj, null, null, obj2, IgnoreReturn: true);
    //        if (array2[1])
    //        {
    //            asTbl = (string)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[1]), typeof(string));
    //        }
    //        HttpContext.Current.Session["spm"] = "1";
    //    }
    //}

    //public static void mTRSPopulateControl(string asTbl, DropDownList acControl, string asSelectCode = "", string asTopEntryText = "", bool loadInactive = false)
    public static void mTRSPopulateControl(string asTbl, string asSelectCode = "", string asTopEntryText = "", bool loadInactive = false)
    {
        //int num = mGetNameIndex(Conversions.ToString(Interaction.IIf(Operators.CompareString(Strings.Left(asTbl, 1), "*", TextCompare: false) == 0, Strings.Mid(asTbl, 2), asTbl)));
        string tableName = asTbl.StartsWith("*") ? asTbl.Substring(1) : asTbl;
        int num = mGetNameIndex(tableName);

        int num2 = -1;

        //if (Operators.CompareString(asTopEntryText, "@", TextCompare: false) != 0)
        //{
        //    acControl.Items.Insert(0, new ListItem(asTopEntryText, ""));
        //    num2 = 0;
        //}
        checked
        {
            int num3 = tTRSCache.tTableList[num].iEntryCount - 1;
            for (int i = 0; i <= num3; i++)
            {
                ref T_TRSCODE reference = ref tTRSCache.tTableList[num].tCodes[i];
                //if (unchecked(!reference.bInactive || loadInactive) | (Operators.CompareString(asSelectCode, reference.sCode, TextCompare: false) == 0))
                //{
                //    string sDesc = reference.sDesc;
                //    ListItem listItem = new ListItem(sDesc, reference.sCode);
                //    if (reference.bInactive)
                //    {
                //        listItem.Attributes.Add("inactive", "1");
                //    }
                //    acControl.Items.Add(listItem);
                //    if (Operators.CompareString(asSelectCode, reference.sCode, TextCompare: false) == 0)
                //    {
                //        acControl.SelectedIndex = acControl.Items.Count - 1;
                //    }
                //}
            }
            //if (!string.IsNullOrEmpty(asSelectCode) && acControl.SelectedIndex == -1)
            //{
            //    if (string.IsNullOrEmpty(asSelectCode))
            //    {
            //        return;
            //    }

            //    if (asTbl == "CUSTOMER" || asTbl == "MFGREPS" || asTbl == "FOBPOINT" || asTbl == "ENGSITE" || asTbl == "MFGSITE")
            //    {
            //        mRefreshTableCache(asTbl);
            //        mTRSPopulateControl("*" + asTbl, acControl, asSelectCode);
            //    }
            //}

            //if ((Operators.CompareString(asSelectCode, "", TextCompare: false) != 0) & (acControl.SelectedIndex == -1))
            //{
            //    if (Operators.CompareString(asSelectCode, "", TextCompare: false) == 0)
            //    {
            //        return;
            //    }
            //    if ((Operators.CompareString(asTbl, "CUSTOMER", TextCompare: false) == 0) | (Operators.CompareString(asTbl, "MFGREPS", TextCompare: false) == 0) | (Operators.CompareString(asTbl, "FOBPOINT", TextCompare: false) == 0) | (Operators.CompareString(asTbl, "ENGSITE", TextCompare: false) == 0) | (Operators.CompareString(asTbl, "MFGSITE", TextCompare: false) == 0))
            //    {
            //        mRefreshTableCache(asTbl);
            //        mTRSPopulateControl("*" + asTbl, acControl, asSelectCode);
            //    }
            //}
            //if ((Operators.CompareString(asSelectCode, "", TextCompare: false) != 0) & (acControl.SelectedIndex == num2))
            //{
            //    acControl.Items.Add(new ListItem(asSelectCode, asSelectCode));
            //    acControl.SelectedIndex = acControl.Items.Count - 1;
            //}
            //AddTip(acControl, "TRS: " + asTbl);
        }
    }

    public static T_TRSCODE mTRSGetCodeInfo(string asTbl, string asEntry)
    {
        T_TRSCODE result = default(T_TRSCODE);
        checked
        {
            //if (Operators.CompareString(Strings.Trim(asEntry), "", TextCompare: false) != 0)
            if (!string.IsNullOrEmpty(asEntry.Trim()))

            {
                string tableName = asTbl.StartsWith("*") ? asTbl.Substring(1) : asTbl;
                int num = mGetNameIndex(tableName);

                //int num = mGetNameIndex(Conversions.ToString(Interaction.IIf(Operators.CompareString(Strings.Left(asTbl, 1), "*", TextCompare: false) == 0, Strings.Mid(asTbl, 2), asTbl)));
                int num2 = tTRSCache.tTableList[num].iEntryCount - 1;
                int num3 = 0;
                while (true)
                {
                    if (num3 <= num2)
                    {
                        if (asEntry.ToLower() == tTRSCache.tTableList[num].tCodes[num3].sCode.ToLower())

                        //if (Operators.CompareString(Strings.LCase(asEntry), Strings.LCase(tTRSCache.tTableList[num].tCodes[num3].sCode), TextCompare: false) == 0)
                        {
                            result = tTRSCache.tTableList[num].tCodes[num3];
                            break;
                        }
                        num3++;
                        continue;
                    }
                    if (asTbl == "CUSTOMER" || asTbl == "MFGREPS" || asTbl == "FOBPOINT" || asTbl == "ENGSITE" || asTbl == "MFGSITE")
                    //if ((Operators.CompareString(asTbl, "CUSTOMER", TextCompare: false) == 0) | (Operators.CompareString(asTbl, "MFGREPS", TextCompare: false) == 0) | (Operators.CompareString(asTbl, "FOBPOINT", TextCompare: false) == 0) | (Operators.CompareString(asTbl, "ENGSITE", TextCompare: false) == 0) | (Operators.CompareString(asTbl, "MFGSITE", TextCompare: false) == 0))
                    {
                        mRefreshTableCache(asTbl);
                        result = mTRSGetCodeInfo("*" + asTbl, asEntry);
                    }
                    break;
                }
            }
            return result;
        }
    }

    public static string mTRSGetSingleDesc(string asTbl, string asEntry)
    {
        //string text = mTRSGetCodeInfo(asTbl, asEntry).sDesc ?? "";
        //if (Operators.CompareString(text, "", TextCompare: false) == 0)
        //{
        //    text = asEntry;
        //}
        //return text;
        return "";
    }


    public static string mTRSGetFormattedData(string asTbl, string asEntry)
    {
        return mTRSGetCodeInfo(asTbl, asEntry).sFormattedData;
    }

    public static void addDeputySelector(string tOwner,int headerColor)
    //public static void addDeputySelector(string tOwner, PlaceHolder phNav, int headerColor)
    {
        //Label label = new Label();
        //label.Text = "<BR>";
        //phNav.Controls.Add(label);
        //HyperLink hyperLink = new HyperLink();
        //string text = Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(" WHERE UserId='", HttpContext.Current.Session["UserImpersonate"]), "'"));
        //text += "   AND getDate() BETWEEN StartDate AND DATEADD(day,1,ExpirationDate)";
        //text += "   AND IsDeleted=0";
        //if (Operators.CompareString(tOwner.cnExecuteForSingleValue("SELECT DeputyId FROM USERPROF_Deputy " + text), "", TextCompare: false) == 0)
        //{
        //    //hyperLink.Text = "Assign&nbsp;Deputy";
        //    //hyperLink.ForeColor = Color.FromArgb(headerColor);
        //}
        //else
        //{
        //    //hyperLink.Text = "Change/Remove&nbsp;Deputy";
        //    //hyperLink.ForeColor = Color.Red;
        //}
        //hyperLink.ID = "hrefDeputy";
        //hyperLink.NavigateUrl = "javascript:goDeputy();";
        //hyperLink.Font.Bold = true;
        //hyperLink.Style.Add("padding-right", "10px");
        //phNav.Controls.Add(hyperLink);
        //Label label2 = new Label();
        //label2.Text += "<BR>\r\n";
        //label2.Text += "<script>\r\n";
        //label2.Text += "function goDeputy() {\r\n";
        //label2.Text += "\twindow.open('../_coreform/frmSelectDeputy.aspx','_blank','height=350,width=560,status=0,toolbar=0,scrollbars=yes,menubar=yes,location=0,titlebar=0');\r\n";
        //label2.Text += "}\r\n";
        //label2.Text += "</script>\r\n";
        //phNav.Controls.Add(label2);
    }

    //public static void addDeputyNavigation(string tOwner, PlaceHolder phNav, int headerColor, string appCode)
    //{
    //    string text = Conversions.ToString(HttpContext.Current.Session["UserImpersonate"]);
    //    if (Operators.CompareString(text, "", TextCompare: false) == 0)
    //    {
    //        return;
    //    }
    //    Table atTable = new Table();
    //    coreTable coreTable2 = new coreTable(ref atTable, bColorsOff: true);
    //    coreTable2.tTable.CellPadding = 0;
    //    coreTable2.tTable.CellSpacing = 0;
    //    coreTable2.tTable.BackColor = Color.FromArgb(10066329);
    //    coreTable2.bColorOff = true;
    //    Label label = new Label();
    //    label.Text = "<BR><BR>Perform Work As:";
    //    label.Font.Bold = true;
    //    label.ForeColor = Color.FromArgb(headerColor);
    //    coreTable2.AddRow();
    //    coreTable2.AddCell().Controls.Add(label);
    //    DropDownList dropDownList = new DropDownList();
    //    string text2 = "SELECT UP.* FROM USERPROF_Deputy  UD";
    //    text2 += " LEFT JOIN USERPROF_UserProfileHeader UP ON UD.UserId=UP.UserId ";
    //    text2 = text2 + " WHERE DeputyId='" + text + "'";
    //    text2 += "   AND getDate() BETWEEN StartDate AND DATEADD(day,1,ExpirationDate)";
    //    text2 += "   AND IsDeleted=0";
    //    text2 = text2 + "   AND Application='" + appCode + "'";
    //    text2 += " UNION ";
    //    text2 = text2 + " SELECT * FROM USERPROF_UserProfileHeader WHERE UserId='" + text + "'";
    //    text2 += " ORDER BY LastName, FirstName";
    //    dropDownList.ID = "dropDeputy";
    //    dropDownList.Width = Unit.Pixel(128);
    //    dropDownList.BackColor = Color.FromArgb(10066329);
    //    dropDownList.ForeColor = Color.White;
    //    dropDownList.Attributes.Add("onchange", "javascript:goImpersonate()");
    //    SqlDataReader sqlDataReader = tOwner.cnExecute(text2);
    //    while (sqlDataReader.Read())
    //    {
    //        dropDownList.Items.Add(new ListItem(tOwner.rstString(sqlDataReader, "LastName") + ", " + tOwner.rstString(sqlDataReader, "FirstName"), tOwner.rstString(sqlDataReader, "UserId")));
    //    }
    //    sqlDataReader.Close();
    //    if (dropDownList.Items.Count != 1)
    //    {
    //        try
    //        {
    //            dropDownList.SelectedValue = Conversions.ToString(HttpContext.Current.Session["UserImpersonate"]);
    //        }
    //        catch (Exception ex)
    //        {
    //            ProjectData.SetProjectError(ex);
    //            Exception ex2 = ex;
    //            dropDownList.SelectedValue = text;
    //            ProjectData.ClearProjectError();
    //        }
    //        coreTable2.AddRow();
    //        coreTable2.AddCell().Controls.Add(dropDownList);
    //        Label label2 = new Label();
    //        string text3 = "<BR>\r\n";
    //        text3 += "<script>\r\n";
    //        text3 += "function goImpersonate() {\r\n";
    //        text3 += "\t$('#txtImpersonate').val($('#navColumn_dropDeputy').val());\r\n";
    //        text3 += "  document.getElementById('Form1').submit();\r\n";
    //        text3 += "}\r\n";
    //        text3 += "</script>\r\n";
    //        label2.Text = text3;
    //        phNav.Controls.Add(coreTable2.tTable);
    //        phNav.Controls.Add(label2);
    //        Label label3 = new Label();
    //        label3.Text = "<BR>";
    //        phNav.Controls.Add(label3);
    //    }
    //}

    //public static void addTRWNavigation(coreUserControl tOwner, PlaceHolder phNav, int headerColor)
    //{
    //    Table atTable = new Table();
    //    coreTable coreTable2 = new coreTable(ref atTable, bColorsOff: true);
    //    coreTable2.tTable.CellPadding = 0;
    //    coreTable2.tTable.CellSpacing = 0;
    //    coreTable2.tTable.BackColor = Color.FromArgb(10066329);
    //    coreTable2.bColorOff = true;
    //    Label label = new Label();
    //    label.Text = "<BR><BR>Open Application:";
    //    label.Font.Bold = true;
    //    label.ForeColor = Color.FromArgb(headerColor);
    //    coreTable2.AddRow();
    //    coreTable2.AddCell().Controls.Add(label);
    //    DropDownList dropDownList = new DropDownList();
    //    string sSQL = "SELECT _desc FROM TRS_Header WHERE table_name = 'Navigate' AND ISNULL(inactive,0) <> '1' ORDER BY table_entry";
    //    dropDownList.ID = "dropNavigate";
    //    dropDownList.Width = Unit.Pixel(128);
    //    dropDownList.BackColor = Color.FromArgb(10066329);
    //    dropDownList.ForeColor = Color.White;
    //    dropDownList.Attributes.Add("onchange", "javascript:goNavigate()");
    //    SqlDataReader sqlDataReader = tOwner.cnExecute(sSQL);
    //    while (sqlDataReader.Read())
    //    {
    //        string text = tOwner.rstString(sqlDataReader, "_desc");
    //        if (Strings.InStr(text, "~~") > 0)
    //        {
    //            string text2 = Strings.LCase(Strings.Mid(text, checked(Strings.InStrRev(text, "/") + 1)));
    //            if ((Operators.CompareString(text2, "", TextCompare: false) == 0) | (Strings.InStr(Strings.LCase(tOwner.Request.ServerVariables["PATH_INFO"]), text2) == 0))
    //            {
    //                string[] array = Strings.Split(text, "~~");
    //                dropDownList.Items.Add(new ListItem("--" + array[0], array[1]));
    //            }
    //        }
    //        else
    //        {
    //            dropDownList.Items.Add(new ListItem(text, ""));
    //        }
    //    }
    //    sqlDataReader.Close();
    //    tOwner.cnClose();
    //    coreTable2.AddRow();
    //    coreTable2.AddCell().Controls.Add(dropDownList);
    //    Label label2 = new Label();
    //    string text3 = "<BR>\r\n";
    //    text3 += "<script>\r\n";
    //    text3 += "function goNavigate() {\r\n";
    //    text3 += "\tvar tObj = document.getElementById('navColumn:dropNavigate');\r\n";
    //    text3 += "\tif(tObj == null)\r\n";
    //    text3 += "\t  tObj = document.getElementById('navColumn_dropNavigate');\r\n";
    //    text3 += "\tif(tObj.value == '')\r\n";
    //    text3 += "\t\t{\r\n";
    //    text3 += "\t\talert('Descriptive headers can not be Opened.');\r\n";
    //    text3 += "\t\treturn;\r\n";
    //    text3 += "\t\t}\r\n";
    //    text3 += "\tvar szWindowName = '_' + tObj.options[tObj.selectedIndex].innerHTML.substring(2,1000);\r\n";
    //    text3 += "\twhile (true)\r\n";
    //    text3 += "\t\t{\r\n";
    //    text3 += "\t\t\tvar start = szWindowName.indexOf(' ',1);\r\n";
    //    text3 += "\t\t\tif(start<=0) break;\r\n";
    //    text3 += "\t\t\tszWindowName = szWindowName.substring(0,start) + '_' + szWindowName.substring(start+1,szWindowName.length);\r\n";
    //    text3 += "\t\t}\r\n";
    //    text3 += "  var tWnd = window.open(tObj.value, szWindowName);\r\n";
    //    text3 += "\ttWnd.focus();\r\n";
    //    text3 += "  tObj.selectedIndex = 0;\r\n";
    //    text3 += "}\r\n";
    //    text3 += "</script>\r\n";
    //    label2.Text = text3;
    //    phNav.Controls.Add(coreTable2.tTable);
    //    phNav.Controls.Add(label2);
    //}

    public static bool RequiredLogComment(string sUserId, string sLogComment)
    {
        bool result = false;
        //if (Operators.CompareString(Strings.LCase(sUserId), "super", TextCompare: false) == 0 && Operators.CompareString(sLogComment, "", TextCompare: false) == 0)
        if (sUserId.ToLower() == "super" && string.IsNullOrEmpty(sLogComment))

        {
            result = true;
        }
        return result;
    }

    public static string getButtonType(string sMode)
    {
        string result = sMode;
        switch (sMode)
        {
            case "saveLink":
                result = "Save (no Exit)";
                break;
            case "saveexitLink":
                result = "Save";
                break;
            case "releaseLink":
                result = "Release";
                break;
            case "approveLink":
                result = "Approve";
                break;
            case "rejectLink":
            case "rejectNoCommentLink":
                result = "Reject";
                break;
            case "terminateLink":
                result = "Terminate";
                break;
            case "NCTVoidLink":
                result = "NCT Void";
                break;
            case "rereleasecarLink":
                result = "ReRelease CAR";
                break;
            case "NoFurtherActionLink":
                result = "No Further Action";
                break;
            default:
                //if (Operators.CompareString(Strings.Left(sMode, 10), "saveAttach", TextCompare: false) == 0)
                //{
                //    result = "Save (Attach)";
                //}
                break;
        }
        return result;
    }

    public static void logAddRecord(corePage tPage, string sApp, string sUserId, string sButton, string sQueueKey, bool bIsSuperUser, string sOtherInfo, string sSUComment, string FolderStatus)
    {
        string text = "INSERT INTO LOG_Modification (";
        text += "Application, SuperUser, Button, UserActual, UserImpersonate, ";
        text += "FolderQueueKey, OtherInfo, UserComment, FolderStatus ";
        text += ") VALUES (";
        text = text + "'" + sApp + "',";
        //text = Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(text + "'", Interaction.IIf(bIsSuperUser, "1", "")), "',"));
        text = text + "'" + (bIsSuperUser ? "1" : "") + "',";

        text = text + "'" + sButton + "',";
        //text = Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(text + "'", HttpContext.Current.Session["UserActual"]), "',"));
        //text = text + "'" + ( HttpContext.Current.Session["UserActual"] ?? "") + "',";

        text = text + "'" + sUserId + "',";
        text = text +(Conversion.Val(sQueueKey)).ToString() + ",";
        text = text + "'" + Strings.Replace(sOtherInfo, "'", "''") + "',";
        text = text + "'" + Strings.Replace(sSUComment, "'", "''") + "',";
        text = text + "'" + FolderStatus + "'";
        text += ")";
        tPage.cnExecuteNonQuery(text);
    }

    public static bool isDecisionDesignUser(string sLogOnUser)
    {
        bool result = false;
        //if (Operators.CompareString(Strings.LCase(Strings.Left(sLogOnUser, 14)), "decisiondesign", TextCompare: false) == 0)
        //{
        //    result = true;
        //}
        //else if (((uint)(Strings.InStr(Strings.LCase(sLogOnUser), "ddc") | Strings.InStr(Strings.LCase(sLogOnUser), "mitchene")) | (0u - ((Operators.CompareString(Strings.LCase(sLogOnUser), "frank", TextCompare: false) == 0) ? 1u : 0u)) | (0u - ((Operators.CompareString(Strings.LCase(sLogOnUser), "ed", TextCompare: false) == 0) ? 1u : 0u)) | (0u - (Strings.LCase(sLogOnUser ?? "").Contains("expertpdf") ? 1u : 0u))) != 0)
        //{
        //    result = true;
        //}
        if (sLogOnUser.Substring(0, 14).ToLower() == "decisiondesign")
        {
            result = true;
        }
        else if (((sLogOnUser.ToLower().Contains("ddc") || sLogOnUser.ToLower().Contains("mitchene")) ||
                  (sLogOnUser.ToLower() == "frank") ||
                  (sLogOnUser.ToLower() == "ed") ||
                  (sLogOnUser?.ToLower().Contains("expertpdf") ?? false)) != false)
        {
            result = true;
        }

        return result;
    }

    public static bool canImpersonate(string sActualUser, string sLogOnUser, string asRoleRequired)
    {
        if (isDecisionDesignUser(sLogOnUser))
        {
            return true;
        }
        if (!modUserProf.mUserHasRole(sActualUser, asRoleRequired))
        {
            return false;
        }
        return true;
    }

    public static string mTRSResolveEntry(string asTableName, string asDesc)
    {
        int num = mGetNameIndex(asTableName);
        ref T_TRSLIST reference = ref tTRSCache.tTableList[num];
        checked
        {
            int num2 = reference.iEntryCount - 1;
            for (int i = 0; i <= num2; i++)
            {
                //if (Operators.CompareString(asDesc, reference.tCodes[i].sDesc, TextCompare: false) == 0)
                if (string.Equals(asDesc, reference.tCodes[i].sDesc, StringComparison.OrdinalIgnoreCase))
                {
                    return reference.tCodes[i].sCode;
                }
            }
            return "";
        }
    }

    public static string System_Configuration_ConfigurationSettings_AppSettings(string strKey)
    {
        //return ConfigurationManager.AppSettings[strKey];
        //return ConfigurationManager.AppSettings[key] ?? defaultValue;
        return "";
    }

    //public static intint DataSaveHistory(string tableHistory, string tableName, string tableField, int tableId)
    public static void DataSaveHistory(string tableHistory, string tableName, string tableField, int tableId)
    {
        //corePage corePage2 = new corePage();
        //string inputStr = corePage2.cnExecuteForSingleValue("SELECT MAX(Version) FROM " + tableHistory + " WHERE " + tableField + " = " + (tableId).ToString());
        //SqlDataReader sqlDataReader = corePage2.cnExecute("SELECT TOP 1 * FROM " + tableName);
        //sqlDataReader.Read();
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder stringBuilder2 = new StringBuilder();
        checked
        {
            //int num = sqlDataReader.FieldCount - 1;
            //for (int i = 0; i <= num; i++)
            //{
            //    //stringBuilder2.Append("," + sqlDataReader.GetName(i));
            //}
            //sqlDataReader.Close();
            stringBuilder.Append("INSERT INTO " + tableHistory + "(");
            stringBuilder.Append("Version");
            stringBuilder.Append(stringBuilder2.ToString());
            stringBuilder.Append(")\r\n");
            stringBuilder.Append("SELECT ");
            //stringBuilder.Append(Conversions.ToString(Conversion.Val(inputStr) + 1.0) + " AS Version");
            stringBuilder.Append(stringBuilder2.ToString());
            //stringBuilder.Append(" FROM " + tableName + " WHERE " + tableField + "=" + Conversions.ToString(tableId));
            //return (int)Math.Round(Conversion.Val(corePage2.cnExecuteGetIdentity(stringBuilder.ToString())));
        }
    }
}
