<template>
  <el-card>
    <h3>Finance Coordinator Quote Response</h3>

    <el-radio-group v-model="quoteStatus">
      <el-radio label="available">
        Quote Response Ready for Team Consensus Meeting
      </el-radio>
      <el-radio label="completed">
        Quote Response ready for Gateway 2 Approval
      </el-radio>
    </el-radio-group>

    <el-divider />

    <div>
      <h4>ECR Worksheets:</h4>
      <el-table :data="[]" style="width: 100%" border>
        <el-table-column label="Source File" width="200" />
        <el-table-column label="Creator" />
        <el-table-column label="Last Updated" />
        <el-table-column label="" />
        <el-table-column label="Description" />
        <el-table-column label="Unit Price" />
        <el-table-column label="Volume" />
      </el-table>
    </div>

    <el-form label-width="150px" class="mt-4">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Tool Lead Time">
            <el-input
              v-model="toolLeadTime"
              readonly
              disabled
              style="width: 150px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Tooling Capacity">
            <el-input
              v-model="toolingCapacity"
              readonly
              disabled
              style="width: 250px"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="Tooling Terms">
            <el-input
              v-model="toolingTerms"
              readonly
              disabled
              style="width: 150px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Piece Price Terms">
            <el-input
              v-model="piecePriceTerms"
              readonly
              disabled
              style="width: 150px"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="F.O.B.">
            <!-- <el-select v-model="fob" disabled style="width: 160px">
              <el-option label=" " value="" /> -->
            <!-- Add more options here -->
            <!-- </el-select> -->
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="GDPEP Category">
            <el-input
              v-model="gdpepCategory"
              readonly
              disabled
              style="width: 150px"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="Economic Level Date - Materials">
            <!-- <el-input
              v-model="econDateMaterial"
              readonly
              disabled
              style="width: 120px"
            /> -->
          </el-form-item></el-col
        ></el-row
      ></el-form
    ></el-card
  >
</template>

<script setup lang="ts">
// import { ref } from "vue";
import { useFormModelStore } from "@/store/modules/formModel";
import { onMounted, ref } from "vue";
const quoteStatus = ref<string>("available"); // 預設選項（可為空字串）
const formStore = useFormModelStore();

onMounted(() => {
  if (formStore.modelCQRHeaderCollection) {
    // formData.value = { ...formStore.modelCQRHeaderCollection.iCQRHeader };
  }
});

const gdpepCategory = ref("");
const piecePriceTerms = ref("");
const toolingTerms = ref("");
const toolingCapacity = ref("");
const toolLeadTime = ref("");
</script>
