﻿using CQR.Application.Repositories;
using CQR.Persistence.Query.Base;
using Dapper;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Query.Repositories;

public class UserRoleQueryRepository : QueryBaseRepository, IUserRoleQueryRepository
{
    public UserRoleQueryRepository(IConfiguration configuration) : base(configuration)
    {
    }

    public string GetManagerByRole(string asRole)
    {
        //throw new NotImplementedException();
        const string sql =
            @"SELECT HDR.UserID
       FROM USERPROF_UserProfileHeader HDR INNER JOIN USERPROF_UserRoles RLS ON HDR.QueueKey = RLS.QueueKey
        WHERE RLS.RoleCode = @RoleCode
        AND RLS.PrimaryInd = 'Y'";

        var value = _connection.ExecuteScalar<string>(sql, new { RoleCode = asRole });
        string result = string.IsNullOrWhiteSpace(value) ? "MGR_" + asRole : value;
        return result;
    }

    public Task<IEnumerable<String>> GetRolesNames(string user_id)
    {
        //var cqrHeaderColl = new CQRHeaderCollecion();
        //var result = new UserRoles();

        const string sql = @"
            SELECT RLS.RoleCode
            FROM USERPROF_UserProfileHeader HDR
            INNER JOIN USERPROF_UserRoles RLS ON HDR.QueueKey = RLS.QueueKey
            WHERE HDR.UserID = @UserId";
        var roles = _connection.QueryAsync<string>(sql, new { UserId = user_id });
        return roles;
    }

}
