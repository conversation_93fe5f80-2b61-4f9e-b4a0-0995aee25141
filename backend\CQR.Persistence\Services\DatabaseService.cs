using CQR.Application.Services;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Command.Services.Implementations
{
    public class DatabaseService : IDatabaseService
    {
        private readonly string _connectionString;

        public DatabaseService(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        public async Task<string> TestConnectionAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    return "Connection Successful!";
                }
            }
            catch (Exception ex)
            {
                return $"Connection Failed: {ex.Message}";
            }
        }

        public async Task<string> GetDataFromDatabaseAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    var command = new SqlCommand("SELECT TOP 1 * FROM YourTable", connection);  // Replace 'YourTable' with a real table in your database
                    var reader = await command.ExecuteReaderAsync();

                    if (await reader.ReadAsync())
                    {
                        return reader["YourColumnName"].ToString(); // Replace 'YourColumnName' with an actual column name
                    }
                    return "No data found!";
                }
            }
            catch (Exception ex)
            {
                return $"Error querying database: {ex.Message}";
            }
        }
    }
}
