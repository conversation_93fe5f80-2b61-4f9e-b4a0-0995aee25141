import { ref, reactive } from "vue";
import type { Ref } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { useRouter } from "vue-router";
import { searchCQR, exportSearchResults } from "@/api/search";
import type { SearchQuery } from "./useSearchForm";

export interface CQRSearchResult {
  queueKey: number;
  cqrNumber: string;
  projectNbr: string;
  revNbr: string;
  oemGroup?: string;
  oemCustomer?: string;
  accountManagerName?: string;
  estimatorName?: string;
  pgmName?: string;
  pdmName?: string;
  engMgrName?: string;
  prdName?: string;
  productDesc?: string;
  gateExitTRS?: string;
  statusTRS?: string;
  releaseDate?: string;
  franIssueDate?: string;
  dueDateFromEng?: string;
  dueDateToBnE?: string;
  custQuoteDueDate?: string;
  openingMeetingDate?: string;
  franDesc?: string;
  platformName?: string;
  modelYearTRS?: string;
  volumePerAnnum?: string;
  approxAnnualValue?: string;
  bkRndInfComments?: string;
  uniqueNumber?: string;
  engPkgComments?: string;
  mfgSiteTRS?: string;
  qsCustomerResp?: string;
  qsComments?: string;
  isSelected: boolean;
  lastUpdatedDate?: string;
}

export interface SearchResultsData {
  results: CQRSearchResult[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  searchSummary?: string;
}

export function useSearchResults() {
  const router = useRouter();

  // 搜尋結果狀態
  const searchResults = reactive<SearchResultsData>({
    results: [],
    totalCount: 0,
    pageNumber: 1,
    pageSize: 50,
    totalPages: 0,
    hasPreviousPage: false,
    hasNextPage: false,
    searchSummary: ""
  });

  // 選中的記錄
  const selectedRows = ref<CQRSearchResult[]>([]);
  const selectedRowKeys = ref<number[]>([]);

  // 執行搜尋
  const executeSearch = async (query: SearchQuery): Promise<void> => {
    try {
      const response = await searchCQR(query);

      if (response.success && response.data) {
        // 映射後端的響應數據，支援兩種格式（帕斯卡式和駝峰式）
        const data = response.data;
        Object.assign(searchResults, {
          results: data.Results || data.results || [],
          totalCount: data.TotalCount || data.totalCount || 0,
          pageNumber: data.PageNumber || data.pageNumber || 1,
          pageSize: data.PageSize || data.pageSize || 50,
          totalPages: data.TotalPages || data.totalPages || 0,
          hasPreviousPage: data.HasPreviousPage || data.hasPreviousPage || false,
          hasNextPage: data.HasNextPage || data.hasNextPage || false,
          searchSummary: data.SearchSummary || data.searchSummary || ""
        });

        // 重置選中項目
        selectedRows.value = [];
        selectedRowKeys.value = [];

        ElMessage.success(`搜尋完成，找到 ${response.data.TotalCount} 筆記錄`);
      } else {
        throw new Error(response.message || "搜尋失敗");
      }
    } catch (error: any) {
      console.error("搜尋執行失敗:", error);
      ElMessage.error(error.message || "搜尋執行失敗");

      // 清空結果
      Object.assign(searchResults, {
        results: [],
        totalCount: 0,
        pageNumber: 1,
        pageSize: query.PageSize,
        totalPages: 0,
        hasPreviousPage: false,
        hasNextPage: false,
        searchSummary: "搜尋失敗"
      });

      throw error;
    }
  };

  // 快速搜尋
  const executeQuickSearch = async (
    searchText: string,
    pageNumber = 1,
    pageSize = 50
  ): Promise<void> => {
    try {
      const response = await fetch(
        `/api/search/quick?searchText=${encodeURIComponent(searchText)}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      const data = await response.json();

      if (data.success) {
        Object.assign(searchResults, data.data);
        selectedRows.value = [];
        selectedRowKeys.value = [];

        ElMessage.success(`快速搜尋完成，找到 ${data.data.totalCount} 筆記錄`);
      } else {
        throw new Error(data.message || "快速搜尋失敗");
      }
    } catch (error: any) {
      console.error("快速搜尋失敗:", error);
      ElMessage.error(error.message || "快速搜尋失敗");
      throw error;
    }
  };

  // 處理行點擊
  const handleRowClick = (row: CQRSearchResult) => {
    console.log("行點擊:", row);
    // 可以在這裡添加行點擊邏輯，例如顯示詳細資訊
  };

  // 處理選擇變更
  const handleSelectionChange = (selection: CQRSearchResult[]) => {
    selectedRows.value = selection;
    selectedRowKeys.value = selection.map(row => row.queueKey);
  };

  // 查看CQR詳情
  const viewCQRDetails = (row: CQRSearchResult) => {
    // 導航到 editCQR 頁面
    console.log("查看CQR詳情，導航到editCQR:", row);
    router.push({
      name: "cqrEdit",
      params: {
        queueKey: row.queueKey.toString()
      }
    });
  };

  // 匯出搜尋結果
  const exportResults = async (
    query: SearchQuery,
    format: "excel" | "csv" = "excel"
  ): Promise<void> => {
    if (searchResults.results.length === 0) {
      ElMessage.warning("沒有搜尋結果可以匯出");
      return;
    }

    const loading = ElLoading.service({
      lock: true,
      text: "正在匯出搜尋結果...",
      spinner: "el-icon-loading"
    });

    try {
      // 移除分頁限制以匯出所有結果
      const exportQuery = {
        ...query,
        pageNumber: 1,
        pageSize: Number.MAX_SAFE_INTEGER
      };

      const response = await exportSearchResults(exportQuery, format);

      if (response.success && response.data) {
        // 建立下載連結
        const blob = new Blob([response.data], {
          type:
            format === "csv"
              ? "text/csv"
              : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `CQR_Search_Results_${new Date().toISOString().slice(0, 10)}.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        ElMessage.success("匯出完成");
      } else {
        throw new Error("匯出失敗");
      }
    } catch (error: any) {
      console.error("匯出失敗:", error);
      ElMessage.error(error.message || "匯出失敗");
    } finally {
      loading.close();
    }
  };

  // 獲取選中項目的統計資訊
  const getSelectionStats = () => {
    if (selectedRows.value.length === 0) return null;

    return {
      count: selectedRows.value.length,
      queueKeys: selectedRowKeys.value,
      summary: `已選擇 ${selectedRows.value.length} 筆記錄`
    };
  };

  // 清除搜尋結果
  const clearResults = () => {
    Object.assign(searchResults, {
      results: [],
      totalCount: 0,
      pageNumber: 1,
      pageSize: 50,
      totalPages: 0,
      hasPreviousPage: false,
      hasNextPage: false,
      searchSummary: ""
    });

    selectedRows.value = [];
    selectedRowKeys.value = [];
  };

  // 重新整理當前頁面
  const refreshCurrentPage = async (query: SearchQuery) => {
    if (searchResults.totalCount > 0) {
      await executeSearch(query);
    }
  };

  // 格式化搜尋摘要
  const formatSearchSummary = (
    totalCount: number,
    hasSearchCriteria: boolean
  ) => {
    if (totalCount === 0 && !hasSearchCriteria) {
      return "搜尋限制 - 無搜尋條件";
    }
    return `找到 ${totalCount} 筆記錄`;
  };

  return {
    // 狀態
    searchResults,
    selectedRows,
    selectedRowKeys,

    // 方法
    executeSearch,
    executeQuickSearch,
    handleRowClick,
    handleSelectionChange,
    viewCQRDetails,
    exportResults,
    getSelectionStats,
    clearResults,
    refreshCurrentPage,
    formatSearchSummary
  };
}
