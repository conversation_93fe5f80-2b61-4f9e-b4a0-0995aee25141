<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Bogus" Version="35.6.1" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="GemBox.Spreadsheet" Version="2025.4.107" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
    <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="MailKit" Version="4.8.0" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <PackageReference Include="FluentValidation" Version="11.10.0" />
    <PackageReference Include="TypeGen" Version="6.0.2" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="SqlSugar" Version="*********" />
    <PackageReference Include="Wangkanai.Detection" Version="8.15.0" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="sapnco">
      <HintPath>lib\sapnco.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="sapnco_utils">
      <HintPath>lib\sapnco_utils.dll</HintPath>
      <Private>true</Private>
    </Reference>
  </ItemGroup>
  <!-- 会在构建时被复制到输出目录。 -->
  <ItemGroup>
    <None Update="lib\sapnco.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="lib\sapnco_utils.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="IServices\" />
    <Folder Include="ValueObjects\" />
  </ItemGroup>
</Project>