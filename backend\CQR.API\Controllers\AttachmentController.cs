using System.IO.Compression;
using CQR.Application.Dto.AttachFile;
using CQR.Application.Repositories;
using CQR.Domain.ATTDIR_AttachFileDirHeaders;
using CQR.Domain.Entities;
using CQR.Infrastructure.Utilities;
using Microsoft.AspNetCore.Mvc;

namespace CQR.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AttachmentController : ControllerBase
    {
        private readonly IAttachFileQueryRepository _attachFileQueryRepository;
        private readonly IUserProfileHeaderQueryRepository _userProfileHeaderQueryRepository;
        private readonly ITRSHeaderQueryRespository _trsHeaderQueryRespository;

        public AttachmentController(
            IAttachFileQueryRepository attachFileQueryRepository,
            IUserProfileHeaderQueryRepository userProfileHeaderQueryRepository,
            ITRSHeaderQueryRespository tRSHeaderQueryRespository
        )
        {
            _attachFileQueryRepository = attachFileQueryRepository;
            _userProfileHeaderQueryRepository = userProfileHeaderQueryRepository;
            _trsHeaderQueryRespository = tRSHeaderQueryRespository;
        }

        // 查询Attachment列表
        [HttpGet("getAttachments")]
        public async Task<ActionResult<IEnumerable<ATTDIR_AttachFileDirHeader>>> GetAttachments(
            [FromQuery] string folderType = null,
            [FromQuery] int queueKey = 0
        )
        {
            IEnumerable<ATTDIR_AttachFileDirHeaderCheckOutDto> result =
                await _attachFileQueryRepository.GetATTDIRFilesByFolderType(folderType, queueKey);
            foreach (var attachFile in result)
            {
                attachFile.AttachTitle = attachFile.AttachTitle.Substring(
                    attachFile.AttachTitle.IndexOf('_') + 1
                );
                IEnumerable<USERPROF_UserProfileHeader> userProfile =
                    await _userProfileHeaderQueryRepository.getUserProfileByNTUserId(
                        attachFile.CreatedByName
                    );
                foreach (var userProfileHeader in userProfile)
                {
                    attachFile.CheckoutUser =
                        userProfileHeader.LastName + "," + userProfileHeader.FirstName;
                }
                attachFile.CreatedDate =
                    DateUtil.FormateDate(attachFile.CreatedDate)
                    + " "
                    + attachFile.CreatedTime.Insert(2, ":");
            }
            return Ok(result);
        }

        // 查询下载模板列表
        [HttpGet("getModelList")]
        public async Task<ActionResult<IEnumerable<String>>> GetModelList(
            [FromQuery] string tableName = null
        )
        {
            IEnumerable<String> result = await _trsHeaderQueryRespository.GetModelList(tableName);
            return Ok(result);
        }

        // 上传附件至临时目录
        [HttpPost("uploadAttachment")]
        public async Task<IActionResult> UploadAttachment(List<IFormFile> files)
        {
            try
            {
                await FileUtils.UploadFile("Zip", files);
                return Ok("文件上传成功");
            }
            catch (Exception e)
            {
                return BadRequest("文件上传失败:" + e.Message);
            }
        }

        // 查看文件View
        [HttpGet("viewAttachment")]
        public IActionResult ViewAttachment(
            [FromQuery] string sSrcFile = null,
            [FromQuery] Boolean bCreateOnSave = false
        )
        {
            if (bCreateOnSave)
            {
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), "Zip", sSrcFile);
                if (System.IO.File.Exists(filePath))
                {
                    // 新建未保存的附件(从临时目录获取)
                    var fileStream = System.IO.File.OpenRead(filePath);
                    string mimeType = GetMimeType(sSrcFile);
                    return File(fileStream, "application/octet-stream", sSrcFile);
                }
                else
                {
                    return NotFound("文件不存在！");
                }
            }
            else if (sSrcFile.Substring(sSrcFile.Length - 4).Equals(".ZIP"))
            {
                // ZIP文件
                return Ok();
            }
            else
            {
                // 普通文件(直接访问附件目录)
                return Ok();
            }
        }

        // 压缩保存文件
        [HttpPost("saveFile")]
        public async Task<IActionResult> SaveFile(List<string> files)
        {
            // 将文件压缩为zip包
            string zipPath = "远程文件库";
            using (ZipArchive archive = ZipFile.Open(zipPath, ZipArchiveMode.Create))
            {
                foreach (string fileName in files)
                {
                    string entryName = Path.GetFileName(fileName); // 文件名作为压缩包内的文件名
                    archive.CreateEntryFromFile(fileName, entryName);
                }
            }
            return Ok("文件保存成功");
        }

        // 删除附件
        [HttpDelete("attachmentDelete")]
        public async Task<IActionResult> DeleteAttachment(int id, string filename)
        {
            await FileUtils.DeleteFile(
                Path.Combine(Directory.GetCurrentDirectory(), "Zip", filename)
            );
            return Ok();
        }

        // 删除Task
        [HttpDelete("taskList")]
        public async Task<IActionResult> DeleteTaskList(int id, string filename)
        {
            await FileUtils.DeleteFile(Path.Combine(Directory.GetCurrentDirectory(), "", filename));
            return Ok();
        }

        private string GetMimeType(string fileName)
        {
            var provider = new Microsoft.AspNetCore.StaticFiles.FileExtensionContentTypeProvider();
            if (!provider.TryGetContentType(fileName, out string mimeType))
            {
                mimeType = "application/octet-stream"; // 默认类型
            }
            return mimeType;
        }
    }
}
