using ClosedXML.Excel;
using CQR.Application.Dto;
using CQR.Application.Repositories;
using Microsoft.AspNetCore.Hosting;
using System.Text;

namespace CQR.Infrastructure.FileStorage;

public class ExcelService : IExcelService
{
    private readonly string _basePath;
    private readonly IWebHostEnvironment _env;
    
    public ExcelService(IWebHostEnvironment env)
    {
        _basePath = Path.Combine(env.WebRootPath, "temp");
        if (!Directory.Exists(_basePath))
        {
            Directory.CreateDirectory(_basePath);
        }
        _env = env;
    }

    public string GenerateExcelFile(string fileName)
    {
        var filePath = Path.Combine(_basePath, fileName);

        using var workbook = new XLWorkbook();
        var worksheet = workbook.Worksheets.Add("Data");
        worksheet.Cell(1, 1).Value = "Hello";
        worksheet.Cell(1, 2).Value = "World";
        workbook.SaveAs(filePath);

        return filePath;
    }

    public byte[] GetExcelFile(string fileName)
    {
        var filePath = Path.Combine(_env.WebRootPath, "files", "excels", fileName);
        if (!File.Exists(filePath))
            throw new FileNotFoundException("找不到檔案", fileName);

        return File.ReadAllBytes(filePath);
    }

    public byte[] ExportSearchResultsToExcel(List<CQRSearchResultDto> results, string title = "CQR搜尋結果")
    {
        using var workbook = new XLWorkbook();
        var worksheet = workbook.Worksheets.Add(title);

        // 設定標題樣式
        var titleRow = worksheet.Row(1);
        titleRow.Height = 25;
        worksheet.Cell(1, 1).Value = title;
        worksheet.Cell(1, 1).Style.Font.Bold = true;
        worksheet.Cell(1, 1).Style.Font.FontSize = 16;
        worksheet.Range(1, 1, 1, 15).Merge();

        // 設定資訊行
        var infoRow = worksheet.Row(2);
        worksheet.Cell(2, 1).Value = $"匯出日期：{DateTime.Now:yyyy-MM-dd HH:mm:ss}";
        worksheet.Cell(2, 8).Value = $"記錄總數：{results.Count}";

        // 設定欄位標題
        var headerRow = 4;
        var headers = new[]
        {
            "CQR #", "OEM Group", "OEM Customer", "Account Manager", "Estimator",
            "Product Description", "Status", "Release Date", "Issue Date",
            "Due Date from Eng", "Quote Due Date", "Opening Meeting Date",
            "Vehicle", "Model Year", "Volume Per Annum", "Annual Value"
        };

        for (int i = 0; i < headers.Length; i++)
        {
            var cell = worksheet.Cell(headerRow, i + 1);
            cell.Value = headers[i];
            cell.Style.Font.Bold = true;
            cell.Style.Fill.BackgroundColor = XLColor.LightGray;
            cell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
        }

        // 填入資料
        var dataStartRow = headerRow + 1;
        for (int i = 0; i < results.Count; i++)
        {
            var result = results[i];
            var row = dataStartRow + i;

            worksheet.Cell(row, 1).Value = result.CQRNumber;
            worksheet.Cell(row, 2).Value = result.OEMGroup ?? "";
            worksheet.Cell(row, 3).Value = result.OEMCustomer ?? "";
            worksheet.Cell(row, 4).Value = result.AccountManagerName ?? "";
            worksheet.Cell(row, 5).Value = result.EstimatorName ?? "";
            worksheet.Cell(row, 6).Value = result.ProductDesc ?? "";
            worksheet.Cell(row, 7).Value = result.StatusTRS ?? "";
            
            // 日期格式處理
            if (result.ReleaseDate.HasValue)
                worksheet.Cell(row, 8).Value = result.ReleaseDate.Value.ToString("yyyy-MM-dd");
            if (result.FRANIssueDate.HasValue)
                worksheet.Cell(row, 9).Value = result.FRANIssueDate.Value.ToString("yyyy-MM-dd");
            if (result.DueDateFromEng.HasValue)
                worksheet.Cell(row, 10).Value = result.DueDateFromEng.Value.ToString("yyyy-MM-dd");
            if (result.CustQuoteDueDate.HasValue)
                worksheet.Cell(row, 11).Value = result.CustQuoteDueDate.Value.ToString("yyyy-MM-dd");
            if (result.OpeningMeetingDate.HasValue)
                worksheet.Cell(row, 12).Value = result.OpeningMeetingDate.Value.ToString("yyyy-MM-dd");

            worksheet.Cell(row, 13).Value = result.PlatformName ?? "";
            worksheet.Cell(row, 14).Value = result.ModelYearTRS ?? "";
            worksheet.Cell(row, 15).Value = result.VolumePerAnnum ?? "";
            worksheet.Cell(row, 16).Value = result.ApproxAnnualValue ?? "";

            // 設定邊框
            for (int j = 1; j <= headers.Length; j++)
            {
                worksheet.Cell(row, j).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            }
        }

        // 自動調整欄寬
        worksheet.Columns().AdjustToContents();

        // 設定最大欄寬
        foreach (var column in worksheet.Columns())
        {
            if (column.Width > 50)
                column.Width = 50;
            else if (column.Width < 10)
                column.Width = 10;
        }

        using var stream = new MemoryStream();
        workbook.SaveAs(stream);
        return stream.ToArray();
    }

    public byte[] ExportSearchResultsToCSV(List<CQRSearchResultDto> results)
    {
        var csv = new StringBuilder();
        
        // 添加BOM以支援中文字符
        csv.Append('\ufeff');
        
        // 標題行
        csv.AppendLine("CQR #,OEM Group,OEM Customer,Account Manager,Estimator,Product Description,Status,Release Date,Issue Date,Due Date from Eng,Quote Due Date,Opening Meeting Date,Vehicle,Model Year,Volume Per Annum,Annual Value");
        
        // 資料行
        foreach (var result in results)
        {
            var row = new[]
            {
                EscapeCsvField(result.CQRNumber),
                EscapeCsvField(result.OEMGroup),
                EscapeCsvField(result.OEMCustomer),
                EscapeCsvField(result.AccountManagerName),
                EscapeCsvField(result.EstimatorName),
                EscapeCsvField(result.ProductDesc),
                EscapeCsvField(result.StatusTRS),
                FormatDateForCsv(result.ReleaseDate),
                FormatDateForCsv(result.FRANIssueDate),
                FormatDateForCsv(result.DueDateFromEng),
                FormatDateForCsv(result.CustQuoteDueDate),
                FormatDateForCsv(result.OpeningMeetingDate),
                EscapeCsvField(result.PlatformName),
                EscapeCsvField(result.ModelYearTRS),
                EscapeCsvField(result.VolumePerAnnum),
                EscapeCsvField(result.ApproxAnnualValue)
            };
            
            csv.AppendLine(string.Join(",", row));
        }
        
        return Encoding.UTF8.GetBytes(csv.ToString());
    }

    private string EscapeCsvField(string? field)
    {
        if (string.IsNullOrEmpty(field))
            return "";
            
        // 如果欄位包含逗號、引號或換行符，需要用引號包圍並轉義引號
        if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
        {
            return "\"" + field.Replace("\"", "\"\"") + "\"";
        }
        
        return field;
    }

    private string FormatDateForCsv(DateTime? date)
    {
        return date?.ToString("yyyy-MM-dd") ?? "";
    }
}
