# CQR 系統程式規格書

## 文檔資訊

| 項目 | 內容 |
|------|------|
| 文檔標題 | CQR (Customer Quote Request) 系統程式規格書 |
| 版本 | 2.0 |
| 建立日期 | 2025-07-22 |
| 最後更新 | 2025-07-22 |
| 文檔狀態 | 草案 |
| 作者 | 開發團隊 |

## 目錄

1. [系統概述](#系統概述)
2. [系統架構](#系統架構)
3. [技術規格](#技術規格)
4. [功能規格](#功能規格)
5. [資料庫設計](#資料庫設計)
6. [API 規格](#api-規格)
7. [前端規格](#前端規格)
8. [後端規格](#後端規格)
9. [安全規格](#安全規格)
10. [性能規格](#性能規格)
11. [部署規格](#部署規格)
12. [測試規格](#測試規格)

---

## 系統概述

### 1.1 專案背景

CQR (Customer Quote Request) 系統是一個企業級的客戶報價請求管理平台，用於處理從客戶詢價到最終報價回應的完整業務流程。本專案將舊有的 ASP.NET WebForm 系統現代化升級為基於 ASP.NET Core API 和 Vue3 的現代化 Web 應用程式。

### 1.2 系統目標

- **現代化技術棧**：採用 ASP.NET Core 8.0 + Vue3 + TypeScript
- **提升用戶體驗**：響應式設計，支援多設備存取
- **改善系統性能**：優化資料存取和查詢效率
- **增強系統安全性**：實施現代化驗證和授權機制
- **提高開發效率**：採用 Clean Architecture 和 CQRS 模式
- **支援微服務架構**：為未來擴展做準備

### 1.3 業務流程概述

```mermaid
graph TD
    A[客戶詢價] --> B[創建 CQR]
    B --> C[Description 階段]
    C --> D[Background 階段]
    D --> E[Engineering Assessment]
    E --> F[Quote Response]
    F --> G[GDPIM Gateway 1]
    G --> H[GDPIM Gateway 2]
    H --> I[Sales Review]
    I --> J[Sales Closeout]
    J --> K[專案結案]
    
    style A fill:#e1f5fe
    style K fill:#e8f5e8
```

### 1.4 系統使用者

| 角色 | 描述 | 主要功能 |
|------|------|----------|
| Account Manager (AMGR) | 客戶經理 | 創建 CQR、管理客戶關係 |
| Sales Director (SDIR) | 銷售總監 | 審核報價、業務決策 |
| Cost Estimator (CEST) | 成本估算師 | 成本分析、報價計算 |
| Engineering Manager (EMGR) | 工程經理 | 技術評估、可行性分析 |
| Finance Coordinator (FCOORD) | 財務協調員 | 財務審核、利潤分析 |
| Business Manager (BMGR) | 業務經理 | 業務流程管理 |

---

## 系統架構

### 2.1 整體架構概覽

```
┌─────────────────────────────────────────────────────────────┐
│                    CQR 系統架構                              │
├─────────────────────────────────────────────────────────────┤
│  前端層 (Frontend Layer)                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Vue 3 + TypeScript + Element Plus + Pinia              │ │
│  │ • 用戶介面組件                                          │ │
│  │ • 狀態管理                                              │ │
│  │ • 路由管理                                              │ │
│  │ • HTTP 客戶端                                           │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  API 層 (API Layer)                                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ ASP.NET Core 8.0 Web API                               │ │
│  │ • RESTful API 端點                                      │ │
│  │ • JWT 驗證授權                                          │ │
│  │ • Swagger 文檔                                          │ │
│  │ • 中介軟體                                              │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  應用層 (Application Layer)                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Clean Architecture + CQRS                              │ │
│  │ • 應用服務 (Application Services)                       │ │
│  │ • 命令查詢處理器 (Command/Query Handlers)               │ │
│  │ • 資料傳輸物件 (DTOs)                                   │ │
│  │ • 驗證器 (Validators)                                   │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  領域層 (Domain Layer)                                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Domain Models & Business Logic                         │ │
│  │ • 實體 (Entities)                                       │ │
│  │ • 值物件 (Value Objects)                               │ │
│  │ • 領域服務 (Domain Services)                           │ │
│  │ • 業務規則                                              │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  基礎設施層 (Infrastructure Layer)                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Data Access & External Services                        │ │
│  │ • Entity Framework Core (Commands)                     │ │
│  │ • SqlSugar ORM (Queries)                               │ │
│  │ • Repository Pattern                                   │ │
│  │ • 外部系統整合 (SAP, SMTP, etc.)                       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  資料層 (Data Layer)                                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ SQL Server Database                                    │ │
│  │ • 關聯式資料庫                                          │ │
│  │ • 預存程序                                              │ │
│  │ • 索引優化                                              │ │
│  │ • 備份與復原                                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 專案結構

```
CQR-System/
├── docs/                           # 文檔目錄
│   ├── CQR_System_Specification.md # 系統規格書
│   ├── API_Documentation.md        # API 文檔
│   └── Database_Schema.md          # 資料庫設計文檔
├── backend/                        # 後端專案
│   ├── CQR.API/                    # Web API 專案
│   ├── CQR.Application/            # 應用層
│   ├── CQR.Domain/                 # 領域層
│   ├── CQR.Infrastructure/         # 基礎設施層
│   ├── CQR.Persistence.Command/    # 命令資料持久層
│   ├── CQR.Persistence.Query/      # 查詢資料持久層
│   └── CQR.Tests/                  # 測試專案
├── frontend/                       # 前端專案
│   ├── src/                        # 源碼目錄
│   │   ├── components/             # Vue 組件
│   │   ├── views/                  # 頁面組件
│   │   ├── store/                  # Pinia 狀態管理
│   │   ├── api/                    # API 客戶端
│   │   └── types/                  # TypeScript 型別定義
│   ├── public/                     # 靜態資源
│   └── package.json               # 依賴包配置
├── legacy/                         # 舊系統程式碼
│   ├── CQR/                        # ASP.NET WebForm 舊系統
│   ├── DLL_source_code/            # DLL 反編譯代碼
│   └── DecisionCore.classic/       # 核心決策邏輯 DLL
└── shared/                         # 共用資源
    ├── templates/                  # Excel 模板
    └── docs/                       # 共用文檔
```

### 2.3 技術架構決策

#### 2.3.1 架構模式

- **Clean Architecture**: 確保業務邏輯與技術實現分離
- **CQRS (Command Query Responsibility Segregation)**: 分離讀寫操作
- **Repository Pattern**: 抽象資料存取層
- **Dependency Injection**: 解耦組件依賴關係

#### 2.3.2 資料存取策略

- **雙 ORM 方案**:
  - Entity Framework Core: 用於命令操作 (CUD)
  - SqlSugar: 用於查詢操作 (R)，提供更佳的查詢性能
- **Unit of Work**: 確保交易一致性
- **Repository Collection**: 統一資料存取介面

---

## 技術規格

### 3.1 開發環境

| 技術棧 | 版本 | 說明 |
|--------|------|------|
| .NET | 8.0 LTS | 長期支援版本 |
| ASP.NET Core | 8.0 | Web API 框架 |
| Entity Framework Core | 8.0 | ORM 框架 (Commands) |
| SqlSugar | 5.1.4+ | ORM 框架 (Queries) |
| Vue.js | 3.4+ | 前端框架 |
| TypeScript | 5.0+ | 型別檢查 |
| Element Plus | 2.4+ | UI 組件庫 |
| Pinia | 2.1+ | 狀態管理 |
| Vite | 5.0+ | 建置工具 |

### 3.2 資料庫

| 項目 | 規格 |
|------|------|
| 資料庫系統 | Microsoft SQL Server 2019+ |
| 字元集 | UTF-8 |
| 連線池 | 最大 100 個連線 |
| 備份策略 | 每日完整備份 + 每小時差異備份 |

### 3.3 第三方套件

#### 3.3.1 後端套件

```xml
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
<PackageReference Include="SqlSugarCore" Version="*********" />
<PackageReference Include="AutoMapper" Version="12.0.1" />
<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
<PackageReference Include="MediatR" Version="12.1.1" />
<PackageReference Include="Serilog.AspNetCore" Version="7.0.0" />
<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
<PackageReference Include="ClosedXML" Version="0.102.0" />
<PackageReference Include="Microsoft.Identity.Web" Version="2.15.2" />
```

#### 3.3.2 前端套件

```json
{
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "@element-plus/icons-vue": "^2.3.0",
    "axios": "^1.6.0",
    "typescript": "^5.0.0",
    "vite": "^5.0.0",
    "@vitejs/plugin-vue": "^4.5.0"
  }
}
```

### 3.4 開發工具

| 工具 | 用途 |
|------|------|
| Visual Studio 2022 | 後端開發 IDE |
| VS Code | 前端開發 IDE |
| SQL Server Management Studio | 資料庫管理 |
| Postman | API 測試 |
| Docker | 容器化部署 |
| Git | 版本控制 |
| Azure DevOps | CI/CD 流水線 |

---

## 功能規格

### 4.1 系統功能總覽

#### 4.1.1 核心業務功能

```mermaid
mindmap
  root((CQR System))
    項目管理
      創建 CQR
      搜尋 CQR
      複製 CQR
      版本控制
    報價流程
      Description
      Background
      Engineering
      Quote Response
      Sales Closeout
    審批管理
      工作流程
      角色權限
      狀態追蹤
      通知提醒
    附件管理
      文件上傳
      版本控制
      在線預覽
      下載歸檔
    報表系統
      Excel 報表
      統計分析
      資料匯出
      範本管理
    系統管理
      用戶管理
      角色權限
      系統設定
      日誌管理
```

### 4.2 功能詳細規格

#### 4.2.1 用戶驗證與授權

**功能描述**: 實現安全的用戶驗證和細粒度的權限控制

**技術實現**:
- Azure AD 整合 (生產環境)
- JWT Token 驗證
- 角色基礎存取控制 (RBAC)
- 假驗證處理器 (開發環境)

**API 端點**:
```
POST   /api/auth/login      # 用戶登入
POST   /api/auth/logout     # 用戶登出
POST   /api/auth/refresh    # 刷新 Token
GET    /api/auth/profile    # 獲取用戶資料
```

**前端組件**:
- LoginView.vue: 登入頁面
- AuthService.ts: 驗證服務
- AuthGuard.ts: 路由守衛

#### 4.2.2 CQR 項目管理

**功能描述**: CQR 項目的完整生命週期管理

**主要功能**:
1. **創建 CQR**
   - 支援新建和基於現有項目創建
   - 自動生成項目編號
   - 初始化項目狀態

2. **CQR 搜尋與篩選**
   - 多條件組合搜尋
   - 進階篩選器
   - 排序和分頁

3. **項目版本控制**
   - 修訂版本管理
   - 歷史記錄追蹤
   - 版本比較

**資料模型**:
```csharp
public class CQRHeader
{
    public int QueueKey { get; set; }
    public string ProjectNbr { get; set; }
    public int RevNbr { get; set; }
    public string Status { get; set; }
    public string CustNbr { get; set; }
    public string ProductDesc { get; set; }
    public DateTime CreatedDate { get; set; }
    public string OriginatorId { get; set; }
    // ... 更多屬性
}
```

#### 4.2.3 報價流程管理

**流程階段**:

1. **Description (項目描述)**
   - 基本項目資訊
   - 客戶需求描述
   - 產品規格定義
   - 初步可行性評估

2. **Background (背景資訊)**
   - 項目背景分析
   - 市場情況說明
   - 競爭對手資訊
   - 風險評估

3. **Engineering Assessment (工程評估)**
   - 技術可行性分析
   - 資源需求評估
   - 開發時程規劃
   - 技術風險識別

4. **Quote Response (報價回應)**
   - 成本計算
   - 利潤分析
   - 報價策略
   - Excel 報告生成

5. **Sales Closeout (銷售結案)**
   - 最終報價確認
   - 客戶回應處理
   - 項目結案作業
   - 經驗總結

**狀態流轉**:
```
創建 → 描述階段 → 背景分析 → 工程評估 → 報價回應 → 銷售審核 → GDPIM閘道1 → GDPIM閘道2 → 最終審批 → 銷售結案 → 項目結案
```

#### 4.2.4 GDPIM 閘道管理

**功能描述**: 兩階段閘道審批機制，確保項目品質和風險控制

**Gate 1 (第一階段閘道)**:
- 項目可行性審核
- 初步風險評估
- 資源分配確認
- 時程計劃審查

**Gate 2 (第二階段閘道)**:
- 詳細技術審查
- 最終成本確認
- 商業可行性驗證
- 最終決策點

**審批矩陣**:
| 項目價值 | Gate 1 審批者 | Gate 2 審批者 |
|----------|---------------|---------------|
| < $100K | EMGR | BMGR |
| $100K - $500K | BMGR | SDIR |
| > $500K | SDIR | VP |

#### 4.2.5 附件管理系統

**功能描述**: 支援多類型文件的上傳、管理和版本控制

**支援文件類型**:
- Excel (.xlsx, .xlsm)
- Word (.docx)
- PDF (.pdf)
- 圖片 (.jpg, .png, .gif)
- 壓縮檔 (.zip, .rar)

**核心功能**:
1. **文件上傳**
   - 拖拽上傳
   - 批次上傳
   - 進度顯示
   - 檔案驗證

2. **版本控制**
   - 自動版本編號
   - 版本歷史追蹤
   - 版本比較
   - 回滾功能

3. **權限管理**
   - 讀取權限
   - 編輯權限
   - 下載權限
   - 刪除權限

4. **在線預覽**
   - PDF 預覽
   - 圖片預覽
   - Excel 在線編輯 (AceOfFix 整合)

**API 端點**:
```
POST   /api/attachments/upload           # 上傳文件
GET    /api/attachments/{id}            # 獲取文件資訊
GET    /api/attachments/{id}/download   # 下載文件
DELETE /api/attachments/{id}            # 刪除文件
GET    /api/attachments/cqr/{cqrId}     # 獲取 CQR 相關文件
```

#### 4.2.6 Excel 報表系統

**功能描述**: 基於範本的動態 Excel 報表生成系統

**報表類型**:
1. **CQR 報價單**
   - 項目基本資訊
   - 成本明細
   - 利潤分析
   - 報價摘要

2. **工程評估報告**
   - 技術規格
   - 資源需求
   - 時程規劃
   - 風險評估

3. **管理報表**
   - 項目統計
   - 進度報告
   - 績效分析
   - 趨勢圖表

**技術實現**:
- ClosedXML: Excel 文件操作
- 範本引擎: 動態數據填充
- 圖表生成: 自動生成圖表
- 格式化: 自動格式設定

#### 4.2.7 工作流程引擎

**功能描述**: 可配置的工作流程管理系統

**工作流程特性**:
- **動態路由**: 根據條件自動分派任務
- **平行審批**: 支援多人同時審批
- **條件分支**: 基於業務規則的流程分支
- **時限控制**: 任務超時自動升級
- **通知機制**: 郵件和系統通知

**工作流程配置**:
```json
{
  "workflowId": "cqr-approval-process",
  "name": "CQR 審批流程",
  "steps": [
    {
      "id": "step1",
      "name": "業務經理審核",
      "assignee": "BMGR",
      "timeLimit": "2d",
      "conditions": {
        "value": {"$lt": 100000}
      }
    },
    {
      "id": "step2", 
      "name": "銷售總監審核",
      "assignee": "SDIR",
      "timeLimit": "3d",
      "conditions": {
        "value": {"$gte": 100000}
      }
    }
  ]
}
```

### 4.3 非功能性需求

#### 4.3.1 性能需求

| 指標 | 要求 |
|------|------|
| 響應時間 | 頁面載入 < 3秒，API 響應 < 500ms |
| 併發用戶 | 支援 100+ 併發用戶 |
| 資料處理 | 單次查詢處理 10,000+ 記錄 |
| 檔案上傳 | 支援單檔 50MB，批次 500MB |

#### 4.3.2 可用性需求

| 指標 | 要求 |
|------|------|
| 系統可用性 | 99.5% uptime |
| 備份策略 | 每日完整備份，實時增量備份 |
| 災難復原 | RTO < 4小時，RPO < 1小時 |
| 監控告警 | 24/7 系統監控，自動告警 |

#### 4.3.3 安全需求

| 類別 | 要求 |
|------|------|
| 身份驗證 | Azure AD 整合，多因子驗證 |
| 授權控制 | 角色基礎存取控制 (RBAC) |
| 資料加密 | 傳輸加密 (TLS 1.3)，敏感資料加密 |
| 稽核日誌 | 完整的用戶操作日誌 |
| 漏洞防護 | OWASP Top 10 防護，定期安全掃描 |

---

## 資料庫設計

### 5.1 資料庫概述

**資料庫管理系統**: Microsoft SQL Server 2019+
**字符編碼**: UTF-8
**索引策略**: 基於查詢模式優化
**分割策略**: 按年度分割大表

### 5.2 核心資料表

#### 5.2.1 CQR_Header (CQR 主表)

**表格描述**: 存儲 CQR 項目的基本資訊和狀態

```sql
CREATE TABLE [dbo].[CQR_Header] (
    [QueueKey] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [ProjectNbr] NVARCHAR(50) NOT NULL,
    [RevNbr] INT NOT NULL DEFAULT(0),
    [Status] NVARCHAR(20) NOT NULL DEFAULT('010100FR'),
    [StatusDesc] NVARCHAR(200),
    [QuoteType] INT NOT NULL DEFAULT(0), -- 0:客戶預CQR, 1:客戶RFQ, 2:閘道或內部, 3:ECR
    
    -- 客戶資訊
    [CustNbr] NVARCHAR(20),
    [CustName] NVARCHAR(200),
    [CustBuyerName] NVARCHAR(100),
    [CustContact] NVARCHAR(100),
    [CustEmail] NVARCHAR(200),
    [CustPhone] NVARCHAR(50),
    
    -- 產品資訊
    [ProductDesc] NVARCHAR(500),
    [Vehicle] NVARCHAR(100),
    [ModelYear] NVARCHAR(20),
    [Platform] NVARCHAR(100),
    [OEMPartNbr] NVARCHAR(100),
    [CustomerPartNbr] NVARCHAR(100),
    [TRWPartNbr] NVARCHAR(100),
    
    -- 數量與價值
    [VolumePerAnnum] DECIMAL(18,2),
    [LifetimeVolume] DECIMAL(18,2),
    [ApproxAnnualValue] DECIMAL(18,2),
    [LifetimeValue] DECIMAL(18,2),
    
    -- 重要日期
    [QuoteNeededDate] DATETIME2,
    [SOPDate] DATETIME2, -- Start of Production
    [EOPDate] DATETIME2, -- End of Production
    [RFQDate] DATETIME2,
    [CustomerResponseDate] DATETIME2,
    
    -- 里程碑日期
    [Milestone1Date] DATETIME2, -- Quote Response Target
    [Milestone2Date] DATETIME2, -- Customer Response
    [Milestone3Date] DATETIME2, -- Final Quote
    [Milestone4Date] DATETIME2, -- Customer Decision
    [Milestone5Date] DATETIME2, -- SOP
    [Milestone6Date] DATETIME2, -- EOP
    
    -- 人員分配
    [OriginatorId] NVARCHAR(50) NOT NULL,
    [OriginatorName] NVARCHAR(100),
    [AccountMgrId] NVARCHAR(50),
    [AccountMgrName] NVARCHAR(100),
    [SalesDirId] NVARCHAR(50),
    [SalesDirName] NVARCHAR(100),
    [CostEstimatorId] NVARCHAR(50),
    [CostEstimatorName] NVARCHAR(100),
    [EngineeringMgrId] NVARCHAR(50),
    [EngineeringMgrName] NVARCHAR(100),
    [BusinessMgrId] NVARCHAR(50),
    [BusinessMgrName] NVARCHAR(100),
    
    -- 報價資訊
    [InternalPrice] DECIMAL(18,4),
    [CustomerPrice] DECIMAL(18,4),
    [ProfitMargin] DECIMAL(18,4),
    [ProfitPercentage] DECIMAL(5,2),
    
    -- 備註欄位
    [ProjectScope] NTEXT,
    [BackgroundInfo] NTEXT,
    [TechnicalNotes] NTEXT,
    [CommercialNotes] NTEXT,
    [InternalComments] NTEXT,
    [CustomerComments] NTEXT,
    
    -- 審核資訊
    [Gate1Status] NVARCHAR(20),
    [Gate1Date] DATETIME2,
    [Gate1Comments] NTEXT,
    [Gate2Status] NVARCHAR(20),
    [Gate2Date] DATETIME2,
    [Gate2Comments] NTEXT,
    
    -- 系統欄位
    [CreatedDate] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
    [CreatedBy] NVARCHAR(50) NOT NULL,
    [ModifiedDate] DATETIME2,
    [ModifiedBy] NVARCHAR(50),
    [IsActive] BIT NOT NULL DEFAULT(1),
    [IsLocked] BIT NOT NULL DEFAULT(0),
    [LockedBy] NVARCHAR(50),
    [LockedDate] DATETIME2,
    [Version] ROWVERSION
);

-- 索引
CREATE UNIQUE INDEX IX_CQR_Header_ProjectRev ON CQR_Header (ProjectNbr, RevNbr);
CREATE INDEX IX_CQR_Header_Status ON CQR_Header (Status);
CREATE INDEX IX_CQR_Header_Customer ON CQR_Header (CustNbr);
CREATE INDEX IX_CQR_Header_Originator ON CQR_Header (OriginatorId);
CREATE INDEX IX_CQR_Header_CreatedDate ON CQR_Header (CreatedDate);
```

#### 5.2.2 ATTDIR_AttachFileDirHeader (附件管理表)

**表格描述**: 管理 CQR 相關的所有附件和文檔

```sql
CREATE TABLE [dbo].[ATTDIR_AttachFileDirHeader] (
    [AttachKey] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [QueueKey] INT NOT NULL,
    [FileName] NVARCHAR(255) NOT NULL,
    [OriginalFileName] NVARCHAR(255) NOT NULL,
    [FileExtension] NVARCHAR(10) NOT NULL,
    [FileSize] BIGINT NOT NULL,
    [FilePath] NVARCHAR(500) NOT NULL,
    [FileType] NVARCHAR(50), -- Document, Spreadsheet, Image, etc.
    [MimeType] NVARCHAR(100),
    
    -- 版本控制
    [Version] INT NOT NULL DEFAULT(1),
    [IsLatestVersion] BIT NOT NULL DEFAULT(1),
    [ParentAttachKey] INT, -- 指向原始版本
    
    -- 分類資訊
    [Category] NVARCHAR(50), -- Quote, Engineering, Sales, etc.
    [SubCategory] NVARCHAR(50),
    [Description] NVARCHAR(500),
    [Tags] NVARCHAR(200),
    
    -- 權限控制
    [AccessLevel] INT NOT NULL DEFAULT(1), -- 1:Public, 2:Restricted, 3:Confidential
    [CreatedBy] NVARCHAR(50) NOT NULL,
    [CreatedDate] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
    [ModifiedBy] NVARCHAR(50),
    [ModifiedDate] DATETIME2,
    
    -- 狀態
    [IsActive] BIT NOT NULL DEFAULT(1),
    [IsCheckedOut] BIT NOT NULL DEFAULT(0),
    [CheckedOutBy] NVARCHAR(50),
    [CheckedOutDate] DATETIME2,
    
    CONSTRAINT FK_AttachFile_CQR FOREIGN KEY (QueueKey) REFERENCES CQR_Header(QueueKey),
    CONSTRAINT FK_AttachFile_Parent FOREIGN KEY (ParentAttachKey) REFERENCES ATTDIR_AttachFileDirHeader(AttachKey)
);

-- 索引
CREATE INDEX IX_AttachFile_QueueKey ON ATTDIR_AttachFileDirHeader (QueueKey);
CREATE INDEX IX_AttachFile_CreatedDate ON ATTDIR_AttachFileDirHeader (CreatedDate);
CREATE INDEX IX_AttachFile_Category ON ATTDIR_AttachFileDirHeader (Category);
```

#### 5.2.3 USERPROF_UserProfileHeader (用戶資料表)

**表格描述**: 存儲系統用戶的基本資訊和設定

```sql
CREATE TABLE [dbo].[USERPROF_UserProfileHeader] (
    [UserKey] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [UserId] NVARCHAR(50) NOT NULL UNIQUE,
    [UserName] NVARCHAR(100) NOT NULL,
    [Email] NVARCHAR(200) NOT NULL,
    [FirstName] NVARCHAR(50),
    [LastName] NVARCHAR(50),
    [DisplayName] NVARCHAR(100),
    
    -- 組織資訊
    [Department] NVARCHAR(100),
    [Title] NVARCHAR(100),
    [Location] NVARCHAR(100),
    [ManagerId] NVARCHAR(50),
    [CostCenter] NVARCHAR(50),
    
    -- 聯絡資訊
    [Phone] NVARCHAR(50),
    [Mobile] NVARCHAR(50),
    [OfficeLocation] NVARCHAR(100),
    
    -- 系統設定
    [TimeZone] NVARCHAR(50) DEFAULT('UTC'),
    [Language] NVARCHAR(10) DEFAULT('en-US'),
    [DateFormat] NVARCHAR(20) DEFAULT('MM/dd/yyyy'),
    [NumberFormat] NVARCHAR(20) DEFAULT('en-US'),
    
    -- 權限相關
    [IsActive] BIT NOT NULL DEFAULT(1),
    [IsLocked] BIT NOT NULL DEFAULT(0),
    [LastLoginDate] DATETIME2,
    [PasswordLastChanged] DATETIME2,
    [FailedLoginAttempts] INT DEFAULT(0),
    
    -- 系統欄位
    [CreatedDate] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
    [CreatedBy] NVARCHAR(50),
    [ModifiedDate] DATETIME2,
    [ModifiedBy] NVARCHAR(50),
    [Version] ROWVERSION
);

-- 索引
CREATE UNIQUE INDEX IX_UserProfile_UserId ON USERPROF_UserProfileHeader (UserId);
CREATE INDEX IX_UserProfile_Email ON USERPROF_UserProfileHeader (Email);
CREATE INDEX IX_UserProfile_Department ON USERPROF_UserProfileHeader (Department);
```

#### 5.2.4 USERPROF_UserRoles (用戶角色表)

**表格描述**: 管理用戶角色和權限分配

```sql
CREATE TABLE [dbo].[USERPROF_UserRoles] (
    [UserRoleKey] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [UserKey] INT NOT NULL,
    [RoleCode] NVARCHAR(20) NOT NULL, -- AMGR, SDIR, CEST, EMGR, etc.
    [RoleName] NVARCHAR(100) NOT NULL,
    [RoleDescription] NVARCHAR(500),
    
    -- 權限範圍
    [Scope] NVARCHAR(50) DEFAULT('Global'), -- Global, Department, Project
    [ScopeValue] NVARCHAR(100), -- 範圍值 (部門名稱、專案編號等)
    
    -- 有效期
    [EffectiveDate] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
    [ExpirationDate] DATETIME2,
    [IsActive] BIT NOT NULL DEFAULT(1),
    
    -- 系統欄位
    [CreatedDate] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
    [CreatedBy] NVARCHAR(50) NOT NULL,
    [ModifiedDate] DATETIME2,
    [ModifiedBy] NVARCHAR(50),
    
    CONSTRAINT FK_UserRole_User FOREIGN KEY (UserKey) REFERENCES USERPROF_UserProfileHeader(UserKey)
);

-- 索引
CREATE INDEX IX_UserRole_UserKey ON USERPROF_UserRoles (UserKey);
CREATE INDEX IX_UserRole_RoleCode ON USERPROF_UserRoles (RoleCode);
CREATE UNIQUE INDEX IX_UserRole_UserRole ON USERPROF_UserRoles (UserKey, RoleCode, Scope, ScopeValue);
```

#### 5.2.5 ROUTING_RGHeader (路由管理表)

**表格描述**: 定義工作流程的路由規則和審批流程

```sql
CREATE TABLE [dbo].[ROUTING_RGHeader] (
    [RoutingKey] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [RoutingName] NVARCHAR(100) NOT NULL,
    [RoutingType] NVARCHAR(50) NOT NULL, -- Approval, Notification, Task
    [Description] NVARCHAR(500),
    
    -- 觸發條件
    [TriggerEvent] NVARCHAR(50) NOT NULL, -- StatusChange, ValueThreshold, DateBased
    [TriggerCondition] NTEXT, -- JSON 格式的條件定義
    
    -- 路由配置
    [AssigneeType] NVARCHAR(20) NOT NULL, -- Role, User, Manager
    [AssigneeValue] NVARCHAR(100) NOT NULL,
    [Priority] INT DEFAULT(1),
    [TimeLimit] INT, -- 時限 (小時)
    [EscalationRule] NTEXT, -- JSON 格式的升級規則
    
    -- 通知設定
    [NotificationEnabled] BIT DEFAULT(1),
    [NotificationTemplate] NVARCHAR(100),
    [ReminderInterval] INT, -- 提醒間隔 (小時)
    
    -- 狀態
    [IsActive] BIT NOT NULL DEFAULT(1),
    [EffectiveDate] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
    [ExpirationDate] DATETIME2,
    
    -- 系統欄位
    [CreatedDate] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
    [CreatedBy] NVARCHAR(50) NOT NULL,
    [ModifiedDate] DATETIME2,
    [ModifiedBy] NVARCHAR(50),
    [Version] ROWVERSION
);

-- 索引
CREATE INDEX IX_Routing_Type ON ROUTING_RGHeader (RoutingType);
CREATE INDEX IX_Routing_Event ON ROUTING_RGHeader (TriggerEvent);
CREATE INDEX IX_Routing_Active ON ROUTING_RGHeader (IsActive, EffectiveDate, ExpirationDate);
```

### 5.3 資料關聯圖

```mermaid
erDiagram
    CQR_Header ||--o{ ATTDIR_AttachFileDirHeader : "has"
    CQR_Header ||--o{ CQR_IHS : "contains"
    CQR_Header ||--o{ CQR_Antares : "references"
    CQR_Header ||--o{ TRS_Header : "generates"
    
    USERPROF_UserProfileHeader ||--o{ USERPROF_UserRoles : "has"
    USERPROF_UserProfileHeader ||--o{ CQR_Header : "creates/owns"
    USERPROF_UserProfileHeader ||--o{ ATTDIR_AttachFileDirHeader : "uploads"
    
    ROUTING_RGHeader ||--o{ TRS_Header : "defines"
    
    CQR_Header {
        int QueueKey PK
        string ProjectNbr
        int RevNbr
        string Status
        string CustNbr
        string ProductDesc
        datetime CreatedDate
        string OriginatorId FK
    }
    
    ATTDIR_AttachFileDirHeader {
        int AttachKey PK
        int QueueKey FK
        string FileName
        string FilePath
        int Version
        datetime CreatedDate
        string CreatedBy FK
    }
    
    USERPROF_UserProfileHeader {
        int UserKey PK
        string UserId
        string UserName
        string Email
        string Department
        bit IsActive
    }
    
    USERPROF_UserRoles {
        int UserRoleKey PK
        int UserKey FK
        string RoleCode
        string RoleName
        bit IsActive
    }
```

### 5.4 資料庫優化策略

#### 5.4.1 索引策略

**主要索引**:
- 所有主鍵自動建立聚集索引
- 外鍵欄位建立非聚集索引
- 常用查詢欄位建立複合索引

**查詢優化索引**:
```sql
-- CQR 搜尋優化
CREATE INDEX IX_CQR_Search ON CQR_Header (Status, CustNbr, OriginatorId, CreatedDate);

-- 附件查詢優化  
CREATE INDEX IX_Attach_Query ON ATTDIR_AttachFileDirHeader (QueueKey, Category, IsActive, CreatedDate);

-- 用戶權限查詢優化
CREATE INDEX IX_UserRole_Auth ON USERPROF_UserRoles (UserKey, RoleCode, IsActive, EffectiveDate, ExpirationDate);
```

#### 5.4.2 分割策略

**按年度分割大表**:
```sql
-- CQR_Header 按年度分割
CREATE PARTITION FUNCTION PF_CQRByYear (DATETIME2)
AS RANGE RIGHT FOR VALUES ('2023-01-01', '2024-01-01', '2025-01-01');

CREATE PARTITION SCHEME PS_CQRByYear
AS PARTITION PF_CQRByYear
TO (FG_CQR_2022, FG_CQR_2023, FG_CQR_2024, FG_CQR_2025);
```

#### 5.4.3 效能監控

**監控指標**:
- 查詢執行時間
- 索引使用率
- 死鎖統計
- 等待統計

**自動統計更新**:
```sql
-- 開啟自動統計更新
ALTER DATABASE CQRDatabase SET AUTO_UPDATE_STATISTICS ON;
ALTER DATABASE CQRDatabase SET AUTO_UPDATE_STATISTICS_ASYNC ON;
```

---

## API 規格

### 6.1 API 設計原則

#### 6.1.1 RESTful 設計

**資源導向設計**:
- 使用名詞表示資源 (`/api/cqrs`, `/api/users`)
- 使用 HTTP 動詞表示操作 (GET, POST, PUT, DELETE)
- 使用 HTTP 狀態碼表示結果

**URL 命名規範**:
```
GET    /api/cqrs                    # 獲取 CQR 列表
GET    /api/cqrs/{id}               # 獲取單一 CQR
POST   /api/cqrs                    # 創建新 CQR
PUT    /api/cqrs/{id}               # 更新 CQR
DELETE /api/cqrs/{id}               # 刪除 CQR
GET    /api/cqrs/{id}/attachments   # 獲取 CQR 的附件列表
```

#### 6.1.2 統一響應格式

**成功響應**:
```json
{
  "success": true,
  "data": {
    // 實際數據
  },
  "message": "操作成功",
  "timestamp": "2025-07-22T10:30:00Z",
  "traceId": "abc123-def456"
}
```

**錯誤響應**:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "請求參數驗證失敗",
    "details": [
      {
        "field": "projectNbr",
        "message": "項目編號不能為空"
      }
    ]
  },
  "timestamp": "2025-07-22T10:30:00Z",
  "traceId": "abc123-def456"
}
```

#### 6.1.3 分頁響應格式

```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalItems": 150,
      "totalPages": 8,
      "hasNextPage": true,
      "hasPreviousPage": false
    }
  }
}
```

### 6.2 認證與授權 API

#### 6.2.1 用戶認證

**POST /api/auth/login**

請求:
```json
{
  "username": "<EMAIL>",
  "password": "password123"
}
```

響應:
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "dGhpc2lzYXJlZnJlc2h0b2tlbg...",
    "expiresIn": 3600,
    "tokenType": "Bearer",
    "user": {
      "userId": "john.doe",
      "userName": "John Doe",
      "email": "<EMAIL>",
      "roles": ["AMGR", "USER"]
    }
  }
}
```

**POST /api/auth/refresh**

請求:
```json
{
  "refreshToken": "dGhpc2lzYXJlZnJlc2h0b2tlbg..."
}
```

**POST /api/auth/logout**

請求標頭:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

#### 6.2.2 用戶資料

**GET /api/auth/profile**

響應:
```json
{
  "success": true,
  "data": {
    "userId": "john.doe",
    "userName": "John Doe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "department": "Sales",
    "title": "Account Manager",
    "roles": [
      {
        "roleCode": "AMGR",
        "roleName": "Account Manager",
        "scope": "Global"
      }
    ],
    "permissions": [
      "CQR_CREATE",
      "CQR_READ",
      "CQR_UPDATE"
    ]
  }
}
```

### 6.3 CQR 管理 API

#### 6.3.1 CQR CRUD 操作

**GET /api/cqrs**

查詢參數:
```
?page=1&pageSize=20&status=010100FR&custNbr=CUST001&projectNbr=PRJ&sortBy=createdDate&sortOrder=desc
```

響應:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "queueKey": 12345,
        "projectNbr": "PRJ-2025-001",
        "revNbr": 0,
        "status": "010100FR",
        "statusDesc": "正在初始化",
        "quoteType": 1,
        "custNbr": "CUST001",
        "custName": "ABC Company Ltd.",
        "productDesc": "Automotive Safety System",
        "volumePerAnnum": 50000,
        "approxAnnualValue": 2500000.00,
        "quoteNeededDate": "2025-08-15T00:00:00Z",
        "sopDate": "2026-01-01T00:00:00Z",
        "originatorId": "john.doe",
        "originatorName": "John Doe",
        "createdDate": "2025-07-22T10:30:00Z",
        "modifiedDate": "2025-07-22T15:45:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalItems": 150,
      "totalPages": 8
    }
  }
}
```

**GET /api/cqrs/{id}**

響應:
```json
{
  "success": true,
  "data": {
    "queueKey": 12345,
    "projectNbr": "PRJ-2025-001",
    "revNbr": 0,
    "status": "010100FR",
    "statusDesc": "正在初始化",
    "quoteType": 1,
    
    // 客戶資訊
    "customer": {
      "custNbr": "CUST001",
      "custName": "ABC Company Ltd.",
      "buyerName": "Jane Smith",
      "contact": "<EMAIL>",
      "email": "<EMAIL>",
      "phone": "******-0123"
    },
    
    // 產品資訊
    "product": {
      "description": "Automotive Safety System",
      "vehicle": "Model X SUV",
      "modelYear": "2026",
      "platform": "Electric Vehicle Platform",
      "oemPartNbr": "OEM-12345",
      "customerPartNbr": "CUST-ABC-001",
      "trwPartNbr": "TRW-SS-2025-001"
    },
    
    // 數量與價值
    "business": {
      "volumePerAnnum": 50000,
      "lifetimeVolume": 250000,
      "approxAnnualValue": 2500000.00,
      "lifetimeValue": 12500000.00
    },
    
    // 重要日期
    "dates": {
      "quoteNeededDate": "2025-08-15T00:00:00Z",
      "sopDate": "2026-01-01T00:00:00Z",
      "eopDate": "2030-12-31T00:00:00Z",
      "rfqDate": "2025-07-20T00:00:00Z",
      "customerResponseDate": "2025-09-01T00:00:00Z"
    },
    
    // 里程碑
    "milestones": {
      "milestone1Date": "2025-08-15T00:00:00Z",
      "milestone2Date": "2025-09-01T00:00:00Z",
      "milestone3Date": "2025-09-15T00:00:00Z",
      "milestone4Date": "2025-10-01T00:00:00Z",
      "milestone5Date": "2026-01-01T00:00:00Z",
      "milestone6Date": "2030-12-31T00:00:00Z"
    },
    
    // 人員分配
    "team": {
      "originator": {
        "userId": "john.doe",
        "userName": "John Doe"
      },
      "accountManager": {
        "userId": "jane.smith",
        "userName": "Jane Smith"
      },
      "salesDirector": {
        "userId": "bob.johnson",
        "userName": "Bob Johnson"
      },
      "costEstimator": {
        "userId": "alice.brown",
        "userName": "Alice Brown"
      },
      "engineeringManager": {
        "userId": "tom.wilson",
        "userName": "Tom Wilson"
      }
    },
    
    // 報價資訊
    "quote": {
      "internalPrice": 50.0000,
      "customerPrice": 65.0000,
      "profitMargin": 15.0000,
      "profitPercentage": 23.08
    },
    
    // 備註
    "notes": {
      "projectScope": "Complete safety system including sensors and control unit",
      "backgroundInfo": "Customer seeking competitive solution for new EV platform",
      "technicalNotes": "Integration with existing vehicle systems required",
      "commercialNotes": "Price sensitive project, focus on value proposition",
      "internalComments": "Strategic customer, important for market expansion",
      "customerComments": "Quality and reliability are top priorities"
    },
    
    // 閘道資訊
    "gates": {
      "gate1": {
        "status": "Pending",
        "date": null,
        "comments": ""
      },
      "gate2": {
        "status": "Not Started",
        "date": null,
        "comments": ""
      }
    },
    
    // 系統資訊
    "metadata": {
      "createdDate": "2025-07-22T10:30:00Z",
      "createdBy": "john.doe",
      "modifiedDate": "2025-07-22T15:45:00Z",
      "modifiedBy": "john.doe",
      "isActive": true,
      "isLocked": false,
      "lockedBy": null,
      "lockedDate": null
    }
  }
}
```

**POST /api/cqrs**

請求:
```json
{
  "projectNbr": "PRJ-2025-002",
  "quoteType": 1,
  "customer": {
    "custNbr": "CUST002",
    "custName": "XYZ Corporation",
    "buyerName": "Mike Davis",
    "email": "<EMAIL>"
  },
  "product": {
    "description": "Engine Control Module",
    "vehicle": "Compact Car",
    "modelYear": "2026"
  },
  "business": {
    "volumePerAnnum": 25000,
    "approxAnnualValue": 1250000.00
  },
  "dates": {
    "quoteNeededDate": "2025-08-30T00:00:00Z",
    "sopDate": "2026-03-01T00:00:00Z"
  },
  "team": {
    "accountManagerId": "jane.smith",
    "salesDirectorId": "bob.johnson"
  }
}
```

**PUT /api/cqrs/{id}**

請求體同 POST，但包含所有需要更新的欄位。

#### 6.3.2 CQR 狀態管理

**PUT /api/cqrs/{id}/status**

請求:
```json
{
  "newStatus": "020100FR",
  "comments": "Description phase completed, moving to Background phase"
}
```

**GET /api/cqrs/{id}/history**

響應:
```json
{
  "success": true,
  "data": [
    {
      "changeId": 1,
      "changeDate": "2025-07-22T10:30:00Z",
      "changedBy": "john.doe",
      "changeType": "StatusChange",
      "oldValue": "010100FR",
      "newValue": "020100FR",
      "comments": "Description phase completed"
    }
  ]
}
```

#### 6.3.3 CQR 複製與版本控制

**POST /api/cqrs/{id}/copy**

請求:
```json
{
  "newProjectNbr": "PRJ-2025-003",
  "copyAttachments": true,
  "copyTeam": false
}
```

**POST /api/cqrs/{id}/revise**

請求:
```json
{
  "revisionReason": "Customer requested specification changes"
}
```

### 6.4 附件管理 API

#### 6.4.1 文件上傳

**POST /api/attachments/upload**

請求 (multipart/form-data):
```
Content-Type: multipart/form-data

file: [binary file data]
queueKey: 12345
category: Quote
description: Initial quote response
```

響應:
```json
{
  "success": true,
  "data": {
    "attachKey": 67890,
    "fileName": "quote_response_v1.xlsx",
    "originalFileName": "Quote Response Template.xlsx",
    "fileSize": 1048576,
    "fileType": "Spreadsheet",
    "version": 1,
    "uploadUrl": "/api/attachments/67890/download"
  }
}
```

#### 6.4.2 文件管理

**GET /api/attachments/cqr/{cqrId}**

響應:
```json
{
  "success": true,
  "data": [
    {
      "attachKey": 67890,
      "fileName": "quote_response_v1.xlsx",
      "originalFileName": "Quote Response Template.xlsx",
      "fileSize": 1048576,
      "fileType": "Spreadsheet",
      "category": "Quote",
      "version": 1,
      "isLatestVersion": true,
      "createdBy": "john.doe",
      "createdDate": "2025-07-22T11:00:00Z",
      "downloadUrl": "/api/attachments/67890/download",
      "previewUrl": "/api/attachments/67890/preview"
    }
  ]
}
```

**GET /api/attachments/{id}/download**

響應標頭:
```
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="quote_response_v1.xlsx"
Content-Length: 1048576
```

#### 6.4.3 版本控制

**POST /api/attachments/{id}/new-version**

請求 (multipart/form-data):
```
file: [binary file data]
comments: Updated pricing based on customer feedback
```

**GET /api/attachments/{id}/versions**

響應:
```json
{
  "success": true,
  "data": [
    {
      "attachKey": 67890,
      "version": 1,
      "fileName": "quote_response_v1.xlsx",
      "fileSize": 1048576,
      "createdBy": "john.doe",
      "createdDate": "2025-07-22T11:00:00Z",
      "isLatestVersion": false
    },
    {
      "attachKey": 67891,
      "version": 2,
      "fileName": "quote_response_v2.xlsx",
      "fileSize": 1124864,
      "createdBy": "jane.smith",
      "createdDate": "2025-07-23T09:15:00Z",
      "isLatestVersion": true
    }
  ]
}
```

### 6.5 搜尋與報表 API

#### 6.5.1 進階搜尋

**POST /api/cqrs/search**

請求:
```json
{
  "filters": {
    "status": ["010100FR", "020100FR"],
    "quoteType": [1, 2],
    "custNbr": "CUST001",
    "productDesc": "safety",
    "volumeRange": {
      "min": 10000,
      "max": 100000
    },
    "valueRange": {
      "min": 500000,
      "max": 5000000
    },
    "dateRange": {
      "field": "createdDate",
      "start": "2025-01-01T00:00:00Z",
      "end": "2025-12-31T23:59:59Z"
    },
    "team": {
      "originatorId": "john.doe",
      "accountManagerId": "jane.smith"
    }
  },
  "sorting": {
    "field": "createdDate",
    "order": "desc"
  },
  "pagination": {
    "page": 1,
    "pageSize": 50
  }
}
```

#### 6.5.2 統計與分析

**GET /api/reports/dashboard**

響應:
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalCQRs": 1250,
      "activeCQRs": 89,
      "completedCQRs": 1161,
      "totalValue": *********.00,
      "averageValue": 100000.00
    },
    "statusDistribution": [
      {
        "status": "010100FR",
        "count": 15,
        "percentage": 16.85
      },
      {
        "status": "020100FR", 
        "count": 23,
        "percentage": 25.84
      }
    ],
    "monthlyTrend": [
      {
        "month": "2025-01",
        "newCQRs": 12,
        "completedCQRs": 18,
        "totalValue": 2500000.00
      }
    ],
    "topCustomers": [
      {
        "custNbr": "CUST001",
        "custName": "ABC Company",
        "count": 25,
        "totalValue": ********.00
      }
    ]
  }
}
```

**GET /api/reports/export/excel**

查詢參數:
```
?format=xlsx&reportType=cqr-list&filters={"status":["010100FR"]}&dateRange=2025-01-01,2025-12-31
```

響應標頭:
```
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="CQR_Report_20250722.xlsx"
```

### 6.6 工作流程 API

#### 6.6.1 任務管理

**GET /api/workflow/tasks**

響應:
```json
{
  "success": true,
  "data": [
    {
      "taskId": "T-12345",
      "queueKey": 12345,
      "projectNbr": "PRJ-2025-001",
      "taskType": "Approval",
      "taskName": "Business Manager Review",
      "assignedTo": "bob.johnson",
      "assignedDate": "2025-07-22T10:00:00Z",
      "dueDate": "2025-07-24T18:00:00Z",
      "priority": "High",
      "status": "Pending",
      "description": "Review project scope and approve to next phase"
    }
  ]
}
```

**PUT /api/workflow/tasks/{taskId}/complete**

請求:
```json
{
  "decision": "Approved",
  "comments": "Project scope is clear and feasible. Approved to proceed to Engineering phase.",
  "nextAssignee": "tom.wilson"
}
```

#### 6.6.2 工作流程配置

**GET /api/workflow/definitions**

**POST /api/workflow/definitions**

請求:
```json
{
  "workflowName": "CQR Approval Process v2",
  "description": "Updated CQR approval workflow with parallel review steps",
  "steps": [
    {
      "stepId": "step1",
      "stepName": "Business Manager Review",
      "assigneeType": "Role",
      "assigneeValue": "BMGR",
      "conditions": {
        "value": {"$lt": 500000}
      },
      "timeLimit": 48,
      "isParallel": false
    },
    {
      "stepId": "step2",
      "stepName": "Sales Director Review", 
      "assigneeType": "Role",
      "assigneeValue": "SDIR",
      "conditions": {
        "value": {"$gte": 500000}
      },
      "timeLimit": 72,
      "isParallel": false
    }
  ]
}
```

### 6.7 錯誤處理

#### 6.7.1 HTTP 狀態碼

| 狀態碼 | 說明 | 使用場景 |
|--------|------|----------|
| 200 | OK | 成功響應 |
| 201 | Created | 資源創建成功 |
| 204 | No Content | 刪除成功，無返回內容 |
| 400 | Bad Request | 請求參數錯誤 |
| 401 | Unauthorized | 未認證或 Token 無效 |
| 403 | Forbidden | 權限不足 |
| 404 | Not Found | 資源不存在 |
| 409 | Conflict | 資源衝突（如重複創建） |
| 422 | Unprocessable Entity | 業務邏輯驗證失敗 |
| 500 | Internal Server Error | 服務器內部錯誤 |

#### 6.7.2 錯誤代碼定義

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "請求參數驗證失敗",
    "details": [
      {
        "field": "projectNbr",
        "code": "REQUIRED",
        "message": "項目編號不能為空"
      },
      {
        "field": "volumePerAnnum",
        "code": "MIN_VALUE",
        "message": "年產量必須大於0"
      }
    ]
  }
}
```

**常見錯誤代碼**:
- `VALIDATION_ERROR`: 參數驗證錯誤
- `RESOURCE_NOT_FOUND`: 資源不存在
- `DUPLICATE_RESOURCE`: 資源重複
- `PERMISSION_DENIED`: 權限不足
- `BUSINESS_RULE_VIOLATION`: 業務規則違反
- `WORKFLOW_ERROR`: 工作流程錯誤
- `FILE_UPLOAD_ERROR`: 文件上傳錯誤

### 6.8 API 文檔與測試

#### 6.8.1 Swagger 配置

**Swagger UI 地址**: `https://api.cqr.company.com/swagger`

**OpenAPI 規範文檔**: `https://api.cqr.company.com/swagger/v1/swagger.json`

#### 6.8.2 API 版本控制

**URL 版本控制**:
```
/api/v1/cqrs      # 版本 1.0
/api/v2/cqrs      # 版本 2.0 (新功能)
```

**標頭版本控制**:
```
Accept: application/json; version=1.0
```

#### 6.8.3 速率限制

**限制策略**:
- 每個 IP 每分鐘最多 100 個請求
- 每個用戶每分鐘最多 500 個請求
- 文件上傳每小時最多 50 個文件

**響應標頭**:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 85
X-RateLimit-Reset: 1658483400
```

---

繼續撰寫剩餘的規格書內容...