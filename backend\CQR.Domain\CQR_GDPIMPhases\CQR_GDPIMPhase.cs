﻿using System.ComponentModel.DataAnnotations;

namespace CQR.Domain.CQR_GDPIMPhases;

public class CQR_GDPIMPhase //: BaseEntity<int>
{
    [Required]
    [StringLength(10)]
    public int? QueueKey { get; set; }

    [Key]
    [Required]
    [StringLength(10)]
    public int GDPIMPhaseId { get; set; }

    [StringLength(10)]
    public int? GDPIMPhase { get; set; }

    [StringLength(50)]
    public string? Category { get; set; }

    public DateTime? ReleaseDate { get; set; }

    [StringLength(50)]
    public string? Volume1 { get; set; }

    [StringLength(50)]
    public string? Volume2 { get; set; }

    [StringLength(50)]
    public string? Volume3 { get; set; }

    [StringLength(50)]
    public string? Volume4 { get; set; }

    [StringLength(50)]
    public string? Volume5 { get; set; }

    [StringLength(50)]
    public string? Volume6 { get; set; }

    [StringLength(50)]
    public string? Volume7 { get; set; }

    [StringLength(50)]
    public string? SellPrice1 { get; set; }

    [StringLength(50)]
    public string? SellPrice2 { get; set; }

    [StringLength(50)]
    public string? SellPrice3 { get; set; }

    [StringLength(50)]
    public string? SellPrice4 { get; set; }

    [StringLength(50)]
    public string? SellPrice5 { get; set; }

    [StringLength(50)]
    public string? SellPrice6 { get; set; }

    [StringLength(50)]
    public string? SellPrice7 { get; set; }

    [StringLength(50)]
    public string? SellPrice8 { get; set; }

    [StringLength(50)]
    public string? SellPrice9 { get; set; }

    [StringLength(50)]
    public string? SellPrice10 { get; set; }

    [StringLength(8)]
    public string? ApprovalUser1 { get; set; }

    [StringLength(8)]
    public string? ApprovalUser2 { get; set; }

    [StringLength(8)]
    public string? ApprovalUser3 { get; set; }

    [StringLength(8)]
    public string? ApprovalUser4 { get; set; }

    [StringLength(8)]
    public string? ApprovalUser5 { get; set; }

    [StringLength(8)]
    public string? ApprovalUser6 { get; set; }

    [StringLength(8)]
    public string? ApprovalUser7 { get; set; }

    [StringLength(50)]
    public string? ApprovalUser8 { get; set; }

    [StringLength(50)]
    public string? ApprovalUser9 { get; set; }

    [StringLength(50)]
    public string? ApprovalUser10 { get; set; }

    [StringLength(50)]
    public string? ApprovalUser11 { get; set; }

    [StringLength(50)]
    public string? ApprovalUser12 { get; set; }

    public DateTime? ApprovalDate1 { get; set; }
    public DateTime? ApprovalDate2 { get; set; }
    public DateTime? ApprovalDate3 { get; set; }
    public DateTime? ApprovalDate4 { get; set; }
    public DateTime? ApprovalDate5 { get; set; }
    public DateTime? ApprovalDate6 { get; set; }
    public DateTime? ApprovalDate7 { get; set; }

    [StringLength(50)]
    public string? ApprovalDate8 { get; set; }

    [StringLength(50)]
    public string? ApprovalDate9 { get; set; }

    [StringLength(50)]
    public string? ApprovalDate10 { get; set; }

    [StringLength(50)]
    public string? ApprovalDate11 { get; set; }

    [StringLength(50)]
    public string? ApprovalDate12 { get; set; }

    [StringLength(1)]
    public string? ApprovalState1 { get; set; }

    [StringLength(1)]
    public string? ApprovalState2 { get; set; }

    [StringLength(1)]
    public string? ApprovalState3 { get; set; }

    [StringLength(1)]
    public string? ApprovalState4 { get; set; }

    [StringLength(1)]
    public string? ApprovalState5 { get; set; }

    [StringLength(1)]
    public string? ApprovalState6 { get; set; }

    [StringLength(1)]
    public string? ApprovalState7 { get; set; }

    [StringLength(1)]
    public string? ApprovalState8 { get; set; }

    [StringLength(1)]
    public string? ApprovalState9 { get; set; }

    [StringLength(1)]
    public string? ApprovalState10 { get; set; }

    [StringLength(1)]
    public string? ApprovalState11 { get; set; }

    [StringLength(1)]
    public string? ApprovalState12 { get; set; }

    [StringLength(1)]
    public string? RequiredState1 { get; set; }

    [StringLength(1)]
    public string? RequiredState2 { get; set; }

    [StringLength(1)]
    public string? RequiredState3 { get; set; }

    [StringLength(1)]
    public string? RequiredState4 { get; set; }

    [StringLength(1)]
    public string? RequiredState5 { get; set; }

    [StringLength(1)]
    public string? RequiredState6 { get; set; }

    [StringLength(1)]
    public string? RequiredState7 { get; set; }

    [StringLength(1)]
    public string? RequiredState8 { get; set; }

    [StringLength(1)]
    public string? RequiredState9 { get; set; }

    [StringLength(1)]
    public string? RequiredState10 { get; set; }

    [StringLength(1)]
    public string? RequiredState11 { get; set; }

    [StringLength(1)]
    public string? RequiredState12 { get; set; }

    public string? Comment { get; set; }

    public static implicit operator Task<object>(CQR_GDPIMPhase v)
    {
        throw new NotImplementedException();
    }
}