﻿using CQR.Domain.Primitives;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CQR.Domain.RoutingRGHeaders;

[Table("ROUTING_RGHeader")]
public class ROUTING_RGHeader : BaseEntity<int>
{
    [Key]
    [Column("QueueKey")] // 改成資料表實際主鍵欄位名稱
    public override int Id { get; protected set; }

    //public int QueueKey { get; private set; } // 主鍵 ID

    public string? FolderType { get; set; }
    public string? FolderNbr { get; set; }
    public string? TaskCode { get; set; }
    public string? PartDwgNbr { get; set; }
    public string? ActionTaskCode { get; set; }
    public string? RoutingTaskCode { get; set; }
    public string? AssignedTo { get; set; }
    public string? SortDate { get; set; }
    public string? SortTime { get; set; }
    public string? AssignedDate { get; set; }
    public string? AsignedTime { get; set; }
    public string? DueDate { get; set; }
    public string? DoneInd { get; set; }
    public string? Result { get; set; }
    public string? DoneDate { get; set; }
    public string? DoneByName { get; set; }
    public string? CurrentTaskInd { get; set; }
    public string? ToBeViewedInd { get; set; }
    public string? NotificationInd { get; set; }
    public string? DoneTm { get; set; }
    public string? EMailInd { get; set; }
    public string? Comment { get; set; }
    public int? FolderNbr_int { get; set; }

    // Computed column (你可以用只讀屬性或在 EF Core 設定 Ignore/Computed)
    //public int? FolderNbrInt => int.TryParse(FolderNbr, out var result) ? result : null;
}