﻿using CQR.Application.Dto.CQRHead;

namespace CQR.Application.UseCases.CQR_Headers.Commands.Handler;

public interface ICQRCommandHandler
{
    Task<bool> Handle(SaveGDPIMPhaseCommand command);
    Task<CQRHeaderCollection> GetCQRCollection(SaveGDPIMPhaseCommand command);
    Task<int> CreateNewCQR(CreateCQRCommand command);
    Task<bool> SaveCQRHeaderCollectionAsync(CQRHeaderCollection cqrHeaderData, string userId);
}
