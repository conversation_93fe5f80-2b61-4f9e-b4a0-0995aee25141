﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.5" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.9.4" />
    <PackageReference Include="NLog" Version="5.4.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CQR.Application\CQR.Application.csproj" />
    <ProjectReference Include="..\CQR.Infrastructure\CQR.Infrastructure.csproj" />
    <ProjectReference Include="..\CQR.Persistence.Query\CQR.Persistence.Query.csproj" />
    <ProjectReference Include="..\CQR.Persistence\CQR.Persistence.Command.csproj" />
    <ProjectReference Include="..\CQR.Domain\CQR.Domain.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="sapnco">
      <HintPath>..\CQR.Domain\lib\sapnco.dll</HintPath>
    </Reference>
    <Reference Include="sapnco_utils">
      <HintPath>..\CQR.Domain\lib\sapnco_utils.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <None Update="..\CQR.Domain\lib\sapnco.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="..\CQR.Domain\lib\sapnco_utils.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Filters\ActionFilter\" />
    <Folder Include="Utilities\NewFolder\" />
  </ItemGroup>


</Project>