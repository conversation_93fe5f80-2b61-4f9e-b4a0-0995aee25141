﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.VisualBasic;
using System.Drawing;
using System.Text;



// decisionCore, Version=1.0.9056.27004, Culture=neutral, PublicKeyToken=null
// decisionCore.coreTable
using Microsoft.VisualBasic.CompilerServices;

public class coreTable
{
    public bool bColorOff;

    public Table tTable;

    public string tOrderBy;

    public string tOrderDir;

    public Color[] RowColor;

    public string sOrderByDefaultDir;

    public string sOnClick;

    public string sCSSClass;

    public string strDefaultCellText;

    /// Private variables (no peeking)
    private bool bClipboardBuild;

    private StringBuilder tClipboardText;

    public void storeClipboardText(bool bShouldStore)
    {
        bClipboardBuild = bShouldStore;
    }

    public string getClipboardText()
    {
        tClipboardText = tClipboardText.Replace("&nbsp;", "");
        tClipboardText = tClipboardText.Replace("<nobr>", "");
        tClipboardText = tClipboardText.Replace("</nobr>", "");
        tClipboardText = tClipboardText.Replace("<br>", "");
        tClipboardText = tClipboardText.Replace("<br />", "#chr(13)##chr(10)#");
        return tClipboardText.ToString() + "\r\n";
    }

    public string stripHTML(string strHTML)
    {
        if (Strings.Len(strHTML) == 0)
        {
            return strHTML;
        }
        string[] array = Strings.Split(strHTML, "<");
        int num = ((Strings.Len(array[0]) > 0) ? 1 : 0);
        int num2 = Information.UBound(array);
        checked
        {
            for (int i = num; i <= num2; i++)
            {
                if (Strings.InStr(array[i], ">") != 0)
                {
                    array[i] = Strings.Mid(array[i], Strings.InStr(array[i], ">") + 1);
                }
                else
                {
                    array[i] = "<" + array[i];
                }
            }
            return Strings.Replace(Strings.Replace(Strings.Mid(Strings.Join(array, ""), 2 - num), ">", "&gt;"), "<", "&lt;");
        }
    }

    //public void addClipboardRow(int iRow)
    //{
    //    checked
    //    {
    //        if (iRow < tTable.Rows.Count)
    //        {
    //            int num = tTable.Rows[iRow].Cells.Count - 1;
    //            for (int i = 0; i <= num; i++)
    //            {
    //                tClipboardText.Append(Strings.Replace(Strings.Replace(Strings.Replace(stripHTML(tTable.Rows[iRow].Cells[i].Text), "\r\n", ""), "<nobr>", ""), "</nobr>", "") + "\t");
    //            }
    //        }
    //    }
    //}

    //public coreTable(ref Table atTable, bool bColorsOff = false)
    //{
    //    tOrderBy = new TextBox();
    //    tOrderDir = new TextBox();
    //    RowColor = new Color[2];
    //    sCSSClass = "";
    //    strDefaultCellText = "";
    //    tClipboardText = new StringBuilder();
    //    sOrderByDefaultDir = "ASC";
    //    tClipboardText = new StringBuilder();
    //    tTable = atTable;
    //    if (bColorsOff)
    //    {
    //        SetRowColors(Color.White, Color.White);
    //    }
    //    else
    //    {
    //        SetRowColors(Color.White, Color.FromArgb(15066597));
    //    }
    //}

    public void SetRowColors(long lColor1, long lColor2)
    {
        checked
        {
            SetRowColors(Color.FromArgb((int)lColor1), Color.FromArgb((int)lColor2));
        }
    }

    public void SetRowColors(Color clrColor1, Color clrColor2)
    {
        RowColor[0] = clrColor1;
        RowColor[1] = clrColor2;
    }

    //public TableRow AddRow(int aiRow = -1, bool abHeader = false)
    //{
    //    TableRow tableRow = new TableRow();
    //    if (!abHeader)
    //    {
    //        if (!bColorOff)
    //        {
    //            tableRow.BackColor = RowColor[tTable.Rows.Count % 2];
    //        }
    //    }
    //    else
    //    {
    //        tableRow.CssClass = "tableHeader";
    //        tableRow.Font.Bold = true;
    //        tableRow.TableSection = TableRowSection.TableHeader;
    //    }
    //    tTable.Rows.AddAt(aiRow, tableRow);
    //    TableRow result = tableRow;
    //    if (bClipboardBuild && tClipboardText.Length > 0)
    //    {
    //        tClipboardText.Append("\r\n");
    //    }
    //    return result;
    //}

    //public TableRow LastRow()
    //{
    //    return tTable.Rows[checked(tTable.Rows.Count - 1)];
    //}

    //public TableCell LastCell()
    //{
    //    checked
    //    {
    //        TableRow tableRow = tTable.Rows[tTable.Rows.Count - 1];
    //        TableCell result = tableRow.Cells[tableRow.Cells.Count - 1];
    //        tableRow = null;
    //        return result;
    //    }
    //}

    //public TableCell AddCell(string asText = "&nbsp;", string asID = "")
    //{
    //    return AddCell(asText, asID, new TableCell());
    //}

    //public TableCell AddHeaderCell(string asText = "&nbsp;", string asID = "")
    //{
    //    return AddCell(asText, asID, new TableHeaderCell());
    //}

    //private TableCell AddCell(string asText, string asID, TableCell newCell)
    //{
    //    TableRow tableRow = tTable.Rows[checked(tTable.Rows.Count - 1)];
    //    if (Operators.CompareString(asText, "", TextCompare: false) == 0)
    //    {
    //        asText = strDefaultCellText;
    //    }
    //    newCell.Text = asText;
    //    newCell.CssClass = sCSSClass;
    //    if (Operators.CompareString(asID, "", TextCompare: false) != 0)
    //    {
    //        newCell.ID = asID;
    //    }
    //    tableRow.Cells.Add(newCell);
    //    if (bClipboardBuild)
    //    {
    //        tClipboardText.Append(Strings.Replace(Strings.Replace(Strings.Replace(asText, "\r\n", "&nbsp;"), "<nobr>", ""), "</nobr>", "") + "\t");
    //    }
    //    return newCell;
    //}

    //public TableCell AddCellEx(string asText, HorizontalAlign aiHorizontalAlign = HorizontalAlign.Left, VerticalAlign aiVerticalAlign = VerticalAlign.Middle, int aiColSpan = 1)
    //{
    //    TableRow atRow = tTable.Rows[checked(tTable.Rows.Count - 1)];
    //    return AddCell(atRow, asText, aiHorizontalAlign, aiVerticalAlign, aiColSpan);
    //}

    //public TableCell AddCell(TableRow atRow, string asText, HorizontalAlign aiHorizontalAlign = HorizontalAlign.Left, VerticalAlign aiVerticalAlign = VerticalAlign.Middle, int aiColSpan = 1)
    //{
    //    TableCell tableCell = new TableCell();
    //    if (Operators.CompareString(asText, "", TextCompare: false) == 0)
    //    {
    //        asText = "&nbsp;";
    //    }
    //    asText = Strings.Replace(asText, "\r\n", " ");
    //    tableCell.Text = asText;
    //    tableCell.HorizontalAlign = aiHorizontalAlign;
    //    tableCell.VerticalAlign = aiVerticalAlign;
    //    tableCell.CssClass = sCSSClass;
    //    if (aiColSpan > 1)
    //    {
    //        tableCell.ColumnSpan = aiColSpan;
    //    }
    //    atRow.Cells.Add(tableCell);
    //    TableCell result = tableCell;
    //    if (bClipboardBuild)
    //    {
    //        tClipboardText.Append(asText + "\t");
    //    }
    //    return result;
    //}

    public void ApplyStyleToObject(ref object obj, string sStyle)
    {
        Array array = sStyle.Split(';');
        int num = Information.UBound(array);
        for (int i = 0; i <= num; i = checked(i + 1))
        {
            Array array2 = (Array)NewLateBinding.LateGet(NewLateBinding.LateIndexGet(array, new object[1] { i }, null), null, "split", new object[1] { ":" }, null, null, null);
            if (Information.UBound(array2) == 1)
            {
                object instance = NewLateBinding.LateGet(obj, null, "style", new object[0], null, null, null);
                object[] array3 = new object[2];
                Array instance2 = array2;
                object[] array4 = new object[1];
                object obj2 = (array4[0] = 0);
                array3[0] = NewLateBinding.LateIndexGet(instance2, array4, null);
                Array instance3 = array2;
                object[] array5 = new object[1];
                object obj3 = (array5[0] = 1);
                array3[1] = NewLateBinding.LateIndexGet(instance3, array5, null);
                object[] array6 = array3;
                bool[] obj4 = new bool[2] { true, true };
                bool[] array7 = obj4;
                NewLateBinding.LateCall(instance, null, "add", array3, null, null, obj4, IgnoreReturn: true);
                if (array7[0])
                {
                    NewLateBinding.LateIndexSetComplex(instance2, new object[2]
                    {
                        obj2,
                        array6[0]
                    }, null, OptimisticSet: true, RValueBase: false);
                }
                if (array7[1])
                {
                    NewLateBinding.LateIndexSetComplex(instance3, new object[2]
                    {
                        obj3,
                        array6[1]
                    }, null, OptimisticSet: true, RValueBase: false);
                }
            }
        }
    }

    //public void SetupSortHeaders(Page atForm, int aiRow = 0, bool abFlag = true, string sPHName = "phSort")
    //{
    //    tOrderBy.ID = tTable.ID + "OrderBy";
    //    if (atForm.FindControl(sPHName).FindControl(tOrderBy.ID) != null)
    //    {
    //        return;
    //    }
    //    tOrderBy.Text = atForm.Request[tOrderBy.ID];
    //    tOrderDir.ID = tTable.ID + "OrderDir";
    //    tOrderDir.Text = atForm.Request[tOrderDir.ID];
    //    atForm.FindControl(sPHName).Controls.Add(tOrderBy);
    //    atForm.FindControl(sPHName).Controls.Add(tOrderDir);
    //    tOrderBy.Style.Add("display", "none");
    //    tOrderDir.Style.Add("display", "none");
    //    string text = tOrderBy.Text;
    //    string text2 = tOrderDir.Text;
    //    if (Operators.CompareString(text2, "", TextCompare: false) == 0)
    //    {
    //        text2 = sOrderByDefaultDir;
    //    }
    //    checked
    //    {
    //        int num = tTable.Rows[aiRow].Cells.Count - 1;
    //        for (int i = 0; i <= num; i++)
    //        {
    //            TableCell tableCell = tTable.Rows[aiRow].Cells[i];
    //            string text3 = Strings.Replace(tableCell.Attributes["OrderBy"], "%2B", "+");
    //            string left = tableCell.Attributes["OrderByDefault"];
    //            string text4 = tableCell.Attributes["OrderByDirection"];
    //            if ((Operators.CompareString(left, "", TextCompare: false) != 0) & (Operators.CompareString(text, "", TextCompare: false) == 0))
    //            {
    //                text = text3;
    //                text2 = Conversions.ToString(NewLateBinding.LateGet(null, typeof(Strings), "UCase", new object[1] { Interaction.IIf(Operators.CompareString(text4, "", TextCompare: false) == 0, "ASC", text4) }, null, null, null));
    //            }
    //            if (Operators.CompareString(text3, "", TextCompare: false) != 0)
    //            {
    //                string text5 = tableCell.Text;
    //                int num2 = Strings.InStr(text5, "<img");
    //                if (num2 > 0)
    //                {
    //                    text5 = Strings.Left(text5, num2 - 1);
    //                }
    //                text5 = "<a href=\"javascript:sort_table('" + tTable.ID + "','" + text3 + "');\">" + text5 + "</a>";
    //                if (unchecked(Operators.CompareString(text, text3, TextCompare: false) == 0 && abFlag))
    //                {
    //                    text5 = Conversions.ToString(Operators.ConcatenateObject(text5, Operators.ConcatenateObject(Operators.ConcatenateObject("<img SRC='../images/Arrow_", Interaction.IIf(Operators.CompareString(text2, "DESC", TextCompare: false) == 0, "Down", "Up")), ".gif' WIDTH='8' HEIGHT='8'>")));
    //                }
    //                tableCell.Text = text5;
    //            }
    //        }
    //        tOrderBy.Text = text;
    //        tOrderDir.Text = text2;
    //    }
    //}

    //public string OrderByClause(string asDefaultOrderBy = "")
    //{
    //    string text = HttpContext.Current.Request[tTable.ID + "OrderBy"];
    //    string right = HttpContext.Current.Request[tTable.ID + "OrderDir"];
    //    string text2;
    //    if (Operators.CompareString(text, "", TextCompare: false) == 0)
    //    {
    //        text2 = Conversions.ToString(Interaction.IIf(Operators.CompareString(asDefaultOrderBy, "", TextCompare: false) == 0, "", " ORDER BY " + asDefaultOrderBy));
    //    }
    //    else
    //    {
    //        Array array = Strings.Split(text, ",");
    //        text2 = " ORDER BY ";
    //        int num = Information.UBound(array);
    //        for (int i = 0; i <= num; i = checked(i + 1))
    //        {
    //            text2 = Conversions.ToString(Operators.AddObject(text2, Operators.ConcatenateObject(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(array, new object[1] { i }, null), " "), right)));
    //            if (i != Information.UBound(array))
    //            {
    //                text2 += ", ";
    //            }
    //        }
    //    }
    //    return text2;
    //}

    public void TableEditAddCellCheckBox(string sId, string sText)
    {
        //CheckBox checkBox = new CheckBox();
        //checkBox.EnableViewState = false;
        //checkBox.Attributes.Add("onclick", "javascript:fnActivateSave();");
        //checkBox.ID = "txt_" + sId;
        //checkBox.Checked = Operators.CompareString(sText, "1", TextCompare: false) == 0;
        //AddCell().Controls.Add(checkBox);
        //_ = null;
    }

    //public TableCell TableEditAddCellLabel(string sId, string sText, DropDownList tDrop = null, int iWidth = 160, int iHeight = -1)
    //{
    //    Label label = new Label();
    //    label.EnableViewState = false;
    //    label.BorderWidth = Unit.Pixel(0);
    //    label.ID = "lbl_" + sId;
    //    label.CssClass = "labelUnderDropdown";
    //    label.Text = sText;
    //    if (iWidth != -1)
    //    {
    //        label.Width = Unit.Pixel(iWidth);
    //    }
    //    if (iHeight != -1)
    //    {
    //        label.Height = Unit.Pixel(iHeight);
    //    }
    //    label.Attributes.Add("OnClick", sOnClick);
    //    Operators.CompareString(sOnClick, "", TextCompare: false);
    //    checked
    //    {
    //        object obj;
    //        if (tDrop == null)
    //        {
    //            TextBox textBox = new TextBox();
    //            textBox.Text = sText;
    //            textBox.Height = Unit.Pixel(20);
    //            textBox.Attributes.Add("onkeydown", "enableSaveKeyDown();");
    //            textBox.EnableViewState = false;
    //            obj = textBox;
    //        }
    //        else
    //        {
    //            obj = tDrop;
    //            if (Operators.CompareString(tDrop.Attributes["onchange"], "", TextCompare: false) == 0)
    //            {
    //                tDrop.Attributes.Add("onchange", "document.getElementById('btnSave').disabled = false;");
    //            }
    //            if (Operators.CompareString(Strings.Left(sText, 1), "*", TextCompare: false) == 0)
    //            {
    //                label.Text = "";
    //            }
    //            int num = tDrop.Items.Count - 1;
    //            for (int i = 0; i <= num; i++)
    //            {
    //                if (Operators.CompareString(Strings.Left(sText, 1), "*", TextCompare: false) == 0)
    //                {
    //                    if (Operators.CompareString(tDrop.Items[i].Value, Strings.Mid(sText, 2), TextCompare: false) == 0)
    //                    {
    //                        tDrop.SelectedIndex = i;
    //                        label.Text = tDrop.SelectedItem.Text;
    //                    }
    //                }
    //                else if (Operators.CompareString(tDrop.Items[i].Text, sText, TextCompare: false) == 0)
    //                {
    //                    tDrop.SelectedIndex = i;
    //                }
    //            }
    //        }
    //        NewLateBinding.LateSet(obj, null, "ID", new object[1] { "txt_" + sId }, null, null);
    //        NewLateBinding.LateCall(NewLateBinding.LateGet(obj, null, "Style", new object[0], null, null, null), null, "Add", new object[2] { "display", "none" }, null, null, null, IgnoreReturn: true);
    //        NewLateBinding.LateSet(obj, null, "CssClass", new object[1] { "cssTextName" }, null, null);
    //        TableCell tableCell = AddCell();
    //        tableCell.Attributes.Add("onClick", sOnClick);
    //        tableCell.Controls.Add(label);
    //        tableCell.Controls.Add((Control)obj);
    //        return tableCell;
    //    }
    //}

    //public void removeAllButHeader()
    //{
    //    while (tTable.Rows.Count > 1)
    //    {
    //        tTable.Rows.RemoveAt(1);
    //    }
    //}

    //public void FixRowColors()
    //{
    //    tTable.Rows[0].CssClass = "tableHeader";
    //    checked
    //    {
    //        int num = tTable.Rows.Count - 1;
    //        int num2 = default(int);
    //        for (int i = 1; i <= num; i++)
    //        {
    //            if (!tTable.Rows[i].Visible | (Operators.CompareString(tTable.Rows[i].Style["display"], "none", TextCompare: false) == 0))
    //            {
    //                num2++;
    //            }
    //            unchecked
    //            {
    //                tTable.Rows[i].BackColor = RowColor[checked(i - num2) % 2];
    //            }
    //        }
    //    }
    //}
}
