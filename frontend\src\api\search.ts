import { http } from "@/utils/http"
import type { SearchQuery } from '@/views/CQRSearch/composables/useSearchForm'

export interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
  code?: number
}

export interface SelectOption {
  value: string
  text: string
}

export interface UserOption {
  value: string
  text: string
  userId?: string
  email?: string
}

export interface RoleUserCollection {
  roleName: string
  roleId: number
  users: UserOption[]
  userCount: number
  displayName?: string
}

export interface RoleUserDictionary {
  [roleName: string]: RoleUserCollection
}

export interface SearchResultResponse {
  Results: any[]
  TotalCount: number
  PageNumber: number
  PageSize: number
  TotalPages: number
  HasPreviousPage: boolean
  HasNextPage: boolean
  SearchSummary?: string
}

// CQR 搜尋 API
export const searchCQR = (query: SearchQuery): Promise<ApiResponse<SearchResultResponse>> => {
  return http.request<ApiResponse<SearchResultResponse>>(
    "post",
    "/api/search/cqr",
    { data: query }
  )
}

// 快速搜尋 API
export const quickSearchCQR = (
  searchText?: string,
  pageNumber: number = 1,
  pageSize: number = 50
): Promise<ApiResponse<SearchResultResponse>> => {
  return http.request<ApiResponse<SearchResultResponse>>(
    "get",
    "/api/search/quick",
    {
      params: {
        searchText,
        pageNumber,
        pageSize
      }
    }
  )
}

// 取得欄位選項 API
export const getFieldOptions = (fieldName: string): Promise<ApiResponse<SelectOption[]>> => {
  return http.request<ApiResponse<SelectOption[]>>(
    "get",
    `/api/search/field-options/${fieldName}`
  )
}

// 取得製造地點選項
export const getManufacturingSiteOptions = (): Promise<ApiResponse<SelectOption[]>> => {
  return getFieldOptions('manufacturingsite')
}

// 取得年式選項
export const getModelYearOptions = (): Promise<ApiResponse<SelectOption[]>> => {
  return getFieldOptions('modelyear')
}

// 取得報價類型選項
export const getQuoteTypeOptions = (): Promise<ApiResponse<SelectOption[]>> => {
  return getFieldOptions('quotetype')
}

// 取得 OEM 群組選項
export const getOEMGroupOptions = (): Promise<ApiResponse<SelectOption[]>> => {
  return getFieldOptions('oemgroup')
}

// 取得 OEM 客戶選項
export const getOEMCustomerOptions = (): Promise<ApiResponse<SelectOption[]>> => {
  return getFieldOptions('oemcustomer')
}

// 取得使用者列表 API (單一角色)
export const getUsers = (role?: string): Promise<ApiResponse<UserOption[]>> => {
  return http.request<ApiResponse<UserOption[]>>(
    "get",
    "/api/search/users",
    { params: { role } }
  )
}

// 一次取得所有角色的使用者列表 (效能更好的方式)
export const getAllRoleUsers = (): Promise<ApiResponse<RoleUserCollection[]>> => {
  return http.request<ApiResponse<RoleUserCollection[]>>(
    "get",
    "/RoleUser/role/roleCollection"
  )
}

// 使用 Dictionary 結構的版本 (O(1) 查找效能)
export const getAllRoleUsersDictionary = (): Promise<ApiResponse<RoleUserDictionary>> => {
  return http.request<ApiResponse<RoleUserDictionary>>(
    "get",
    "/RoleUser/role/roleCollectionDictionary"
  )
}

// 從角色集合中提取特定角色的使用者選項 (Array 版本，向後兼容)
export const extractRoleUsers = (roleCollections: RoleUserCollection[], roleName: string): UserOption[] => {
  const roleCollection = roleCollections.find(
    collection => collection.roleName.toLowerCase() === roleName.toLowerCase()
  )
  return roleCollection?.users || []
}

// 從角色字典中提取特定角色的使用者選項 (Dictionary 版本，O(1) 查找)
export const extractRoleUsersFromDictionary = (roleUserDictionary: RoleUserDictionary, roleName: string): UserOption[] => {
  // 直接查找，無需遍歷
  const roleCollection = roleUserDictionary[roleName] || roleUserDictionary[roleName.toLowerCase()]
  return roleCollection?.users || []
}

// 快取角色字典數據 (避免重複請求)
let cachedRoleUserDictionary: RoleUserDictionary | null = null
let cacheTimestamp: number = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5分鐘快取

export const getCachedAllRoleUsersDictionary = async (): Promise<RoleUserDictionary> => {
  const now = Date.now()
  
  // 如果快取有效，直接返回
  if (cachedRoleUserDictionary && (now - cacheTimestamp) < CACHE_DURATION) {
    return cachedRoleUserDictionary
  }
  
  // 重新獲取數據
  try {
    const response = await getAllRoleUsersDictionary()
    if (response.success && response.data) {
      cachedRoleUserDictionary = response.data
      cacheTimestamp = now
      return cachedRoleUserDictionary
    }
  } catch (error) {
    console.error('獲取角色字典失敗:', error)
  }
  
  return cachedRoleUserDictionary || {}
}

// 向後兼容的版本 (使用 Array)
export const getCachedAllRoleUsers = async (): Promise<RoleUserCollection[]> => {
  const dictionary = await getCachedAllRoleUsersDictionary()
  return Object.values(dictionary)
}

// 使用 Dictionary 效能更好的方式取得各角色選項 (O(1) 查找)
export const getOriginatorOptions = async (): Promise<ApiResponse<UserOption[]>> => {
  try {
    const roleUserDictionary = await getCachedAllRoleUsersDictionary()
    const users = extractRoleUsersFromDictionary(roleUserDictionary, 'OriginatorId')
    return { success: true, data: users, message: '取得發起人選項成功' }
  } catch (error) {
    console.error('取得發起人選項失敗，使用舊方式:', error)
    return getUsers('originator')
  }
}

export const getAccountManagerOptions = async (): Promise<ApiResponse<UserOption[]>> => {
  try {
    const roleUserDictionary = await getCachedAllRoleUsersDictionary()
    const users = extractRoleUsersFromDictionary(roleUserDictionary, 'AccountMgrId')
    return { success: true, data: users, message: '取得客戶經理選項成功' }
  } catch (error) {
    console.error('取得客戶經理選項失敗，使用舊方式:', error)
    return getUsers('accountmanager')
  }
}

export const getEstimatorOptions = async (): Promise<ApiResponse<UserOption[]>> => {
  try {
    const roleUserDictionary = await getCachedAllRoleUsersDictionary()
    const users = extractRoleUsersFromDictionary(roleUserDictionary, 'CostEstimatorId')
    return { success: true, data: users, message: '取得估價員選項成功' }
  } catch (error) {
    console.error('取得估價員選項失敗，使用舊方式:', error)
    return getUsers('estimator')
  }
}

export const getPDMOptions = async (): Promise<ApiResponse<UserOption[]>> => {
  try {
    const roleUserDictionary = await getCachedAllRoleUsersDictionary()
    const users = extractRoleUsersFromDictionary(roleUserDictionary, 'PETMId')
    return { success: true, data: users, message: '取得PDM選項成功' }
  } catch (error) {
    console.error('取得PDM選項失敗，使用舊方式:', error)
    return getUsers('pdm')
  }
}

export const getPGMOptions = async (): Promise<ApiResponse<UserOption[]>> => {
  try {
    const roleUserDictionary = await getCachedAllRoleUsersDictionary()
    const users = extractRoleUsersFromDictionary(roleUserDictionary, 'PGMId')
    return { success: true, data: users, message: '取得PGM選項成功' }
  } catch (error) {
    console.error('取得PGM選項失敗，使用舊方式:', error)
    return getUsers('pgm')
  }
}

// 清除快取 (可選用)
export const clearRoleUsersCache = (): void => {
  cachedRoleUserDictionary = null
  cacheTimestamp = 0
}

// 匯出搜尋結果 API
export const exportSearchResults = (
  query: SearchQuery,
  format: 'excel' | 'csv' = 'excel'
): Promise<ApiResponse<Blob>> => {
  return http.request<ApiResponse<Blob>>(
    "post",
    "/api/search/export",
    {
      data: query,
      params: { format },
      responseType: 'blob'
    }
  )
}

// 搜尋預設條件相關 API
export interface SearchPreset {
  id?: string
  presetName: string
  criteria: SearchQuery
  isDefault: boolean
  createdDate?: string
  lastUsedDate?: string
}

// 儲存搜尋預設條件
export const saveSearchPreset = (preset: SearchPreset): Promise<ApiResponse<any>> => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/search/presets",
    { data: preset }
  )
}

// 取得搜尋預設條件
export const getSearchPresets = (): Promise<ApiResponse<SearchPreset[]>> => {
  return http.request<ApiResponse<SearchPreset[]>>(
    "get",
    "/api/search/presets"
  )
}

// 刪除搜尋預設條件
export const deleteSearchPreset = (presetId: string): Promise<ApiResponse<any>> => {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/search/presets/${presetId}`
  )
}

// 應用搜尋預設條件
export const applySearchPreset = (presetId: string): Promise<ApiResponse<SearchQuery>> => {
  return http.request<ApiResponse<SearchQuery>>(
    "post",
    `/api/search/presets/${presetId}/apply`
  )
}

// 取得搜尋歷史記錄
export interface SearchHistory {
  id?: string
  searchQuery: SearchQuery
  searchTime: string
  resultsCount: number
  searchSummary?: string
}

export const getSearchHistory = (limit: number = 10): Promise<ApiResponse<SearchHistory[]>> => {
  return http.request<ApiResponse<SearchHistory[]>>(
    "get",
    "/api/search/history",
    { params: { limit } }
  )
}

// 清除搜尋歷史記錄
export const clearSearchHistory = (): Promise<ApiResponse<any>> => {
  return http.request<ApiResponse<any>>(
    "delete",
    "/api/search/history"
  )
}

// 搜尋統計 API
export interface SearchStats {
  totalSearches: number
  averageResultsCount: number
  mostUsedFilters: Array<{ filter: string, count: number }>
  searchTrends: Array<{ date: string, count: number }>
}

export const getSearchStats = (): Promise<ApiResponse<SearchStats>> => {
  return http.request<ApiResponse<SearchStats>>(
    "get",
    "/api/search/stats"
  )
}

// 進階搜尋建議 API
export interface SearchSuggestion {
  field: string
  value: string
  count: number
  description?: string
}

export const getSearchSuggestions = (query: string): Promise<ApiResponse<SearchSuggestion[]>> => {
  return http.request<ApiResponse<SearchSuggestion[]>>(
    "get",
    "/api/search/suggestions",
    { params: { query } }
  )
}

// 搜尋結果下鑽 API
export const getCQRRelatedData = (queueKey: number): Promise<ApiResponse<any>> => {
  return http.request<ApiResponse<any>>(
    "get",
    `/api/search/cqr/${queueKey}/related`
  )
}

// 批量操作 API
export const batchOperationCQRs = (
  queueKeys: number[],
  operation: 'export' | 'update' | 'delete',
  params?: any
): Promise<ApiResponse<any>> => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/search/batch-operation",
    {
      data: {
        queueKeys,
        operation,
        params
      }
    }
  )
}

// 搜尋結果快取相關 API
export const getCachedSearchResults = (cacheKey: string): Promise<ApiResponse<SearchResultResponse>> => {
  return http.request<ApiResponse<SearchResultResponse>>(
    "get",
    `/api/search/cache/${cacheKey}`
  )
}

export const setCachedSearchResults = (
  cacheKey: string,
  results: SearchResultResponse
): Promise<ApiResponse<any>> => {
  return http.request<ApiResponse<any>>(
    "post",
    `/api/search/cache/${cacheKey}`,
    { data: results }
  )
}

// 預設匯出函數（向後相容）
export default {
  searchCQR,
  quickSearchCQR,
  getFieldOptions,
  getUsers,
  getAllRoleUsers,
  getAllRoleUsersDictionary,
  getCachedAllRoleUsers,
  getCachedAllRoleUsersDictionary,
  extractRoleUsers,
  extractRoleUsersFromDictionary,
  clearRoleUsersCache,
  exportSearchResults,
  saveSearchPreset,
  getSearchPresets
}
