// src/utils/api.ts
//test
// import http from "@/utils/http";
import { http } from "@/utils/http";
import type { ApiResponse } from "@/types/api";
import type { AxiosRequestConfig } from "axios";

export const apiRequest = async <T>(
  method: "get" | "post" | "put" | "delete",
  url: string,
  payload?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  // 組合 Axios 設定
  const axiosConfig: AxiosRequestConfig = {
    ...config,
    data: payload // 把 payload 放進去 data 屬性
  };

  const res = await http.request<ApiResponse<T>>(method, url, axiosConfig);

  if (!res.success) {
    throw new Error("API returned unsuccessful");
  }

  return res.data;
};
// export const apiRequest = async <T>(
//   method: "get" | "post" | "put" | "delete",
//   url: string,
//   payload?: unknown
// ): Promise<T> => {
//   const res = await http.request<ApiResponse<T>>({
//     method,
//     url,
//     data: payload
//   });

//   if (!res.data.success) {
//     throw new Error("API returned unsuccessful");
//   }

//   return res.data.data;
// };
