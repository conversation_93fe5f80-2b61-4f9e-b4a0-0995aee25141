﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	  internalLogLevel="Trace"
      internalLogFile="${basedir}\Log\internal_nlog\internal-nlog.txt">

  <!-- enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

  <!-- the targets to write to -->
  <targets>
    <!-- write logs to file  -->
    <target xsi:type="File" name="allfile" fileName="${basedir}\Log\web\nlog-all-${shortdate}.log"
            layout="${longdate} [${uppercase:${level}}] ${message} ${newline}${exception:format=tostring}" />

    <!-- another file log, only own logs. Uses some ASP.NET core renderers -->
    <target xsi:type="File" name="ownFile-web" fileName="${basedir}\Log\coreown\nlog-own-${shortdate}.log"
            layout="${longdate} [${uppercase:${level}}] ${logger} | ${message} ${newline}${exception:format=tostring} | url: ${aspnet-request-url} action: ${aspnet-mvc-action}" />

    <target xsi:type="Console" name="lifetimeConsole"
            layout="${date} [${uppercase:${level}}] ${message} ${newline}${exception}" />
  </targets>

  <!-- rules to map from logger name to target -->
  <rules>
    <!--All logs, including from Microsoft-->
    <logger name="*" minlevel="Info" writeTo="allfile" />

    <!--Output hosting lifetime messages to make Docker / Visual Studio happy -->
    <logger name="Microsoft.Hosting.Lifetime" level="Info" writeTo="lifetimeConsole, ownFile-web" final="true" />
    <!--Skip non-critical Microsoft logs and so log only own logs-->
    <logger name="Microsoft.*" maxlevel="Info" final="true" />
    <!-- BlackHole without writeTo -->
    <logger name="*" minlevel="Trace" writeTo="ownFile-web" />
  </rules>
</nlog>