﻿using CQR.Domain.Interfaces.Repository;

namespace CQR.Persistence.Command.Persistence;


public static class UnitOfWorkExtensions
{
    /// <summary>
    /// Execute multiple operations in a single transaction
    /// </summary>
    public static async Task ExecuteInTransactionAsync(
        this IUnitOfWork unitOfWork,
        params Func<Task>[] operations)
    {
        var uow = unitOfWork as UnitOfWork;
        if (uow == null)
            throw new InvalidOperationException("UnitOfWork must be of type UnitOfWork to use this extension.");

        await uow.ExecuteInTransactionAsync(async () =>
        {
            foreach (var operation in operations)
            {
                await operation();
            }
        });
    }

    /// <summary>
    /// Save changes and handle common exceptions
    /// </summary>
    public static async Task<(bool Success, string ErrorMessage)> TrySaveChangesAsync(
        this IUnitOfWork unitOfWork,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
            return (true, null);
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Concurrency"))
        {
            return (false, "The record has been modified by another user. Please refresh and try again.");
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("database"))
        {
            return (false, "An error occurred while saving to the database. Please try again.");
        }
        catch (Exception ex)
        {
            return (false, $"An unexpected error occurred: {ex.Message}");
        }
    }
}