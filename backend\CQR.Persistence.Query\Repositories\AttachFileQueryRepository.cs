﻿using CQR.Application.Dto.AttachFile;
using CQR.Application.Repositories;
using CQR.Persistence.Query.Base;
using Dapper;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Query.Repositories;

public class AttachFileQueryRepository : QueryBaseRepository, IAttachFileQueryRepository
{
    public AttachFileQueryRepository(IConfiguration configuration) : base(configuration)
    {
    }

    public Task<IEnumerable<ATTDIR_AttachFileDirHeaderCheckOutDto>> GetATTDIRAttachFileDirHeaderCheckOutDtos(int queueKey)
    {
        //throw new NotImplementedException();
        var sql = $@"
          SELECT AH.* 
         , (SELECT TOP 1 CheckoutUser FROM ATTDIR_CheckoutLog CL WHERE CL.QueueKey=AH.QueueKey AND CL.CheckinDate IS NULL) AS CheckoutUser
         , (SELECT TOP 1 CheckoutDate FROM ATTDIR_CheckoutLog CD WHERE CD.QueueKey=AH.QueueKey AND CD.CheckinDate IS NULL) AS CheckoutDate
         FROM ATTDIR_AttachFileDirHeader AH
         WHERE  FolderNbr_int=@queueKey";

        return _connection.QueryAsync<ATTDIR_AttachFileDirHeaderCheckOutDto>(sql, new { queueKey = queueKey });
    }

    public Task<IEnumerable<ATTDIR_AttachFileDirHeaderCheckOutDto>> GetATTDIRFilesByFolderType(string folderType, int queueKey)
    {
        var sql = $@"
          SELECT AH.* 
         , (SELECT TOP 1 CheckoutUser FROM ATTDIR_CheckoutLog CL WHERE CL.QueueKey=AH.QueueKey AND CL.CheckinDate IS NULL) AS CheckoutUser
         , (SELECT TOP 1 CheckoutDate FROM ATTDIR_CheckoutLog CD WHERE CD.QueueKey=AH.QueueKey AND CD.CheckinDate IS NULL) AS CheckoutDate
         FROM ATTDIR_AttachFileDirHeader AH
         WHERE FolderTypeCode = @folderType AND FolderNbr_int=@queueKey";

        return _connection.QueryAsync<ATTDIR_AttachFileDirHeaderCheckOutDto>(sql, new { folderType = folderType, queueKey = queueKey });
    }
}
