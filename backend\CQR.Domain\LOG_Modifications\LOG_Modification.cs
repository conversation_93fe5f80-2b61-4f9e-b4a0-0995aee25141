﻿using System.ComponentModel.DataAnnotations;

namespace CQR.Domain.LOG_Modifications;

public class LOG_Modification
{
    [Key]
    public string? QueueKey { get; set; }
    public string? Application { get; set; }
    public string? SuperUser { get; set; }
    public string? Button { get; set; }
    public string? UserActual { get; set; }
    public string? UserImpersonate { get; set; }
    public string? FolderQueueKey { get; set; }
    public string? OtherInfo { get; set; }
    public string? UserComment { get; set; }

    public DateTime DateModified { get; set; }
    public string? FolderStatus { get; set; }
}
