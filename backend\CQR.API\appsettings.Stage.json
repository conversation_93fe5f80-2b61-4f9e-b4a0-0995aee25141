{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=CQR_DEV;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=true;User ID=sa2;Password=Init1234;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "SAP": {"AppServerHost": "your.sap.server.host", "SystemNumber": "00", "Client": "100", "User": "yourUsername", "Password": "yourPassword", "Language": "EN"}, "SmtpSettings": {"SMTP_PORT": "25", "SMTP_HOST": "***********", "SMTP_SECURE": false, "SMTP_IGNORE_TLS": true, "SMPT_DESC": "bcs smtp", "SMTP_MAILER": "<EMAIL>"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Debug", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/applog-.txt", "rollingInterval": "Day"}}], "Enrich": ["FromLogContext", "WithMachineName"], "Properties": {"ApplicationName": "Your ASP.NET Core App"}}}