﻿using CQR.Domain.Interfaces.Repositories;

namespace CQR.Domain.Interfaces.Repository;

public interface IGenericBasicRepository<T>:IReadRepository<T>, IWriteRepository<T>
{
    //int GetLatestSequenceKey(string sequenceName);
    //T GetById(int id);
    //IEnumerable<T> GetAll();
    //bool Add(T entity);
    ///// <summary>
    ///// for Identity
    ///// </summary>
    ///// <param name="entity"></param>
    ///// <returns></returns>
    //bool AddExcludeKey(T entity);
    ////bool AddMultiple(IList<T> entity);

    //bool Update(T entity);
    ////bool UpdateMultiple(IList<T> entity);

    //bool Delete(T entity);
    //bool DeleteMultiple(IList<T> entity);

}
