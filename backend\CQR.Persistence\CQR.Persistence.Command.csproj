﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CQR.Application\CQR.Application.csproj" />
    <ProjectReference Include="..\CQRLIB\CQR.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="sapnco">
      <HintPath>..\CQRLIB\bin\Debug\net8.0\sapnco.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
