using CQR.Application.Dto.CQRHead;
using CQR.Application.Dto.IHSFolder;
using CQR.Application.Services;
using CQR.Application.UseCases.CQR_Headers.Commands;
using CQR.Domain.CQRHeaders;
using Microsoft.AspNetCore.Mvc;

namespace CQRAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CQRHeaderController : ControllerBase
{
    private readonly IApplicationServices _appServices;

    public CQRHeaderController(IApplicationServices appServices)
    {
        _appServices = appServices;
    }

    // TODO: 等待前台 search 頁面 DTO 定義完成後實作
    // [HttpGet("getAllCQRHeaderInPage")]
    // public async Task<ActionResult<IEnumerable<dynamic>>> GetAllCQRHeader([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string status = null)
    // {
    //     var query = new GetAllCQRHeaderQuery(pageNumber, pageSize, status);
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }
    [HttpGet("CQRHeaderCollection/QueueKey/{queueKey}")]
    public async Task<ActionResult<CQRHeaderCollection>> getCQRCoillection(int queueKey)
    {
        var result = await _appServices.QueryService.GetCollectionAsync(queueKey);
        return Ok(result);
    }
    // TODO: 重構為 GetAllCQRHeaderQuery
    // [HttpGet]
    // [Route("getAllCQRHeader")]
    // public async Task<ActionResult<IEnumerable<CQR.Domain.CQRHeaders.CQR_Header>>> getAllCQRHeader()
    // {
    //     var query = new GetAllCQRHeaderQuery(1, 20);
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }
    // TODO: 重構為 GetCQRHeaderPagedQuery
    // [HttpGet("CQRHeader/pageIndex/{pageIndex}/pageSize/{pageSize}")]
    // public async Task<ActionResult<IEnumerable<CQR_Header>>> GetAllCQRHeader([FromQuery] CQRHeaderQueryRequest request)
    // {
    //     var query = new GetCQRHeaderPagedQuery(request.PageIndex, request.PageSize, request.OrderByProperty, request.Ascending);
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }
    [HttpGet]
    [Route("QueueKey/{QueueKey}")]
    public async Task<ActionResult<CQR_Header?>> getCQRHeaderById(int QueueKey)
    {
        var result = await _appServices.QueryService.GetHeaderByKeyAsync(QueueKey);
        return Ok(result);
    }

    [HttpPost]
    [Route("SaveCQRHeaderCollection")]
    public async Task<ActionResult<bool>> SaveCQRHeaderCollection(
        [FromBody] CQRHeaderCollection cqrHeaderDto
    )
    {
        try
        {
            // 1. Input validation
            if (cqrHeaderDto == null)
            {
                return BadRequest("CQR Header data is required");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // 2. Use ApplicationService to save the collection
            // TODO: Get UserId from authentication context
            var result = await _appServices.CommandHandler.SaveCQRHeaderCollectionAsync(cqrHeaderDto, "IBERLANG");

            // 4. Return result
            if (result)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest("Failed to save CQR Header Collection");
            }
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            // Log error (already logged in handler)
            return StatusCode(500, "An error occurred while saving CQR Header Collection");
        }
    }

    [HttpPost]
    [Route("ReleaseCQRHeader")]
    public async Task<ActionResult<bool>> ReleaseCQRHeader([FromBody] CQR_Header cqrHeader)
    {
        return NotFound();
    }

    [HttpPost]
    [Route("RejectCQRHeader")]
    public async Task<ActionResult<bool>> RejectCQRHeader([FromBody] CQR_Header cqrHeader)
    {
        return NotFound();
    }

    [HttpPost]
    [Route("ApproveCQRHeader")]
    public async Task<ActionResult<bool>> ApproveCQRHeader([FromBody] CQR_Header cqrHeader)
    {
        return NotFound();
    }

    [HttpPost]
    [Route("TerminateCQRHeader")]
    public async Task<ActionResult<bool>> TerminateCQRHeader([FromBody] CQR_Header cqrHeader)
    {
        return NotFound();
    }

    // TODO: 重構為 ValidateCQRCommand
    // [HttpPost("validateCreateCQRCriteria")]
    // public async Task<IActionResult> ValidateCQR([FromBody] ValidateCQRRequest request)
    // {
    //     var command = new ValidateCQRCommand(request);
    //     var result = await _mediator.Send(command);
    //     return Ok(result);
    // }

    // TODO: 重構為 CreateCQRHeaderDraftCommand
    // [HttpPost]
    // [Route("CreateNewCQRHeaderDraft")]
    // public async Task<ActionResult<int>> CreateNewCQRHeaderDraft()
    // {
    //     if (!ModelState.IsValid)
    //     {
    //         return BadRequest(ModelState);
    //     }
    //     var command = new CreateCQRHeaderDraftCommand { UserId = "lyon" };
    //     var queuekey = await _mediator.Send(command);
    //     return Ok(queuekey);
    // }
    // [HttpGet]
    // [Route("CQRIHSFolder/QueueKey/{QueueKey}")]
    // public async Task<ActionResult<CQR_GDPIMPhase>> getIHSFolderByQueuekey(int QueueKey)
    // {
    //    var result = await srv.Repositories.CQRIHSFolderRepository.GetByQueueKeyAsync(QueueKey);
    //    return Ok(result);
    // }
    // TODO: 重構為 GetRoutingHeaderByQueueKeyQuery
    // [HttpGet]
    // [Route("RoutingHeader/QueueKey/{QueueKey}")]
    // public async Task<ActionResult<ROUTING_RGHeader>> getByFoldNBR(int QueueKey)
    // {
    //     var query = new GetRoutingHeaderByQueueKeyQuery(QueueKey);
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }
    // TODO: 重構為 GetAllRoutingHeadersQuery
    // [HttpGet]
    // [Route("RoutingHeader")]
    // public async Task<ActionResult<ROUTING_RGHeader>> getAllRutiningHeaders()
    // {
    //     var query = new GetAllRoutingHeadersQuery();
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }
    // TODO: 重構為 GetRoutingHeaderByQueueKeyAndFolderTypeQuery
    // [HttpGet]
    // [Route("RoutingHeader/QueueKey/{QueueKey}/FolderType/${folderType}")]
    // public async Task<ActionResult<ROUTING_RGHeader>> getAllRutiningHeaders(int queuekey, string folderType)
    // {
    //     var query = new GetRoutingHeaderByQueueKeyAndFolderTypeQuery(queuekey, folderType);
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }
    //[HttpGet("excel/{fileName}")]
    //public IActionResult DownloadExcel(string fileName)
    //{
    //    var filePath = _excelService.GenerateExcelFile(fileName);

    //    var mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    //    var bytes = System.IO.File.ReadAllBytes(filePath);

    //    return File(bytes, mimeType, fileName);
    //}
    //[HttpGet]
    //[Route("RoutingHeader/QueueKey/{QueueKey}/FolderType/${folderType}")]
    //public async Task<ActionResult<ROUTING_RGHeader>> getAllRutiningHeadersByQueuekeyAndFolderType(int queuekey, string folderType)
    //{
    //    var result = await srv.Repositories.RoutingRGHeaderRepository.GetByFoldNbrAndFolerType(queuekey, folderType);
    //    return Ok(result);
    //}
    // TODO: 重構為 GetAttachFileByQueueKeyQuery
    // [HttpGet]
    // [Route("AttachFile/QueueKey/{QueueKey}")]
    // public async Task<ActionResult<ROUTING_RGHeader>> getATTDIRAttachFileDirHeaderCheckOutByQueuekey(int QueueKey)
    // {
    //     var query = new GetAttachFileByQueueKeyQuery(QueueKey);
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }
    // PUT: api/CQRHeader/5
    [HttpPut("QueueKey/{QueueKey}")]
    //public async Task<IActionResult> UpdateCQRHeader(int id, [FromBody] CQR_GDPIMPhase header)
    //{
    //    //if (id != header.Id)
    //    //{
    //    //    return BadRequest("ID mismatch");
    //    //}

    //    //if (!ModelState.IsValid)
    //    //{
    //    //    return BadRequest(ModelState);
    //    //}

    //    //try
    //    //{
    //    //    await _CQRHeaderDao.UpdateAsync(header);
    //    //    return NoContent();
    //    //}
    //    //catch (KeyNotFoundException)
    //    //{
    //    return NotFound();
    //    //}
    //}
    [HttpPost("UploadAttachment")]
    public async Task<IActionResult> UploadFile(IFormFile file)
    {
        // todo check :Dim bCanAddViewReAttachAttachmt As Boolean = (tCQR.iOpenMode <> OPEN_View) And (bAfmAttachmtEnable Or tCQR.bSuperUser)
        if (file == null || file.Length == 0)
            return BadRequest("No file uploaded.");

        var savePath = Path.Combine("Uploads", file.FileName);
        using (var stream = new FileStream(savePath, FileMode.Create))
        {
            await file.CopyToAsync(stream);
        }

        return Ok(new { message = "Upload successful", fileName = file.FileName });
    }

    // DELETE: api/CQRHeader/5
    [HttpDelete("QueueKey/{QueueKey}")]
    public async Task<IActionResult> DeleteCQRHeader(int id)
    {
        try
        {
            //await _CQRHeaderDao.DeleteAsync(id);
            return NoContent();
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
    }

    //[HttpGet]
    //[Route("countAllCQRHeader")]
    //public async Task<ActionResult<int>> countCQRHeader()
    //{
    //    var result = await _cqrIhsRepository.countTotalAsync();
    //    return Ok(result);
    //}
    // TODO: 重構為 GetCQRGDPIMPhaseByQueueAndPhaseQuery
    // [HttpGet]
    // [Route("GetByQueueAndPhase/QueueKey/${queuekey}/Phase/${phase}")]
    // public async Task<ActionResult<int>> GetByQueueAndPhase(int queuekey, int phase)
    // {
    //     var query = new GetCQRGDPIMPhaseByQueueAndPhaseQuery(queuekey, phase);
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }

    // TODO: 重構為 GetDisplayNameQuery
    // [HttpGet]
    // [Route("GetDisplayName/{fieldName}")]
    // public async Task<ActionResult<string>> GetDisplayName(string fieldName)
    // {
    //     var query = new GetDisplayNameQuery(fieldName);
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }

    // TODO: 重構為 GetAwardModelYearQuery
    // [HttpGet]
    // [Route("GetAwardModelYear")]
    // public async Task<ActionResult<IEnumerable<Tuple<string, string>>>> GetAwardModelYear()
    // {
    //     var query = new GetAwardModelYearQuery();
    //     var result = await _mediator.Send(query);
    //     return Ok(result);
    // }

    [HttpGet]
    [Route("GetIHSFolderCtierias")]
    public async Task<ActionResult<IHSFolderCriteria>> GetIHSFolderCtierias()
    {
        try
        {
            var result = await _appServices.QueryService.GetIHSFolderCriteriasAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, "An error occurred while retrieving IHS Folder Criterias");
        }
    }

    //[HttpGet]
    //[Route("GetIHSFieldAsync")]
    //public async Task<ActionResult<dynamic>> GetIHSFieldAsync()
    //{
    //    var result = await _cqrIhsRepository.GetIHSFieldAsync("ProductionNameplate", "ProductionNameplate");
    //    return Ok(result);
    //}

    //[HttpGet]
    //[Route("UseDatabase")]
    //public ActionResult<string> GetUseDatabase()
    //{
    //    var msgDB = ConfigUtil.GetUseDatabase(_configuration);
    //    return Ok($"Running....{msgDB}");
    //}

    //[HttpGet]
    //[Route("ASPNETCORE_ENVIRONMENT")]
    //public ActionResult<string> GetASPNETCORE_ENVIRONMENT()
    //{
    //    // 返回字符串响应
    //    var result = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

    //    return Ok($"ASPNETCORE_ENVIRONMENT:{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}");
    //}
    ////https://learn.microsoft.com/zh-tw/azure/active-directory-b2c/enable-authentication-web-api?tabs=csharpclient
    //[HttpGet]
    //[Route("Identity")]
    //public ActionResult GetUserIdentity()
    //{
    //    return Ok(new { name = User.Identity.Name });
    //}

    //[HttpGet]
    //[Route("/Home/Error")]
    //public IActionResult Error()
    //{
    //    return StatusCode(500); // 返回500状态码
    //}
}
