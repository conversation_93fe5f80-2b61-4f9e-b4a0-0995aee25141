import type { RouteRecordName } from "vue-router";

export type cacheType = {
  mode: string;
  name?: RouteRecordName;
};

export type positionType = {
  startIndex?: number;
  length?: number;
};

export type appType = {
  sidebar: {
    opened: boolean;
    withoutAnimation: boolean;
    // 判断是否手动点击Collapse
    isClickCollapse: boolean;
  };
  layout: string;
  device: string;
  viewportSize: { width: number; height: number };
  sortSwap: boolean;
};

export type multiType = {
  path: string;
  name: string;
  meta: any;
  query?: object;
  params?: object;
};

export type setType = {
  title: string;
  fixedHeader: boolean;
  hiddenSideBar: boolean;
};

export type userType = {
  avatar?: string;
  username?: string;
  nickname?: string;
  roles?: Array<string>;
  permissions?: Array<string>;
  verifyCode?: string;
  currentPage?: number;
  isRemembered?: boolean;
  loginDay?: number;
  token?: string;
  userInfo: userInfo | null;
};

export type userInfo = {
  username: string;
  roles: string[];
  email?: string;
  [key: string]: any;
};

export type RoleUserInfo = {
  fullName: string;
  userId: string;
};

// 用戶資料型別
export interface UserFullName {
  fullName: string;
  userId: string;
}

// 角色用戶集合型別
export interface RoleUserCollection {
  roleName: string;
  roleId: number;
  users: UserFullName[];
}

// 如果需要包含用戶數量的完整版本
export interface RoleUserCollectionComplete {
  roleName: string;
  roleId: number;
  users: UserFullName[];
  userCount: number;
  displayName?: string; // 可選屬性
}

// 或者使用 union type
export type RoleName = "PETMId" | "PGMId" | "CostEstimatorId" | "AccountMgrId";

// 定義 API 的完整回傳資料結構
export type HttpResponseResult = {
  statusCode: number;
  success: boolean;
  data: any;
};

export type OEMHiearchy = {
  kunnr: string;
  hkunnr: string;
  vkorg: string;
  oem: string;
  oeM_NAME: string;
  oemgroup: string;
  oemgrouP_NAME: string;
  erP_OEM: string;
  erP_OEM_Group: string;
};

export type OEM = {
  oem: string;
  oeM_NAME: string;
};
export type OEMHGroup = {
  oemgroup: string;
  oemgrouP_NAME: string;
};

export interface IhsDataItem {
  plantMnemonic: string;
  oemGroup: string;
  oem: string;
  platform: string;
  program: string;
  nameplate: string;
  region: string;
  country: string;
  sop: string;
  eop: string;
  prodDesc: string;
  soldFrom: string;
  finalAssembly: string;
}

export type CQRHeader = {
  cqrDesc: string;
  ihsData: IhsDataItem[];
  modelYear: string;
  volPerAnnum: string;
  awardQuarter: string;
  awardYear: string;
  productLife: string;
  annualValue: string;
  currency: string;
  volumeComments: string;
  targetPricingComment: string;
  tdrDate: string;
  tdrNoInput: boolean;
  custQuoteDate: string;
  startOfProd: string;
  regionalQuotingTeam: string;
  openingMeetingDate: string;
  commercialManager: string;
  engineeringSite: string;
  bgInfo: string;
  custProdTargets: string;
  obsolReq: string;
  accountManager: string;
  accountDirector: string;
  communication: string;
  financeCoordinator: string;
  engineeringDirector: string;
  asilLevel: string;
  pmCoordinator: string;
  engineeringCoordinator: string;
  autosar: string;
  meCoordinator: string;
  purchasingCoordinator: string;
  cybersecurity: string;
  otherRelevantFeatures: string;
  timeframeOKInd: string;
  informationOKInd: string;
  workProceedOKInd: string;
  elecInputReqdInd: string;
  ifCheckedDoneInd: string;
  healthAndSafetyInd: string;
  dateOfCSR: string;
  dueDateFromEng: string;
  dueDateToBnE: string;
  prdId: string;
  leadValidation: string;
  leadValidationNoCostImpact: string;
  leadPurchasingNoCostImpact: string;
};

export interface CQRIHSFolder {
  id: number;
  queueKey: number;
  uniqueNumber: number | null;
  archived: boolean | null;
  archivedByQueueKey: number | null;
  archivedByUser: string | null;
  archivedDate: string | null;
  dateAdded: string | null;
  dateUpdated: string | null;
  coreNameplatePlantMnemonic: string;
  region: string;
  country: string;
  platform: string;
  program: string;
  productionNameplate: string;
  startOfProduction: string; // 格式如 "2026-08"
  endOfProduction: string; // 格式如 "2034-07"
  oemGroup: string;
  oem: string;
  productDescription: string;
  productGrouping: string | null;
  soldFrom: string;
  finalAssembly: string;
}

export interface ROUTING_RGHeader {
  id: number;
  folderType: string;
  folderNbr: string;
  taskCode: string;
  partDwgNbr: string;
  actionTaskCode: string;
  routingTaskCode: string;
  assignedTo: string;
  sortDate: string;
  sortTime: string;
  assignedDate: string;
  asignedTime: string;
  dueDate: string;
  doneInd: any;
  result: any;
  doneDate: any;
  doneByName: any;
  currentTaskInd: string;
  toBeViewedInd: any;
  notificationInd: string;
  doneTm: any;
  eMailInd: string;
  comment: any;
  folderNbr_int: number;
}

export interface AttachFileInfo {
  checkoutUser: any;
  attdiR_CheckoutLog: any;
  queueKey: number;
  attachTypeCode: string;
  mrpCompanyCode: any;
  partNbr: any;
  revLevel: any;
  attachTitle: string;
  fileLocation: string;
  viewPgmCode: any;
  createdByName: string;
  createdDate: string;
  createdTime: string;
  createdByLocCode: any;
  folderTypeCode: string;
  folderNbr: string;
  comments: any;
  folderStatus: string;
  customData: string;
  mimeType: any;
  folderNbr_int: number;
}

// export interface CreateCQRForm {
//   cqrType: string;
//   projectNumber: string;
//   quoteType: string;
//   QueueKey: number;
// }

export interface ValidateCQRRequest {
  cqrType: string;
  projectNumber: string;
  // ModifiedProjNbr: string;
  // ModifiedRevNbr: string;
  quoteType: string;
  // QueueKey: number;
}
