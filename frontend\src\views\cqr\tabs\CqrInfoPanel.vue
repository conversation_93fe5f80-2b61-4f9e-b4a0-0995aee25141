<template>
  <el-card class="cqr-card" shadow="never">
    <div class="info-inline">
      <span><strong>CQR Number:</strong> {{ cqrNumber }}</span>
      <span><strong>Originator:</strong> {{ originator }}</span>
      <span><strong>Origination Date:</strong> {{ originationDate }}</span>
      <span><strong>Status:</strong> {{ status }}</span>
      <span><strong>OEM Group:</strong> {{ oemGroup }}</span>
      <span><strong>Quote Type:</strong> {{ quoteType }}</span>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// Props 接收外部資料
defineProps<{
  cqrNumber: string;
  originator: string;
  originationDate: string;
  status: string;
  oemGroup: string;
  quoteType: string;
}>();
</script>
<style scoped>
.cqr-card {
  /* background-color: #003300; */
  color: black;
  font-family: "Courier New", monospace;
  padding: 1px 1px;
}

.info-inline {
  display: flex;
  flex-wrap: nowrap;
  gap: 14px;
  white-space: nowrap;
  overflow-x: auto;
  font-size: 14px;
  align-items: center;
}

.info-inline span {
  display: inline-block;
}
</style>
