﻿using System.Text;
using GemBox.Spreadsheet;
using Microsoft.AspNetCore.Http;

namespace CQR.Doa;

public class ModCQR
{
    // Constants
    public const int OPEN_Edit = 1;
    public const int OPEN_View = 2;
    public const string USER_AKOVACIC = "ASEIBERT";

    public const string BUSPLAN_TARGET = "00000001";
    public const string BUSPLAN_REPLACEMENTTARGET = "00000002";
    public const string BUSPLAN_OPPORTUNITY = "00000003";
    public const string BUSPLAN_OTHER = "00000004";

    private readonly IHttpContextAccessor _httpContextAccessor;
    public ModCQR(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }


    //// Method for optional message
    //public static void OptionalMessage(Page atPage, string sMsg, string linkBack = "releaseOptionalLink")
    //{
    //    var str = new StringBuilder();
    //    str.Append("<script type=\"text/javascript\">\n");
    //    str.Append($"   OptionalMessage('{sMsg.Replace("'", "`")}', '{linkBack}');\n");
    //    str.Append("</script>\n");
    //    atPage.ClientScript.RegisterStartupScript(atPage.GetType(), "optionalMessage", str.ToString());
    //}

    // Method for alert message
    //public static void AlertMessage(Page atPage, string sMsg, string sCaption)
    //{
    //    var script = new StringBuilder();
    //    script.Append("<script type='text/javascript'>");
    //    script.Append($"   AlertMessage('{sCaption.Replace("'", "\\'")}', \"{sMsg}\", 'auto', 'auto', true);");
    //    script.Append("</script>");
    //    atPage.ClientScript.RegisterClientScriptBlock(atPage.GetType(), "alertMessage", script.ToString());
    //}

    //// Get session object
    //public static object GetSessionObject(Page atPage, int aiQueueKey)
    //{
    //    return GetSessionCQR(atPage, aiQueueKey);
    //}

    // Strip extension from path
    public static string mStripExt(string asPath)
    {
        string sTemp = asPath;
        for (int iLoop = asPath.Length; iLoop > 0; iLoop--)
        {
            if (asPath[iLoop - 1] == '.')
            {
                sTemp = asPath.Substring(0, iLoop - 1);
                break;
            }
            else if (asPath[iLoop - 1] == '\\' || asPath[iLoop - 1] == '/' || asPath[iLoop - 1] == ':')
            {
                break;
            }
        }
        return sTemp;
    }

    // Strip path from filename
    public static string mStripPath(string asPath)
    {
        string sTemp = asPath;
        for (int iLoop = asPath.Length; iLoop > 0; iLoop--)
        {
            if (asPath[iLoop - 1] == '\\' || asPath[iLoop - 1] == '/' || asPath[iLoop - 1] == ':')
            {
                sTemp = asPath.Substring(iLoop);
                break;
            }
        }
        return sTemp;
    }

    // Set header info for the page
    //public static void SetHeaderInfo(corePage tPage, pageHeader pageHdr)
    //{
    //    if (!tPage.IsPostBack)
    //    {
    //        int iQueueKey = int.Parse(tPage.Request["QueueKey"]);
    //        var tConcern = GetSessionCQR(tPage, iQueueKey);
    //        tConcern.SetHeaderInfo(pageHdr);
    //    }
    //}

    // Remove characters
    public static string mRemoveChars(string asValue)
    {
        string sTemp = asValue.Replace(" ", "").Replace("*", "%");
        return sTemp;
    }

    // Format dollar amount with commas
    public static string mCommifyDollarAmount(string numberStr, int places = 0)
    {
        if (string.IsNullOrEmpty(numberStr))
        {
            return "";
        }
        string format = places == 0 ? "###,###,##0" : places == 2 ? "###,###,##0.00" : "###,###,##0.0000";
        return string.Format(format, Convert.ToDecimal(numberStr));
    }

    // Remove commas and dollar sign
    public static string mDecommifyDollarAmount(string numberStr)
    {
        return numberStr.Replace(",", "").Replace("$", "");
    }

    // Get attachment directory
    public static string Get_AttachDirectory()
    {
        return "../Attachments";
    }

    // Set redirect page
    //public  void SetRedirectPage()
    //{
    //    HttpContext.Current.Session["CQR_OpenedFrom"] = _httpContextAccessor.HttpContext.Request["AppMgr"];
    //}
    public void SetRedirectPage()
    {
        var context = _httpContextAccessor.HttpContext;

        // 取 URL Query 字串 ?AppMgr=1 這種
        var appMgr = context.Request.Query["AppMgr"].ToString();

        // 或者取 POST 來的資料
        // var appMgr = context.Request.Form["AppMgr"].ToString();

        // 儲存到 Session
        context.Session.SetString("CQR_OpenedFrom", appMgr);
    }

    // Get redirect page
    public string GetRedirectPage()
    {
        // 使用 IHttpContextAccessor 來取得 HttpContext
        var context = _httpContextAccessor.HttpContext;

        // 檢查 Session 值
        if (context.Session.GetString("CQR_OpenedFrom") == "1")
        {
            // 判斷 PATH_INFO 是否包含 "_staging"
            string sSuffix = context.Request.Path.Value.Contains("_staging", StringComparison.OrdinalIgnoreCase) ? "_staging" : "";
            return $"/ApplicationManager{sSuffix}";
        }
        else if (context.Session.GetString("LastSearch") == "REPORTS")
        {
            return "../Source/SearchView.aspx";
        }

        return "../Source/Search.aspx";
    }


    // Change background of TextArea
    //public static void TextAreaBG(TextBox textArea)
    //{
    //    if (string.IsNullOrEmpty(textArea.Attributes["OnClick"]))
    //    {
    //        textArea.Style.Add("background-color", "silver");
    //    }
    //}

    // Set GemBox license
    public static void SetGemBoxLicense()
    {
        SpreadsheetInfo.SetLicense("E3JO-TSTN-883A-LM58");
    }

    // Latin to ASCII transliteration
    public static string LatinToAscii(string inString)
    {
        var newString = new StringBuilder();
        inString = System.Text.RegularExpressions.Regex.Replace(inString, @"[\x00-\x1f]", string.Empty);

        foreach (var ch in inString.Normalize(System.Text.NormalizationForm.FormKD))
        {
            newString.Append(ch);
        }

        return newString.ToString();
    }

}