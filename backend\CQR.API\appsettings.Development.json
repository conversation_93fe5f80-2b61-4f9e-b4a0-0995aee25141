{"ConnectionStrings": {"DefaultConnection": "Server=localhost\\MSSQLSERVER02;Database=TempRestore2;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=true;User ID=lyon;Password=******"}, "Authentication": {"UseAzureAd": false}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "TenantId": "dc94cd8a-025c-455a-9e35-c80612a79987", "ClientId": "c89dd02e-f016-41e1-8491-db6e4c76bc8e", "Audience": "api://c89dd02e-f016-41e1-8491-db6e4c76bc8e"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "SAP": {"RFC_NAME": "BAQ", "RFC_POOL_SIZE": 5, "RFC_IDLE_TIMEOUT": 600, "AppServerHost": "*********", "SystemNumber": "00", "Client": "100", "User": "RFC_XSA_USER", "Password": "Welcome#12", "Language": "EN"}, "SmtpSettings": {"SMTP_PORT": "25", "SMTP_HOST": "***********", "SMTP_SECURE": false, "SMTP_IGNORE_TLS": true, "SMPT_DESC": "bcs smtp", "SMTP_MAILER": "<EMAIL>"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Debug", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/applog-.txt", "rollingInterval": "Day"}}], "Enrich": ["FromLogContext", "WithMachineName"], "Properties": {"ApplicationName": "Your ASP.NET Core App"}}}