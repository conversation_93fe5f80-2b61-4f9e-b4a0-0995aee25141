﻿using CQR.Application.Dto.RoutingTask;
using CQR.Application.Repositories;
using CQR.Persistence.Query.Base;
using Dapper;
using Microsoft.Extensions.Configuration;
using System.Data;

namespace CQR.Persistence.Query.Repositories;

public class CqrHeaderQueryRepository : QueryBaseRepository, ICqrHeaderQueryRepository
{
    public CqrHeaderQueryRepository(IConfiguration configuration) : base(configuration)
    {
    }

    public Task<long?> GetPreviousRevisionQueueKey(string projectNumber, string revision, int modifiedRevision)
    {
        throw new NotImplementedException();
    }

    public async Task<IEnumerable<RoutingTaskDto>> GetRoutingTaskByQueuekey(int queueKey, string fodlerType)
    {
        var sSQL = @"SELECT
              QueueKey,              TaskCode,              ActionTaskCode,              RoutingTaskCode,
              AssignedTo,              AssignedDate,              AsignedTime,              DueDate,
              Result =
                CASE
                  WHEN UPPER(LEFT(Result, 3)) = 'ACK' THEN 'Acknowledged'
                  WHEN UPPER(LEFT(Result, 3)) = 'TER' THEN 'Terminated'
                  ELSE Result
                END,
              PartDwgNbr,              NotificationInd,              CurrentTaskInd,              DoneInd,
              ToBeViewedInd,              DoneDate,              DoneTm,              DoneByName
                    FROM ROUTING_RGHeader
            WHERE FolderNbr = @FolderNbr
              AND FolderType = @FolderType
            ORDER BY
              SortDate, SortTime, TaskCode, PartDwgNbr, ActionTaskCode, RoutingTaskCode";
        return await _connection.QueryAsync<RoutingTaskDto>(sSQL, new { FolderNbr = queueKey, FolderType = fodlerType });
    }

    public async Task<T> ExecuteScalarAsync<T>(string sql, object parameters = null)
    {
        try
        {
            if (_connection.State != System.Data.ConnectionState.Open)
                _connection.Open();
            
            return await _connection.ExecuteScalarAsync<T>(sql, parameters);
        }
        catch (Exception ex)
        {
            throw new DataException($"ExecuteScalarAsync failed. SQL: {sql}, Error: {ex.Message}", ex);
        }
    }

    public async Task<IEnumerable<T>> QueryAsync<T>(string sql, object parameters = null)
    {
        try
        {
            if (_connection.State != System.Data.ConnectionState.Open)
                _connection.Open();
            
            return await _connection.QueryAsync<T>(sql, parameters);
        }
        catch (Exception ex)
        {
            throw new DataException($"QueryAsync failed. SQL: {sql}, Error: {ex.Message}", ex);
        }
    }

}
