<template>
  <el-main class="no-padding-top">
    <CqrInfoPanel
      cqrNumber="83501 (New)"
      originator="<PERSON>, <PERSON>"
      originationDate="14-Feb-2025"
      status="CQR Being Initiated"
      oemGroup="Volkswagen"
      quoteType="Customer RFQ"
    />
    <NavButtonPanel
      :enabled-buttons="['save', 'saveexit']"
      :show-void="false"
      @action="handleAction"
    />
    <el-tabs v-model="activeTab" :stretch="true">
      <el-tab-pane label="Description" name="description">
        <DescriptionTab v-if="activeTab === 'description'" />
      </el-tab-pane>
      <el-tab-pane label="Engineering" name="engineering">
        <EngineerTab v-if="activeTab === 'engineering'" />
      </el-tab-pane>
      <el-tab-pane label="Quote Response" name="quote-response">
        <QuoteResponseTab
      /></el-tab-pane>
      <el-tab-pane label="Sales Closeout" name="sales-closeout"
        ><SalesCloseoutTab />
      </el-tab-pane>
      <el-tab-pane label="GDPIM Gateway 1" name="gdpim-gateway-1">
        <GDPIMGatewayTab
      /></el-tab-pane>
      <el-tab-pane label="GDPIM Gateway 2" name="gdpim-gateway-2"
        ><GDPIMGatewayTab
      /></el-tab-pane>
      <el-tab-pane label="Attachment" name="attachment">
        <AttachmentTab />
      </el-tab-pane>
      <el-tab-pane label="Routing" name="routing">
        <RoutingTab />
      </el-tab-pane>
    </el-tabs>
    <UploadDialog ref="uploadDialogRef" v-model="store.visible" />
    <NavButtonPanel
      :enabled-buttons="['save', 'saveexit']"
      :show-void="false"
      @action="handleAction"
    />
    <!-- <div style="background: #f0f0f0; padding: 10px; margin: 10px 0">
      <p>Current Route: {{ route.fullPath }}</p>
      <p>Active Tab (component state): {{ activeTab }}</p>
      <p>Route Matched: {{ route.matched.length }}</p>
    </div> -->

    <!-- <div
      style="
        background: #f0f0f0;
        padding: 10px;
        margin: 10px 0;
        font-family: monospace;
      "
    > -->
    <!-- <h4>調試信息:</h4>
      <p><strong>Current Route:</strong> {{ route.fullPath }}</p>
      <p><strong>Route Name:</strong> {{ route.name }}</p>
      <p><strong>Route Params:</strong> {{ JSON.stringify(route.params) }}</p>
      <p><strong>Active Tab (component state):</strong> {{ activeTab }}</p>
      <p><strong>Queue Key:</strong> {{ queueKey }}</p>
      <p><strong>Route Matched:</strong> {{ route.matched.length }} routes</p>
      <p><strong>Matched Routes:</strong></p> -->
    <!-- <ul>
        <li v-for="(match, index) in route.matched" :key="index">
          {{ index }}: {{ match.name }} ({{ match.path }})
        </li>
      </ul> -->
    <!-- <p>
        <strong>Current Tab From Route (getTabFromRoute()):</strong> -->
    <!-- {{ getTabFromRoute() }} -->
    <!-- </p> -->
    <!-- </div> -->
  </el-main>
</template>
<script setup lang="ts">
import { onMounted, watch, ref, nextTick, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import type { TabsPaneContext } from "element-plus";

import DescriptionTab from "@/views/cqr/tabs/DescriptionTab.vue";
import EngineerTab from "@/views/cqr/tabs/EngineerTab.vue";
import QuoteResponseTab from "@/views/cqr/tabs/QuoteResponseTab.vue";
import SalesCloseoutTab from "@/views/cqr/tabs/SalesCloseoutTab.vue";
import GDPIMGatewayTab from "@/views/cqr/tabs/GDPIMGatewayTab.vue";
import AttachmentTab from "@/views/cqr/tabs/AttachmentTab.vue";
import RoutingTab from "@/views/cqr/tabs/RoutingTab.vue";
import CqrInfoPanel from "@/views/cqr/tabs/CqrInfoPanel.vue";
import NavButtonPanel from "@/views/cqr/tabs/NavEditButtonPanel.vue";
import UploadDialog from "@/views/cqr/tabs/UploadDialog.vue";

// import the correct member from formModel, for example:
import { useFormModelStore } from "@/store/modules/formModel";
// or remove this line if you do not use anything from formModel
import { useUploadDialogStore } from "@/store/modules/uploadDialog";
import type { CQRHeaderCollection } from "@/types/cqr/CQRHeaderCollection";

const formStore = useFormModelStore();

const store = useUploadDialogStore();

const route = useRoute();
const router = useRouter();

const queueKey = computed(() => route.params.queueKey as string);
const id = computed(() => route.params.queueKey as string); // id is same as queueKey
const visibleUpload = ref(false);
const activeTab = ref("description"); // Initialize with default

// 父元件
const descTabRef = ref();
const engTabRef = ref();

// 使用 interface 作為型別約束
const header = ref<CQRHeaderCollection>({
  // sUniqueNumber: "",
  sPlatform: "",
  sProgram: "",
  bArchived: false
});

// const cqrHeaderCl = ref<CQRHeaderCollection>(new CQRHeaderCollection());
// const header = ref<CQRHeaderCollection>(new CQRHeaderCollection());

function handleAction(action) {
  console.log("使用者點了：", action);
  // 根據 action 處理對應邏輯

  if (action === "saveexit") {
    // 假設這裡是保存並退出的邏輯
    console.log("保存並退出");
    // 例如，保存數據後導航到其他頁面
    router.push({ name: "cqr-list" });
  } else if (action === "save") {
    //TODO 檢查條件 取到表單資料,發request 檢查為modify .

    // 保存邏輯
    console.log("保存");
    //TODO 取到所有tab 資料,將它寫入到, 加入檢驗規則,create new cqr

    // var createCQRHeaderInput = {
    //   cQRHeader: null
    //   // queueKey: queueKey.value,
    //   // 其他必要的欄位
    // };

    // CQRHeaderCollection;

    const payload = {
      cqrHeader: formStore.modelCQRHeader, // DescriptionTab
      ihsFolder: formStore.modelIHSFolder
      // engineer: formStore.en,
      // quoteResponse: formStore.data
      // ... 其他 tab
    };

    // 修改屬性
    header.value.iCQRHeader = formStore.modelCQRHeader;
    // 這裡可以調用 API 或其他方法來保存數據
    formStore
      .saveCQRHeaderCollection(header)
      .then(response => {
        console.log("保存成功", response);
        // 可以在這裡處理保存成功後的邏輯
      })
      .catch(error => {
        console.error("保存失敗", error);
        // 處理錯誤情況
      });
  } else if (action === "void") {
    // 處理 void 邏輯
    console.log("處理 void");
  } else {
    console.log(`處理 ${action} 動作`);
  }
}

// 供外部呼叫打開 dialog
function open(): void {
  visibleUpload.value = true;
}
// 定義供父組件使用的 method
defineExpose({
  open
});

// Manual navigation for testing (can be removed later)
async function navigateToTab(tabName: string) {
  await onTabClick({ paneName: tabName } as TabsPaneContext); // Reuse onTabClick logic
}

onMounted(async () => {
  const intQueuekey = Number(queueKey.value);
  //TODO 取得參數queuekey 若大於0
  if (intQueuekey > 0) {
    //TODO 載入資料 CQR_Header_collection.
    formStore.fetchModelCQRHeaderCollectionByQueuekey(intQueuekey);
  }

  await formStore.fetchDictCodes();

  //  const CQR_CALCCURR = formStore.getCodesByGroup("CQR_CALCCURR")
  // console.log("🔍 QuoteType codes:", quoteTypes);
});
</script>
<style>
.no-padding-top {
  padding-top: 0px !important;
}
</style>
