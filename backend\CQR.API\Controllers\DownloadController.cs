﻿using CQR.Application.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace CQR.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DownloadController : ControllerBase
{
    private readonly IExcelService _excelService;

    public DownloadController(IExcelService excelService)
    {
        _excelService = excelService;
    }

    [HttpGet("excel/{fileName}")]
    public IActionResult DownloadExcel(string fileName)
    {
        var filePath = _excelService.GenerateExcelFile(fileName);

        var mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        var bytes = System.IO.File.ReadAllBytes(filePath);

        return File(bytes, mimeType, fileName);
    }
}

