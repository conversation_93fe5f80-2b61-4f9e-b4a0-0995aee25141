
using CQR.API.Authentication;
using CQR.API.Middleware;
using CQR.Application.UseCases.CQR_Headers.Commands.Handler;
using CQR.Application.Common;
using CQR.Application.Interfaces;
using CQR.Application.Repositories;
using CQR.Application.Services;
using CQR.Domain.Interfaces.Repository;
using CQR.Domain.CQR_AntaresFolders;
using CQR.Domain.CQR_GDPIMPhases;
using CQR.Domain.CQR_IHSs;
using CQR.Domain.CQRIHSFolder;
using CQR.Domain.IServices;
using CQR.Domain.ROUTING_RGHeaders;
using CQR.Domain.Services.Implementations;
using CQR.Domain.TRS_Headers;
using CQR.Infrastructure.FileStorage;
using CQR.Persistence.Command.Persistence;
using CQR.Persistence.Command.Repositories;
using CQR.Persistence.DoCommandmain.Repositories;
using CQR.Persistence.Query.Implementations;
using CQR.Persistence.Query.Repositories;
using CQRLIB.Config.SqlSugar;
using DotNetGPLAPI.Filters;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Web;
using Serilog;

internal class Program
{
    private static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        var useAzureAd = builder.Configuration.GetValue<bool>("Authentication:UseAzureAd");

        // 註冊 AutoMapper
        //todo builder.Services.AddAutoMapper(typeof(MappingProfile)); // 指定映射配置類別
        // builder.Services.AddAutoMapper(typeof(MapperConfig));
        // builder.Services.AddAutoMapper(typeof(CQRLIB.Config.MappingProfile)); // 指定映射配置類別

        // 設定 Serilog
        builder.Host.UseSerilog((context, configuration) =>
        {
            configuration.ReadFrom.Configuration(context.Configuration);
        });

        // Configure the connection string
        var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
        Console.WriteLine($"Connection String: {connectionString}");

        builder.Services.AddDbContext<CQRDbContext>(options =>
            options.UseSqlServer(connectionString,
                b => b.MigrationsAssembly("CQR.API")));

        // 将 SqlSugarClient 注入依赖注入容器
        builder.Services.AddSingleton(provider => SqlSugarConfiguration.GetDbContext(connectionString));
        // 註冊 SqlConnectionService 到 DI 容器
        //builder.Services.AddSingleton<ISqlConnectionService, SqlConnectionService>();

        // 讀取配置中的連接字串
        //string connectionString = builder.Configuration.GetConnectionString("DefaultConnection");

        // 註冊 SqlConnectionService 並注入連接字串
        builder.Services.AddSingleton<ISqlConnectionService>(provider =>
            new SqlConnectionService(connectionString));

        // Add DbContext to the services container
        // builder.Services.AddDbContext<CQrDbContext>(options => options.UseSqlServer(connectionString));

        #region 註冊到 DI 容器中
        builder.Services.AddScoped<ICQRIHSRepository, CQRIHSRepository>();
        builder.Services.AddScoped<CQR.Domain.CQRHeaders.ICQRHeaderRepository, CQRHeaderRepository>();
        builder.Services.AddScoped<ICQRGDPIMPhasesRepository, CQRGDPIMPhasesRepository>();
        builder.Services.AddScoped<CQR.Domain.TRS_Headers.ICodeRepository, CodeRepository>();
        builder.Services.AddScoped<ICQRIHSFolderRepository, CQRIHSFolderRepository>();
        //:Query
        builder.Services.AddScoped<IReportQueryRepository, CQRIHSQueryRepository>();
        builder.Services.AddScoped<IUserQueryRepository, UserQueryRepository>();
        builder.Services.AddScoped<IRoleUserQueryRepository, RoleUserQueryRepository>();
        builder.Services.AddScoped<IERPMasterQueryRepository, ERPMasterQueryRepository>();
        builder.Services.AddScoped<IRoutingRGHeaderQueryRepository, RoutingRGHeaderQueryRepository>();
        builder.Services.AddScoped<IRoutingRGHeaderRepository, RoutingRGHeaderRepository>();
        builder.Services.AddScoped<IAttachFileQueryRepository, AttachFileQueryRepository>();
        builder.Services.AddScoped<IUserRoleQueryRepository, UserRoleQueryRepository>();
        builder.Services.AddScoped<ICqrHeaderQueryRepository, CqrHeaderQueryRepository>();
        builder.Services.AddScoped<ICQRAntaresFolderRepository, CQRAntaresFolderRepository>();
        builder.Services.AddScoped<IUserProfileHeaderQueryRepository, UserProfileHeaderQueryRepository>();
        builder.Services.AddScoped<ITRSHeaderQueryRespository, TRSHeaderQueryRepository>();
        builder.Services.AddScoped<ICQRAntaresQueryRepository, CQRAntaresQueryRepository>();
        builder.Services.AddScoped<ICQRValidationService, CQRValidationService>();
        builder.Services.AddScoped<IAttachmentService, CQR.Application.Services.AttachmentService>();

        //Unit of work
        builder.Services.AddScoped<CQR.Domain.Interfaces.Repository.IUnitOfWork, UnitOfWork>();
        //:Handler
        builder.Services.AddScoped<ICQRCommandHandler, CQRCommandHandler>();

        // Collection
        //builder.Services.AddScoped<RepositoryCollection>();
        builder.Services.AddScoped<IRepositoryCollection, RepositoryCollection>();
        builder.Services.AddScoped<ICQRQueryService, CQRQueryService>();
        builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
        builder.Services.AddScoped<IApplicationServices, ApplicationServices>();

        // Infrastructure
        builder.Services.AddScoped<IExcelService, ExcelService>();


        // MediatR has been replaced with ApplicationServices pattern


        //IServiceCollection serviceCollection = builder.Services.AddMediatR(typeof(Program)); // 或任何你的 Handler 所在的 Assembly
        //builder.Services.AddValidatorsFromAssemblyContaining<CreateProductInputValidator>();

        // 註冊 PipelineBehavior
        //builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));


        //builder.Services.AddScoped<IProductRepository, ProductRepository>();
        //builder.Services.AddScoped<IProductDetailRepository, ProductDetailRepository>();
        //builder.Services.AddScoped<IProductService, ProductService>();
        //builder.Services.AddScoped<ISapConnectionService, SapConnectionService>();

        //builder.Services.AddScoped<ICustomService, CustomService>();

        //配置 FluentValidation
        //builder.Services.AddFluentValidation(fv => fv.RegisterValidatorsFromAssemblyContaining<CreateProductRequestValidator>());

        #endregion

        // Add services to the container.
        // builder.Services.AddControllers();
        builder.Services.AddHttpContextAccessor();

        // :Azure Directroy
        //builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        //    .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"));
        //builder.Services.AddAuthorization();

        //builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).addmi.AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"));

        if (useAzureAd)
        {
            builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"));

            builder.Services.AddAuthorization();
        }
        else
        {
            // 開發模式下：註冊假的認證（永遠通過）
            builder.Services.AddAuthentication("Fake")
                .AddScheme<AuthenticationSchemeOptions, FakeAuthHandler>("Fake", null);

            builder.Services.AddAuthorization();
        }

        // :註冊服務
        builder.Services.AddControllers(options =>
        {
            options.Filters.Add<ResultFilter>();
        });
        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen();

        /*tood 
        builder.Services.AddQuartz(q =>
                {
                    q.UseMicrosoftDependencyInjectionScopedJobFactory();
                    var jobKey = new JobKey("SampleJob");
                    q.AddJob<SampleJob>(opts => opts.WithIdentity(jobKey));
                    q.AddTrigger(opts => opts
                        .ForJob(jobKey)
                        .WithIdentity("SampleJob-trigger")
                        .WithCronSchedule("0/5 * * * * ?")); // 每 5 秒运行一次
                });
        builder.Services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);
        */

        // 設定 CORS
        // builder.Services.AddCors(options =>
        // {
        //     options.AddPolicy("AllowReactApp",
        //         policy =>
        //         {
        //             policy.WithOrigins("http://localhost:8000") // React 應用的本地地址
        //                   .AllowAnyMethod()
        //                   .AllowAnyHeader();
        //         });
        // });

        builder.Services.AddCors(options =>
           {
               options.AddPolicy("AllowAll",
                   builder => builder.AllowAnyOrigin()
                                     .AllowAnyMethod()
                                     .AllowAnyHeader());
           });

        var app = builder.Build();


        if (app.Environment.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }
        else
        {
            app.UseExceptionHandler("/Home/Error");
        }

        // Configure the HTTP request pipeline.
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        // 全域異常處理中介軟體
        app.UseMiddleware<GlobalExceptionMiddleware>();

        // app.UseHttpsRedirection();// 如果想要禁用 SSL，請移除此行


        //app.MapGet("/customer", (ICustomService customService) =>
        //{
        //    return customService.GetMessage();
        //});

        // app.MapGet("/product", () =>
app.UseHttpsRedirection();
        // {
        //     return new string[] { "Product1", "Product2", "Product3" };
        //     // return customSer vice.GetMessage();…
        // });

        // app.MapGet("/api/product", () =>
        // {
        //     return new string[] { "Product1", "Product2", "Product3" };
        //     // return customSer vice.GetMessage();…
        // });

        // app.MapGet("/weatherforecast", () =>
        // {
        //     var forecast = Enumerable.Range(1, 5).Select(index =>
        //         new WeatherForecast
        //         (
        //             DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
        //             Random.Shared.Next(-20, 55),
        //             summaries[Random.Shared.Next(summaries.Length)]
        //         ))
        //         .ToArray();
        //     return forecast;
        // })
        // .WithName("GetWeatherForecast")
        // .WithOpenApi();




        //240326 add  存取 upload file
        app.Use(next => new RequestDelegate(
             async context =>
             {
                 context.Request.EnableBuffering();
                 await next(context);
             }
            ));



        // app.UseCors("AllowReactApp"); // 啟用 CORS 策略
        app.UseCors("AllowAll");
        app.MapControllers();

        app.UseAuthentication();  // ⬅️ 重要
        app.UseAuthorization();

        //  app.UseEndpoints(endpoints => { endpoints.MapControllers(); });
        app.Run();
    }
}

#region 註冊到 DI 容器中

#endregion

//record WeatherForecast(DateOnly Date, int TemperatureC, string? Summary)
//{
//    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
//}
