﻿namespace CQR.Application.Dto.IHSFolder;

public class IHSFolderCriteria
{
    public IEnumerable<string> OEM { get; set; } = new List<string>();
    public IEnumerable<string> OEMGroup { get; set; } = new List<string>();
    public IEnumerable<string> Platform { get; set; } = new List<string>();
    public IEnumerable<string> Program { get; set; } = new List<string>();
    public IEnumerable<string> ProductionNameplate { get; set; } = new List<string>();
    public IEnumerable<Tuple<string, string>> Product { get; set; } = new List<Tuple<string, string>>();
    public IEnumerable<Tuple<string, string>> Site { get; set; } = new List<Tuple<string, string>>();
}