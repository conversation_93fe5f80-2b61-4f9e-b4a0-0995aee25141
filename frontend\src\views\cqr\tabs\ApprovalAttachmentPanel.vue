<template>
  <div class="approval-attachment-panel">
    <div class="header">
      <span class="title">Approval Attachments:</span>
      <!-- <el-button type="primary" size="small" text @click="onAddAttachment">
        add
      </el-button> -->
      <el-button @click="store.open()">Upload File</el-button>
    </div>

    <el-table :data="attachments" style="width: 100%; margin-top: 10px" border>
      <el-table-column prop="filename" label="Source File" />
      <el-table-column label="">
        <template #default="scope">
          <!-- 可放刪除、預覽等操作按鈕 -->
          <el-button
            type="danger"
            size="small"
            @click="emit('delete', scope.row)"
          >
            Delete
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { useUploadDialogStore } from "@/store/modules/uploadDialog";

const store = useUploadDialogStore();
const props = defineProps({
  attachments: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(["add", "delete"]);

function onAddAttachment() {
  emit("add");
}
</script>

<style scoped>
.approval-attachment-panel {
  border: 1px solid #ccc;
  padding: 16px;
}

.header {
  display: flex;
  align-items: center;
  font-size: 14pt;
  font-weight: bold;
}

.header .title {
  margin-right: 16px;
}
</style>
