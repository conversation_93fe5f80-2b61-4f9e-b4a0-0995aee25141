﻿using Microsoft.EntityFrameworkCore;
using CQR.Domain.TRS_Headers;
using CQR.Domain.CQR_TRS_Headers;

namespace CQR.Persistence.Command.Repositories;

public class CodeRepository : EntityRepository<CQR_TRS_Header, int>, ICodeRepository
{
    public CodeRepository(CQRDbContext context) : base(context) { }

    public Task<bool> createCode(string userLogin, string table_name, string desc)
    {
        throw new NotImplementedException();
    }

    //public int mTRSGetNextNumber(string asEntry, string asDefault, ref string asNewNumber)
    //{
    //    //corePage corePage2 = new corePage();
    //    string text = "SELECT _desc FROM TRS_Header";
    //    text += " WHERE table_name = 'NUMBERS'";
    //    text += " AND table_entry = '" + asEntry.Replace("'", "''") + "'";

    //    // 執行查詢，獲取當前數字
    //    string text2 = _connection.ExecuteScalar<string>(text);

    //    if (string.IsNullOrEmpty(text2)) // 如果未找到數字
    //    {
    //        // 查詢表格中當前最大的數字
    //        text = "SELECT TOP 1 table_entry FROM TRS_Header";
    //        text += " WHERE table_name = 'NUMBERS'";
    //        text += " ORDER BY table_entry DESC";

    //        // 再次執行查詢
    //        text2 = _connection.ExecuteScalar<string>(text);

    //        // 如果還是沒有數字，使用預設值
    //        if (string.IsNullOrEmpty(text2))
    //        {
    //            text2 = asDefault;
    //        }
    //        else
    //        {
    //            // 否則，將最大數字加 1
    //            text2 = (int.Parse(text2) + 1).ToString();
    //        }

    //        // 格式化數字，保證長度符合預設格式
    //        //text2 = text2.PadLeft(Math.Max(text2.Length, asDefault.Length), '0');

    //        // 插入新數字到表格
    //        text = "INSERT INTO TRS_Header (table_name, table_entry, _desc, child, format_data) VALUES (";
    //        text += "'NUMBERS', ";
    //        text += "'" + asEntry + "', ";
    //        text += "'" + text2 + "', ";
    //        text += "'','')";
    //    }
    //    else
    //    {
    //        // 如果已經有數字，則將其加 1
    //        text2 = (int.Parse(text2) + 1).ToString();

    //        // 格式化數字
    //        text2 = text2.PadLeft(Math.Max(text2.Length, asDefault.Length), '0');

    //        // 更新表格中的數字
    //        text = "UPDATE TRS_Header SET _desc = '" + text2 + "' WHERE table_name = 'NUMBERS' AND table_entry = '" + asEntry.Replace("'", "''") + "'";
    //    }

    //    // 執行 SQL 操作
    //    _connection.Execute(text);

    //    // 返回新生成的數字
    //    asNewNumber = text2;

    //    return -1; // 表示操作成功
    //}

    //public async Task<IEnumerable<CQR_TRS_Header>> GetGPLCodesAsync()
    //{
    //    var sql = @"SELECT  top 10 * From [dbo].[TRS_Header]";
    //    var result = await _connection.QueryAsync<CQR_TRS_Header>(sql, new { });
    //    return result;
    //}

    //public async Task<IEnumerable<CQR_TRS_Header>> GetGPLCodesDapper()
    //{
    //    string[] oCriterias = {
    //        "STATUSGPL",    "INCOTERM", "GPL_FINALASSEMBLY", "GPL_METAL", "GPL_PARTSTATUS",
    //        "GPL_PRICEREASON", "GPL_SOLDFROM", "GPL_TIER1", "TASKGPL", "CURRENCY", "GPL_FORM_TYPE", "GPL_FORM_PRICE_ACTION", "GPL_PO_STATUS", "STATUSGPL_FORM"
    //    };
    //    string sqlCmd = @"select* FROM [dbo].[GPL.TRS_Header] where table_name  in @Values";
    //    var result = await _connection.QueryAsync<CQR_TRS_Header>(sqlCmd, new { Values = oCriterias });
    //    return result;
    //}

    public Task<IList<string>> GetGPLCodesByTableNameInAsync(string table_name)
    {
        throw new NotImplementedException();
    }

    public Task<bool> updateById(string userLogin, CQR_TRS_Header parament)
    {
        throw new NotImplementedException();
    }
    public async Task<Dictionary<string, List<CQR_TRS_Header>>> GetDictCode()
    {
        const string INACTIVE = "1";
        // 查詢包含分組欄位和描述欄位的 SQL
        var parameters = new { };
        //var parameters = new { table_name = tableName };
        //string sql = $@"SELECT 
        //                table_entry AS Value,
        //                [desc] AS Description,
        //                format_data AS FormatData,
        //                inactive AS IsInactive,
        //                ISNULL({groupByField}, 'Uncategorized') AS GroupKey
        //              FROM [dbo].[GPL.TRS_Header] 
        //              WHERE table_name = @table_name 
        //              AND (inactive IS NULL OR inactive = 0)
        //              ORDER BY GroupKey, Description";

        var result = await _context.Set<CQR_TRS_Header>()
              .Where(h => h.inactive != "1") // Apply the inactive filter
              //.Take(50)
             .ToListAsync();

        if (result == null || !result.Any())
        {
            // No data found - consider logging this
            return new Dictionary<string, List<CQR_TRS_Header>>();
        }
        //var result = await _dbConnection.QueryAsync<CQR_TRS_Header>(sql, parameters, transaction: _transaction);

        // Group and create dictionary with the ACTUAL items from the query
        return result
            .GroupBy(item => item.table_name)
            .ToDictionary(
                group => group.Key,
                group => group.ToList() // Use the actual items, not new empty ones
            );
    }

    public Task<IEnumerable<CQR_TRS_Header>> GetGPLCodesAsync()
    {
        throw new NotImplementedException();
    }
}
/*
select  top 100 * from TRS_Header where table_name='QAFMDLYR'


select  top 100 * from TRS_Header where table_name='CQR_GateExit'

select  top 100 * from TRS_Header where table_name='MFGSITE';

select  top 100 * from TRS_Header where table_name='GPN_PRODSEG';

select  top 100 * from TRS_Header where table_name='CQR_CALCCURR';
select  top 100 * from TRS_Header where table_name='CQR_PF_Communication';
select  top 100 * from TRS_Header where table_name='CQR_PF_AUTOSAR';

select  top 100 * from TRS_Header where table_name='CQR_PF_Cybersecurity';
*/