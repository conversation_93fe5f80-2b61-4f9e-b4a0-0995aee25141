export interface RoutingTaskDto {
  QueueKey?: string;
  TaskCode?: string;
  ActionTaskCode?: string;
  RoutingTaskCode?: string;
  AssignedTo?: string;
  AssignedDate?: string;
  AsignedTime?: string; // 注意拼字 AsignedTime（原始欄位）
  DueDate?: string;
  Result?: string; // "Acknowledged", "Terminated", or original
  PartDwgNbr?: string;
  NotificationInd?: string;
  CurrentTaskInd?: string;
  DoneInd?: string;
  ToBeViewedInd?: string;
  DoneDate?: string;
  DoneTm?: string;
  DoneByName?: string;
}
