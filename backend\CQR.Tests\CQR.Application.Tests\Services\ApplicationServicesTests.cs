using CQR.Application.Interfaces;
using CQR.Application.Repositories;
using CQR.Application.Services;
using CQR.Application.UseCases.CQR_Headers.Commands.Handler;
using CQR.Infrastructure.Database;
using CQR.Infrastructure.External.SAP;
using Moq;
using Xunit;

namespace CQR.Application.Tests.Services;

public class ApplicationServicesTests
{
    private readonly Mock<ICQRCommandHandler> _mockCommandHandler;
    private readonly Mock<ICQRQueryService> _mockQueryService;
    private readonly Mock<ICQRValidationService> _mockValidationService;
    private readonly Mock<IAttachmentService> _mockAttachmentService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<ICustomService> _mockCustomService;
    private readonly Mock<IExcelService> _mockExcelService;
    private readonly Mock<IDatabaseService> _mockDatabaseService;
    private readonly Mock<ISapRFCService> _mockSapRFCService;
    private readonly Mock<ISqlConnectionService> _mockSqlConnectionService;
    private readonly Mock<ISapConnectionService> _mockSapConnectionService;
    private readonly Mock<ICurrentUserService> _mockCurrentUserService;
    private readonly Mock<IRepositoryCollection> _mockRepositoryCollection;
    
    private readonly IApplicationServices _applicationServices;

    public ApplicationServicesTests()
    {
        _mockCommandHandler = new Mock<ICQRCommandHandler>();
        _mockQueryService = new Mock<ICQRQueryService>();
        _mockValidationService = new Mock<ICQRValidationService>();
        _mockAttachmentService = new Mock<IAttachmentService>();
        _mockProductService = new Mock<IProductService>();
        _mockCustomService = new Mock<ICustomService>();
        _mockExcelService = new Mock<IExcelService>();
        _mockDatabaseService = new Mock<IDatabaseService>();
        _mockSapRFCService = new Mock<ISapRFCService>();
        _mockSqlConnectionService = new Mock<ISqlConnectionService>();
        _mockSapConnectionService = new Mock<ISapConnectionService>();
        _mockCurrentUserService = new Mock<ICurrentUserService>();
        _mockRepositoryCollection = new Mock<IRepositoryCollection>();

        _applicationServices = new ApplicationServices(
            _mockCommandHandler.Object,
            _mockQueryService.Object,
            _mockValidationService.Object,
            _mockAttachmentService.Object,
            _mockProductService.Object,
            _mockCustomService.Object,
            _mockExcelService.Object,
            _mockDatabaseService.Object,
            _mockSapRFCService.Object,
            _mockSqlConnectionService.Object,
            _mockSapConnectionService.Object,
            _mockCurrentUserService.Object,
            _mockRepositoryCollection.Object
        );
    }

    [Fact]
    public void ApplicationServices_ShouldInitializeAllServices()
    {
        // Assert
        Assert.NotNull(_applicationServices.CommandHandler);
        Assert.NotNull(_applicationServices.QueryService);
        Assert.NotNull(_applicationServices.ValidationService);
        Assert.NotNull(_applicationServices.AttachmentService);
        Assert.NotNull(_applicationServices.ProductService);
        Assert.NotNull(_applicationServices.CustomService);
        Assert.NotNull(_applicationServices.ExcelService);
        Assert.NotNull(_applicationServices.DatabaseService);
        Assert.NotNull(_applicationServices.SapRFCService);
        Assert.NotNull(_applicationServices.SqlConnectionService);
        Assert.NotNull(_applicationServices.SapConnectionService);
        Assert.NotNull(_applicationServices.CurrentUserService);
        Assert.NotNull(_applicationServices.Repositories);
    }

    [Fact]
    public void ApplicationServices_ShouldReturnCorrectServiceInstances()
    {
        // Assert
        Assert.Same(_mockCommandHandler.Object, _applicationServices.CommandHandler);
        Assert.Same(_mockQueryService.Object, _applicationServices.QueryService);
        Assert.Same(_mockValidationService.Object, _applicationServices.ValidationService);
        Assert.Same(_mockAttachmentService.Object, _applicationServices.AttachmentService);
        Assert.Same(_mockProductService.Object, _applicationServices.ProductService);
        Assert.Same(_mockCustomService.Object, _applicationServices.CustomService);
        Assert.Same(_mockExcelService.Object, _applicationServices.ExcelService);
        Assert.Same(_mockDatabaseService.Object, _applicationServices.DatabaseService);
        Assert.Same(_mockSapRFCService.Object, _applicationServices.SapRFCService);
        Assert.Same(_mockSqlConnectionService.Object, _applicationServices.SqlConnectionService);
        Assert.Same(_mockSapConnectionService.Object, _applicationServices.SapConnectionService);
        Assert.Same(_mockCurrentUserService.Object, _applicationServices.CurrentUserService);
        Assert.Same(_mockRepositoryCollection.Object, _applicationServices.Repositories);
    }

    [Fact]
    public void ApplicationServices_ShouldImplementIApplicationServices()
    {
        // Assert
        Assert.IsAssignableFrom<IApplicationServices>(_applicationServices);
    }
}