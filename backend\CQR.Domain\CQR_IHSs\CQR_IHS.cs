﻿using CQR.Domain.Primitives;
using System.ComponentModel.DataAnnotations;

namespace CQR.Domain.CQR_IHSs;

public class CQR_IHS : BaseEntity<int>
{
    [Required]
    [StringLength(10)]
    public int CQR_IHSId { get; set; }

    [StringLength(50)]
    public string CoreNameplatePlantMnemonic { get; set; }

    public string CorePassword { get; set; }
    [StringLength(50)]
    public string Region { get; set; }

    [StringLength(50)]
    public string Country { get; set; }

    [StringLength(50)]
    public string Platform { get; set; }

    [StringLength(50)]
    public string Program { get; set; }

    [StringLength(50)]
    public string ProductionNameplate { get; set; }

    [StringLength(50)]
    public string StartOfProduction { get; set; }

    [StringLength(50)]
    public string EndOfProduction { get; set; }

    [StringLength(50)]
    public string OEMGroup { get; set; }

    [StringLength(50)]
    public string OEM { get; set; }
}