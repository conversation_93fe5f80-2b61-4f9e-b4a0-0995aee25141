{"version": "0.2.0", "configurations": [{"name": "Launch CQR API (Debug)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-backend", "program": "${workspaceFolder}/backend/CQR.API/bin/Debug/net8.0/CQR.API.dll", "args": [], "cwd": "${workspaceFolder}/backend/CQR.API", "console": "integratedTerminal", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s/swagger"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:7244;http://localhost:5268"}, "requireExactSource": false}, {"name": "Launch CQR API (HTTP Only)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-backend", "program": "${workspaceFolder}/backend/CQR.API/bin/Debug/net8.0/CQR.API.dll", "args": [], "cwd": "${workspaceFolder}/backend/CQR.API", "console": "integratedTerminal", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "http://localhost:5268/swagger"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5268"}, "requireExactSource": false}, {"name": "Attach to CQR API", "type": "coreclr", "request": "attach"}], "compounds": [{"name": "Launch Full Stack (API + Frontend)", "configurations": ["Launch CQR API (Debug)"], "stopAll": true}]}