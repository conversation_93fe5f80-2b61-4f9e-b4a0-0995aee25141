# MCP Server 使用範例

## 🚀 啟動 Server

```bash
# 1. 安裝依賴
npm install

# 2. 複製環境變數
cp .env.example .env
# 填入你的 API Keys

# 3. 啟動為 MCP Server
npm run start -- --mcp

# 4. 或啟動為 HTTP API
npm run start
```

## 🛠️ 使用工具

### 1. 詢問特定模型
```javascript
// 在 Claude Desktop 中使用
ask_model({
  "model": "claude-3-sonnet",
  "question": "如何優化這個 CQR 查詢方法？",
  "context": "這個方法每天會被調用 10000 次"
})
```

### 2. 比較不同模型
```javascript
compare_models({
  "models": ["claude-3-sonnet", "gpt-4", "claude-3-haiku"],
  "question": "在 DDD 架構中，ApplicationServices 應該如何設計？"
})
```

### 3. 獲取項目上下文
```javascript
get_project_context({})
```

## 🌐 HTTP API 使用

```bash
# 獲取可用模型
curl http://localhost:3000/models

# 詢問模型
curl -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-sonnet",
    "question": "解釋這段代碼的作用",
    "context": "這是 CQR 項目的查詢服務"
  }'
```

## 📝 實際使用場景

### 1. 代碼審查
```
使用 cqr_code_review 提示詞審查以下代碼：
[貼上你的代碼]
```

### 2. 架構分析
```
使用 cqr_architecture_analysis 分析當前的服務設計：
[描述你的設計]
```

### 3. 技術選型
```
compare_models({
  "models": ["claude-3-sonnet", "gpt-4"],
  "question": "在我們的 CQR 系統中，應該選擇 Entity Framework 還是 SqlSugar？"
})
```

## 🔧 進階配置

### 添加新模型
在 `index.js` 的 `MODELS` 對象中添加：
```javascript
'new-model': {
  provider: 'custom',
  model: 'model-name',
  description: '模型描述'
}
```

### 自定義工具
添加新的工具到 `tools/list` 和 `tools/call` 處理器中。

### 項目特定提示詞
在 `CQR_PROJECT_CONTEXT` 中添加更多項目特定信息。