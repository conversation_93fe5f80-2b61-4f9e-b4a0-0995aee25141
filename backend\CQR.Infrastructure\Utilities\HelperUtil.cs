using MimeKit;
using MailKit.Net.Smtp;
using MailKit.Security;
using System.Reflection;
using System.Text.RegularExpressions;

namespace CQR.Infrastructure.Utilities;

public class HelperUtil
{
    public static string SafeFileName(string filename)
    {
        foreach (char c in Path.GetInvalidFileNameChars())
        {
            filename = filename.Replace(c, '_');
        }
        return filename;
    }

    public static string GetEmailFirstPart(string email)
    {
        string username = "";
        // 寻找@的位置
        int atIndex = email.IndexOf('@');

        if (atIndex != -1)
        {
            // 获取@前面的子串
            username = email.Substring(0, atIndex);
            //Console.WriteLine("Username: " + username);
        }
        else
        {
            throw new Exception("Invalid email address");
            //Console.WriteLine("Invalid email address");
        }
        return username;
    }

    public static string[] GetStringEmail(string mailStringJoin)
    {
        string[] recipients = mailStringJoin.Split(
            new char[] { ';' },
            StringSplitOptions.RemoveEmptyEntries
        );
        return recipients;
    }

    //private static bool TryGetUserFromContext(HttpContext context, out UserDto user)
    //{
    //    if (context.Items.TryGetValue("user", out var userObject)
    //        && userObject is UserDto retrievedUser)
    //    {
    //        user = retrievedUser;
    //        return true;
    //    }
    //    user = null;
    //    return false;
    //}
    public static void SetPropertyValue(object obj, string propertyName, object value)
    {
        // 使用反射获取对象的类型
        Type type = obj.GetType();

        // 使用反射获取属性信息
        PropertyInfo property = type.GetProperty(propertyName);

        // 使用反射设置属性值
        if (property != null && property.CanWrite)
        {
            property.SetValue(obj, value);
        }
    }

    public static Dictionary<string, string> ParseKeyValuePairs(string input)
    {
        Dictionary<string, string> keyValuePairs = new Dictionary<string, string>();

        // Extract key-value pairs using regular expression
        string pattern = @"(?<key>\w+)\s*=\s*(?<value>[^,}]+)";
        MatchCollection matches = Regex.Matches(input, pattern);

        foreach (Match match in matches)
        {
            string key = match.Groups["key"].Value.Trim();
            string value = match.Groups["value"].Value.Trim();

            keyValuePairs[key] = value;
        }

        return keyValuePairs;
    }

    public static bool IsPositiveInteger(string input)
    {
        // Attempt to parse the input as an integer
        if (int.TryParse(input, out int number))
        {
            // Check if the parsed number is positive
            return number > 0;
        }

        // If parsing fails, or the number is not positive, return false
        return false;
    }

    public static string GetFSPathName(string fileStorageName)
    {
        const string StoragePathDir = "upload";

        // Check if the folder exists, create it if not
        var dir = Path.Combine(Directory.GetCurrentDirectory(), StoragePathDir);
        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }

        // Combine the storage path directory and file storage name
        var newDirName = Path.Combine(StoragePathDir, fileStorageName);

        return newDirName;
    }

    public async Task<string> RazorViewToStringAsync(string viewName, object model)
    {
        //var httpContext = new DefaultHttpContext { RequestServices = _serviceProvider };
        //var actionContext = new ActionContext(httpContext, new RouteData(), new ActionDescriptor());

        //using (var sw = new StringWriter())
        //{
        //    var viewEngineResult = _serviceProvider.GetRequiredService<ICompositeViewEngine>()
        //        .FindView(actionContext, viewName, false);

        //    if (!viewEngineResult.Success)
        //    {
        //        throw new InvalidOperationException($"Couldn't find view: {viewName}");
        //    }

        //    var view = viewEngineResult.View;
        //    var viewData = new ViewDataDictionary(new EmptyModelMetadataProvider(), new ModelStateDictionary())
        //    {
        //        Model = model
        //    };

        //    var tempData = new TempDataDictionary(actionContext.HttpContext, _serviceProvider.GetRequiredService<ITempDataProvider>());
        //    var viewContext = new ViewContext(
        //        actionContext,
        //        view,
        //        viewData,
        //        tempData,
        //        sw,
        //        new HtmlHelperOptions()
        //    );

        //    await view.RenderAsync(viewContext);

        //    return sw.ToString();
        //}
        return "";
    }

    public async Task SendEmailAsync(MimeMessage message)
    {
        // 建立Mail類別

        //var message = new MimeMessage();

        // 添加寄件者
        message.From.Add(new MailboxAddress("寄件者名稱", "寄件者信箱"));

        // 添加收件者
        message.To.Add(new MailboxAddress("收件者名稱", "收件者信箱"));

        // 設定郵件標題
        message.Subject = "郵件標題";

        // 使用 BodyBuilder 建立郵件內容
        var bodyBuilder = new BodyBuilder();

        // 設定文字內容
        bodyBuilder.TextBody = "文字內容";

        // 設定 HTML 內容
        bodyBuilder.HtmlBody = "<p> HTML 內容 </p>";

        // 設定附件
        bodyBuilder.Attachments.Add("檔案路徑");

        // 設定郵件內容
        //message.Body = builder.ToMessageBody();

        // 建立SMTPClient
        using (var client = new SmtpClient())
        {
            // 此範例已Gmail為例
            var host = "smtp.gmail.com";
            var port = 587;

            // 預設為(Auto)也可以如有錯誤可透過明確定義來試試
            //client.Connect(host,port);
            // 明確定義透過TLS(StartTls)連線建立連線服務
            client.Connect(host, port, SecureSocketOptions.StartTls);

            // 透過指定用戶發送：用戶名、密碼驗證
            client.Authenticate("<EMAIL>", "password");

            // 發送Mail
            client.Send(message);

            // 斷開連接(ture)
            client.Disconnect(true);

            Console.WriteLine("Done");
        }
    }
}
