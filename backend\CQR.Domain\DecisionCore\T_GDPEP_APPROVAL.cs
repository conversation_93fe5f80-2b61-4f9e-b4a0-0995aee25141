﻿using CQR.Domain.DecisionCore;

namespace CQR.Core.DecisionCore;

public class T_GDPEP_APPROVAL
{
    public int iGDPEPPhaseId;

    public string sGDPIMCategory;

    public string sReleaseDate;

    public string sComments;

    public string[] sVolume;

    public string[] sSellPrice;

    public string[] sApprovalUser;

    public string[] sRequiredState;

    public string[] sApprovalState;

    public string[] sApprovalStateDbs;

    public string[] sApprovalDate;

    public string[] sApprovalRole;

    public string sReleaseGateway;

    public clsAttachment tAttachment;

    public T_GDPEP_APPROVAL()
    {
        sVolume = new string[13];
        sSellPrice = new string[11];
        sApprovalUser = new string[13];
        sRequiredState = new string[13];
        sApprovalState = new string[13];
        sApprovalStateDbs = new string[13];
        sApprovalDate = new string[13];
        sApprovalRole = new string[15]
        {
            "ZERO", "ODIR", "MDIR", "FDIR", "ENG", "CDEV", "PDIR", "BDIR", "8", "9",
            "10", "11", "12", "13", "14"
        };
        sReleaseGateway = "";
        tAttachment = new clsAttachment();
    }
}
