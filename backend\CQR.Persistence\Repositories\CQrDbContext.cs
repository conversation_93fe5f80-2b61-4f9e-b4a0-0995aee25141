﻿using CQR.Domain.CQR_AntaresFolders;
using CQR.Domain.CQR_GDPIMPhases;
using CQR.Domain.CQR_IHSs;
using CQR.Domain.CQR_TRS_Headers;
using CQR.Domain.CQRIHSFolder;
using CQR.Domain.LOG_Modifications;
using CQR.Domain.RoutingRGHeaders;
using Microsoft.EntityFrameworkCore;

namespace CQR.Persistence.Command.Repositories;

public class CQRDbContext : DbContext
{
    public CQRDbContext(DbContextOptions<CQRDbContext> options) : base(options) { }

    public DbSet<CQR.Domain.CQRHeaders.CQR_Header> CQR_Header { get; set; }
    //modelBuilder.Entity<CQR_Header>().ToTable("CQR_Header"); // 這裡指定正確的資料表名字
    public DbSet<CQR_GDPIMPhase> CQR_GDPIMPhase { get; set; }
    public DbSet<CQR_TRS_Header> CQR_TRS_Header { get; set; }
    public DbSet<CQR_IHS> CQR_IHS { get; set; }
    //public DbSet<CQR_GDPIMPhase> CQR_GDPIMPhase { get; set; }
    public DbSet<CQR_IHSFolder> CQR_IHSFolder { get; set; }
    public DbSet<ROUTING_RGHeader> ROUTING_RGHeader { get; set; }
    public DbSet<CQRAntaresFolder> CQRAntaresFolder { get; set; }
    //public DbSet<Antares> Antares { get; set; }
    public DbSet<LOG_Modification> LOG_Modification { get; set; }



    //public DbSet<AttachmentCheckoutLog> AttachmentCheckoutLog { get; set; }

    //public DbSet<USERPROF_UserProfileHeader> USERPROF_UserProfileHeader { get; set; }
    //public DbSet<USERPROF_UserRoles> USERPROF_UserRoles { get; set; }



}
