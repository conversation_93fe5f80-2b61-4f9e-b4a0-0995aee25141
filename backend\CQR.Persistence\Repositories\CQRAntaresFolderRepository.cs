﻿using CQR.Domain.CQR_AntaresFolders;

namespace CQR.Persistence.Command.Repositories;

public class CQRAntaresFolderRepository : EntityRepository<CQRAntaresFolder, int>, ICQRAntaresFolderRepository
{
    public CQRAntaresFolderRepository(CQRDbContext context) : base(context)
    {
    }

    public async Task<bool> deleteNonArchivedAndQueuekey(int queuekey)
    {
        var toDelete = _context.CQRAntaresFolder.Where(f => !f.Archived && f.QueueKey == queuekey).ToList();

        _context.CQRAntaresFolder.RemoveRange(toDelete);
        return true;
    }

    //public Task<Tuple<string, string>> getProductsList()
    //{
    //    _context.CQRAntaresFolder.
    //    //throw new NotImplementedException();
    //}

    public async Task<bool> InsertAntaresFolder(IList<CQRAntaresFolder> antaresRecordList)
    {
        var toInsert = _context.CQRAntaresFolder.Where(a => antaresRecordList.Contains(a)).ToList();

        foreach (var ant in toInsert)
        {
            //var folder = new CQRAntaresFolder
            //{
            //    QueueKey = iQueueKey,
            //    UniqueNumber = ant.UniqueNumber,
            //    DateAdded = ant.DateAdded,
            //    DateUpdated = ant.DateUpdated,
            //    VB_ID = ant.VB_ID,
            //    OEMGroup = ant.OEMGroup,
            //    OEM = ant.OEM,
            //    Platform = ant.Platform,
            //    Program = ant.Program,
            //    Nameplate = ant.Nameplate,
            //    Country = ant.Country,
            //    Region = ant.Region,
            //    NewBusinessCategory = ant.NewBusinessCategory,
            //    Status = ant.Status,
            //    ProductId = ant.ProductId,
            //    ProductDescription = ant.ProductDescription,
            //    SoldFrom = ant.SoldFrom,
            //    SOP = ant.SOP,
            //    EOP = ant.EOP,
            //    FinalAssembly = ant.FinalAssembly,
            //    ProductGrouping = ant.ProductGrouping,
            //    Archived = false
            //    // 其他欄位...
            //};
            await _context.CQRAntaresFolder.AddAsync(ant);
            //}
        }
        return true;
    }
}
