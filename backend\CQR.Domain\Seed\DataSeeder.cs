//using CQRLIB.Models;
namespace CQRLIB
{

    //public class DataSeeder
    //{
    //    private readonly CQrDbContext _context;

    //    public DataSeeder(CQrDbContext context)
    //    {
    //        _context = context;
    //    }

    //    public void Seed()
    //    {
    //        var faker = new Faker<Product>()
    //            .RuleFor(p => p.Name, f => f.Commerce.ProductName());
    //        // .RuleFor(p => p.Price);

    //        for (int i = 0; i < 100; i++)
    //        {
    //            var product = faker.Generate();
    //            _context.Product.Add(product);
    //        }

    //        _context.SaveChanges();
    //    }
    //}

}