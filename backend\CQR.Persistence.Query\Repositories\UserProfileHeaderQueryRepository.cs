﻿using CQR.Application.Repositories;
using CQR.Domain.Entities;
using CQR.Persistence.Query.Base;
using Dapper;
using Microsoft.Extensions.Configuration;

namespace CQR.Persistence.Query.Repositories
{
    public class UserProfileHeaderQueryRepository : QueryBaseRepository, IUserProfileHeaderQueryRepository
    {
        public UserProfileHeaderQueryRepository(IConfiguration configuration) : base(configuration)
        {
        }

        public Task<IEnumerable<USERPROF_UserProfileHeader>> getUserProfileByNTUserId(string userId)
        {
            String sql = $@"SELECT LastName, FirstName, CompanyCode, LocationCode, DepartmentCode, NTUserId FROM USERPROF_UserProfileHeader where UserId  = @userId";
            return _connection.QueryAsync<USERPROF_UserProfileHeader>(sql, new { userId = userId });
        }
    }
}
