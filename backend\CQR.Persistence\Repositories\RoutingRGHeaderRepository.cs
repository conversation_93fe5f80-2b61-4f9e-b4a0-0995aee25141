﻿using CQR.Domain.ROUTING_RGHeaders;
using CQR.Domain.RoutingRGHeaders;
using CQR.Persistence.Command.Repositories;
using Microsoft.EntityFrameworkCore;

namespace CQR.Persistence.DoCommandmain.Repositories;

public class RoutingRGHeaderRepository
    : GenericRepository<ROUTING_RGHeader>,
        IRoutingRGHeaderRepository
{
    public RoutingRGHeaderRepository(CQRDbContext context)
        : base(context) { }

    public async Task<IEnumerable<ROUTING_RGHeader>> GetByFoldNBR(int queueKey)
    {
        var result = await _context
            .ROUTING_RGHeader.Where(x =>
                x.FolderNbr_int == queueKey && x.FolderType == "FR" && x.NotificationInd != "1"
            )
            .OrderBy(x => x.TaskCode)
            .ToListAsync();
        return result;
    }

    public async Task<IEnumerable<ROUTING_RGHeader>> GetAllAsync()
    {
        var result = await _context.ROUTING_RGHeader.ToListAsync();
        return result;
    }

    public async Task<IEnumerable<ROUTING_RGHeader>> GetByFoldNbrAndFolerType(
        int queueKey,
        string folderType
    )
    {
        var result = await _context
            .ROUTING_RGHeader.Where(s => s.FolderNbr_int == queueKey && s.FolderType == folderType)
            .ToListAsync();
        return result;
    }
}
