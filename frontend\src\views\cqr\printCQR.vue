<template>
  <el-main>
    <el-form ref="formRef" @submit.prevent="onSubmit">
      <!-- 頁首 -->

      <!-- 搜尋條件 -->
      <el-row class="search-row" align="middle">
        <el-col>
          <el-select
            v-model="selectedReport"
            placeholder="Report"
            @change="goReportClick"
          >
            <el-option label="Pipeline" value="PL" />
            <el-option label="Open CQR Status" value="OC" />
            <el-option label="Win Loss" value="WL" />
            <el-option label="Sales Report" value="SR" />
            <el-option label="CQR Due Date Status Report" value="DD" />
          </el-select>
        </el-col>
        <el-col>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            start-placeholder="Due Date From"
            end-placeholder="Due Date To"
            style="width: 220px"
          />
        </el-col>
      </el-row>

      <!-- 結果表格 -->
      <el-table :data="tableData" style="width: 100%; margin-top: 12px">
        <el-table-column prop="CBM" label="CBM" width="125" />
        <el-table-column
          prop="salesDirector"
          label="Sales Director"
          width="125"
        />
        <el-table-column
          prop="accountManager"
          label="Account Manager"
          width="125"
        />
        <el-table-column prop="cqrNo" label="CQR #" />
        <el-table-column prop="cqrDesc" label="CQR Description" />
        <el-table-column prop="productDesc" label="Product Description" />
        <el-table-column prop="awardStatus" label="Award Status" />
        <el-table-column prop="winLossComments" label="Win-Loss Comments" />
        <el-table-column
          prop="businessType"
          label="Business Type"
          width="125"
        />
        <el-table-column prop="mfgLocation" label="Mfg Location" width="125" />
        <el-table-column prop="greenlightPayment" label="Greenlight Payment" />
        <el-table-column prop="year1" label="Year 1" />
        <el-table-column prop="salesYear1" label="Sales Year 1 (000)" />
        <!-- 可依需求加入後續 Year 2~10 -->
      </el-table>
    </el-form>
  </el-main>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";

// 隱藏欄位資料
const formData = reactive({
  viewState: "",
  hdLanguage: "",
  submitType: "",
  tabName: "nhNewHire",
  hdClearSearch: "0",
  txtRowId: "0"
});

// 頁首資料
const headerTitle = ref("");
const loggedInUser = ref("Berlanga, Isidro");
const foldersFound = ref(1);

// 導覽選單
const selectedApp = ref("");
const apps = [
  { label: "Action List", value: "app1" },
  { label: "Back 2 Basics Metrics", value: "app2" }
  // ...其餘 App 列表
];

// 搜尋參數
const selectedReport = ref("WL");
const dateRange = ref<string[]>([]);

// 表格資料（目前為空示範）
const tableData = ref<any[]>([]);

// 方法
function onSubmit() {
  alert("Submit form");
}
function onSearch() {
  onSubmit();
}
function onCreate() {
  alert("Create clicked");
}
function onEdit() {
  alert("Edit clicked");
}
function onView() {
  alert("View clicked");
}
function onPrintCQR() {
  window.open("Print.aspx?QueueKey=" + formData.txtRowId);
}
function onReturnToSearch() {
  alert("Return to Search clicked");
}
function onPrintResults() {
  alert("Print Results");
}
function onCopyToClipboard() {
  // 實作複製資料到剪貼簿
}

// 導向應用程式
function onNavigateApp(val: string) {
  // 根據 val 取得 URL 再使用 window.open
  alert(`Navigate to ${val}`);
}

function goReportClick(val: string) {
  console.log("Selected report:", val);
}
</script>

<style scoped>
.header-row {
  background-color: white;
  height: 80px;
  padding: 12px;
}
.nav-row {
  margin: 12px 0;
}
.search-row {
  margin: 8px 0;
}
</style>
