using CQR.Application.Interfaces;
using CQR.Application.Repositories;
using CQR.Application.UseCases.CQR_Headers.Commands.Handler;
using CQR.Domain.IServices;

namespace CQR.Application.Services;

public interface IApplicationServices
{
    // Command & Query Services
    ICQRCommandHandler CommandHandler { get; }
    ICQRQueryService QueryService { get; }
    ICQRValidationService ValidationService { get; }

    // Business Services
    IAttachmentService AttachmentService { get; }

    // IProductService ProductService { get; }
    // ICustomService CustomService { get; }

    // Infrastructure Services
    IExcelService ExcelService { get; }

    // External Integration Services
    // ISqlConnectionService SqlConnectionService { get; }
    // ISapConnectionService SapConnectionService { get; }

    // Cross-Cutting Services
    ICurrentUserService CurrentUserService { get; }

    // Data Access
    IRepositoryCollection Repositories { get; }
}
