import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
import { error } from "node:console";
import type { AttachFileInfo, ValidateCQRRequest } from "@/store/types";
import { apiRequest } from "./api";
import type { CQRHeaderCollection } from "@/types/cqr/CQRHeaderCollection";

// 創建資料
// export const createCqr = async (data: any) => {
//   try {
//     const response = await axios.post(apiUrl, data);
//     return response.data;
//   } catch (error) {
//     console.error("Error creating CQR:", error);
//     throw error;
//   }
// };

// 共用錯誤檢查
function validateQueueKey(queuekey: number): void {
  if (!Number.isInteger(queuekey) || queuekey <= 0) {
    throw new Error("Invalid queuekey");
  }
}

// 所有 API 前綴
const CQR_BASE = "CQRHeader";

// 獲取資料
export const getCqrList = async () => {
  //todo change type
  return http.request<any>("get", baseUrlApi("CQRHeader/getAllCQRHeader"), {});
};

// export const validateCQR = async (request: ValidateCQRRequest) => {
//   return http.request<any>("post", baseUrlApi(`CQRHeader/validateCQR`), {
//     data: request
//   });
// };

export const getCQRHeaderById = async (queuekey: number) => {
  //todo change type
  // if (!number(queuekey)> 0) throw new Error(`queuekey is not defined`);

  return http.request<any>(
    "get",
    baseUrlApi(`CQRHeader/QueueKey/${queuekey}`),
    {}
  );
};

export const getDictCode = async () => {
  return http.request<any>("get", baseUrlApi(`Code/groupCodes`), {});
};
export const createNewCQRHeaderForm = async (CreateCQRHeaderInput: any) => {
  return http.request<any>(
    "post",
    baseUrlApi(`CQRHeader/CreateNewCQRHeaderForm`),
    {
      data: CreateCQRHeaderInput
    }
  );
};
export const getIHSFolderByQueuekey = async (queuekey: number) => {
  //todo change type
  // if (!number(queuekey)> 0) throw new Error(`queuekey is not defined`);

  return http.request<any>(
    "get",
    baseUrlApi(`CQRHeader/CQRIHSFolder/QueueKey/${queuekey}`),
    {}
  );
};

export const getIHSFolderCtierias = async () => {
  //todo change type
  return http.request<any>(
    "get",
    baseUrlApi(`CQRHeader/GetIHSFolderCtierias`),
    {}
  );
};
export const getAwardModelYear = async () => {
  //todo change type
  return http.request<any>(
    "get",
    baseUrlApi(`CQRHeader/GetAwardModelYear`),
    {}
  );
};
export const validateCreateCQRCriteria = async (vcr: ValidateCQRRequest) => {
  //todo change type
  return http.request<any>(
    "post",
    baseUrlApi(`CQRHeader/validateCreateCQRCriteria`),
    {
      data: vcr
    }
  );
};
export const CreateNewCQRHeaderDraft = async () => {
  //todo change type
  return http.request<any>(
    "post",
    baseUrlApi(`CQRHeader/CreateNewCQRHeaderDraft`),
    {}
  );
};
export const SaveCQRHeaderCollection = async (dto: CQRHeaderCollection) => {
  return http.request<any>({
    method: "post",
    url: baseUrlApi("CQRHeader/SaveCQRHeaderCollection"),
    data: dto // 👈 正確放在 body 裡
  });
};
// export const SaveCQRHeaderCollection = async (dto: CQRHeaderCollection) => {
//   //todo change type
//   return http.request<any>(
//     "put",
//     baseUrlApi(`CQRHeader/SaveCQRHeaderCollection`, dto),
//     {}
//   );
// };

export const getRoutingHeaderByQueuekey = async (queuekey: number) => {
  //todo change type
  // if (!number(queuekey)> 0) throw new Error(`queuekey is not defined`);

  return http.request<any>(
    "get",
    baseUrlApi(`CQRHeader/RoutingHeader/QueueKey/${queuekey}`),
    {}
  );
};
export const getATTDIRAttachFileDirHeaderCheckOutByQueuekey = async (
  queuekey: number
) => {
  //todo change type

  return http.request<any>(
    "get",
    baseUrlApi(`CQRHeader/AttachFile/QueueKey/${queuekey}`),
    {}
  );
};

export const getAttachFileByQueueKey = (queuekey: number) =>
  apiRequest<AttachFileInfo[]>(
    "get",
    baseUrlApi(`CQRHeader/AttachFile/QueueKey/${queuekey}`)
  );

export const getGPLCodesByTableName = (tableName: string) =>
  apiRequest<AttachFileInfo[]>(
    "get",
    baseUrlApi(`CQRHeader/Code/tableName/${tableName}`)
  );

export const getCQRHeaderCollection = (queuekey: number) =>
  apiRequest<any>(
    "get",
    baseUrlApi(`CQRHeader/CQRHeaderCollection/QueueKey/${queuekey}`)
  );

export const getUserRoleCollection = () =>
  apiRequest<any>("get", baseUrlApi(`User/users-with-roles`));
/**
 * 上傳附件 API（使用 FormData 傳檔案）
 * @param queueKey - 上傳對應的資料 key（可選）
 * @param file - 要上傳的檔案
 */
export const uploadAttachment = (queueKey: number, file: File) => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("queueKey", queueKey.toString());

  return apiRequest<any>(
    "post",
    baseUrlApi("CQRHeader/UploadAttachment"),
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

// 加载Attachments列表
export const getAttachments = (folderType: string, queueKey: number) =>
  apiRequest<AttachFileInfo[]>(
    "get",
    baseUrlApi(
      `Attachment/getAttachments?folderType=${folderType}&queueKey=${queueKey}`
    )
  );

// 加载Attachment下载模板列表
export const getModelList = (tableName: string) =>
  apiRequest<string[]>(
    "get",
    baseUrlApi(`Attachment/getModelList?tableName=${tableName}`)
  );

// // 更新資料
// export const updateCqr = async (id: string, data: any) => {
//   try {
//     const response = await axios.put(`${apiUrl}/${id}`, data);
//     return response.data;
//   } catch (error) {
//     console.error("Error updating CQR:", error);
//     throw error;
//   }
// };

// // 刪除資料
// export const deleteCqr = async (id: string) => {
//   try {
//     const response = await axios.delete(`${apiUrl}/${id}`);
//     return response.data;
//   } catch (error) {
//     console.error("Error deleting CQR:", error);
//     throw error;
//   }
// };
