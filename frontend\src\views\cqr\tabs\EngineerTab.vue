<template>
  <el-card>
    <el-row>
      <el-col :span="24">
        <template v-slot:header>
          <div class="xheader">Resource And Technical Assessment</div>
        </template>
        <el-row>
          <el-col :span="12">
            <el-button type="text" @click="handleAttachmentClick"
              ><u>Attachment</u></el-button
            >
          </el-col>
        </el-row>

        <el-form v-if="formData">
          <!-- Is the CQR achievable in the time allowed -->
          <el-form-item label="Is the CQR achievable in the time allowed?">
            <el-radio-group v-model="formData.timeframeOKInd">
              <el-radio label="1">Yes</el-radio>
              <el-radio label="0">No</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- Is there sufficient information to complete the CQR -->
          <el-form-item
            label="Is there sufficient information to complete the CQR?"
          >
            <el-radio-group v-model="formData.informationOKInd">
              <el-radio label="1">Yes</el-radio>
              <el-radio label="0">No</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- Can Work Proceed? -->
          <el-form-item label="Can Work Proceed?">
            <el-radio-group v-model="formData.workProceedOKInd">
              <el-radio label="1">Yes</el-radio>
              <el-radio label="0">No</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- Must Health, Safety & Environmental issues be considered -->
          <el-form-item
            label="Must Health, Safety & Environmental issues be considered due to this change or by Quoting of this new business?"
          >
            <el-radio-group v-model="formData.healthAndSafetyInd">
              <el-radio label="1">Yes</el-radio>
              <el-radio label="0">No</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- IATF 16949 Customer Specific Requirements -->
          <el-form-item>
            <el-checkbox
              v-model="formData.ifCheckedDoneInd"
              :true-label="'1'"
              :false-label="'0'"
              >IATF 16949 Customer Specific Requirements has been considered by
              Engineering and Quality</el-checkbox
            >
          </el-form-item>

          <!-- Date of CSR -->
          <el-form-item label="Date of CSR">
            <!-- <el-input v-model="formData.dateOfCSR" type="date" readonly /> -->
            <el-date-picker
              v-model="formData.dateOfCSR"
              type="date"
              format="DD-MMM-YYYY"
            />
          </el-form-item>

          <el-divider />

          <template v-slot:header>
            <div class="xheader">Responsibilities</div>
          </template>

          <el-row>
            <el-col :span="12">
              <!-- Due Date to Cost Estimating -->
              <el-form-item label="Due Date to Cost Estimating">
                <!-- <el-input
                    v-model="formData.dueDateToBnE"
                    type="text"
                    readonly
                  /> -->
                <el-date-picker
                  v-model="formData.dueDateToBnE"
                  type="date"
                  format="DD-MMM-YYYY"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <!-- Due Date from Design Eng -->
              <el-form-item label="Due Date from Design Eng">
                <!-- <el-input
                    v-model="formData.dueDateFromEng"
                    type="text"
                    readonly
                  /> -->
                <el-date-picker
                  v-model="formData.dueDateFromEng"
                  type="date"
                  format="DD-MMM-YYYY"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- Design Responsibilities -->
          <el-form-item label="PRD - Person Responsible for Design">
            <el-input v-model="formData.prdId" />
            <!-- <el-select v-model="designResp" placeholder="Select PRD">
                <el-option label="Alagia, Pierluigi" value="PALAGIA" />
              </el-select> -->
          </el-form-item>

          <!-- Is Other Design Input Needed? -->
          <el-form-item label="Is Other Design Input Needed?">
            <el-radio-group v-model="isDesignNeeded1">
              <el-radio label="yes">Yes</el-radio>
              <el-radio label="no">No</el-radio>
            </el-radio-group>
            <div v-if="isDesignNeeded1 === 'yes'">
              <el-select v-model="elecPRD1" placeholder="Select PRD">
                <el-option label="Penning, Klaus" value="KPENNING" />
              </el-select>
            </div>
            <el-checkbox v-model="chkElecPRD1">No Input</el-checkbox>
          </el-form-item>

          <!-- Additional Design Input (if necessary) -->
          <el-form-item label="Is Other Design Input Needed?">
            <el-radio-group v-model="isDesignNeeded2">
              <el-radio label="yes">Yes</el-radio>
              <el-radio label="no">No</el-radio>
            </el-radio-group>
            <div v-if="isDesignNeeded2 === 'yes'">
              <el-select v-model="elecPRD2" placeholder="Select PRD">
                <el-option label="Oral, Mert" value="OMERT" />
              </el-select>
            </div>
            <el-checkbox v-model="chkElecPRD2">No Input</el-checkbox>
          </el-form-item>

          <!-- Repeat for other design inputs as needed -->
        </el-form>
      </el-col>
      <el-row>
        <el-col :span="24">
          <el-table v-if="showTable" :data="tableData" style="width: 100%">
            <el-table-column prop="rctECO" label="RCT/ECO" width="75" />
            <el-table-column prop="partName" label="Part Name" width="200" />
            <el-table-column
              prop="obsolescenceCost"
              label="Obsolescence Cost"
              width="125"
            />
          </el-table>

          <el-form v-if="formData">
            <el-form-item label="Manufacturing Site">
              <el-select
                v-model="mfgSite"
                placeholder="Select Manufacturing Site"
                :disabled="isDisabled"
              >
                <el-option label="Reynosa, Mexico" value="00000004" />
              </el-select>
            </el-form-item>

            <el-form-item label="Is Gateway 1 Approval Required?">
              <el-radio-group v-model="gateway1Approval" :disabled="isDisabled">
                <el-radio label="yes">Yes</el-radio>
                <el-radio label="no">No</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="Is Gateway 2 Approval Required?">
              <el-radio-group v-model="gateway2Approval" :disabled="isDisabled">
                <el-radio label="yes">Yes</el-radio>
                <el-radio label="no">No</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="Manufacturing Lead">
              <el-select
                v-model="leadManufacturing"
                placeholder="Select Manufacturing Lead"
                style="width: 235px"
              >
                <el-option label="Aktay, Arif" value="AAKTAY" />
              </el-select>
              <el-checkbox v-model="leadManufacturingNoCostImpact"
                >No Cost Impact</el-checkbox
              >
            </el-form-item>

            <el-form-item label="Purchasing Lead">
              <!-- <el-select
                v-model="formData.leadPurchasingNoCostImpact"
                placeholder="Select Purchasing Lead"
                style="width: 235px"
                :disabled="isDisabled"
              /> -->
              <el-input v-model="formData.leadPurchasingNoCostImpact" />
              <!-- <el-checkbox v-model="formData.leadPurchasingNoCostImpact"
                >No Cost Impact</el-checkbox
              > -->
            </el-form-item>
            <el-form-item label="Validation Lead">
              <!-- <el-select
                v-model="formData.leadValidation"
                placeholder="Select Lead"
                class="disabledSelect"
                style="width: 232.5px"
              > -->
              <!-- <el-option
                  v-for="item in validationLeads"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                /> -->
              <!-- </el-select> -->
              <!-- <el-checkbox
                v-model="formData.leadValidationNoCostImpact"
                :true-label="'1'"
                :false-label="'0'"
              >
                No Cost Impact
              </el-checkbox> -->

              <el-input v-model="formData.leadValidation" />
              <el-input v-model="formData.leadValidationNoCostImpact" />
            </el-form-item>

            <el-form-item label="Engineering Package Comments">
              <el-input
                v-model="engPackageComments"
                type="textarea"
                :disabled="true"
                rows="5"
                placeholder="Comments..."
                style="background-color: silver"
              />
            </el-form-item>
          </el-form>

          <div v-if="rctDataVisible">
            <el-button type="primary" @click="viewRCTDetails"
              >RCT/ECO Obsolescence Details for Purchased Parts</el-button
            >
            <span>Total Obsolescence: ${{ totalObsolescence }}</span>
          </div>
        </el-col>
      </el-row>
    </el-row>
    <!-- 加這段來輸出 formData 的內容 -->
    <!-- <pre v-if="formData">{{ JSON.stringify(formData, null, 2) }}</pre> -->
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from "vue";
import { useLoadModel } from "@/composables/useLoadModel";
import { useFormModelStore } from "@/store/modules/formModel";
import { useRoute } from "vue-router";
// import type { FormData } from "@/store/types";
import type { CQRHeader } from "@/store/types";
import formdesign from "@/router/modules/formdesign";

const { store } = useLoadModel();
const route = useRoute();
const searchQueueKey = ref(Number(route.params.queueKey));

const formData = ref<CQRHeader>(null);
// const formData = ref<FormData>();
const formStore = useFormModelStore();
const rctDataVisible = ref(false); // 或 true，看你需求

onMounted(() => {
  if (formStore.modelCQRHeaderCollection) {
    // formData.value = formStore.model;
    formData.value = { ...formStore.modelCQRHeaderCollection.iCQRHeader };
  }
});

// const radio = ref(3);
// const timeframeOKInd = ref("1");

// // 中介 computed，用來轉換 "1"/"0" ↔ "yes"/"no"
// const timeframeDisplay = computed<string>({
//   get() {

//     return formData.value.timeframeOKInd === "1" ? "yes" : "no";
//   },
//   set(val: string) {
//     formData.value.timeframeOKInd = val === "yes" ? "1" : "0";
//   }
// });

// onMounted(() => {
//   if (formStore.model) {
//     formData.value = {
//       ...formStore.model // 只會覆蓋已存在的欄位，其他保留
//     };
//   }
// });
</script>

<style scoped>
.box-card {
  margin: 20px;
}
.card-header {
  margin-bottom: 10px;
}
.section-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}
.header-text {
  font-size: 14pt;
}
</style>
