﻿using CQR.Persistence.Command.Repositories;
using Microsoft.EntityFrameworkCore;

namespace CQRLIB.Tests.test_persistence;

public class DbFixture : IAsyncLifetime
{
    public CQRDbContext Context { get; private set; }

    public DbFixture()
    {
        // 配置数据库连接
        var options = new DbContextOptionsBuilder<CQRDbContext>()
            .UseSqlServer("Server=(localdb)\\mssqllocaldb;Database=TestDb;Trusted_Connection=True;")
            .Options;

        Context = new CQRDbContext(options);
    }

    public async Task InitializeAsync()
    {
        // 创建数据库并执行迁移
        await Context.Database.MigrateAsync();
    }

    public async Task DisposeAsync()
    {
        // 清理数据库
        await Context.Database.EnsureDeletedAsync();
        await Context.DisposeAsync();
    }
}
