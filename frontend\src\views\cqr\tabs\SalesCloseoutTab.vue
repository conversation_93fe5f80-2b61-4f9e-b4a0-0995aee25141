<template>
  <el-card>
    <el-form :model="form" label-width="160px">
      <el-card>
        <div>
          <img src="../images/trw_cqr_header_sco.gif" alt="Header" />
        </div>

        <el-row>
          <el-col :span="24">
            <el-form-item label="Quote Status">
              <el-radio-group v-model="form.quoteStatus" disabled>
                <el-radio label="Not Quoted">Not Quoted</el-radio>
                <el-radio label="Verbal Quote" style="display: none"
                  >Verbal Quote</el-radio
                >
                <el-radio label="Quote Letter">Quote Letter</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="Award Status">
              <el-radio-group v-model="form.awardStatus" disabled>
                <el-radio label="Win">Win</el-radio>
                <el-radio label="Loss">Loss</el-radio>
                <el-radio label="Other">Other</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="Quote/Letter Date">
              <el-date-picker
                v-model="form.quoteDate"
                type="date"
                placeholder="Select date"
                disabled
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="Date Won">
              <el-date-picker
                v-model="form.dateWon"
                type="date"
                placeholder="Select date"
                disabled
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="Sales Administrator">
              <el-select
                v-model="form.salesAdmin"
                disabled
                placeholder="Select"
              >
                <el-option
                  v-for="item in admins"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="Quote Status Comments">
              <el-input
                v-model="form.qsComments"
                type="textarea"
                readonly
                :rows="4"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="Win-Loss Comments">
              <el-input
                v-model="form.winLossComments"
                type="textarea"
                readonly
                :rows="4"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <a href="SearchPNL.aspx" target="_blank">P&amp;L Search</a>
          </el-col>

          <!-- Table section: Award Status table -->
          <el-table
            :data="awardStatusData"
            border
            style="width: 100%; margin-top: 20px"
          >
            <el-table-column label="P&L" prop="pl" />
            <el-table-column label="Award Status" prop="status" />
            <el-table-column label="Sales Business Unit" prop="unit" />
            <el-table-column label="Quoted Currency" prop="currency" />
            <el-table-column label="Description" prop="description" />
            <el-table-column label="Avg Ann Volume" prop="volume" />
            <el-table-column label="Sales Price Quoted" prop="priceQuoted" />
            <el-table-column label="Sales Price SOP" prop="priceSOP" />
            <el-table-column label="Avg Ann Sales" prop="annualSales" />
            <el-table-column label="Avg Ann DVP" prop="annualDVP" />
            <el-table-column label="Avg Ann MPBT" prop="annualMPBT" />
            <el-table-column label="Full Cost EVA" prop="costEVA" />
            <el-table-column label="Inc. EVA" prop="incEVA" />
          </el-table>
        </el-row>
      </el-card>
    </el-form>
  </el-card>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from "vue";

import { useFormModelStore } from "@/store/modules/formModel";
import { ROUTING_RGHeader } from "@/store/types";

const formStore = useFormModelStore();
const routingData = ref<ROUTING_RGHeader[]>([]);

onMounted(() => {
  if (formStore.modelRoutingHeader) {
    routingData.value = [...formStore.modelRoutingHeader];
  }
});

interface FormData {
  quoteStatus: string;
  awardStatus: string;
  quoteDate: string;
  dateWon: string;
  salesAdmin: string;
  qsComments: string;
  winLossComments: string;
}

interface AdminOption {
  label: string;
  value: string;
}

const form = reactive<FormData>({
  quoteStatus: "",
  awardStatus: "",
  quoteDate: "",
  dateWon: "",
  salesAdmin: "",
  qsComments: "",
  winLossComments: ""
});

const admins = ref<AdminOption[]>([
  { label: "Admin A", value: "adminA" },
  { label: "Admin B", value: "adminB" }
]);

const awardStatusData = ref<any[]>([]); // 可以改成具體型別 if known
// export default {
//   data() {
//     return {
//       form: {
//         quoteStatus: "",
//         awardStatus: "",
//         quoteDate: "",
//         dateWon: "",
//         salesAdmin: "",
//         qsComments: "",
//         winLossComments: ""
//       },
//       admins: [
//         { label: "Admin A", value: "adminA" },
//         { label: "Admin B", value: "adminB" }
//       ],
//       awardStatusData: []
//     };
//   }
// };
</script>

<script setup lang="ts"></script>

<style scoped>
img {
  margin-bottom: 10px;
}
</style>

<style lang="scss" scoped></style>
