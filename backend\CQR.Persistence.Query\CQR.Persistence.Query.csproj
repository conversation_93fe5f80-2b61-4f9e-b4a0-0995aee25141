﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Repositories\ReportRepository.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CQR.Application\CQR.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="EntityQuery\" />
    <Folder Include="ReportQuery\" />
  </ItemGroup>

</Project>
