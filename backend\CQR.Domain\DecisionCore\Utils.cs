﻿
using System.Globalization;
using System.Reflection;
using System.Resources;
using Microsoft.VisualBasic.CompilerServices;

namespace CQR.Domain.DecisionCore;
public sealed class Utils
{
    private enum PropertyKind
    {
        ReadWrite,
        ReadOnly,
        WriteOnly
    }

    internal const int SEVERITY_ERROR = int.MinValue;

    internal const int FACILITY_CONTROL = 655360;

    internal const int FACILITY_RPC = 65536;

    internal const int FACILITY_ITF = 262144;

    internal const int SCODE_FACILITY = 536805376;

    private const int ERROR_INVALID_PARAMETER = 87;

    internal const char chPeriod = '.';

    internal const char chSpace = ' ';

    internal const char chIntlSpace = '\u3000';

    internal const char chZero = '0';

    internal const char chHyphen = '-';

    internal const char chPlus = '+';

    internal const char chLetterA = 'A';

    internal const char chLetterZ = 'Z';

    internal const char chColon = ':';

    internal const char chSlash = '/';

    internal const char chBackslash = '\\';

    internal const char chTab = '\t';

    internal const char chCharH0A = '\n';

    internal const char chCharH0B = '\v';

    internal const char chCharH0C = '\f';

    internal const char chCharH0D = '\r';

    internal const char chLineFeed = '\n';

    internal const char chDblQuote = '"';

    internal const char chGenericManglingChar = '`';

    internal const CompareOptions OptionCompareTextFlags = CompareOptions.IgnoreCase | CompareOptions.IgnoreKanaType | CompareOptions.IgnoreWidth;

    private static ResourceManager m_VBAResourceManager;

    private static bool m_TriedLoadingResourceManager;

    private const string ResourceMsgDefault = "Message text unavailable.  Resource file 'Microsoft.VisualBasic resources' not found.";

    private const string VBDefaultErrorID = "ID95";

    internal static char[] m_achIntlSpace = new char[2] { ' ', '\u3000' };

    private static readonly Type VoidType = Type.GetType("System.Void");

    private static readonly object ResourceManagerSyncObj = new object();

    private static Assembly m_VBRuntimeAssembly;

    internal static ResourceManager VBAResourceManager
    {
        get
        {
            if (m_VBAResourceManager != null)
            {
                return m_VBAResourceManager;
            }
            object resourceManagerSyncObj = ResourceManagerSyncObj;
            ObjectFlowControl.CheckForSyncLockOnValueType(resourceManagerSyncObj);
            bool lockTaken = false;
            try
            {
                Monitor.Enter(resourceManagerSyncObj, ref lockTaken);
                if (!m_TriedLoadingResourceManager)
                {
                    try
                    {
                        m_VBAResourceManager = new ResourceManager("Microsoft.VisualBasic", Assembly.GetExecutingAssembly());
                    }
                    catch (StackOverflowException ex)
                    {
                        throw ex;
                    }
                    catch (OutOfMemoryException ex2)
                    {
                        throw ex2;
                    }
                    catch (ThreadAbortException ex3)
                    {
                        throw ex3;
                    }
                    catch (Exception)
                    {
                    }
                    m_TriedLoadingResourceManager = true;
                }
            }
            finally
            {
                if (lockTaken)
                {
                    Monitor.Exit(resourceManagerSyncObj);
                }
            }
            return m_VBAResourceManager;
        }
    }

    public static Array CopyArray(Array arySrc, Array aryDest)
    {
        if (arySrc == null)
        {
            return aryDest;
        }
        int length = arySrc.Length;
        if (length == 0)
        {
            return aryDest;
        }
        //if (aryDest.Rank != arySrc.Rank)
        //{
        //    //throw ExceptionUtils.VbMakeException(new InvalidCastException(GetResourceString("Array_RankMismatch")), 9);
        //    throw ExceptionUtils.VbMakeException(new InvalidCastException(GetResourceString("Array_RankMismatch")), 9);

        //}
        checked
        {
            int num = aryDest.Rank - 2;
            for (int i = 0; i <= num; i++)
            {
                if (aryDest.GetUpperBound(i) != arySrc.GetUpperBound(i))
                {
                    throw new ArrayTypeMismatchException("Array type mismatch.");

                    //throw ExceptionUtils.VbMakeException(new ArrayTypeMismatchException(GetResourceString("Array_TypeMismatch")), 9);
                }
            }
            if (length > aryDest.Length)
            {
                length = aryDest.Length;
            }
            if (arySrc.Rank > 1)
            {
                int rank = arySrc.Rank;
                int length2 = arySrc.GetLength(rank - 1);
                int length3 = aryDest.GetLength(rank - 1);
                if (length3 == 0)
                {
                    return aryDest;
                }
                int length4 = Math.Min(length2, length3);
                int num2 = unchecked(arySrc.Length / length2) - 1;
                for (int j = 0; j <= num2; j++)
                {
                    Array.Copy(arySrc, j * length2, aryDest, j * length3, length4);
                }
            }
            else
            {
                Array.Copy(arySrc, aryDest, length);
            }
            return aryDest;
        }
    }

    internal static Assembly VBRuntimeAssembly
    {
        get
        {
            if ((object)m_VBRuntimeAssembly != null)
            {
                return m_VBRuntimeAssembly;
            }
            m_VBRuntimeAssembly = Assembly.GetExecutingAssembly();
            return m_VBRuntimeAssembly;
        }
    }

}
