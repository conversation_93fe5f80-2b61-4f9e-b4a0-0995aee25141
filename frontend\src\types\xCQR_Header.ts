export interface xCQR_Header {
  QueueKey: number;
  LockUserId?: string;
  LockTime?: string; // Date or string based on actual format

  AwardYear?: string;
  AwardQuarter?: string;
  ProjectNbr?: string;
  RevNbr?: string;

  CalculationCurrency?: string;
  RegionalQuotingTeam?: string;
  OpeningMeetingDate?: string;
  Communication?: string;
  ASILLevel?: string;
  AUTOSAR?: string;
  AMECoordinator?: string;
  PurchasingCoordinator?: string;
  Cybersecurity?: string;
  TDRNoInput?: string;

  Status?: string;
  StatusDate?: string;
  ProductDesc?: string;
  CustNbr?: string;
  CustBuyerName?: string;
  CustEngineerName?: string;
  Vehicle?: string;
  ModelYear?: string;
  RFQRefNbr?: string;
  RFQRcvdDate?: string;
  VolumePerAnnum?: string;
  OriginalProductLife?: string;
  RemainingProductLife?: string;
  CustQuoteDueDate?: string;
  TDRDate?: string;
  QuoteResponseDueDate?: string;
  FRANIssueDate?: string;

  ManufacturingSite?: string;
  EngineeringSite?: string;
  PIMSite?: string;
  NewModRepl?: string;

  InForecastInd?: string;
  ApproxAnnualValue?: string;
  QuoteType?: string;
  Gateway?: string;
  IfCheckedInd?: string;
  IfCheckedDoneInd?: string;

  ProductionReqdInd?: string;
  PrototypeReqdInd?: string;
  PrototypeQty?: string;
  PiecePriceReqdInd?: string;
  ToolingReqdInd?: string;
  DesignPropReqdInd?: string;
  TimingPlanReqdInd?: string;

  Milestone1Date?: string;
  Milestone2Date?: string;
  Milestone3Date?: string;
  Milestone4Date?: string;
  Milestone5Date?: string;
  Milestone6Date?: string;

  ObsolescenceReqdInd?: string;
  CommissionReqdInd?: string;
  CommissionPercent?: string;
  MfgRepCompany?: string;
  MfgRepIndividual?: string;

  OriginatorId?: string;
  OriginationDate?: string;
  AccountMgrId?: string;
  SalesAcctDirectorId?: string;
  CostEstimatorId?: string;
  PGMId?: string;
  PETMId?: string;
  PRDId?: string;
  EngineeringManagerId?: string;
  CommercialManager?: string;

  TimeframeOKInd?: string;
  InformationOKInd?: string;
  WorkProceedOKInd?: string;

  ElecInputReqdInd?: string;
  ElecInputReqdInd2?: string;
  ElecInputReqdInd3?: string;
  ElecInputReqdInd4?: string;

  ElecPETMId?: string;
  ElecPETMId2?: string;
  ElecPETMId3?: string;
  ElecPETMId4?: string;

  DueDateFromEng?: string;
  DueDateToBnE?: string;

  Unused1?: string;
  NoRctFolders?: string;
  Unused2?: string;

  LastUpdatedDate?: string;
  LastUpdatedTime?: string;
  LastUpdatedVersion?: string;
  LastUpdatedBy?: string;

  QuoteCompletedSent?: string;
  QuoteResponseSent?: string;
  FranClosedOut?: string;
  Unused_002?: string;

  QRFOB?: string;
  QRToolLeadTime?: string;
  QRMaterialDate?: string;
  QRLaborDate?: string;
  QRToolingCapacity?: string;
  QRProgramClass?: string;
  FRANDesc?: string;
  HealthAndSafetyInd?: string;

  QSQuoteStatus?: string;
  QSAwardStatus?: string;
  QSQuoteDate?: string;
  QSCustomerResp?: string;
  QSCustomerPO?: string;
  QSCustomerAuth?: string;
  QSCustomerFran?: string;
  QSCommentId?: string;

  ModNeedsCostInd?: string;
  PSMId?: string;
  PAId?: string;

  VPAComments?: string;
  IntPricComments?: string;
  BkRndInfComments?: string;
  OtherInfo?: string;
  ActionComments?: string;
  QRToolingComments?: string;
  QAFComments?: string;
  EngPkgComments?: string;
  QSComments?: string;
  CustProdComments?: string;

  ResponseLevel?: string;
  BusinessClassif?: string;
  ProductCategory?: string;
  ProductLine?: string;
  WarrantyRqmts?: string;
  PerformanceRqmts?: string;
  CurrentSupplier?: string;
  CustomerJob1?: string;
  LocationSales?: string;
  LocationEngineer?: string;
  LocationShipping?: string;
}
