using CQR.Application.Dto.RoleUser;
using CQR.Application.Repositories;
using CQR.Domain.Enum;
using Microsoft.AspNetCore.Mvc;

namespace CQRAPI.Controllers
{
    //[Route("/RoleUser")]
    [ApiController]
    //[Route("api/[controller]")]
    [Route("RoleUser")]
    public class RoleUserController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IRoleUserQueryRepository roleUserQueryRepository;

        public RoleUserController(
            IConfiguration configuration,
            IRoleUserQueryRepository oRoleUserQueryRepository
        )
        {
            //_configuration = configuration;
            this.roleUserQueryRepository = oRoleUserQueryRepository;
        }

        //[HttpGet]
        //public ActionResult<dynamic> LoadUserRoles()
        //{
        //    return Ok($"Running....");
        //}
        [HttpGet]
        [Route("OriginatorList")]
        public async Task<IActionResult> GetUseDatabase()
        {
            var result = await roleUserQueryRepository.getOriginatorList();
            return Ok(result);
        }

        [HttpGet]
        [Route("role/roleEnum/${roleEnum}")]
        public async Task<IActionResult> getRoleUserListByRole()
        {
            var result = await roleUserQueryRepository.getRoleUserListByRole(RoleEnum.PGMId);
            return Ok(result);
        }

        [HttpGet]
        [Route("role/roleName/${roleName}")]
        public async Task<IActionResult> getRoleUserListByRoleName(string roleName)
        {
            var result = await roleUserQueryRepository.getRoleUserListByRoleName(roleName);
            return Ok(result);
        }

        [HttpGet]
        [Route("role/roleCollection")]
        public async Task<IActionResult> getRoleUseCollection()
        {
            var roleUserCollection = new List<RoleUserCollectionDto>();
            foreach (RoleEnum role in Enum.GetValues(typeof(RoleEnum)))
            {
                var result = await roleUserQueryRepository.getRoleUserListByRole(role);

                roleUserCollection.Add(
                    new RoleUserCollectionDto
                    {
                        RoleName = role.ToString(),
                        RoleId = (int)role,
                        Users = result.ToList(),
                        UserCount = result.Count(),
                    }
                );
            }
            return Ok(roleUserCollection);
        }

        //[HttpGet]
        //[Route("ASPNETCORE_ENVIRONMENT")]
        //public ActionResult<string> GetASPNETCORE_ENVIRONMENT()
        //{
        //    // 返回字符串响应
        //    var result = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        //    return Ok($"ASPNETCORE_ENVIRONMENT:{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}");
        //}
        ////https://learn.microsoft.com/zh-tw/azure/active-directory-b2c/enable-authentication-web-api?tabs=csharpclient
        //[HttpGet]
        //[Route("Identity")]
        //public ActionResult GetUserIdentity()
        //{
        //    return Ok(new { name = User.Identity.Name });
        //}

        //[HttpGet]
        //[Route("/Home/Error")]
        //public IActionResult Error()
        //{
        //    return StatusCode(500); // 返回500状态码
        //}
    }
}
