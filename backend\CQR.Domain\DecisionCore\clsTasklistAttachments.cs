﻿using Microsoft.VisualBasic;

using Microsoft.VisualBasic.CompilerServices;

namespace CQR.Domain.DecisionCore;
public class clsTasklistAttachments : clsAttachment
{
    private clsCQR tCqr;

    public clsTasklistAttachments(clsCQR _tCqr)
    {
        tCqr = _tCqr;
    }

    //public override bool CustomColumnsHeader(coreTable tbl)
    //{
    //    tbl.AddCell();
    //    tbl.AddCell("History");
    //    return true;
    //}

    public override bool CustomColumnsInto(int attIndex, int rowNumber, coreTable tbl, bool bReadOnly)
    {
        string fullFilePath = tCqr.tTasklist.Get_AttachDirectoryAceOffix() + tCqr.tTasklist.tTab.tAttInfo[attIndex].sSrcFile;
        string fileToEdit = Strings.Replace(fullFilePath, "\\\\swinus-as04.ad.one-bcs.com", "d:");
        checked
        {
            fileToEdit = Strings.Left(fileToEdit, Strings.InStrRev(fileToEdit, "_v") + 3);
            fileToEdit += "_*";
            int fileLoc = Strings.InStrRev(fileToEdit, "\\");
            string filePath = Strings.Left(fileToEdit, fileLoc - 1);
            string[] fileList = Directory.GetFiles(filePath, Strings.Mid(fileToEdit, fileLoc + 1));
            string cellText = "";
            if (fileList.Length > 0)
            {
                cellText = "<span onclick=\"hoverTip2('hover" + Conversions.ToString(attIndex) + "');\" onmouseout=\"UnTip()\"><a href='#'>show</a></span>";
                cellText = cellText + "<div id='hover" + Conversions.ToString(attIndex) + "' style='display:none'>";
                string[] array = fileList;
                foreach (string thisFile in array)
                {
                    cellText = cellText + "<a href='../aceoffix/AceOffix.aspx?FileName=" + fullFilePath + "&ro=1' target='_blank'>";
                    string[] revInfo = Strings.Split(Strings.Replace(Strings.Replace(Strings.Mid(thisFile, Strings.InStrRev(thisFile, "_v") + 5), ".xlsx", ""), " ", "_"), "_");
                    cellText = ((revInfo.Length <= 2) ? (cellText + revInfo[0]) : (cellText + "<nobr>" + modTools.mDateCygnetToScreen(revInfo[0]) + " " + Strings.Left(revInfo[1], 2) + ":" + Strings.Right(revInfo[1], 2) + " " + modUserProf.UserNameFromId(revInfo[2]) + "</nobr><br>"));
                    cellText += "</a>";
                }
                cellText += "</div>";
            }
            //tbl.AddCell("<a href='javascript:DownloadFile(\"href" + Conversions.ToString(rowNumber) + "\")' id='href" + Conversions.ToString(rowNumber) + "' fileName='../Attachments/CQR/" + Strings.Format(tCqr.iQueueKey, "0000000000") + "/" + tCqr.tTasklist.tTab.tAttInfo[attIndex].sSrcFile + "'>download</a>");
            //tbl.AddCell(cellText);
            return true;
        }
    }

    public override bool CustomColumnsFrom(int attIndex)
    {
        return true;
    }
}
