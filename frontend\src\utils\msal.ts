// src/utils/msal.ts
import { PublicClientApplication } from "@azure/msal-browser"

export const msalInstance = new PublicClientApplication({
  auth: {
    clientId: "4e322a3a-fd45-467a-822b-de163ba5f8a2",
    authority: "https://login.microsoftonline.com/dc94cd8a-025c-455a-9e35-c80612a79987",
    redirectUri: "http://localhost:8848", // 或部署的網址
  },
  cache: {
    cacheLocation: "localStorage",
    storeAuthStateInCookie: false,
  },
})
