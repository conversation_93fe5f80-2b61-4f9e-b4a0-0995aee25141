// src/store/cqrCreateCriteria.ts
import { defineStore } from "pinia";
import type { ValidateCQRRequest } from "../types";

// 預設值抽出來
const defaultCriteria: ValidateCQRRequest = {
  cqrType: "New",
  projectNumber: "",
  quoteType: "0"
  // ModifiedProjNbr: 0,
  // ModifiedRevNbr: 0,
  // HdQuoteType: ""
};

export const useCreateCQRFormStore = defineStore("createCQRForm", {
  state: () => ({
    // criteria: null // 或初始型別，如 {}
    criteria: { ...defaultCriteria }, // 初始化為預設值
    queuekey: 0
  }),
  actions: {
    setCriteria(payload: ValidateCQRRequest) {
      this.criteria = payload;
    },

    clearCriteria() {
      // this.criteria = null;
      this.criteria = { ...defaultCriteria };
    }
  }
});
