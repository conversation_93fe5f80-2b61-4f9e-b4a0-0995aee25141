import { ref, reactive, computed, watch } from 'vue'
import type { Ref } from 'vue'

export interface SortConfig {
  field: string
  direction: 'asc' | 'desc'
}

export interface PaginationConfig {
  currentPage: number
  pageSize: number
  total: number
  pageSizes: number[]
}

export interface SortableColumn {
  prop: string
  label: string
  sortable: boolean
  defaultSort?: 'asc' | 'desc'
}

export function useSortAndPagination() {
  // 排序狀態
  const sortConfig = reactive<SortConfig>({
    field: 'projectNbr',
    direction: 'desc'
  })

  // 分頁狀態
  const paginationConfig = reactive<PaginationConfig>({
    currentPage: 1,
    pageSize: 50,
    total: 0,
    pageSizes: [20, 50, 100, 200, 500]
  })

  // 可排序的欄位定義
  const sortableColumns: SortableColumn[] = [
    { prop: 'cqrNumber', label: 'CQR #', sortable: true, defaultSort: 'desc' },
    { prop: 'projectNbr', label: '專案編號', sortable: true, defaultSort: 'desc' },
    { prop: 'revNbr', label: '版本', sortable: true, defaultSort: 'desc' },
    { prop: 'statusTRS', label: '狀態', sortable: true },
    { prop: 'releaseDate', label: '發佈日期', sortable: true, defaultSort: 'desc' },
    { prop: 'franIssueDate', label: '發行日期', sortable: true, defaultSort: 'desc' },
    { prop: 'dueDateFromEng', label: '工程到期日', sortable: true, defaultSort: 'asc' },
    { prop: 'custQuoteDueDate', label: '報價到期日', sortable: true, defaultSort: 'asc' },
    { prop: 'openingMeetingDate', label: '開會日期', sortable: true, defaultSort: 'desc' },
    { prop: 'oemGroup', label: 'OEM群組', sortable: true },
    { prop: 'oemCustomer', label: 'OEM客戶', sortable: true },
    { prop: 'accountManagerName', label: '客戶經理', sortable: true },
    { prop: 'productDesc', label: '產品描述', sortable: true },
    { prop: 'platformName', label: '平台', sortable: true },
    { prop: 'modelYearTRS', label: '年式', sortable: true },
    { prop: 'volumePerAnnum', label: '年產量', sortable: true, defaultSort: 'desc' },
    { prop: 'approxAnnualValue', label: '預估年產值', sortable: true, defaultSort: 'desc' }
  ]

  // 計算分頁資訊
  const paginationInfo = computed(() => {
    const { currentPage, pageSize, total } = paginationConfig
    const totalPages = Math.ceil(total / pageSize)
    const startRecord = total === 0 ? 0 : (currentPage - 1) * pageSize + 1
    const endRecord = Math.min(currentPage * pageSize, total)
    
    return {
      totalPages,
      startRecord,
      endRecord,
      hasPreviousPage: currentPage > 1,
      hasNextPage: currentPage < totalPages,
      isFirstPage: currentPage === 1,
      isLastPage: currentPage === totalPages,
      summary: `顯示第 ${startRecord} 到 ${endRecord} 項，共 ${total} 項記錄`
    }
  })

  // 排序變更處理
  const handleSortChange = (column: any, prop: string, order: string | null) => {
    console.log('排序變更:', { column, prop, order })
    
    if (!order) {
      // 清除排序
      sortConfig.field = 'projectNbr'
      sortConfig.direction = 'desc'
    } else {
      sortConfig.field = prop
      sortConfig.direction = order === 'ascending' ? 'asc' : 'desc'
    }
  }

  // Element Plus 表格排序變更處理器
  const handleTableSortChange = ({ column, prop, order }: any) => {
    handleSortChange(column, prop, order)
  }

  // 分頁大小變更處理
  const handlePageSizeChange = (newSize: number) => {
    paginationConfig.pageSize = newSize
    paginationConfig.currentPage = 1 // 重置到第一頁
  }

  // 當前頁變更處理
  const handleCurrentPageChange = (newPage: number) => {
    paginationConfig.currentPage = newPage
  }

  // 跳轉到指定頁面
  const goToPage = (page: number) => {
    const { totalPages } = paginationInfo.value
    
    if (page < 1) page = 1
    if (page > totalPages) page = totalPages
    
    paginationConfig.currentPage = page
  }

  // 跳轉到第一頁
  const goToFirstPage = () => {
    goToPage(1)
  }

  // 跳轉到最後一頁
  const goToLastPage = () => {
    goToPage(paginationInfo.value.totalPages)
  }

  // 上一頁
  const goToPreviousPage = () => {
    goToPage(paginationConfig.currentPage - 1)
  }

  // 下一頁
  const goToNextPage = () => {
    goToPage(paginationConfig.currentPage + 1)
  }

  // 重置分頁
  const resetPagination = () => {
    paginationConfig.currentPage = 1
    paginationConfig.total = 0
  }

  // 更新總記錄數
  const updateTotal = (total: number) => {
    paginationConfig.total = total
    
    // 如果當前頁超出範圍，調整到最後一頁
    const { totalPages } = paginationInfo.value
    if (paginationConfig.currentPage > totalPages && totalPages > 0) {
      paginationConfig.currentPage = totalPages
    }
  }

  // 設定預設排序
  const setDefaultSort = (field: string, direction: 'asc' | 'desc' = 'desc') => {
    sortConfig.field = field
    sortConfig.direction = direction
  }

  // 取得欄位的排序配置
  const getColumnSortConfig = (prop: string) => {
    const column = sortableColumns.find(col => col.prop === prop)
    if (!column) return null

    return {
      sortable: column.sortable,
      sortOrders: column.sortable ? ['ascending', 'descending'] : [],
      defaultSort: column.defaultSort || 'desc'
    }
  }

  // 檢查欄位是否可排序
  const isColumnSortable = (prop: string): boolean => {
    const column = sortableColumns.find(col => col.prop === prop)
    return column?.sortable || false
  }

  // 取得當前排序狀態的顯示文字
  const getCurrentSortText = computed(() => {
    const column = sortableColumns.find(col => col.prop === sortConfig.field)
    if (!column) return '預設排序'
    
    const directionText = sortConfig.direction === 'asc' ? '升序' : '降序'
    return `${column.label} (${directionText})`
  })

  // 快速排序預設
  const quickSortPresets = [
    { label: 'CQR編號 (降序)', field: 'cqrNumber', direction: 'desc' as const },
    { label: 'CQR編號 (升序)', field: 'cqrNumber', direction: 'asc' as const },
    { label: '發佈日期 (最新)', field: 'releaseDate', direction: 'desc' as const },
    { label: '發佈日期 (最舊)', field: 'releaseDate', direction: 'asc' as const },
    { label: '到期日期 (最急)', field: 'custQuoteDueDate', direction: 'asc' as const },
    { label: '產值 (高到低)', field: 'approxAnnualValue', direction: 'desc' as const },
    { label: '年產量 (高到低)', field: 'volumePerAnnum', direction: 'desc' as const }
  ]

  // 套用快速排序預設
  const applyQuickSort = (preset: typeof quickSortPresets[0]) => {
    sortConfig.field = preset.field
    sortConfig.direction = preset.direction
  }

  // 多欄位排序支援（進階功能）
  const multiSort = ref<Array<{ field: string; direction: 'asc' | 'desc' }>>([])

  const addMultiSort = (field: string, direction: 'asc' | 'desc' = 'asc') => {
    const existingIndex = multiSort.value.findIndex(item => item.field === field)
    
    if (existingIndex >= 0) {
      // 更新現有排序
      multiSort.value[existingIndex].direction = direction
    } else {
      // 添加新排序
      multiSort.value.push({ field, direction })
    }
  }

  const removeMultiSort = (field: string) => {
    multiSort.value = multiSort.value.filter(item => item.field !== field)
  }

  const clearMultiSort = () => {
    multiSort.value = []
  }

  // 分頁大小選項的智能建議
  const getRecommendedPageSize = (total: number): number => {
    if (total <= 50) return 20
    if (total <= 200) return 50
    if (total <= 1000) return 100
    return 200
  }

  // 監聽排序變更
  watch([() => sortConfig.field, () => sortConfig.direction], 
    ([newField, newDirection], [oldField, oldDirection]) => {
      console.log('排序狀態變更:', {
        from: { field: oldField, direction: oldDirection },
        to: { field: newField, direction: newDirection }
      })
    }
  )

  // 監聽分頁變更
  watch([() => paginationConfig.currentPage, () => paginationConfig.pageSize], 
    ([newPage, newSize], [oldPage, oldSize]) => {
      console.log('分頁狀態變更:', {
        from: { page: oldPage, size: oldSize },
        to: { page: newPage, size: newSize }
      })
    }
  )

  return {
    // 狀態
    sortConfig,
    paginationConfig,
    multiSort,
    
    // 配置
    sortableColumns,
    quickSortPresets,
    
    // 計算屬性
    paginationInfo,
    getCurrentSortText,
    
    // 排序方法
    handleSortChange,
    handleTableSortChange,
    setDefaultSort,
    getColumnSortConfig,
    isColumnSortable,
    applyQuickSort,
    
    // 多欄位排序
    addMultiSort,
    removeMultiSort,
    clearMultiSort,
    
    // 分頁方法
    handlePageSizeChange,
    handleCurrentPageChange,
    goToPage,
    goToFirstPage,
    goToLastPage,
    goToPreviousPage,
    goToNextPage,
    resetPagination,
    updateTotal,
    getRecommendedPageSize
  }
}