using Microsoft.AspNetCore.Mvc;

namespace CQRAPI.Controllers;

//[Route("/RoleUser")]
[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IConfiguration _configuration;
    //private readonly IRoleUserQueryRepository _RoleUserQueryRepository;

    public AuthController(IConfiguration configuration)
    {
        //_configuration = configuration;
        //this._RoleUserQueryRepository = oRoleUserQueryRepository;
    }
    ////todo 
    //[HttpGet]
    //public ActionResult<dynamic> LoadUserRoles()
    //{
    //    return Ok($"Running....");
    //}
    //[HttpGet]
    //[Route("OriginatorList")]
    //public async Task<IActionResult> GetUseDatabase()
    //{
    //    var result= await _RoleUserQueryRepository.GetOriginatorList();
    //    return Ok(result);
    //}

    //[HttpGet]
    //[Route("ASPNETCORE_ENVIRONMENT")]
    //public ActionResult<string> GetASPNETCORE_ENVIRONMENT()
    //{
    //    // 返回字符串响应
    //    var result = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

    //    return Ok($"ASPNETCORE_ENVIRONMENT:{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}");
    //}
    ////https://learn.microsoft.com/zh-tw/azure/active-directory-b2c/enable-authentication-web-api?tabs=csharpclient
    //[HttpGet]
    //[Route("Identity")]
    //public ActionResult GetUserIdentity()
    //{
    //    return Ok(new { name = User.Identity.Name });
    //}

    //[HttpGet]
    //[Route("/Home/Error")]
    //public IActionResult Error()
    //{
    //    return StatusCode(500); // 返回500状态码
    //}

}
