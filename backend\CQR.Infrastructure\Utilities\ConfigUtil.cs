using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;

namespace GPL_Core.Utilities
{
    public class ConfigUtil
    {
        public static string GetUseDatabase(IConfiguration _configuration)
        {
            // 使用 IConfiguration 获取配置值
            var someValue = _configuration["ConnectionStrings"];
            // 返回字符串响应
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            // 解析連接字串
            var builder = new SqlConnectionStringBuilder(connectionString);

            // 獲取連接IP和資料庫名稱
            string server = builder.DataSource;
            string database = builder.InitialCatalog;

            // 顯示連接IP和資料庫名稱
            var msgDB = $"Server: {server}, Database: {database}";
            return msgDB;
        }
        public static string GetSectionValue(IConfiguration _configuration, string sectionKey)
        {
            // 使用 GetSection 獲取配置數據
            var sectionSection = _configuration.GetSection(sectionKey);
            var strSystemmainter = "";
            // 檢查配置是否存在並且有值
            if (sectionSection != null && !string.IsNullOrEmpty(sectionSection.Value))
            {
                // 配置有值，進行相應的操作
                strSystemmainter = sectionSection.Value;

                // 接下來，你可以使用 strSystemmainter 變數進行操作
                return strSystemmainter;
            }
            return strSystemmainter;
        }
    }
}
