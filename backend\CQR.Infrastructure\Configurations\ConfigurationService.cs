using Microsoft.Extensions.Configuration;

namespace CQR.Infrastructure.Configurations;

public class ConfigurationService
{
    private readonly IConfiguration _configuration;

    // 使用構造函數注入 IConfiguration 物件
    public ConfigurationService(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    // 讀取 SmtpSettings 設定
    public SmtpSettings GetSmtpSettings()
    {
        // 從配置中讀取 SmtpSettings 部分
        var smtpSettings = _configuration.GetSection("SmtpSettings").Get<SmtpSettings>();
        return smtpSettings;
    }
}
