﻿using CQR.Domain.CQRIHSFolder;
using CQR.Domain.Interfaces.Repository;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.EntityFrameworkCore;
using CQR.Persistence.Command.Repositories;
using CQR.Domain.CQR_IHSs;
using CQR.Domain.CQR_GDPIMPhases;
using CQR.Domain.ROUTING_RGHeaders;
using CQR.Domain.CQRHeaders;

namespace CQR.Persistence.Command.Persistence;

public class UnitOfWork : IUnitOfWork
{
    private readonly CQRDbContext _context;
    private IDbContextTransaction _transaction;
    private ICQRIHSFolderRepository _cqrihsFolderRepository;
    private bool _disposed = false;

    // Lazy initialization 的 repositories
    private ICQRIHSRepository _cqrIhsRepository;
    private ICQRHeaderRepository _cqrHeaderRepository;
    private ICQRIHSFolderRepository _cqrIhsFolderRepository;

    // Lazy initialization of repositories
    public ICQRIHSFolderRepository CQRIHSFolders
    {
        get
        {
            return _cqrihsFolderRepository ??= new CQRIHSFolderRepository(_context);
        }
    }

    public UnitOfWork(CQRDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    public ICQRIHSRepository CQRIHSRepository =>
       _cqrIhsRepository ??= new CQRIHSRepository(_context);

    public ICQRHeaderRepository CQRHeaderRepository =>
        _cqrHeaderRepository ??= new CQRHeaderRepository(_context);

    public ICQRIHSFolderRepository CQRIHSFolderRepository =>
        _cqrIhsFolderRepository ??= new CQRIHSFolderRepository(_context);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateConcurrencyException ex)
        {
            // Handle concurrency conflicts
            throw new InvalidOperationException("Concurrency conflict occurred while saving changes.", ex);
        }
        catch (DbUpdateException ex)
        {
            // Handle database update errors
            throw new InvalidOperationException("An error occurred while saving changes to the database.", ex);
        }
    }

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
            throw new InvalidOperationException("A transaction is already in progress.");

        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
            throw new InvalidOperationException("No transaction is in progress.");

        try
        {
            await _transaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await RollbackTransactionAsync(cancellationToken);
            throw;
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
            throw new InvalidOperationException("No transaction is in progress.");

        try
        {
            await _transaction.RollbackAsync(cancellationToken);
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    // Legacy method for backward compatibility
    public async Task<int> SaveAsync()
    {
        return await SaveChangesAsync();
    }

    // Transaction management with automatic rollback
    public async Task ExecuteInTransactionAsync(Func<Task> operation, CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            // If already in transaction, just execute the operation
            await operation();
            return;
        }

        await BeginTransactionAsync(cancellationToken);
        try
        {
            await operation();
            await CommitTransactionAsync(cancellationToken);
        }
        catch
        {
            await RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            // If already in transaction, just execute the operation
            return await operation();
        }

        await BeginTransactionAsync(cancellationToken);
        try
        {
            var result = await operation();
            await CommitTransactionAsync(cancellationToken);
            return result;
        }
        catch
        {
            await RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    // Check if transaction is active
    public bool IsTransactionActive => _transaction != null;

    public IRoutingRGHeaderRepository RoutingRGHeaderRepository => throw new NotImplementedException();

    public ICQRGDPIMPhasesRepository CQRGDPIMPhasesRepository => throw new NotImplementedException();


    // Get current transaction (for advanced scenarios)
    public IDbContextTransaction GetCurrentTransaction() => _transaction;

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            // Dispose transaction if still active
            _transaction?.Dispose();
            _transaction = null;

            // Dispose context
            _context?.Dispose();
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    public async ValueTask DisposeAsync()
    {
        if (!_disposed)
        {
            // Dispose transaction if still active
            if (_transaction != null)
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }

            // Dispose context
            if (_context != null)
            {
                await _context.DisposeAsync();
            }

            _disposed = true;
        }

        GC.SuppressFinalize(this);
    }
}
