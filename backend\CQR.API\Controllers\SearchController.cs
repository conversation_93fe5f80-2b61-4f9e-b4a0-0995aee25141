using CQR.Application.Dto;
using CQR.Application.Interfaces;
using CQR.Application.Queries;
using CQR.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CQR.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SearchController : BaseController
    {
        private readonly IApplicationServices _appServices;
        private readonly ILogger<SearchController> _logger;

        public SearchController(
            IApplicationServices appServices,
            ILogger<SearchController> logger,
            ICurrentUserService currentUser
        )
            : base(currentUser)
        {
            _appServices = appServices;
            _logger = logger;
        }

        /// <summary>
        /// 執行CQR進階搜尋
        /// </summary>
        /// <param name="query">搜尋查詢條件</param>
        /// <returns>搜尋結果</returns>
        [HttpPost]
        [Route("cqr")]
        public async Task<ActionResult<SearchCQRResponse>> SearchCQR(
            [FromBody] SearchCQRQuery query
        )
        {
            try
            {
                _logger.LogInformation(
                    "開始執行CQR搜尋，頁面：{PageNumber}，大小：{PageSize}",
                    query.PageNumber,
                    query.PageSize
                );

                var result = await _appServices.QueryService.SearchCQRAsync(query);

                _logger.LogInformation("CQR搜尋完成，找到 {TotalCount} 筆記錄", result.TotalCount);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CQR搜尋執行失敗");
                return StatusCode(500, new { message = "搜尋執行失敗", error = ex.Message });
            }
        }

        /// <summary>
        /// 快速搜尋CQR（僅基本條件）
        /// </summary>
        /// <param name="searchText">搜尋文字</param>
        /// <param name="pageNumber">頁碼</param>
        /// <param name="pageSize">每頁大小</param>
        /// <returns>搜尋結果</returns>
        [HttpGet]
        [Route("quick")]
        public async Task<ActionResult<SearchCQRResponse>> QuickSearch(
            [FromQuery] string? searchText,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 50
        )
        {
            try
            {
                var query = new SearchCQRQuery
                {
                    ProductDescription = searchText,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    UseWildcard = true,
                };

                // 如果搜尋文字是數字，嘗試作為CQR編號搜尋
                if (
                    !string.IsNullOrEmpty(searchText) && int.TryParse(searchText, out int cqrNumber)
                )
                {
                    query.CQRFromLeft = cqrNumber.ToString();
                    query.CQRToLeft = cqrNumber.ToString();
                }

                var result = await _appServices.QueryService.SearchCQRAsync(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "快速搜尋執行失敗");
                return StatusCode(500, new { message = "快速搜尋執行失敗", error = ex.Message });
            }
        }

        /// <summary>
        /// 取得搜尋欄位的可選值列表
        /// </summary>
        /// <param name="fieldName">欄位名稱</param>
        /// <returns>可選值列表</returns>
        [HttpGet]
        [Route("field-options/{fieldName}")]
        public async Task<ActionResult<List<SelectItem>>> GetFieldOptions(string fieldName)
        {
            try
            {
                var options = fieldName.ToLower() switch
                {
                    "manufacturingsite" => await GetManufacturingSiteOptions(),
                    "modelyear" => await GetModelYearOptions(),
                    "quotetype" => GetQuoteTypeOptions(),
                    "oemgroup" => await GetOEMGroupOptions(),
                    "oemcustomer" => await GetOEMCustomerOptions(),
                    _ => new List<SelectItem>(),
                };

                return Ok(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得欄位選項失敗，欄位：{FieldName}", fieldName);
                return StatusCode(500, new { message = "取得欄位選項失敗", error = ex.Message });
            }
        }

        /// <summary>
        /// 取得使用者列表（用於人員欄位）
        /// </summary>
        /// <param name="role">角色類型</param>
        /// <returns>使用者列表</returns>
        [HttpGet]
        [Route("users")]
        public async Task<ActionResult<List<UserFullNameDto>>> GetUsers(
            [FromQuery] string? role = null
        )
        {
            try
            {
                // 這裡應該調用用戶服務來取得使用者列表
                // 暫時返回空列表，實際實作需要連接到用戶管理系統
                var users = new List<UserFullNameDto>();

                return Ok(users);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得使用者列表失敗");
                return StatusCode(500, new { message = "取得使用者列表失敗", error = ex.Message });
            }
        }

        /// <summary>
        /// 匯出搜尋結果
        /// </summary>
        /// <param name="query">搜尋查詢條件</param>
        /// <param name="format">匯出格式 (excel, csv)</param>
        /// <returns>匯出檔案</returns>
        [HttpPost]
        [Route("export")]
        public async Task<ActionResult> ExportSearchResults(
            [FromBody] SearchCQRQuery query,
            [FromQuery] string format = "excel"
        )
        {
            try
            {
                _logger.LogInformation("開始匯出搜尋結果，格式：{Format}", format);

                // 移除分頁限制以取得所有結果
                var exportQuery = new SearchCQRQuery
                {
                    ProjectNumber = query.ProjectNumber,
                    Originator = query.Originator,
                    Status = query.Status,
                    ProductDescription = query.ProductDescription,
                    Customer = query.Customer,
                    Platform = query.Platform,
                    DateFrom = query.DateFrom,
                    DateTo = query.DateTo,
                    SortBy = query.SortBy,
                    SortDirection = query.SortDirection,
                    FindLastTwoFolders = query.FindLastTwoFolders,
                    QuoteReport = query.QuoteReport,
                    SortDescending = query.SortDescending,
                    CQRFromLeft = query.CQRFromLeft,
                    CQRFromRight = query.CQRFromRight,
                    CQRToLeft = query.CQRToLeft,
                    CQRToRight = query.CQRToRight,
                    UseWildcard = query.UseWildcard,
                    ManufacturingSite = query.ManufacturingSite,
                    InitialReleaseDateFrom = query.InitialReleaseDateFrom,
                    DueDateFrom = query.DueDateFrom,
                    CQROriginator = query.CQROriginator,
                    AccountManager = query.AccountManager,
                    PageSize = 10000, // 設定合理的最大值，避免記憶體問題
                    PageNumber = 1,
                };

                var result = await _appServices.QueryService.SearchCQRAsync(exportQuery);

                if (!result.Results.Any())
                {
                    return BadRequest(new { message = "沒有搜尋結果可以匯出" });
                }

                var fileName = $"CQR_Search_Results_{DateTime.Now:yyyyMMdd_HHmmss}";

                if (format.ToLower() == "csv")
                {
                    var csvContent = _appServices.ExcelService.ExportSearchResultsToCSV(
                        result.Results
                    );
                    _logger.LogInformation(
                        "成功匯出CSV檔案，記錄數：{Count}",
                        result.Results.Count
                    );
                    return File(csvContent, "text/csv; charset=utf-8", $"{fileName}.csv");
                }
                else
                {
                    var excelContent = _appServices.ExcelService.ExportSearchResultsToExcel(
                        result.Results,
                        "CQR搜尋結果"
                    );
                    _logger.LogInformation(
                        "成功匯出Excel檔案，記錄數：{Count}",
                        result.Results.Count
                    );
                    return File(
                        excelContent,
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        $"{fileName}.xlsx"
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "匯出搜尋結果失敗，格式：{Format}", format);
                return StatusCode(500, new { message = "匯出失敗", error = ex.Message });
            }
        }

        /// <summary>
        /// 儲存搜尋條件為預設值
        /// </summary>
        /// <param name="preset">搜尋預設條件</param>
        /// <returns>儲存結果</returns>
        [HttpPost]
        [Route("presets")]
        public async Task<ActionResult> SaveSearchPreset([FromBody] CQRSearchFilterDto preset)
        {
            try
            {
                // 實作搜尋預設條件儲存邏輯
                // 這裡需要連接到用戶偏好設定系統

                return Ok(new { message = "搜尋預設條件已儲存", presetName = preset.PresetName });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "儲存搜尋預設條件失敗");
                return StatusCode(500, new { message = "儲存失敗", error = ex.Message });
            }
        }

        /// <summary>
        /// 取得使用者的搜尋預設條件
        /// </summary>
        /// <returns>預設條件列表</returns>
        [HttpGet]
        [Route("presets")]
        public async Task<ActionResult<List<CQRSearchFilterDto>>> GetSearchPresets()
        {
            try
            {
                // 實作取得使用者搜尋預設條件邏輯
                var presets = new List<CQRSearchFilterDto>();

                return Ok(presets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得搜尋預設條件失敗");
                return StatusCode(500, new { message = "取得預設條件失敗", error = ex.Message });
            }
        }

        #region Private Methods

        private async Task<List<SelectItem>> GetManufacturingSiteOptions()
        {
            // 實作從TRS_Header取得製造地點選項
            return new List<SelectItem>
            {
                new SelectItem { Value = "", Text = "*" },
                new SelectItem { Value = "001", Text = "Site 001" },
                new SelectItem { Value = "002", Text = "Site 002" },
            };
        }

        private async Task<List<SelectItem>> GetModelYearOptions()
        {
            // 實作從TRS_Header取得年式選項
            var currentYear = DateTime.Now.Year;
            var options = new List<SelectItem>
            {
                new SelectItem { Value = "", Text = "*" },
            };

            for (int i = currentYear - 5; i <= currentYear + 10; i++)
            {
                options.Add(new SelectItem { Value = i.ToString(), Text = i.ToString() });
            }

            return options;
        }

        private List<SelectItem> GetQuoteTypeOptions()
        {
            return new List<SelectItem>
            {
                new SelectItem { Value = "", Text = "*" },
                new SelectItem { Value = "C", Text = "Customer" },
                new SelectItem { Value = "T", Text = "Internal" },
            };
        }

        private async Task<List<SelectItem>> GetOEMGroupOptions()
        {
            // 實作從Antares/IHS資料取得OEM群組選項
            return new List<SelectItem>
            {
                new SelectItem { Value = "", Text = "*" },
            };
        }

        private async Task<List<SelectItem>> GetOEMCustomerOptions()
        {
            // 實作從Antares/IHS資料取得OEM客戶選項
            return new List<SelectItem>
            {
                new SelectItem { Value = "", Text = "*" },
            };
        }

        #endregion
    }
}
