export class GDPEP_APPROVAL {
  iGDPEPPhaseId: number = 0;

  sGDPIMCategory?: string;
  sReleaseDate?: string;
  sComments?: string;

  sVolume: string[];
  sSellPrice: string[];
  sApprovalUser: string[];
  sRequiredState: string[];
  sApprovalState: string[];
  sApprovalStateDbs: string[];
  sApprovalDate: string[];
  sApprovalRole: string[];

  sReleaseGateway?: string;

  // tAttachment: clsAttachment;

  constructor() {
    this.sVolume = new Array(13).fill("");
    this.sSellPrice = new Array(11).fill("");
    this.sApprovalUser = new Array(13).fill("");
    this.sRequiredState = new Array(13).fill("");
    this.sApprovalState = new Array(13).fill("");
    this.sApprovalStateDbs = new Array(13).fill("");
    this.sApprovalDate = new Array(13).fill("");
    this.sApprovalRole = [
      "ZERO",
      "ODIR",
      "MDIR",
      "FDIR",
      "ENG",
      "CDEV",
      "PDIR",
      "BDIR",
      "8",
      "9",
      "10",
      "11",
      "12",
      "13",
      "14"
    ];
    this.sReleaseGateway = "";
    // this.tAttachment = new clsAttachment();
  }
}
