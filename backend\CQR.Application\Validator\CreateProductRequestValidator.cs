using CQR.Domain.Entities;
using FluentValidation;

namespace CQR.Application.Validators;

public class CreateProductRequestValidator : AbstractValidator<Product>
{
    public CreateProductRequestValidator()
    {
        RuleFor(x => x.Name).NotEmpty().WithMessage("Name is required.");
        RuleFor(x => x.Stock).NotEmpty().WithMessage("Valid Stock is required.");
        RuleFor(x => x.Price).NotEmpty().WithMessage("Valid Price is required.");
    }
}
