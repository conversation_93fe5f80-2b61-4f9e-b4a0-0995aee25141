﻿namespace CQR.Domain.DecisionCore;


// CQR, Version=1.0.9223.23847, Culture=neutral, PublicKeyToken=null
// CQR.clsCQR.IHS
public struct IHS
{
    public string sCoreNameplatePlantMnemonic;

    public string sOEMGroup;

    public string sOEMCustomer;

    public string sPlatform;

    public string sProgram;

    public string sNameplate;

    public string sRegion;

    public string sCountry;

    public string sSOP;

    public string sEOP;

    public string sProductDescription;

    public string sProductGrouping;

    public string sSoldFrom;

    public string sFinalAssembly;

    public bool bArchived;
}
