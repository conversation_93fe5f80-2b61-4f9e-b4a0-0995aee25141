2. 拉取 IIS 官方镜像
powershell
Copy
Edit
docker pull mcr.microsoft.com/windows/servercore/iis
或者：

powershell
Copy
Edit
docker pull mcr.microsoft.com/windows/nanoserver/iis
servercore/iis：基于 Windows Server Core，包含完整的 IIS

nanoserver/iis：基于 Nano Server，更轻量，但功能有限


3. 运行 IIS 容器
powershell
Copy
Edit
docker run -d -p 8080:80 --name my-iis mcr.microsoft.com/windows/servercore/iis
-d：后台运行

-p 8080:80：映射本机 8080 端口到 IIS 80 端口

--name my-iis：容器命名为 my-iis

