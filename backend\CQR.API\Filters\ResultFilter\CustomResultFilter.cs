﻿using CQRLIB.Dtos;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;

namespace CQR.API.Filters.ResultFilter
{
    public class CustomResultFilter : IResultFilter
    {
        public void OnResultExecuted(ResultExecutedContext context)
        {
            // 在这里可以在结果执行后进行一些处理
        }

        public void OnResultExecuting(ResultExecutingContext context)
        {
            // 在这里可以在结果执行前进行一些处理
            if (context.Result is ObjectResult objectResult)
            {
                // 获取原始的数据
                var originalData = objectResult.Value;
                var oStatusCode = objectResult.StatusCode.Value;// context.HttpContext.Response.StatusCode;
                // 创建包装数据
                var wrappedData = new
                {
                    context.HttpContext.Response.StatusCode,
                    Data = originalData
                    // 这里可以添加其他需要的信息
                };
                // 判断是否为成功状态码
                if (IsSuccessStatusCode(oStatusCode))
                {
                    // 成功处理逻辑
                    // 可以根据需要添加其他信息
                    var wrappedDataAjaxResult = new AjaxJsonResult(oStatusCode!, originalData);
                    // 包装结果
                    var wrappedResult = new ObjectResult(wrappedDataAjaxResult)
                    {
                        StatusCode = context.HttpContext.Response.StatusCode
                    };
                    context.Result = wrappedResult;
                }
                else
                {
                    // 替换原始结果
                    //context.Result = wrappedResult;
                }
            }
        }

        // 判断是否为成功状态码的辅助方法
        private bool IsSuccessStatusCode(int statusCode)
        {
            return statusCode >= 200 && statusCode < 300;
        }
    }


}
