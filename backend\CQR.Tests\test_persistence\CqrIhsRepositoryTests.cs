﻿//using CQR.Core.Repos.Impls;
//using Microsoft.EntityFrameworkCore;
//using Moq;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//using Moq;
//using System.Collections.Generic;
//using System.Threading.Tasks;
//using CQRLIB.Persistence;
//using Microsoft.EntityFrameworkCore;
//using Xunit;
//using CQR.Core.Model;
//using SqlSugar;


//namespace CQRLIB.Tests.test_persistence;


//public class CqrIhsRepositoryTests
//{
//    private readonly Mock<DbSet<CQR_Header>> _mockSet;
//    private readonly Mock<CQrDbContext> _mockContext;
//    private readonly CqrIhsRepository _repository;

//    public CqrIhsRepositoryTests()
//    {
//        // 设置模拟的 DbSet
//        _mockSet = new Mock<DbSet<CQR_Header>>();
//        _mockContext = new Mock<CQrDbContext>();

//        // 设置 DbSet 的行为
//        _mockContext.Setup(m => m.Set<CQR_Header>()).Returns(_mockSet.Object);

//        // 实例化 CqrIhsRepository
//        _repository = new CqrIhsRepository(_mockContext.Object);
//    }

//    [Fact]
//    public async Task GetByQueueKeyAsync_Returns_CqrIhsModel_When_Found()
//    {
//        // Arrange
//        var queueKey = 1;
//        var expectedModel = new CQR_Header
//        {
//            QueueKey = queueKey,
//            //CoreNameplatePlantMnemonic = "TestMnemonic",
//            //Region = "TestRegion",
//            //Country = "TestCountry",
//            //Platform = "TestPlatform",
//            //Program = "TestProgram",
//            //ProductionNameplate = "TestNameplate",
//            //StartOfProduction = "2025-01-01",
//            //EndOfProduction = "2026-01-01",
//            //OEMGroup = "TestGroup",
//            //OEM = "TestOEM"
//        };

//        // 设置模拟行为
//        _mockSet.Setup(m => m.FirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<CQR_Header, bool>>>(), It.IsAny<System.Threading.CancellationToken>()))
//                .ReturnsAsync(expectedModel);

//        // Act
//        var result = await _repository.GetByQueueKeyAsync(queueKey);

//        // Assert
//        Assert.NotNull(result);
//        Assert.Equal(queueKey, result.QueueKey);
//    }

//    [Fact]
//    public async Task GetByQueueKeyAsync_Returns_Null_When_Not_Found()
//    {
//        // Arrange
//        var queueKey = 999; // 不存在的 queueKey

//        // 设置模拟行为
//        _mockSet.Setup(m => m.FirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<CQR_Header, bool>>>(), It.IsAny<System.Threading.CancellationToken>()))
//                .ReturnsAsync((CQR_Header)null);

//        // Act
//        var result = await _repository.GetByQueueKeyAsync(queueKey);

//        // Assert
//        Assert.Null(result);
//    }

//    [Fact]
//    public async Task GetByRegionAsync_Returns_CqrIhsModels_When_Found()
//    {
//        // Arrange
//        var region = "TestRegion";
//        var expectedModels = new List<CQR_Header>
//            {
//                new CQR_Header { QueueKey = 1, Region = region, Country = "TestCountry" },
//                new CQR_Header { QueueKey = 2, Region = region, Country = "AnotherCountry" }
//            };

//        // 设置模拟行为
//        _mockSet.Setup(m => m.Where(It.IsAny<System.Linq.Expressions.Expression<System.Func<CQR_Header, bool>>>())).Returns(expectedModels.AsQueryable());

//        // Act
//        var result = await _repository.GetByRegionAsync(region);

//        // Assert
//        Assert.NotEmpty(result);
//        Assert.Equal(region, result.First().Region);
//    }

//    [Fact]
//    public async Task InsertAsync_Saves_Model_Correctly()
//    {
//        // Arrange
//        var newModel = new CQR_Header
//        {
//            QueueKey = 3,
//            //CoreNameplatePlantMnemonic = "NewMnemonic",
//            //Region = "NewRegion"
//        };

//        // Act
//        var insertedId = await _repository.InsertAsync(newModel);

//        // Assert
//        Assert.Equal(newModel.QueueKey, insertedId);
//    }
//}
