using Microsoft.AspNetCore.Mvc;

namespace DotNetGPLAPI.Controllers;

[ApiController]
//[AllowAnonymous]
[Route("api/[controller]")]
public class SysConfigurController : ControllerBase
{
    // private User user;
    private readonly IHttpContextAccessor _contextAccessor;
    // private IBusServicesColleciton busServicesColleciton;
    private readonly ILogger<SysConfigurController> _logger;

    public SysConfigurController(ILogger<SysConfigurController> logger,
         IHttpContextAccessor httpContextAccessor
         //  IBusServicesColleciton _BusServicesColleciton
         )
    {
        _logger = logger;
        _contextAccessor = httpContextAccessor;
        // busServicesColleciton = _BusServicesColleciton;
        // user = HelperUtil.GetHttpContextUserInfo(_contextAccessor.HttpContext);
    }
    // [HttpGet]
    // [Route("userParaments")]
    // public IActionResult GetUserParaments()
    // {
    //     var result = busServicesColleciton.repositoryCollection.userParamentDao.GetUserParaments();
    //     return Ok(result);
    // }
    // [HttpPut]
    // [Route("userParaments")]
    // public IActionResult UpdateUserParament([FromBody] SYSTEM_User_Parament oEntity)
    // {
    //     var result = busServicesColleciton.repositoryCollection.userParamentDao.UpdateUserParament(
    //         user.name, oEntity);
    //     return Ok(result);
    // }
    // [HttpGet]
    // [Route("configures")]
    // public async Task<IActionResult> GetSystemConfigures()
    // {
    //     var result = busServicesColleciton.repositoryCollection.systemConfigureDao.GetSystemConfigures();
    //     return Ok(result);
    // }
    // [HttpGet]
    // [Route("configuresInKeyValue")]
    // public async Task<IActionResult> GetSystemConfiguresInKeyValue()
    // {
    //     var result = busServicesColleciton.repositoryCollection.systemConfigureDao.GetDictSystemConfiguresInKeyValue();
    //     return Ok(result);
    // }
    // [HttpGet]
    // [Route("configure/name/{configurename}")]
    // public async Task<IActionResult> GetSystemConfigureByKey(string configurename)
    // {
    //     var result = busServicesColleciton.repositoryCollection.systemConfigureDao.GetSystemConfigureByKey(configurename);
    //     return Ok(result);
    // }
    // [HttpPut]
    // [Route("updateSysParament")]
    // public async Task<IActionResult> UpdateSysParament([FromBody] SYSTEM_System_Parament oEntity)
    // {
    //     var result = busServicesColleciton.repositoryCollection.systemConfigureDao.UpdateSysParamentEntity(user.name, oEntity);
    //     return Ok(result);
    // }
    // [HttpGet]
    // [Route("sapServiceResourceList")]
    // public async Task<IActionResult> GetSapServiceResourceList()
    // {
    //     var result = busServicesColleciton.repositoryCollection.sapServiceResourceDao.GetSapServiceResourceList();
    //     return Ok(result);
    // }
    // [HttpGet()]
    // [Route("checkIsActiveSapServiceResourceList/sapResourceId/{sapResourceId}")]
    // public async Task<IActionResult> CheckIsActiveSapServiceResourceList(string sapResourceId)
    // {
    //     var result = busServicesColleciton.busSAPService.CheckIsActiveSapResourceByResourceId(user, sapResourceId);
    //     return Ok(result);
    // }

}
