﻿namespace CQR.Domain.USERPROF_UserProfileHeaders
{
    public class USERPROF_UserProfileHeader
    {
        public int QueueKey { get; private set; }
        public string UserId { get; private set; }
        public string LastName { get; private set; }
        public string FirstName { get; private set; }
        public string MiddleInitial { get; private set; }
        public string CompanyCode { get; private set; }
        public string LocationCode { get; private set; }
        public string DepartmentCode { get; private set; }
        public string NTUserId { get; private set; }
        public string GWMailId { get; private set; }
        public string Inactive { get; private set; }
        public string Flags { get; private set; }
        public string ereqPlant { get; private set; }
        public string OverrideEmail { get; private set; }
        public string OverrideEmailAddress { get; private set; }
        public int RemindAllActions { get; private set; }
        public DateTime LastUpdatedDate { get; private set; }
        public string LastUpdatedBy { get; private set; }
        public DateTime CreatedDate { get; private set; }
        public string CreatedBy { get; private set; }
    }
}
