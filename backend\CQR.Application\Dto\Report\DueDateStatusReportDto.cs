﻿namespace CQR.Application.Dto.Report;

public class DueDateStatusReportDto
{
    public string? QueueKey { get; set; }
    public string? CQRNumber { get; set; }
    public string? QuoteType { get; set; }
    public string? QuoteDescription { get; set; }
    public string? SOPDate { get; set; }
    public string? GateExit { get; set; }
    public string? FolderStatus { get; set; }
    public string? RegionalQuoteTeam { get; set; }
    public string? SoldFrom { get; set; }
    public string? FinalAssembly { get; set; }
    public string? OEMCustomer { get; set; }
    public string? ProductDescription { get; set; }
    public string? Originator { get; set; }
    public string? AccountManager { get; set; }
    public string? SalesAccountDirector { get; set; }
    public string? EngineeringDirector { get; set; }
    public string? EngineeringCoordinator { get; set; }
    public string? PurchasingCoordinator { get; set; }
    public string? PMCoordinator { get; set; }
    public string? MECoordinator { get; set; }
    public string? FinanceCoordinator { get; set; }
    public string? DesignEngineer { get; set; }
    public string? ManufacturingLead { get; set; }
    public string? PurchasingLead { get; set; }
    public string? ValidationLead { get; set; }

    public DateTime? CQROriginationDate { get; set; }
    public DateTime? CQRIssueDate { get; set; }
    public int? CQRIssuedDays { get; set; }
    public DateTime? TaskListCompletion { get; set; }
    public DateTime? QuoteResponseDate { get; set; }
    public string? QuoteResponseCompleteDate { get; set; }
    public int? QuoteResponseDays { get; set; }

    public DateTime? Gateway1ApprovalDate { get; set; }
    public DateTime? Gateway2ApprovalDate { get; set; }

    public DateTime? QuoteDueDate { get; set; }
    public DateTime? DueDateFromEng { get; set; }
    public DateTime? SentToCustomer { get; set; }
    public DateTime? DateWon { get; set; }
    public string? QuoteStatus { get; set; }
    public string? AwardStatus { get; set; }

    public string? CustomerDaysAllowedToQuote { get; set; }
    public string? DaysEarlyLate { get; set; }
    public string? DaysNeededToQuote { get; set; }

    public int? CQRIssuedToGate1Days { get; set; }
    public int? Gate1ToEngineeringDays { get; set; }
    public int? EngineeringToMEDays { get; set; }
    public int? EngineeringToPRDays { get; set; }
    public int? TaskListCompletionToBusinessCase { get; set; }
    public int? BusinessCaseToGateway2 { get; set; }
    public string? Gateway2ToQuoteSent { get; set; }

    public DateTime? EngineeringDirectorReleaseDate { get; set; }
    public int? EngineeringDirectorReleaseDays { get; set; }
    public DateTime? EngineeringCoordinatorReleaseDate { get; set; }
    public int? EngineeringCoordinatorReleaseDays { get; set; }
    public DateTime? OtherPRDDate { get; set; }
    public int? OtherPRDDays { get; set; }

    public DateTime? DueDateToBnE { get; set; }
    public DateTime? ManufacturingLeadReleaseDate { get; set; }
    public int? ManufacturingLeadReleaseDays { get; set; }

    public DateTime? PurchasingLeadReleaseDate { get; set; }
    public int? PurchasingLeadReleaseDays { get; set; }
    public DateTime? ManufacturingCoordReleaseDate { get; set; }
    public int? ManufacturingCoordReleaseDays { get; set; }
    public DateTime? PurchasingCoordReleaseDate { get; set; }
    public int? PurchasingCoordReleaseDays { get; set; }

    public DateTime? TestReleaseDate { get; set; }
    public int? TestReleaseDays { get; set; }
    public DateTime? QuoteDueDateMinus1 { get; set; }
    public DateTime? QuoteResponseDate2 { get; set; } // 若與前面 QuoteResponseDate 重複，可合併
}
