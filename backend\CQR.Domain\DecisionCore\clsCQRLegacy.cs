﻿
// CQR, Version=1.0.9223.23847, Culture=neutral, PublicKeyToken=null
// CQR.clsCQRLegacy
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using Microsoft.Data.SqlClient;
using Microsoft.Office.Interop.Excel;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

namespace CQR.Domain.DecisionCore;
public class clsCQRLegacy : clsCQR
{
    public string RequiredFieldsReject()
    {
        string sRqd = "";
        string sFail1 = "";
        string sFail2 = "";
        if ((Operators.CompareString(sStatus, "040150FR", TextCompare: false) == 0) & (Operators.CompareString(sNewCommentsQAF, "", TextCompare: false) == 0))
        {
            sFail1 = "P&L Quote Comments";
            sFail2 = "P&L(s)";
        }
        if (((Operators.CompareString(sStatus, "040200FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "040300FR", TextCompare: false) == 0)) & (Operators.CompareString(sNewCommentsQRTooling, "", TextCompare: false) == 0))
        {
            sFail1 = "Finance Coordinator Quote Response";
            sFail2 = "the Quote Response";
        }
        if (Operators.CompareString(sFail1, "", TextCompare: false) != 0)
        {
            z_AddToRequired(ref sRqd, " Comments Are Required When Rejecting " + sFail2 + ".");
        }
        return sRqd;
    }

    public string RequiredFields(string asLinkUsed_)
    {
        string sRqd = "";
        //corePage atPage = new corePage();
        string sLinkUsed = asLinkUsed_;
        string sOptional = "";
        if (Operators.CompareString(sLinkUsed, "rejectNoCommentLink", TextCompare: false) == 0)
        {
            sLinkUsed = "rejectLink";
        }
        checked
        {
            if (Operators.CompareString(sLinkUsed, "releaseLink", TextCompare: false) == 0)
            {
                int iLoop = 1;
                do
                {
                    if (tUserData.bIsGateway[iLoop])
                    {
                        RequiredFieldsGDPEPApprovals(ref sRqd);
                        if (tRouting.mHasCurrentTask("41", "QA", "QA", tGateway[1].sApprovalUser[iLoop]) | tRouting.mHasCurrentTask("42", "QA", "QA", tGateway[2].sApprovalUser[iLoop]))
                        {
                            return sRqd;
                        }
                    }
                    iLoop++;
                }
                while (iLoop <= 12);
            }
            if (tRouting.mHasCurrentTask("01", "BQ", "BQ", "*") & tUserData.bIsCmgr)
            {
                RequiredFieldsGDPEP(ref sRqd, asLinkUsed_);
            }
            switch (sLinkUsed)
            {
                case "releaseGDPEPLink":
                    RequiredFieldsGDPEP(ref sRqd, asLinkUsed_);
                    break;
                case "releaseLink":
                case "releasesortsLink":
                case "approveLink":
                case "rejectLink":
                case "rejectNoCommentLink":
                    if (Operators.CompareString(sLinkUsed, "rejectLink", TextCompare: false) == 0 && Operators.CompareString(Strings.Trim(sNewCommentsAction), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Please enter a reject comment when rejecting");
                        break;
                    }
                    if (Operators.CompareString(Strings.Trim(sCQRDesc), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "CQR Description");
                    }
                    if (Operators.CompareString(Strings.Trim(sProductDescription), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Product Description");
                    }
                    if (Operators.CompareString(Strings.Trim(sOEMGroup), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "OEM Group");
                    }
                    if (Operators.CompareString(Strings.Trim(sPlatform), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Vehicle");
                    }
                    if (Operators.CompareString(Strings.Trim(sModelYear), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Model Year");
                    }
                    if (Operators.CompareString(Strings.Trim(sVolumePerAnnum), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Volume Per Annum");
                    }
                    if (Operators.CompareString(sStatus, "010150FR", TextCompare: false) <= 0 && Operators.CompareString(Strings.Trim(sEngineeringSite), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Engineering Site");
                    }
                    if (Operators.CompareString(Strings.Trim(sCustomerQuoteDueDate), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Customer Quote Due Date");
                    }
                    if (Operators.CompareString(Strings.Trim(sCustomerQuoteDueDate), sOriginationDate, TextCompare: false) < 0)
                    {
                        z_AddToRequired(ref sRqd, "Customer Quote Due Date must be after the Origination Date");
                    }
                    if (Operators.CompareString(Strings.Trim(sRemProductLife), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Product Life");
                    }
                    if (Operators.CompareString(sOriginationDate, "", TextCompare: false) != 0 && Operators.CompareString(Strings.Trim(sCustomerSOP), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Customer (Start of Prod)");
                    }
                    if ((Operators.CompareString(sGateway, "1", TextCompare: false) != 0) & (Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0) & (Operators.CompareString(sOriginationDate, "20070413", TextCompare: false) > 0))
                    {
                        if (Operators.CompareString(sCustomerGroup, "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Customer Group");
                        }
                        if (Operators.CompareString(sProductCategory, "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Product Category");
                        }
                        if (Operators.CompareString(sBusinessPlan, "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Business Plan");
                        }
                    }
                    if (!mIsDesignProposalOnly() & (Operators.CompareString(Strings.Trim(sApproxAnnualValue), "", TextCompare: false) == 0))
                    {
                        z_AddToRequired(ref sRqd, "Approximate Annual Value");
                    }
                    if (Operators.CompareString(sQuoteType, "2", TextCompare: false) != 0)
                    {
                        if (Operators.CompareString(Strings.Trim(sCustomerBuyer), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Customer Buyer");
                        }
                        if (Operators.CompareString(Strings.Trim(sCustomerEngineer), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Customer Engineer");
                        }
                    }
                    if (Operators.CompareString(sLinkUsed, "rejectLink", TextCompare: false) != 0 && tUserData.bIsPetm && Conversion.Val(sFranNewModRepl) != 1.0 && Operators.CompareString(Strings.Trim(sManufacturingSite), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Manufacturing Site");
                    }
                    if (tUserData.bIsPrd)
                    {
                        if (iLucasPartCount == 0)
                        {
                            z_AddToRequired(ref sRqd, "TRW Part Number(s)");
                        }
                        if (iCustPartCount == 0)
                        {
                            z_AddToRequired(ref sRqd, "Customer Part Number(s)");
                        }
                    }
                    if ((Operators.CompareString(Strings.Trim(sDbsCommentsTargetPricing), "", TextCompare: false) == 0) & (Operators.CompareString(Strings.Trim(sNewCommentsTargetPricing), "", TextCompare: false) == 0))
                    {
                        z_AddToRequired(ref sRqd, "Competitor Intelligence/Target Pricing");
                    }
                    if ((Operators.CompareString(Strings.Trim(sComment), "", TextCompare: false) == 0) & (Operators.CompareString(Strings.Trim(sNewComment), "", TextCompare: false) == 0))
                    {
                        z_AddToRequired(ref sRqd, "Background Information");
                    }
                    if (Operators.CompareString(sObsolescenceRequiredInd, "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Obsolescence Required?");
                    }
                    if (unchecked((0u - (((Operators.CompareString(sQuoteType, "2", TextCompare: false) == 0) & (Operators.CompareString(sGateway, "", TextCompare: false) == 0)) ? 1u : 0u)) & (uint)Strings.InStr(sSecurityMode, "A")) != 0)
                    {
                        z_AddToRequired(ref sRqd, "Gateway?");
                    }
                    if (((Operators.CompareString(sQuoteType, "2", TextCompare: false) == 0) & (Operators.CompareString(sGateway, "1", TextCompare: false) == 0) & (Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0)) && !mCheckFormAttached("quote information"))
                    {
                        z_AddToRequired(ref sRqd, "Please attach the Quote Information spreadsheet.");
                    }
                    if (bOrigIsSDir | bOrigIsAMgr | (Operators.CompareString(sQuoteType, "2", TextCompare: false) != 0))
                    {
                        if (!bOrigIsSDir & (Operators.CompareString(Strings.Trim(sAccountManager), "", TextCompare: false) == 0))
                        {
                            z_AddToRequired(ref sRqd, "Sales Account Manager");
                        }
                        if (Operators.CompareString(Strings.Trim(sSalesAccountDirector), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Sales Account Director");
                        }
                    }
                    if (!mIsDesignProposalOnly() && Conversions.ToDouble(sFranNewModRepl) != 2.0 && Operators.CompareString(sStatus, "010150FR", TextCompare: false) != 0 && (tUserData.bIsMBnE | ((Operators.CompareString(sStatus, "020100FR", TextCompare: false) >= 0) & (Operators.CompareString(sStatus, "030100FR", TextCompare: false) != 0))) && Operators.CompareString(sLinkUsed, "rejectLink", TextCompare: false) != 0 && Operators.CompareString(Strings.Trim(sManufacturingSite), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Manufacturing Site");
                    }
                    if (Operators.CompareString(sLinkUsed, "rejectLink", TextCompare: false) != 0 && ((Operators.CompareString(sStatus, "010150FR", TextCompare: false) == 0) & tUserData.bIsMBnE & (Conversion.Val(sRevision) == 1.0)))
                    {
                        if (Operators.CompareString(Strings.Trim(sManufacturingSite), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Manufacturing Site");
                        }
                        if (Operators.CompareString(Strings.Trim(sEngineeringSite), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Engineering Site");
                        }
                    }
                    if (Operators.CompareString(Strings.Trim(sCostEstimator), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Finance Coordinator");
                    }
                    if (Operators.CompareString(Strings.Trim(sPGM_aka_CoC), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Engineering Director");
                    }
                    if (Operators.CompareString(Strings.Trim(sPDM_aka_ProgramManager), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "PM Coordinator");
                    }
                    if (Operators.CompareString(sOriginationDate, "20090311", TextCompare: false) > 0 && Operators.CompareString(Strings.Trim(sEngineeringManager), "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "Engineering Coordinator");
                    }
                    if ((Strings.InStr(sSecurityMode, "C") > 0) & tUserData.bIsPetm & bDualRelease)
                    {
                        if (Operators.CompareString(Strings.Trim(sTimeFrameOkInd), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Time Frame OK");
                        }
                        if (Operators.CompareString(Strings.Trim(sInformationOkInd), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Information OK");
                        }
                        if (Operators.CompareString(Strings.Trim(sHealthInd), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Health, Safety & Environmental issues must be considered");
                        }
                        string sTemp = modTools.mDateScreenToCygnet(sDueDateToBidsAndEstimating);
                        if ((Operators.CompareString(sTimeFrameOkInd, "1", TextCompare: false) == 0) & (Operators.CompareString(sTemp, "", TextCompare: false) != 0) & (Operators.CompareString(sTemp, Strings.Format(DateAndTime.Now, "yyyyMMdd"), TextCompare: false) < 0))
                        {
                            z_AddToRequired(ref sOptional, "The 'time allowed' can't be selected as yes if the due date to Bids & Estimating has already passed");
                        }
                        if (Operators.CompareString(Strings.Trim(sDueDateFromEngineering), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Due Date from Design Eng");
                        }
                        if (Operators.CompareString(sWorkProceedOkInd, "1", TextCompare: false) == 0)
                        {
                            if (Operators.CompareString(Strings.Trim(sEngineerAssigned), "", TextCompare: false) == 0)
                            {
                                z_AddToRequired(ref sRqd, "Design Responsibilites");
                            }
                            if (Operators.CompareString(Strings.Trim(sElecInputReqdInd[1]), "", TextCompare: false) == 0)
                            {
                                z_AddToRequired(ref sRqd, "Other Design Input PDM");
                            }
                            if (Operators.CompareString(sElecInputReqdInd[1], "1", TextCompare: false) == 0 && Operators.CompareString(Strings.Trim(sElecPRD[1]), "", TextCompare: false) == 0)
                            {
                                z_AddToRequired(ref sRqd, "Electronics PDM");
                            }
                            if ((Operators.CompareString(sIfCheckedInd, "1", TextCompare: false) == 0) & (Operators.CompareString(sQS9000Considered, "1", TextCompare: false) != 0))
                            {
                                z_AddToRequired(ref sRqd, "QS9000 Section III Must Be Considered");
                            }
                        }
                        else if ((Operators.CompareString(Strings.Trim(sWorkProceedOkInd), "", TextCompare: false) == 0) & (Operators.CompareString(Strings.Trim(sTimeFrameOkInd), "1", TextCompare: false) != 0) & (Operators.CompareString(Strings.Trim(sInformationOkInd), "1", TextCompare: false) != 0))
                        {
                            z_AddToRequired(ref sRqd, "Can Work Proceed");
                        }
                    }
                    if (!(tRouting.mHasCurrentTask("08", "BA", "BA", "*") & (tUserData.bIsPsm | (Operators.CompareString(base.UserId, "ASEIBERT", TextCompare: false) == 0))) && tUserData.bIsPrd && tRouting.mHasCurrentTask("13", "CA", "CA", "*"))
                    {
                        if ((iRCTCount < 1) & (iEPLCount < 1))
                        {
                            if (Operators.CompareString(sNoRCTFolders, "1", TextCompare: false) != 0)
                            {
                                z_AddToRequired(ref sRqd, "At least one Released RCT/ECO/ECM folder must be associated, or the checkbox stating no folders will be associated must be checked.");
                            }
                            else if (Operators.CompareString(sCommentsEP + sNewCommentsEP, "", TextCompare: false) == 0)
                            {
                                z_AddToRequired(ref sRqd, "If no RCT/ECO/ECM folders will be associated with this CQR, comments stating why must be entered.");
                            }
                        }
                        else
                        {
                            int num = iRCTCount - 1;
                            int iLoop;
                            //for (iLoop = 0; iLoop <= num && Operators.CompareString(atPage.cnExecuteForSingleValue("SELECT Status FROM CHGREQ_RequestHeader WHERE QueueKey = " + sRCTItem[iLoop]), "010400RC", TextCompare: false) <= 0; iLoop++)
                            //{
                            //}
                            //if (iLoop > iRCTCount)
                            //{
                            //    z_AddToRequired(ref sRqd, "The RCT/ECO folder(s) associated with this CQR have not been released.");
                            //}
                            int num2 = iEPLCount - 1;
                            for (iLoop = 0; iLoop <= num2; iLoop++)
                            {
                                //if (Operators.CompareString(atPage.cnExecuteForSingleValue("SELECT Status FROM EPL_Header WHERE QueueKey = " + sEPLItem[iEPLCount - 1]), "010100EP", TextCompare: false) > 0)
                                //{
                                //    break;
                                //}
                            }
                            if (iLoop > iEPLCount)
                            {
                                z_AddToRequired(ref sRqd, "The ECM folder(s) associated with this CQR have not been released.");
                            }
                        }
                    }
                    if (Operators.CompareString(sLinkUsed, "rejectLink", TextCompare: false) != 0 && iQAFCount == 0 && (tUserData.bIsCost & (Operators.CompareString(sQRCompletedAndSent, "1", TextCompare: false) != 0)) && mHasQRCostTask())
                    {
                        z_AddToRequired(ref sRqd, "Quote Response - Completed and Sent");
                    }
                    if (bUserHasOrigRole && tRouting.mHasCurrentTask("25", "CA", "CA", base.UserId))
                    {
                        z_AddToRequired(ref sRqd, "Quote Response - Received");
                    }
                    if (Operators.CompareString(sLinkUsed, "rejectLink", TextCompare: false) != 0)
                    {
                        if (tUserData.bIsCost & (Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0))
                        {
                            if (iQAFCount == 0)
                            {
                                if (Operators.CompareString(Strings.Trim(sQRLeadTime), "", TextCompare: false) == 0)
                                {
                                    z_AddToRequired(ref sRqd, "Quote Response - Lead Time");
                                }
                                if (Operators.CompareString(Strings.Trim(sQRToolCap), "", TextCompare: false) == 0)
                                {
                                    z_AddToRequired(ref sRqd, "Quote Response - Tool Capacity");
                                }
                                if (Operators.CompareString(Strings.Trim(sDbsCommentsQRTooling + sNewCommentsQRTooling), "", TextCompare: false) == 0)
                                {
                                    z_AddToRequired(ref sRqd, "Quote Response - Tooling/Comments");
                                }
                            }
                            if (Operators.CompareString(Strings.Trim(sQRFOB), "", TextCompare: false) == 0)
                            {
                                z_AddToRequired(ref sRqd, "Quote Response - F.O.B.");
                            }
                        }
                        if ((tUserData.bIsCmgr & (Operators.CompareString(sStatus, "040100FR", TextCompare: false) > 0)) && Operators.CompareString(Strings.Trim(sQRProgClass), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Quote Response - Program Classification");
                        }
                        bool bQSRequired = default(bool);
                        if (mHasCloseOutRole() & (tRouting.mHasCurrentTask("25", "CA", "CA", "*") | tRouting.mHasCurrentTask("28", "BA", "BA", "*")))
                        {
                            bQSRequired = true;
                        }
                        if ((Operators.CompareString(sStatus, "040200FR", TextCompare: false) == 0) & tUserData.bIsAMgr)
                        {
                            bQSRequired = true;
                        }
                        if ((Operators.CompareString(sStatus, "040300FR", TextCompare: false) == 0) & tUserData.bIsAMgr)
                        {
                            bQSRequired = true;
                        }
                        if (bQSRequired)
                        {
                            if (Operators.CompareString(sQSQuoteStatus, "", TextCompare: false) == 0)
                            {
                                z_AddToRequired(ref sRqd, "Sales Closeout - Quote Status");
                            }
                            if ((Operators.CompareString(sQSQuoteStatus, "2", TextCompare: false) == 0) & (Operators.CompareString(sStatus, "040300FR", TextCompare: false) == 0))
                            {
                                if (Operators.CompareString(sQSAwardStatus, "", TextCompare: false) == 0)
                                {
                                    z_AddToRequired(ref sRqd, "Sales Closeout - Award Status when Quote Letter is selected");
                                }
                                if ((Operators.CompareString(sQSAwardStatus, "2", TextCompare: false) == 0) & (Operators.CompareString(sNewQSComments, "", TextCompare: false) == 0))
                                {
                                    z_AddToRequired(ref sRqd, "Sales Closeout - Comments when Award Status is other ");
                                }
                            }
                            if (Conversion.Val(sQSQuoteStatus) > 0.0)
                            {
                                if (Operators.CompareString(sQSQuoteDate, "", TextCompare: false) == 0)
                                {
                                    z_AddToRequired(ref sRqd, "Sales Closeout - Quote/Letter Date");
                                }
                            }
                            else if (Operators.CompareString(sQuoteType, "2", TextCompare: false) != 0 && ((Operators.CompareString(Strings.Trim(sQSComments), "", TextCompare: false) == 0) & (Operators.CompareString(Strings.Trim(sNewQSComments), "", TextCompare: false) == 0)))
                            {
                                z_AddToRequired(ref sRqd, "Sales Closeout - Comments (if not quoted)");
                            }
                            if (tRouting.mHasCurrentTask("41", "QA", "QA", "*") | tRouting.mHasCurrentTask("41", "AA", "AA", "*"))
                            {
                                z_AddToRequired(ref sRqd, "Gateway 1 Approvals must be completed");
                            }
                            if (tRouting.mHasCurrentTask("42", "QA", "QA", "*") | tRouting.mHasCurrentTask("42", "AA", "AA", "*"))
                            {
                                z_AddToRequired(ref sRqd, "Gateway 2 Approvals must be completed");
                            }
                        }
                    }
                    if (tUserData.bIsPsm & tRouting.mHasCurrentTask("08", "BA", "BA", "*"))
                    {
                        if (Operators.CompareString(Strings.Trim(sPSM), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Program Support Specialist");
                        }
                        if (Operators.CompareString(Strings.Trim(sPA), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Program Administrator (PA)");
                        }
                    }
                    if (!(tUserData.bIsPsm & tRouting.mHasCurrentTask("08", "BA", "BA", "*")) && (tUserData.bIsPrd & tRouting.mHasCurrentTask("13", "CA", "CA", "*")) && ((Conversion.Val(sRevision) >= 1.0) & (Conversion.Val(sRevision) <= 2.0)))
                    {
                        bool bFoundChecklist = false;
                        bool bFoundKickoff = false;
                        bool bFoundERD = false;
                        if (tAttachmentResponse.tTab.iAttCount != 0)
                        {
                            bFoundChecklist = mCheckFormAttached("checklist");
                            bFoundKickoff = mCheckFormAttached("opening meeting agenda");
                            bFoundERD = mCheckFormAttached("eng r&d") | mCheckFormAttached("erd");
                        }
                        //if (!modTRS.isDecisionDesignUser(Conversions.ToString(HttpContext.Current.Session["UserActual"])))
                        //{
                        //    if (!bFoundChecklist)
                        //    {
                        //        z_AddToRequired(ref sRqd, "Please attach the checklist");
                        //    }
                        //    if (!bFoundKickoff)
                        //    {
                        //        z_AddToRequired(ref sRqd, "Please attach the opening meeting agenda");
                        //    }
                        //    if (!bFoundERD & tUserData.bIsPrd)
                        //    {
                        //        z_AddToRequired(ref sRqd, "Please attach the ERD form");
                        //    }
                        //}
                    }
                    if (!checkHolidays(sCustomerQuoteDueDate))
                    {
                        z_AddToRequired(ref sRqd, "Customer Quote Due Date can't be a weekend or holiday.");
                    }
                    if (!checkHolidays(sDueDateToBidsAndEstimating))
                    {
                        z_AddToRequired(ref sRqd, "Due Date to Bid & Estimating can't be a weekend or holiday.");
                    }
                    if (!checkHolidays(sDueDateFromEngineering))
                    {
                        z_AddToRequired(ref sRqd, "Due Date from Engineering can't be a weekend or holiday.");
                    }
                    if ((Operators.CompareString(sDueDateFromEngineering, "", TextCompare: false) != 0) & (Operators.CompareString(sDueDateToBidsAndEstimating, "", TextCompare: false) != 0))
                    {
                        if (Operators.CompareString(modTools.mDateScreenToCygnet(sDueDateFromEngineering), modTools.mDateScreenToCygnet(sDueDateToBidsAndEstimating), TextCompare: false) > 0)
                        {
                            z_AddToRequired(ref sOptional, "Due Date From Engineering is later than Due Date to Bids & Estimating");
                        }
                        else if (Operators.CompareString(modTools.mDateScreenToCygnet(sDueDateFromEngineering), sOriginationDate, TextCompare: false) < 0)
                        {
                            z_AddToRequired(ref sOptional, "Due Date From Engineering is before the Origination Date");
                        }
                    }
                    if (Operators.CompareString(sOriginationDate, "19980201", TextCompare: false) >= 0 && Operators.CompareString(sTimeFrameOkInd, "1", TextCompare: false) == 0)
                    {
                        if (Operators.CompareString(sDueDateFromEngineering, "", TextCompare: false) != 0 && Operators.CompareString(modTools.mDateScreenToCygnet(sDueDateFromEngineering), modTools.mDateScreenToCygnet(sCustomerQuoteDueDate), TextCompare: false) > 0)
                        {
                            z_AddToRequired(ref sOptional, "Due Date From Engineering is after Customer Quote Due Date");
                        }
                        if (Operators.CompareString(sDueDateToBidsAndEstimating, "", TextCompare: false) != 0 && Operators.CompareString(modTools.mDateScreenToCygnet(sDueDateToBidsAndEstimating), modTools.mDateScreenToCygnet(sCustomerQuoteDueDate), TextCompare: false) > 0)
                        {
                            z_AddToRequired(ref sOptional, "Due Date to Bids and Estimating is after Customer Quote Due Date");
                        }
                    }
                    if (((Operators.CompareString(sStatus, "020100FR", TextCompare: false) < 0) & (tUserData.bIsAMgr | tUserData.bIsSDir)) && Operators.CompareString(sDueDateToBidsAndEstimating, "", TextCompare: false) != 0 && Operators.CompareString(modTools.mDateScreenToCygnet(sDueDateToBidsAndEstimating), modTools.mDateScreenToCygnet(Conversions.ToString(DateAndTime.Now)), TextCompare: false) <= 0)
                    {
                        z_AddToRequired(ref sRqd, "Due Date to Bids and Estimating tomorrow or after.");
                    }
                    if (tUserData.bIsPetm & bDualRelease)
                    {
                        if (Operators.CompareString(Strings.Trim(sDueDateToBidsAndEstimating), "", TextCompare: false) == 0)
                        {
                            z_AddToRequired(ref sRqd, "Due Date to Bids and Estimating");
                        }
                        else if (Operators.CompareString(modTools.mDateScreenToCygnet(sDueDateToBidsAndEstimating), sOriginationDate, TextCompare: false) < 0)
                        {
                            z_AddToRequired(ref sRqd, "Due Date to Bids and Estimating must be after the Origination Date");
                        }
                    }
                    break;
            }
            if (Operators.CompareString(sLinkUsed, "rejectLink", TextCompare: false) == 0)
            {
                sOptional = "";
            }
            if (Operators.CompareString(sRqd, "", TextCompare: false) != 0)
            {
                return sRqd;
            }
            if (Operators.CompareString(sOptional, "", TextCompare: false) != 0)
            {
                sOptional = "Optional Tasks:" + sOptional;
                return sOptional + "\\nWould you still like to advance the folder?";
            }
            return "";
        }
    }

    public void RequiredFieldsGDPEP(ref string sRqd, string asLinkUsed_)
    {
        if ((tUserData.bIsCmgr & (Conversion.Val(sRevision) <= Conversion.Val(modTRS.mTRSGetSingleDesc("CQRCMGR", "ACTION")))) && Operators.CompareString(sOriginationDate, "20070423", TextCompare: false) >= 0)
        {
            if (Operators.CompareString(sGDPEPPhase1Required, "", TextCompare: false) == 0)
            {
                z_AddToRequired(ref sRqd, "GDPEP Gate 1 yes/no");
            }
            if (Operators.CompareString(sGDPEPPhase2Required, "", TextCompare: false) == 0)
            {
                z_AddToRequired(ref sRqd, "GDPEP Gate 2 yes/no");
            }
            if ((Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) != 0) & (Operators.CompareString(sGDPEPPhase2Required, "1", TextCompare: false) == 0) & (Conversion.Val(sRevision) == 1.0))
            {
                z_AddToRequired(ref sRqd, "GDPEP Gate 1 must be Yes if GDPEP Gate 2 is Yes for all .1 revision folders");
            }
        }
        if (!(tUserData.bIsCmgr & (Operators.CompareString(Strings.LCase(Strings.Left(asLinkUsed_, 4)), "save", TextCompare: false) != 0) & ((Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) == 0) | (Operators.CompareString(sGDPEPPhase2Required, "1", TextCompare: false) == 0))))
        {
            return;
        }
        bool bHasGW1 = tRouting.mHasCurrentTask("41", "AA", "AA", "*");
        bool bHasGW2 = tRouting.mHasCurrentTask("42", "AA", "AA", "*");
        if (Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) == 0)
        {
            if (Conversion.Val(sRevision) <= Conversion.Val(modTRS.mTRSGetSingleDesc("CQRCMGR", "ACTION")) && tRouting.mHasCurrentTask("01", "BQ", "BQ", "*"))
            {
                bHasGW1 = true;
            }
            if (!bHasGW2 & !tRouting.mHasTask("41", "AA"))
            {
                bHasGW1 = true;
            }
        }
        int iGW = Conversions.ToInteger(Interaction.IIf(bHasGW2, 2, RuntimeHelpers.GetObjectValue(Interaction.IIf(bHasGW1, 1, 0))));
        if (!((iGW == 1) | ((iGW == 2) & (Operators.CompareString(tGateway[iGW].sReleaseGateway, "1", TextCompare: false) == 0))))
        {
            return;
        }
        if (Operators.CompareString(tGateway[iGW].sGDPIMCategory, "", TextCompare: false) == 0)
        {
            z_AddToRequired(ref sRqd, "Gateway " + Conversions.ToString(iGW) + " Category");
        }
        int iLoop = 1;
        checked
        {
            do
            {
                if (Operators.CompareString(tGateway[iGW].sVolume[iLoop], "", TextCompare: false) == 0)
                {
                    z_AddToRequired(ref sRqd, "Gateway " + Conversions.ToString(iGW) + " Volume " + Conversions.ToString(iLoop));
                }
                iLoop++;
            }
            while (iLoop <= 2);
            if (!bHasGW2)
            {
                return;
            }
            iLoop = 1;
            do
            {
                if (Operators.CompareString(tGateway[iGW].sSellPrice[iLoop], "", TextCompare: false) == 0)
                {
                    z_AddToRequired(ref sRqd, "Gateway " + Conversions.ToString(iGW) + " Sell Price " + Conversions.ToString(iLoop));
                }
                iLoop++;
            }
            while (iLoop <= 1);
        }
    }

    public void RequiredFieldsGDPEPApprovals(ref string sRqd)
    {
        int num = Information.UBound(tGateway[1].sApprovalState);
        checked
        {
            for (int iLoop = 1; iLoop <= num; iLoop++)
            {
                if (!tUserData.bIsGateway[iLoop])
                {
                    continue;
                }
                int jLoop = 1;
                do
                {
                    if (tRouting.mHasCurrentTask("4" + Conversions.ToString(jLoop), "QA", "QA", tGateway[jLoop].sApprovalUser[iLoop]) && Operators.CompareString(tGateway[jLoop].sApprovalState[iLoop], "", TextCompare: false) == 0)
                    {
                        z_AddToRequired(ref sRqd, "GDPEP Gate " + Conversions.ToString(jLoop) + " Approval");
                    }
                    if ((Operators.CompareString(tGateway[jLoop].sApprovalState[iLoop], "0", TextCompare: false) == 0) & (Operators.CompareString(sNewCommentsGDPEPRejection, "", TextCompare: false) == 0))
                    {
                        sRqd = sRqd + "@Reason required when rejecting GDPEP Gate " + Conversions.ToString(jLoop) + ".\r\nEnter in Comments box to the right.";
                    }
                    jLoop++;
                }
                while (jLoop <= 2);
            }
        }
    }

    public void z_AdvanceGDPEPGateway(int iGateway)
    {
        string[] tUsers = null;
        if ((iGateway == 1) & (Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) != 0))
        {
            return;
        }
        tRouting.mCloseTask("4" + Conversions.ToString(iGateway), "AA", "AA", "*", "Released");
        tGateway[iGateway].sReleaseDate = Conversions.ToString(DateAndTime.Now);
        checked
        {
            int iUserCount = default(int);
            int iLoop;
            if (iGateway == 1)
            {
                if (!tRouting.mHasCurrentTask("42", "AA", "AA", "*"))
                {
                    tRouting.mCreateTask("42", "AA", "AA", tUserData.sManagerCmgr);
                }
            }
            else
            {
                tRouting.AddRoleToArray("CQRSAFETY", ref tUsers, ref iUserCount);
                int num = iUserCount - 1;
                for (iLoop = 0; iLoop <= num; iLoop++)
                {
                    tRouting.mCreateTask("4" + Conversions.ToString(iGateway), "N1", "N1", tUsers[iLoop], "", abNote: true);
                }
            }
            iUserCount = 0;
            if (!tRouting.mHasTask("4" + Conversions.ToString(iGateway), "N1"))
            {
                tRouting.AddRoleToArray("CQGWNFY", ref tUsers, ref iUserCount);
                int num2 = iUserCount - 1;
                for (iLoop = 0; iLoop <= num2; iLoop++)
                {
                    tRouting.mCreateTask("4" + Conversions.ToString(iGateway), "N1", "N1", tUsers[iLoop], "", abNote: true);
                }
            }
            iLoop = 1;
            do
            {
                if (Operators.CompareString(tGateway[iGateway].sApprovalUser[iLoop], "", TextCompare: false) == 0)
                {
                    tGateway[iGateway].sApprovalUser[iLoop] = tUserData.sManagerGateway[iLoop];
                }
                if ((Operators.CompareString(tGateway[iGateway].sApprovalUser[iLoop], "", TextCompare: false) != 0) & !tRouting.mHasCurrentTask("4" + Conversions.ToString(iGateway), "QA", "QA", tGateway[iGateway].sApprovalUser[iLoop]))
                {
                    tRouting.mCreateTask("4" + Conversions.ToString(iGateway), "QA", "QA", tGateway[iGateway].sApprovalUser[iLoop]);
                }
                iLoop++;
            }
            while (iLoop <= 12);
            if (iGateway == 1)
            {
                T_GDPEP_APPROVAL t_GDPEP_APPROVAL = tGateway[1];
                tGateway[2].sGDPIMCategory = t_GDPEP_APPROVAL.sGDPIMCategory;
                int num3 = Information.UBound(t_GDPEP_APPROVAL.sVolume);
                for (iLoop = 1; iLoop <= num3; iLoop++)
                {
                    tGateway[2].sVolume[iLoop] = t_GDPEP_APPROVAL.sVolume[iLoop];
                }
                int num4 = Information.UBound(t_GDPEP_APPROVAL.sApprovalUser);
                for (iLoop = 1; iLoop <= num4; iLoop++)
                {
                    tGateway[2].sApprovalUser[iLoop] = "";
                }
                t_GDPEP_APPROVAL = null;
            }
        }
    }

    public bool z_IsGDPEPUser()
    {
        bool bExitWhenDone = false;
        int iLoop = 1;
        checked
        {
            do
            {
                int iGateway = 1;
                do
                {
                    if (tUserData.bIsGateway[iLoop])
                    {
                        bExitWhenDone = tUserData.bIsGateway[iLoop];
                    }
                    iGateway++;
                }
                while (iGateway <= 2);
                iLoop++;
            }
            while (iLoop <= 12);
            return bExitWhenDone;
        }
    }

    public void z_AdvanceStatusGDPEP(bool bRelease)
    {
        int iLoop = 1;
        checked
        {
            do
            {
                int iGateway = 1;
                do
                {
                    if (!bRelease)
                    {
                        T_GDPEP_APPROVAL t_GDPEP_APPROVAL = tGateway[iGateway];
                        if ((Operators.CompareString(t_GDPEP_APPROVAL.sApprovalState[iLoop], t_GDPEP_APPROVAL.sApprovalStateDbs[iLoop], TextCompare: false) != 0) & !tRouting.mHasTask("4" + Conversions.ToString(iGateway), "QA", "", t_GDPEP_APPROVAL.sApprovalUser[iLoop]))
                        {
                            tRouting.mCreateTask("4" + Conversions.ToString(iGateway), "QA", "QA", t_GDPEP_APPROVAL.sApprovalUser[iLoop], "", abNote: true);
                        }
                        t_GDPEP_APPROVAL = null;
                    }
                    else if (!((iGateway == 1) & (Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) != 0)) && tUserData.bIsGateway[iLoop])
                    {
                        T_GDPEP_APPROVAL t_GDPEP_APPROVAL2 = tGateway[iGateway];
                        if ((Operators.CompareString(t_GDPEP_APPROVAL2.sApprovalUser[iLoop], "", TextCompare: false) != 0) & !tRouting.mHasTask("4" + Conversions.ToString(iGateway), "QA", "", t_GDPEP_APPROVAL2.sApprovalUser[iLoop]))
                        {
                            tRouting.mCreateTask("4" + Conversions.ToString(iGateway), "QA", "QA", t_GDPEP_APPROVAL2.sApprovalUser[iLoop], "", abNote: true);
                        }
                        if ((Operators.CompareString(t_GDPEP_APPROVAL2.sApprovalState[iLoop], "", TextCompare: false) != 0) & tRouting.mHasCurrentTask("4" + Conversions.ToString(iGateway), "QA", "QA", t_GDPEP_APPROVAL2.sApprovalUser[iLoop]))
                        {
                            tRouting.mCloseTask("4" + Conversions.ToString(iGateway), "QA", "QA", t_GDPEP_APPROVAL2.sApprovalUser[iLoop], Conversions.ToString(Interaction.IIf(Operators.CompareString(t_GDPEP_APPROVAL2.sApprovalState[iLoop], "0", TextCompare: false) == 0, "Rejected", "Approved")));
                            if ((Operators.CompareString(t_GDPEP_APPROVAL2.sApprovalState[iLoop], "0", TextCompare: false) == 0) & !tRouting.mHasCurrentTask("4" + Conversions.ToString(iGateway), "XA", "XA", "*"))
                            {
                                tRouting.mCreateTask("4" + Conversions.ToString(iGateway), "XA", "XA", tUserData.sManagerCmgr, "", abNote: true);
                            }
                            t_GDPEP_APPROVAL2.sApprovalDate[iLoop] = Conversions.ToString(DateAndTime.Now);
                        }
                        t_GDPEP_APPROVAL2 = null;
                    }
                    iGateway++;
                }
                while (iGateway <= 2);
                iLoop++;
            }
            while (iLoop <= 12);
        }
    }

    public void z_AdvanceStatus(string asLinkUsed)
    {
        string[] sUsers = null;
        bool bHad10BA = tRouting.mHasCurrentTask("10", "BA", "BA", "*");
        bool bHad08BA = tRouting.mHasCurrentTask("08", "BA", "BA", "*");
        bool bHas01BQ = tRouting.mHasCurrentTask("01", "BQ", "BQ", "*");
        if (Operators.CompareString(asLinkUsed, "releaseGDPEPLink", TextCompare: false) == 0 || bHas01BQ)
        {
            if ((tUserData.bIsCmgr & ((Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) == 0) | (Operators.CompareString(sGDPEPPhase2Required, "1", TextCompare: false) == 0))) && (tRouting.mHasCurrentTask("42", "AA", "AA", "*") & (Operators.CompareString(tGateway[2].sReleaseGateway, "1", TextCompare: false) == 0)))
            {
                z_AdvanceGDPEPGateway(2);
                return;
            }
            if (!bHas01BQ)
            {
                return;
            }
        }
        switch (asLinkUsed)
        {
            case "releaseLink":
            case "releasesortsLink":
                sMode = "Released";
                break;
            case "approveLink":
                sMode = "Approved";
                break;
            case "rejectLink":
            case "rejectNoCommentLink":
                sMode = "Rejected";
                break;
            case "terminateLink":
                sMode = "Terminate";
                break;
        }
        int iUserCount = default(int);
        if (Operators.CompareString(sMode, "Terminate", TextCompare: false) == 0)
        {
            tRouting.mCloseAllTasks("Terminat");
            mNotifyUserArray(sUsers, iUserCount, "99", "ZZ");
            z_UpdateStatus("090900FR");
            return;
        }
        if (((Operators.CompareString(sMode, "Released", TextCompare: false) == 0) & (tUserData.bIsPsm | (Operators.CompareString(base.UserId, "ASEIBERT", TextCompare: false) == 0))) && tRouting.mHasCurrentTask("08", "BA", "BA", "*"))
        {
            tRouting.mCloseTask("08", "BA", "BA", "*", sMode);
        }
        checked
        {
            if ((Operators.CompareString(sStatus, "040150FR", TextCompare: false) > 0) & tUserData.bIsCmgr & (Operators.CompareString(sMode, "Released", TextCompare: false) == 0))
            {
                int num = iQAFCount - 1;
                for (int iLoop = 0; iLoop <= num; iLoop++)
                {
                    tPNL[iLoop].sQafStatus = "010100QF";
                    tPNL[iLoop].sStatusDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
                }
                int num2 = tRouting.mCount();
                for (int iLoop = 1; iLoop <= num2; iLoop++)
                {
                    tRouting.mDeleteTask("25", "BA", "BA", base.UserId);
                }
                tRouting.mPNLRGDelete(isSales: true);
                tRouting.mPNLRGDelete(isSales: false);
                z_UpdateStatus("040100FR");
                tRouting.mCreateTask("22", "CA", "CA", sCostEstimator, modTools.mDateScreenToCygnet(sCustomerQuoteDueDate));
                return;
            }
            string sCaseStatus = sStatus;
            if (unchecked(tUserData.bIsPsm && bHad08BA) & (Operators.CompareString(sMode, "Released", TextCompare: false) == 0))
            {
                tRouting.mCloseTask("08", "BA", "BA", "*", "Released");
                if (tRouting.mHasCurrentTask("10", "BA", "BA", "*"))
                {
                    sCaseStatus = "";
                }
                if (tRouting.mHasCurrentTask("01", "GA", "GA", "*"))
                {
                    sCaseStatus = "";
                }
                if (tRouting.mHasCurrentTask("13", "CA", "CA", "*"))
                {
                    sCaseStatus = "";
                }
            }
            else
            {
                switch (sCaseStatus)
                {
                    case "010100FR":
                        if ((Operators.CompareString(sMode, "Released", TextCompare: false) == 0) | (Operators.CompareString(sMode, "Approved", TextCompare: false) == 0))
                        {
                            tRouting.mCloseTask("01", "BA", "BA", "*", "Released");
                            if ((Operators.CompareString(sFranNewModRepl, "2", TextCompare: false) == 0) & (Conversion.Val(sQuoteType) < 2.0) & ((Operators.CompareString(sManufacturingSite, "WIN", TextCompare: false) == 0) | (Operators.CompareString(sManufacturingSite, "********", TextCompare: false) == 0)))
                            {
                                tRouting.mNotifyRole("CQRRELMOD", "01", "N1");
                            }
                            tRouting.mCloseTask("01", "BX", "BX", "*", "Released");
                            if (Operators.CompareString(sManufacturingSite, "", TextCompare: false) == 0)
                            {
                                sManufacturingSite = "********";
                            }
                            sGDPEPPhase1Required = Conversions.ToString(Interaction.IIf(Conversion.Val(sRevision) == 1.0, "1", "2"));
                            sGDPEPPhase2Required = "1";
                            if (((Operators.CompareString(sOriginator, tUserData.sManagerBids, TextCompare: false) == 0) & ((Conversion.Val(sRevision) == 1.0) | (Operators.CompareString(sModNeedsCostInd, "1", TextCompare: false) == 0))) && !tRouting.mHasCurrentTask("07", "BA", "BA", "*"))
                            {
                                tRouting.mCreateTask("07", "BA", "BA", tUserData.sManagerBids);
                                tRouting.mCloseTask("07", "BA", "BA", "*", "Released");
                            }
                            if (Operators.CompareString(sOriginator, sPGM_aka_CoC, TextCompare: false) == 0 && !tRouting.mHasCurrentTask("01", "GA", "GA", "*"))
                            {
                                tRouting.mCreateTask("01", "GA", "GA", sPGM_aka_CoC);
                                tRouting.mCloseTask("01", "GA", "GA", "*", "Approved");
                                z_goGDPEPPhaseI();
                            }
                            if (Operators.CompareString(sOriginator, sEngineeringManager, TextCompare: false) == 0 && !tRouting.mHasCurrentTask("10", "BA", "BA", "*"))
                            {
                                tRouting.mCreateTask("10", "BA", "BA", sEngineeringManager);
                                tRouting.mCloseTask("10", "BA", "BA", "*", "Released");
                            }
                            if (Operators.CompareString(sGateway, "1", TextCompare: false) == 0)
                            {
                                tRouting.mCreateTask("01", "EE", "EE", "SYOUNG", "", abNote: true);
                            }
                            tRouting.mCreateTask("01", "BR", "BR", tUserData.sManagerCmgr, "", abNote: true);
                            if (((Operators.CompareString(sQuoteType, "0", TextCompare: false) == 0) | (Operators.CompareString(sQuoteType, "1", TextCompare: false) == 0)) & (Conversion.Val(sRevision) > 1.0))
                            {
                                tRouting.mCreateTask("01", "BS", "BS", modUserProf.mUPGetManager("CQRSAFETY"), "", abNote: true);
                            }
                            z_LeaveOriginator();
                        }
                        break;
                    case "010150FR":
                        if (Operators.CompareString(sIndTask, "3", TextCompare: false) == 0)
                        {
                            if (Operators.CompareString(sMode, "Approved", TextCompare: false) == 0)
                            {
                                tRouting.mCloseTask("01", "BQ", "BQ", "*", "Approved");
                                z_LeaveOriginator();
                            }
                            if (Operators.CompareString(sMode, "Rejected", TextCompare: false) == 0)
                            {
                                tRouting.mCloseTask("01", "BQ", "BQ", "*", "Rejected");
                                z_UpdateStatus("010100FR");
                                tRouting.mCreateTask("01", "BX", "BX", sOriginator);
                            }
                        }
                        break;
                    case "010200FR":
                        if (Operators.CompareString(sMode, "Rejected", TextCompare: false) == 0)
                        {
                            tRouting.mCloseTask("01", "CA", "CA", "*", "Rejected");
                            tRouting.mCloseTask("01", "CA", "CA", "*", "Rejected");
                            z_UpdateStatus("010300FR");
                            tRouting.mCreateTask("01", "DB", "DB", Conversions.ToString(Interaction.IIf(tUserData.bIsSDir, sAccountManager, sSalesAccountDirector)), "", abNote: true);
                            tRouting.mCreateTask("01", "DB", "DB", sOriginator, "", abNote: true);
                        }
                        if (Operators.CompareString(sMode, "Approved", TextCompare: false) == 0)
                        {
                            tRouting.mCloseTask("01", "CA", "CA", "*", "Approved");
                            tRouting.mCloseTask("01", "CA", "CA", "*", "Approved");
                            mSendNotification(sOriginator, Conversions.ToString(Interaction.IIf(tUserData.bIsSDir, sAccountManager, sSalesAccountDirector)), "01", "EA", "EA", Conversions.ToString(Interaction.IIf(tUserData.bIsSDir, sAccountManager, sSalesAccountDirector)), "", abNote: true);
                            mSendNotification(sOriginator, tUserData.sManagerFran, "01", "EA", "EA", tUserData.sManagerFran, "", abNote: true);
                            mFranLeaveSales("020100FR", "020300FR", abNotifyCost: true, abNotifyCQRInitiated: false);
                        }
                        break;
                    case "020100FR":
                    case "020200FR":
                    case "020300FR":
                    case "020400FR":
                        mProcess020x00(ref sMode);
                        break;
                    case "020500FR":
                        if (Operators.CompareString(sMode, "Released", TextCompare: false) == 0)
                        {
                            tRouting.mCloseTask("01", "IA", "IA", "*", "Released");
                            tRouting.mCreateTask("04", "BA", "BA", sSalesAccountDirector, "", abNote: true);
                            tRouting.mCreateTask("04", "BA", "BA", sAccountManager, "", abNote: true);
                            if (Operators.CompareString(sGateway, "1", TextCompare: false) != 0)
                            {
                                tRouting.mCreateTask("01", "GA", "GA", sPGM_aka_CoC);
                            }
                            mFranIssueHardCodes("04", "BA");
                            if (Conversion.Val(sQuoteType) == 2.0)
                            {
                                mFranLeaveSales("020200FR", "020400FR", abNotifyCost: false, abNotifyCQRInitiated: false);
                            }
                            else
                            {
                                mFranLeaveSales("020100FR", "020300FR", abNotifyCost: false, abNotifyCQRInitiated: false);
                            }
                        }
                        break;
                    case "030100FR":
                        if (Operators.CompareString(sMode, "Released", TextCompare: false) == 0)
                        {
                            tRouting.mCloseTask("10", "DA", "DA", "*", "Released");
                            z_UpdateStatus(Conversions.ToString(Interaction.IIf(tRouting.mHasCurrentTask("07", "BA", "BA", "*"), "020300FR", "020100FR")));
                            tRouting.mCreateTask("10", "BA", "BA", sEngineeringManager);
                        }
                        break;
                    case "030200FR":
                    case "030300FR":
                        if (Operators.CompareString(sMode, "Released", TextCompare: false) != 0)
                        {
                            break;
                        }
                        if (tUserData.bIsPrd)
                        {
                            if (((Operators.CompareString(sNoRCTFolders, "1", TextCompare: false) == 0) | (iRCTCount > 0) | (iEPLCount > 0)) & tRouting.mHasCurrentTask("13", "CA", "CA", "*"))
                            {
                                tRouting.mCloseTask("13", "CA", "CA", "*", "Released");
                            }
                            if (tRouting.mHasCurrentTask("13", "PR", "PR", base.UserId))
                            {
                                tRouting.mCloseTask("13", "PR", "PR", base.UserId, "Released");
                            }
                            if (!tRouting.mHasCurrentTask("13", "CA", "CA", "*") & !tRouting.mHasCurrentTask("13", "PR", "PR", "*"))
                            {
                                z_UpdateStatus("040100FR");
                                tRouting.mCreateTask("22", "BA", "BA", sCostEstimator, modTools.mDateScreenToCygnet(sCustomerQuoteDueDate));
                                tRouting.mCloseTask("01", "JA", "JA", sPGM_aka_CoC, "Released");
                                tRouting.mCloseTask("01", "JA", "JA", sPDM_aka_ProgramManager, "Released");
                            }
                        }
                        if (Operators.CompareString(base.UserId, tUserData.sManagerBids, TextCompare: false) == 0)
                        {
                            tRouting.mCloseTask("07", "BA", "BA", tUserData.sManagerBids, "Released");
                        }
                        break;
                    case "030400FR":
                        if (Operators.CompareString(sMode, "Released", TextCompare: false) == 0 && tUserData.bIsPrd)
                        {
                            tRouting.mCloseTask("13", "DA", "DA", "*", "Released");
                            z_UpdateStatus("040200FR");
                            if (bOrigIsAMgr | bOrigIsSDir | (Conversion.Val(sQuoteType) == 1.0))
                            {
                                tRouting.mCreateTask("25", "BA", "BA", sOriginator, "", abNote: true);
                                tRouting.mCreateTask("25", "BA", "BA", getLooney(), "", abNote: true);
                                tRouting.mCreateTask("28", "BA", "BA", sAccountManager);
                            }
                            else
                            {
                                tRouting.mCreateTask("28", "BA", "BA", sOriginator);
                            }
                            tRouting.mCloseTask("01", "JA", "JA", sPGM_aka_CoC, "Released");
                            tRouting.mCloseTask("01", "JA", "JA", sPDM_aka_ProgramManager, "Released");
                        }
                        break;
                    case "040100FR":
                    case "040150FR":
                        if ((Operators.CompareString(sMode, "Rejected", TextCompare: false) == 0) & (Operators.CompareString(sCaseStatus, "040100FR", TextCompare: false) == 0))
                        {
                            z_CloseQuoteResponseTask(sMode);
                            z_UpdateStatus("030200FR");
                            tRouting.mCreateTask("13", "CA", "CA", sEngineerAssigned, modTools.mDateScreenToCygnet(sDueDateFromEngineering));
                        }
                        else
                        {
                            mProcess0401x0FR(sMode);
                        }
                        break;
                    case "040200FR":
                    case "040400FR":
                        if (Operators.CompareString(sMode, "Rejected", TextCompare: false) == 0)
                        {
                            int num3 = tRouting.mCount();
                            for (int iLoop = 1; iLoop <= num3; iLoop++)
                            {
                                tRouting.mCloseTask("25", "BA", "BA", base.UserId, "Rejected");
                            }
                            tRouting.mCloseTask("28", "BA", "BA", "*", "Rejected");
                            tRouting.mCloseTask("28", "MA", "MA", "*", "Rejected");
                            z_UpdateStatus("040100FR");
                            tRouting.mCreateTask("22", "CA", "CA", sCostEstimator, modTools.mDateScreenToCygnet(sCustomerQuoteDueDate));
                            sQRCompletedAndSent = "";
                        }
                        if ((Operators.CompareString(sMode, "Released", TextCompare: false) == 0) | (Operators.CompareString(sMode, "Approved", TextCompare: false) == 0))
                        {
                            if (Operators.CompareString(sMode, "Released", TextCompare: false) == 0 && (!tRouting.mHasCurrentTask("28", "NA", "NA", "*") & !tRouting.mHasCurrentTask("28", "BA", "BA", "*") & !tRouting.mHasCurrentTask("28", "MA", "MA", "*")))
                            {
                                return;
                            }
                            tRouting.mCloseTask("28", "BA", "BA", "*", sMode);
                            tRouting.mCloseTask("28", "MA", "MA", "*", sMode);
                            if ((iQAFCount == 0) | (Operators.CompareString(sQuoteType, "2", TextCompare: false) == 0) | (Operators.CompareString(sQSQuoteStatus, "2", TextCompare: false) != 0))
                            {
                                z_UpdateStatus("090100FR");
                            }
                            else
                            {
                                z_UpdateStatus("040300FR");
                                tRouting.mCreateTask("28", "SA", "SA", sAccountManager);
                            }
                            if (!bUserIsOrig)
                            {
                                tRouting.mCreateTask("25", "BA", "BA", sOriginator, "", abNote: true);
                                tRouting.mCreateTask("25", "BA", "BA", getLooney(), "", abNote: true);
                            }
                        }
                        break;
                    case "040300FR":
                        if (Operators.CompareString(sMode, "Released", TextCompare: false) == 0)
                        {
                            tRouting.mCloseTask("28", "BA", "BA", "*", "Released");
                            tRouting.mCloseTask("28", "SA", "SA", "*", "Released");
                            tRouting.AddUserToArray(sPGM_aka_CoC, ref sUsers, ref iUserCount);
                            tRouting.AddUserToArray(sEngineerAssigned, ref sUsers, ref iUserCount);
                            tRouting.AddUserToArray(sElecPRD[1], ref sUsers, ref iUserCount);
                            string sTaskCode;
                            if (Operators.CompareString(sQSAwardStatus, "0", TextCompare: false) == 0)
                            {
                                tRouting.AddUserToArray(sAccountManager, ref sUsers, ref iUserCount);
                                tRouting.AddUserToArray(sSalesAccountDirector, ref sUsers, ref iUserCount);
                                tRouting.AddUserToArray(sCostEstimator, ref sUsers, ref iUserCount);
                                tRouting.AddUserToArray(sPA, ref sUsers, ref iUserCount);
                                tRouting.AddUserToArray(sPSM, ref sUsers, ref iUserCount);
                                tRouting.AddRoleToArray("CQRNBUS", ref sUsers, ref iUserCount);
                                z_UpdateStatus("040700FR");
                                tRouting.mCreateTask("30", "AA", "AA", sPDM_aka_ProgramManager);
                                sTaskCode = "N1";
                            }
                            else
                            {
                                tRouting.AddUserToArray(sPDM_aka_ProgramManager, ref sUsers, ref iUserCount);
                                tRouting.AddRoleToArray("WLNOT", ref sUsers, ref iUserCount);
                                z_UpdateStatus("090100FR");
                                sTaskCode = Conversions.ToString(Interaction.IIf(Operators.CompareString(sQSAwardStatus, "1", TextCompare: false) == 0, "N3", "N5"));
                            }
                            mNotifyUserArray(sUsers, iUserCount, "30", sTaskCode);
                        }
                        break;
                    case "040700FR":
                        if ((Operators.CompareString(sMode, "Released", TextCompare: false) == 0) & tUserData.bIsPDM)
                        {
                            tRouting.AddRoleToArray("WLNOT", ref sUsers, ref iUserCount);
                            tRouting.AddUserToArray(sEngineeringManager, ref sUsers, ref iUserCount);
                            mNotifyUserArray(sUsers, iUserCount, "30", "N1");
                            tRouting.mCloseTask("30", "AA", "AA", "*", "Released");
                            z_UpdateStatus("090100FR");
                        }
                        break;
                }
            }
        }
        if ((bHad10BA || bHad08BA) & (!tRouting.mHasCurrentTask("10", "BA", "BA", "*") & !tRouting.mHasCurrentTask("08", "BA", "BA", "*")))
        {
            if (!tRouting.mHasCurrentTask("13", "BA", "BA", sPSM))
            {
                mSendNotification(sOriginator, sPSM, "13", "BA", "BA", sPSM, "", abNote: true);
            }
            if (!tRouting.mHasCurrentTask("13", "BA", "BA", sPA))
            {
                mSendNotification(sOriginator, sPA, "13", "BA", "BA", sPA, "", abNote: true);
            }
        }
    }

    public void z_LeaveOriginator()
    {
        bool bDouged = default(bool);
        if (bOrigIsAMgr | bOrigIsSDir)
        {
            mFranLeaveSales("020100FR", "020300FR", abNotifyCost: false, abNotifyCQRInitiated: false);
        }
        else
        {
            mFranLeaveSales("020200FR", "020400FR", abNotifyCost: true, abNotifyCQRInitiated: false);
            bDouged = true;
            if ((Operators.CompareString(sOriginator, sPGM_aka_CoC, TextCompare: false) == 0) & (Operators.CompareString(sOriginator, sPDM_aka_ProgramManager, TextCompare: false) == 0))
            {
                mFranLeavePetm();
            }
        }
        if (!bDouged & (Operators.CompareString(sStatus, "010100FR", TextCompare: false) == 0) & (Conversion.Val(sRevision) <= Conversions.ToDouble(modTRS.mTRSGetSingleDesc("CQRCMGR", "NOTIFY"))))
        {
            tRouting.mCreateTask("01", "BR", "BR", tUserData.sManagerCmgr, "", abNote: true);
            bDouged = true;
        }
    }

    public void z_LeaveOriginatorNotification()
    {
        string[] sUsers = null;
        if (Operators.CompareString(sSalesAccountDirector, "MMORTIME", TextCompare: false) == 0)
        {
            tRouting.mCreateTask("01", "EA", "EA", "MAWOOD", "", abNote: true);
        }
        tRouting.mCreateTask("01", "EA", "EA", sPDM_aka_ProgramManager, "", abNote: true);
        if (bOrigIsAMgr | bOrigIsSDir)
        {
            NotifyUserCQRInitiated(abNotifyCost: false);
            mSendNotification(sOriginator, sSalesAccountDirector, "01", "EA", "EA", sSalesAccountDirector, "", abNote: true);
            if (Operators.CompareString(sSalesAccountDirector, sAccountManager, TextCompare: false) != 0)
            {
                mSendNotification(sOriginator, sAccountManager, "01", "EA", "EA", sAccountManager, "", abNote: true);
            }
            if ((Operators.CompareString(sSalesAccountDirector, "", TextCompare: false) != 0) | (Operators.CompareString(sAccountManager, "", TextCompare: false) != 0))
            {
            }
            if (Conversion.Val(sRevision) > 1.0)
            {
                mSendNotification(sOriginator, sCostEstimator, "01", "EA", "EA", sCostEstimator, "", abNote: true);
            }
            mSendNotification(sOriginator, tUserData.sManagerFran, "01", "EA", "EA", tUserData.sManagerFran, "", abNote: true);
        }
        else
        {
            int iUserCount = default(int);
            tRouting.AddUserToArray(sSalesAccountDirector, ref sUsers, ref iUserCount);
            tRouting.AddUserToArray(sAccountManager, ref sUsers, ref iUserCount);
            tRouting.AddUserToArray(tUserData.sManagerFran, ref sUsers, ref iUserCount);
            if ((Operators.CompareString(sSalesAccountDirector, "", TextCompare: false) != 0) | (Operators.CompareString(sAccountManager, "", TextCompare: false) != 0))
            {
            }
            mNotifyUserArray(sUsers, iUserCount, "01", "FA");
            NotifyUserCQRInitiated(abNotifyCost: true);
            mFranIssueHardCodes("01", "FA");
        }
    }

    public void mProcess0401x0FR(string sMode)
    {
        int iDoneCount = 0;
        int iUserCount = 0;
        string[] sUsers = null;
        if ((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) & !tUserData.bIsCost)
        {
            return;
        }
        checked
        {
            if ((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) & tUserData.bIsCost & (Operators.CompareString(sIndTask, "4", TextCompare: false) == 0))
            {
                int num = iQAFCount - 1;
                int iLoop;
                for (iLoop = 0; iLoop <= num && !(tPNL[iLoop].bRelease & !tPNL[iLoop].bDeleteOnSave); iLoop++)
                {
                }
                if (!((iQAFCount > 0) & (iLoop < iQAFCount)))
                {
                    if (Operators.CompareString(sQRCompletedAndSent, "1", TextCompare: false) == 0)
                    {
                        tRouting.mCloseTask("22", "BA", "BA", "*", sMode);
                        tRouting.mCloseTask("22", "CA", "CA", "*", sMode);
                        tRouting.mCloseTask("24", "CA", "CA", "*", sMode);
                        mAdvanceTo040200();
                        return;
                    }
                    int num2 = iQAFCount - 1;
                    for (iLoop = 0; iLoop <= num2; iLoop++)
                    {
                        if (!tPNL[iLoop].bDeleteOnSave & (Operators.CompareString(tPNL[iLoop].sQafStatus, "010100QF", TextCompare: false) <= 0))
                        {
                            return;
                        }
                    }
                    z_UpdateStatus("040150FR");
                    return;
                }
                z_CloseQuoteResponseTask(sMode);
                z_UpdateStatus("040150FR");
                tRouting.AddUserToArray(sAccountManager, ref sUsers, ref iUserCount);
                tRouting.AddUserToArray(sSalesAccountDirector, ref sUsers, ref iUserCount);
                tRouting.AddUserToArray(sOriginator, ref sUsers, ref iUserCount);
                int num3 = iQAFCount - 1;
                for (iLoop = 0; iLoop <= num3; iLoop++)
                {
                    int num4 = iUserCount - 1;
                    for (int jLoop = 0; jLoop <= num4; jLoop++)
                    {
                        if (Operators.CompareString(sUsers[jLoop], mQafSalesActionGuy(), TextCompare: false) == 0)
                        {
                            sUsers[jLoop] = "";
                        }
                    }
                }
                if ((Operators.CompareString(sManufacturingSite, "********", TextCompare: false) == 0) & (iQAFCount > 1))
                {
                    string sTemp = modTRS.mTRSGetSingleDesc("FRPLMFG", "PL001");
                    string[] tArray = Strings.Split(sTemp, ",");
                    int num5 = Information.UBound(tArray);
                    for (iLoop = 0; iLoop <= num5; iLoop++)
                    {
                        tRouting.AddUserToArray(tArray[iLoop], ref sUsers, ref iUserCount);
                    }
                }
                mNotifyUserArray(sUsers, iUserCount, "23", "N1");
                tRouting.mCreateTask("23", "N1", "N1", getLooney(), "", abNote: true);
                iDoneCount = -100;
            }
            int num6 = iQAFCount - 1;
            for (int iLoop = 0; iLoop <= num6; iLoop++)
            {
                //corePage tPage = new corePage();
                string sQaf = "";//mGetAttachmentQueueKey(tPage, iLoop);
                string sStat = tPNL[iLoop].sQafStatus;
                if (tPNL[iLoop].bRelease & !tPNL[iLoop].bDeleteOnSave)
                {
                    if (tUserData.bIsCost & (Operators.CompareString(sStat, "010100QF", TextCompare: false) <= 0) & (Operators.CompareString(sIndTask, "4", TextCompare: false) == 0) & (Operators.CompareString(sMode, "Released", TextCompare: false) == 0))
                    {
                        if (!tRouting.mHasCurrentTask("24", "AA", "AA", "*", Strings.Right("**********" + sQaf, 10)))
                        {
                            tRouting.mCreateTask("24", "AA", "AA", "", "", abNote: false, Strings.Right("**********" + sQaf, 10));
                        }
                        else
                        {
                            tRouting.mPNLRGTaskComplete("24", "CA", "CA", sCostEstimator, Strings.Right("**********" + sQaf, 10), sMode);
                            iDoneCount = -100;
                        }
                        tRouting.mCreateTask("24", "DA", "DA", sPDM_aka_ProgramManager, modTools.mDateScreenToCygnet(sCustomerQuoteDueDate), abNote: false, Strings.Right("**********" + sQaf, 10));
                        if (!tRouting.mHasTask("24", "N1", Strings.Right("**********" + sQaf, 10)))
                        {
                            mSendNotification(sOriginator, sPDM_aka_ProgramManager, "24", "N1", "N1", sPDM_aka_ProgramManager, modTools.mDateScreenToCygnet(sQuoteRespDueDate), abNote: true, Strings.Right("**********" + sQaf, 10));
                            mSendNotification(sOriginator, sPGM_aka_CoC, "24", "N1", "N1", sPGM_aka_CoC, modTools.mDateScreenToCygnet(sQuoteRespDueDate), abNote: true, Strings.Right("**********" + sQaf, 10));
                            mSendNotification(sOriginator, mQafSalesActionGuy(), "24", "N1", "N1", mQafSalesActionGuy(), modTools.mDateScreenToCygnet(sQuoteRespDueDate), abNote: true, Strings.Right("**********" + sQaf, 10));
                        }
                        z_AdvanceQaf(iLoop, Strings.Right("**********" + sQaf, 10), "010200QF");
                    }
                    if ((Operators.CompareString(sStat, "010200QF", TextCompare: false) == 0) & (Operators.CompareString(sMode, "Released", TextCompare: false) != 0))
                    {
                        if (tUserData.bIsPDM)
                        {
                            tRouting.mPNLRGTaskComplete("24", "DA", "DA", "*", Strings.Right("**********" + sQaf, 10), sMode);
                            if (Operators.CompareString(sMode, "Rejected", TextCompare: false) != 0)
                            {
                                tRouting.mCreateTask("24", "DA", "DA", modUserProf.mUPGetManager("CMGR"), modTools.mDateScreenToCygnet(sCustomerQuoteDueDate), abNote: false, Strings.Right("**********" + sQaf, 10));
                            }
                        }
                        else
                        {
                            if (!tUserData.bIsCmgr)
                            {
                                return;
                            }
                            tRouting.mPNLRGTaskComplete("24", "DA", "DA", "*", Strings.Right("**********" + sQaf, 10), sMode);
                        }
                    }
                    if (Operators.CompareString(sMode, "Rejected", TextCompare: false) == 0)
                    {
                        z_UpdateStatus("040100FR");
                        z_AdvanceQaf(iLoop, Strings.Right("**********" + sQaf, 10), "010100QF");
                        tRouting.mPNLTaskUpdate(sQaf);
                        tRouting.mCreateTask("24", "CA", "CA", sCostEstimator, modTools.mDateScreenToCygnet(sCustomerQuoteDueDate), abNote: false, sQaf);
                    }
                    else if (Operators.CompareString(sMode, "Released", TextCompare: false) != 0)
                    {
                        if ((Operators.CompareString(sStat, "010200QF", TextCompare: false) == 0) & tUserData.bIsCmgr)
                        {
                            sStat = "010300QF";
                        }
                        if (Operators.CompareString(sStat, "010300QF", TextCompare: false) == 0 && (!tRouting.mHasCurrentTask("24", "EA", "EA", "*", Strings.Right("**********" + sQaf, 10)) & !tRouting.mHasCurrentTask("24", "EB", "EB", "*", Strings.Right("**********" + sQaf, 10))))
                        {
                            z_AdvanceQaf(iLoop, Strings.Right("**********" + sQaf, 10), "010400QF");
                            retrievePNLGridData();
                            iDoneCount++;
                        }
                    }
                }
                if (Operators.CompareString(tPNL[iLoop].sOldQafKey, "", TextCompare: false) != 0)
                {
                    tRouting.mDeleteAllTasksForDrawing(Strings.Right("**********" + tPNL[iLoop].sOldQafKey, 10));
                    tRouting.mDeleteAllTasksForDrawing(Strings.Right("**********" + tPNL[iLoop].sQAFKey, 10));
                }
                if ((Operators.CompareString(sStat, "010400QF", TextCompare: false) == 0) | ((Operators.CompareString(sStatus, "040150FR", TextCompare: false) == 0) & (Operators.CompareString(sStat, "010100QF", TextCompare: false) == 0)))
                {
                    iDoneCount++;
                }
            }
            if (iDoneCount == iQAFCount)
            {
                mAdvanceTo040200();
            }
        }
    }

    public void mAdvanceTo040200()
    {
        z_UpdateStatus("040200FR");
        if (Conversion.Val(sQuoteType) == 2.0)
        {
            tRouting.mCreateTask("28", "BA", "BA", sOriginator);
        }
        else
        {
            tRouting.mCreateTask("28", "MA", "MA", sAccountManager);
        }
    }

    public void mProcess020x00(ref string sMode)
    {
        if (Operators.CompareString(sMode, "Rejected", TextCompare: false) == 0)
        {
            if (!(tUserData.bIsPgm & ((Operators.CompareString(sPGM_aka_CoC, base.UserId, TextCompare: false) == 0) | (Operators.CompareString(sIndTask, "2", TextCompare: false) == 0))))
            {
                return;
            }
            tRouting.mCloseTask("01", "GA", "GA", "*", "Rejected");
            z_UpdateStatus("020500FR");
            tRouting.mCreateTask("01", "IA", "IA", sOriginator);
            if (!bOrigIsSDir)
            {
                tRouting.mCreateTask("01", "HA", "HA", sSalesAccountDirector, "", abNote: true);
            }
            if (!bOrigIsAMgr)
            {
                tRouting.mCreateTask("01", "HA", "HA", sAccountManager, "", abNote: true);
            }
            if (Operators.CompareString(sOriginator, sPDM_aka_ProgramManager, TextCompare: false) != 0)
            {
                tRouting.mCreateTask("01", "HA", "HA", sPDM_aka_ProgramManager, "", abNote: true);
            }
            if (tRouting.mHasCurrentTask("10", "BA", "BA", "*"))
            {
                tRouting.mDeleteTask("10", "BA", "BA", "*");
            }
            if (tRouting.mHasCurrentTask("07", "BA", "BA", "*"))
            {
                if (Operators.CompareString(sOriginator, tUserData.sManagerBids, TextCompare: false) != 0)
                {
                    tRouting.mCreateTask("01", "HA", "HA", tUserData.sManagerBids, "", abNote: true);
                }
                tRouting.mDeleteTask("07", "BA", "BA", "*");
            }
            else
            {
                tRouting.mCreateTask("01", "HA", "HA", sCostEstimator, "", abNote: true);
            }
            return;
        }
        bool bHas10BA = tRouting.mHasCurrentTask("10", "BA", "BA", "*");
        bool bHas01GA = tRouting.mHasCurrentTask("01", "GA", "GA", "*");
        bool bHas07BA = tRouting.mHasCurrentTask("07", "BA", "BA", "*");
        bool bHad01GA = bHas01GA;
        bool bHad10BA = bHas10BA;
        bool bAdvance = false;
        bool bJustRelased07BA = false;
        if (Operators.CompareString(sMode, "Approved", TextCompare: false) == 0)
        {
            if ((Operators.CompareString(base.UserId, sPGM_aka_CoC, TextCompare: false) == 0) | (Operators.CompareString(sIndTask, "2", TextCompare: false) == 0))
            {
                tRouting.mCloseTask("01", "GA", "GA", "*", "Approved");
                bHas01GA = false;
                bAdvance = true;
                z_goGDPEPPhaseI();
            }
            if (tUserData.bIsPetm & bDualRelease)
            {
                sMode = "Released";
            }
        }
        if (!bDualRelease)
        {
            return;
        }
        if (Operators.CompareString(sMode, "Released", TextCompare: false) == 0 && Operators.CompareString(base.UserId, tUserData.sManagerBids, TextCompare: false) == 0)
        {
            tRouting.mCloseTask("07", "BA", "BA", "*", "Released");
            bHas07BA = false;
            bAdvance = true;
            bJustRelased07BA = true;
            if (Operators.CompareString(sRevision, "0001", TextCompare: false) == 0)
            {
                mSendNotification(sOriginator, sCostEstimator, "07", "CA", "CA", sCostEstimator, "", abNote: true);
            }
        }
        if (((!tUserData.bIsCmgr & ((Operators.CompareString(base.UserId, sPDM_aka_ProgramManager, TextCompare: false) == 0) | bDualRelease)) && !bJustRelased07BA) & (Operators.CompareString(sMode, "Released", TextCompare: false) == 0))
        {
            tRouting.mCloseTask("10", "BA", "BA", "*", Conversions.ToString(Interaction.IIf(Operators.CompareString(sWorkProceedOkInd, "1", TextCompare: false) != 0, "Rejected", "Released")));
            bHas10BA = false;
            bAdvance = true;
            if (Operators.CompareString(sWorkProceedOkInd, "1", TextCompare: false) != 0)
            {
                z_UpdateStatus("030100FR");
                tRouting.mCreateTask("10", "DA", "DA", sOriginator);
                return;
            }
            if (Operators.CompareString(sTimeFrameOkInd, "1", TextCompare: false) != 0)
            {
                tRouting.mCreateTask("10", "CA", "CA", sOriginator, "", abNote: true);
            }
        }
        if (bAdvance)
        {
            if (!bHas10BA && !bHas01GA && (bHad10BA || bHad01GA) && bHas07BA)
            {
                mSendNotification(sOriginator, sEngineerAssigned, "12", "CA", "CA", sEngineerAssigned, "", abNote: true);
                z_UpdateStatus(Conversions.ToString(Interaction.IIf(Operators.CompareString(sStatus, "020100FR", TextCompare: false) == 0, "020300FR", "020400FR")));
            }
            if (!bHas01GA && !bHas07BA && !bHas10BA && ((Operators.CompareString(sMode, "Released", TextCompare: false) == 0) | (Operators.CompareString(sMode, "Approved", TextCompare: false) == 0)))
            {
                mFranLeavePetm();
            }
        }
    }

    public void mNotifyManufacturingSite()
    {
        if (Operators.CompareString(sManufacturingSite, "", TextCompare: false) == 0 || Operators.CompareString(sStatus, "010150FR", TextCompare: false) <= 0 || Operators.CompareString(sStatus, "040200FR", TextCompare: false) >= 0)
        {
            return;
        }
        if ((Conversion.Val(sRevision) == 1.0) | (Operators.CompareString(sModNeedsCostInd, "1", TextCompare: false) == 0))
        {
        }
        if (tRouting.mHasTask("01", "EM") && Operators.CompareString(sManufacturingSiteDbs, sManufacturingSite, TextCompare: false) == 0)
        {
            return;
        }
        tRouting.mDeleteCurrentTask("01", "EM", "EM", "*");
        string sList = modTRS.mTRSGetSingleDesc("FRANMFG" + sEngineeringSite, sManufacturingSite);
        if (Operators.CompareString(sList, sManufacturingSite, TextCompare: false) == 0)
        {
            sList = "";
        }
        if (Operators.CompareString(sList, "", TextCompare: false) != 0)
        {
            string[] tArray = Strings.Split(sList, ",");
            int num = Information.UBound(tArray);
            for (int iLoop = 0; iLoop <= num; iLoop = checked(iLoop + 1))
            {
                tArray[iLoop] = Strings.Trim(tArray[iLoop]);
                if (Operators.CompareString(tArray[iLoop], "", TextCompare: false) != 0)
                {
                    if (tRouting.mHasTask("01", "EA", "", tArray[iLoop]))
                    {
                        return;
                    }
                    tRouting.mCreateTask("01", "EM", "EM", tArray[iLoop], "", abNote: true);
                }
            }
            tRouting.mCreateTask("01", "EM", "EM", sPDM_aka_ProgramManager, "", abNote: true);
        }
        sManufacturingSiteDbs = sManufacturingSite;
    }

    public void mFranLeaveSales(string asToPetmStatus, string asToMbneStatus, bool abNotifyCost, bool abNotifyCQRInitiated)
    {
        int iRevLevel = checked((int)Math.Round(Conversion.Val(sRevision)));
        if (abNotifyCQRInitiated)
        {
            NotifyUserCQRInitiated(abNotifyCost);
        }
        sCQRIssueDate = modTools.mDateScreenToCygnet(Conversions.ToString(DateAndTime.Now));
        int bNewTask = default(int);
        if (Operators.CompareString(sGateway, "1", TextCompare: false) != 0 && !tRouting.mHasTask("01", "GA"))
        {
            tRouting.mCreateTask("01", "GA", "GA", sPGM_aka_CoC);
            bNewTask = -1;
        }
        if (!tRouting.mHasTask("10", "BA"))
        {
            tRouting.mCreateTask("10", "BA", "BA", sEngineeringManager);
            bNewTask = -1;
        }
        sPIMSite = sEngineeringSite;
        if (Conversion.Val(sRevision) == 1.0)
        {
            if (Operators.CompareString(sEngineeringSite, "3", TextCompare: false) == 0)
            {
                tRouting.mCreateTask("08", "BA", "BA", "AWAN");
            }
            else
            {
                tRouting.mCreateTask("08", "BA", "BA", "ASEIBERT");
            }
        }
        if (iRevLevel == 1)
        {
            if (tRouting.mHasCurrentTask("01", "GA", "GA", "*") | tRouting.mHasCurrentTask("10", "BA", "BA", "*"))
            {
                z_UpdateStatus(asToPetmStatus);
            }
            else
            {
                z_UpdateStatus(asToMbneStatus);
            }
        }
        else
        {
            z_UpdateStatus(asToPetmStatus);
        }
        if (bNewTask == 0)
        {
            mFranLeavePetm();
        }
    }

    public void NotifyUserCQRInitiated(bool abNotifyCost)
    {
        string[] sUser = null;
        checked
        {
            int iRevLevel = (int)Math.Round(Conversion.Val(sRevision));
            int iUserCount = default(int);
            if (abNotifyCost && (((iRevLevel == 1) & (Operators.CompareString(sOriginator, tUserData.sManagerBids, TextCompare: false) == 0)) | ((iRevLevel > 1) & (Operators.CompareString(sOriginator, sCostEstimator, TextCompare: false) != 0))))
            {
                tRouting.AddUserToArray(sCostEstimator, ref sUser, ref iUserCount);
            }
            string sCode = Conversions.ToString(Interaction.IIf(Conversion.Val(sQuoteType) == 2.0, "FA", "EA"));
            tRouting.AddRoleToArray("FR00", ref sUser, ref iUserCount);
            int num = iUserCount - 1;
            for (int iLoop = 0; iLoop <= num; iLoop++)
            {
                tRouting.mCreateTask("01", sCode, sCode, sUser[iLoop], "", abNote: true);
            }
        }
    }

    public void mFranIssueHardCodes(string asCode1, string asCode2)
    {
        if (!(tRouting.mHasTask(asCode1, asCode2) & (Operators.CompareString(sManufacturingSite, sManufacturingSiteDbs, TextCompare: false) == 0)))
        {
            string sList = modTRS.mTRSGetSingleDesc("FRANMFG" + sEngineeringSite, sManufacturingSite);
            z_NotifyComma(sList, asCode1, asCode2);
            if (Operators.CompareString(asCode2, "EA", TextCompare: false) != 0)
            {
                tRouting.mCreateTask(asCode1, asCode2, asCode2, tUserData.sManagerCmgr, "", abNote: true);
            }
            sManufacturingSiteDbs = sManufacturingSite;
        }
    }

    public void mFranLeavePetm()
    {
        tRouting.mCloseTask("10", "BA", "BA", "*", "Released");
        if ((Operators.CompareString(Strings.UCase(sPlatform), "NS", TextCompare: false) == 0) | (Operators.CompareString(Strings.UCase(sPlatform), "GS", TextCompare: false) == 0) | (Operators.CompareString(Strings.UCase(sPlatform), "NS/GS", TextCompare: false) == 0))
        {
            tRouting.mCreateTask("13", "BA", "BA", "JDAVIS", "", abNote: true);
        }
        if (Operators.CompareString(sPrototypeInd, "1", TextCompare: false) == 0)
        {
            tRouting.mCreateTask("13", "BA", "BA", "PHANSON", "", abNote: true);
        }
        if (Operators.CompareString(sElecInputReqdInd[1], "1", TextCompare: false) == 0)
        {
            tRouting.mCreateTask("13", "BG", "BG", sElecPRD[1], "", abNote: true);
        }
        if (mIsDesignProposalOnly())
        {
            z_UpdateStatus("030400FR");
            tRouting.mCreateTask("13", "DA", "DA", sEngineerAssigned, modTools.mDateScreenToCygnet(sCustomerQuoteDueDate));
        }
        else
        {
            z_UpdateStatus("030200FR");
            tRouting.mCreateTask("13", "CA", "CA", sEngineerAssigned, modTools.mDateScreenToCygnet(sDueDateFromEngineering));
        }
        z_LeaveOriginatorNotification();
    }

    public bool mCheckRelease(corePage atPage, string sLink)
    {
        checked
        {
            if ((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) & tUserData.bIsCost & (Operators.CompareString(sIndTask, "4", TextCompare: false) != 0))
            {
                int num = tRouting.mCount() - 1;
                for (int iLoop = 0; iLoop <= num; iLoop++)
                {
                    clsRouting.T_ROUTING tTask = tRouting.mGetTaskInfo(iLoop);
                    if ((!tTask.bNote & !tTask.bDoneInd & (Operators.CompareString(tTask.sAssigned, "", TextCompare: false) != 0)) && !(((Operators.CompareString(tTask.sTask, "22", TextCompare: false) == 0) & (Operators.CompareString(tTask.sTaskAction, "BA", TextCompare: false) == 0)) | ((Operators.CompareString(tTask.sTask, "22", TextCompare: false) == 0) & (Operators.CompareString(tTask.sTaskAction, "CA", TextCompare: false) == 0)) | (Operators.CompareString(tTask.sTask, "24", TextCompare: false) == 0) | (Operators.CompareString(tTask.sTask, "41", TextCompare: false) == 0) | (Operators.CompareString(tTask.sTask, "42", TextCompare: false) == 0)) && Operators.CompareString(sLink, "rejectLink", TextCompare: false) != 0)
                    {
                        //HttpContext.Current.Response.Write("<script>alert('The Quote Response can\\'t be released until the other outstanding actions have been released.');</script>");
                        return false;
                    }
                }
            }
            //if ((Operators.CompareString(sLink, "releaseGDPEPLink", TextCompare: false) != 0) & (Operators.CompareString(atPage.Request["ReopenPNL"], "", TextCompare: false) == 0))
            //{
            //    //if ((Operators.CompareString(sStatus, "040150FR", TextCompare: false) > 0) & tUserData.bIsCmgr & (Operators.CompareString(sMode, "Released", TextCompare: false) == 0))
            //    //{
            //    //    atPage.Response.Write("<Script language='VBScript'>\r\n");
            //    //    atPage.Response.Write("   answer = MsgBox(\"Are you sure you want to send this CQR back to Bids & Estimating?\", vbYesNo)\r\n");
            //    //    atPage.Response.Write("</Script>\r\n");
            //    //    atPage.Response.Write("<Script language='JavaScript'>\r\n");
            //    //    atPage.Response.Write("   if (answer == 6){\r\n");
            //    //    atPage.Response.Write("       document.location='Routing.aspx?QueueKey=" + Conversions.ToString(iQueueKey) + "&DualRelease=" + atPage.Request["DualRelease"] + "&Link=" + atPage.Request["__EVENTTARGET"] + "&ReopenPNL=1';}");
            //    //    atPage.Response.Write("   else if (answer == 7){\r\n");
            //    //    atPage.Response.Write("       document.location='Routing.aspx?QueueKey=" + Conversions.ToString(iQueueKey) + "&DualRelease=" + atPage.Request["DualRelease"] + "&Link=" + atPage.Request["__EVENTTARGET"] + "';}");
            //    //    atPage.Response.Write("   else{\r\n");
            //    //    atPage.Response.Write("       document.location='Routing.aspx?QueueKey=" + Conversions.ToString(iQueueKey) + "&DualRelease=" + atPage.Request["DualRelease"] + "&Link=" + atPage.Request["__EVENTTARGET"] + "';}");
            //    //    atPage.Response.Write("</Script>\r\n");
            //    //    return false;
            //    //}
            //    return true;
            //}
            return true;
        }
    }

    public void CheckSaveAdvanceGDPEP(bool bRelease)
    {
        z_AdvanceStatusGDPEP(bRelease);
        if (Operators.CompareString(sStatus, "040150FR", TextCompare: false) != 0 && z_IsGDPEPUser())
        {
            bRelease = false;
        }
        mRerouteTasks();
        if ((Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) == 0) & (Operators.CompareString(sGDPEPPhase1Required, sGDPEPPhase1RequiredDbs, TextCompare: false) != 0))
        {
            if (Conversion.Val(sRevision) > Conversion.Val(modTRS.mTRSGetSingleDesc("CQRCMGR", "ACTION")))
            {
                tRouting.mCreateTask("41", "AA", "AA", tUserData.sManagerCmgr);
            }
        }
        else if (((Operators.CompareString(sGDPEPPhase1Required, "2", TextCompare: false) == 0) & (Operators.CompareString(sGDPEPPhase2Required, "1", TextCompare: false) == 0) & (Operators.CompareString(sGDPEPPhase2Required, sGDPEPPhase2RequiredDbs, TextCompare: false) != 0)) && !tRouting.mHasCurrentTask("42", "AA", "AA", "*"))
        {
            tRouting.mCreateTask("42", "AA", "AA", tUserData.sManagerCmgr);
        }
    }

    public void z_goGDPEPPhaseI()
    {
        if (Operators.CompareString(sGDPEPPhase1Required, "1", TextCompare: false) == 0)
        {
            z_AdvanceGDPEPGateway(1);
            if (!tRouting.mHasCurrentTask("41", "AA", "AA", "*"))
            {
            }
        }
    }

    public bool mQafCheckRelease(corePage atPage)
    {
        int releaseCount = 0;
        bool allDelete = true;
        bool mQafCheckRelease = true;
        if (iQAFCount != 0 && !((Operators.CompareString(sStatus, "040150FR", TextCompare: false) != 0) & (Operators.CompareString(sStatus, "040100FR", TextCompare: false) != 0)) && !((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) & !tUserData.bIsCost))
        {
            checked
            {
                if (iQAFCount > 0)
                {
                    int num = iQAFCount - 1;
                    for (int iLoop = 0; iLoop <= num; iLoop++)
                    {
                        if (!tPNL[iLoop].bDeleteOnSave)
                        {
                            allDelete = false;
                        }
                        if (Operators.CompareString(tPNL[iLoop].bChecked, "on", TextCompare: false) == 0)
                        {
                            tPNL[iLoop].bRelease = true;
                            releaseCount++;
                            continue;
                        }
                        if (Operators.CompareString(tPNL[iLoop].sQafStatus, "010100QF", TextCompare: false) > 0)
                        {
                            releaseCount++;
                        }
                        tPNL[iLoop].bRelease = false;
                    }
                }
            }
            if (releaseCount == 0 && !allDelete)
            {
                string sTemp = "";
                switch (sMode)
                {
                    case "Released":
                        sTemp = Conversions.ToString(Interaction.IIf(Operators.CompareString(sStatus, "010100QF", TextCompare: false) <= 0, "Release", ""));
                        break;
                    case "Approved":
                        sTemp = Conversions.ToString(Interaction.IIf(Operators.CompareString(sStatus, "010100QF", TextCompare: false) > 0, "Approve", ""));
                        break;
                    case "Rejected":
                        sTemp = Conversions.ToString(Interaction.IIf(Operators.CompareString(sStatus, "010100QF", TextCompare: false) > 0, "Reject", ""));
                        break;
                }
                if ((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) & (Operators.CompareString(sQRCompletedAndSent, "1", TextCompare: false) == 0))
                {
                    //if (Conversions.ToDouble(atPage.Request["ReleaseNoPNL"]) == 1.0)
                    //{
                    //    goto IL_02d1;
                    //}
                    //if ((Operators.CompareString(atPage.Request["ReleaseNoPNL"], "", TextCompare: false) == 0) & (Operators.CompareString(sMode, "Released", TextCompare: false) == 0))
                    //{
                    //    AlertNoPNLBox(atPage, "You have chosen not to release any P&Ls.  Releasing the Quote Response will prevent P&Ls from being released in the future.  Are you sure you want to advance the folder without releasing any P&Ls?");
                    //}
                }
                else
                {
                    modCQR.AlertMessage(atPage, "Please select 1 or more P&L(s) to " + sTemp, "CQR");
                }
                mQafCheckRelease = false;
            }
        }
        goto IL_02d1;
    IL_02d1:
        return mQafCheckRelease;
    }

    public bool mQafTaskForUser(string asKey)
    {
        asKey = Strings.Right("**********" + asKey, 10);
        bool mQafTaskForUser = default(bool);
        if (tUserData.bIsPDM && z_QafTask(sPDM_aka_ProgramManager, asKey, "DA"))
        {
            mQafTaskForUser = true;
        }
        if (tUserData.bIsCmgr && z_QafTask(tUserData.sManagerCmgr, asKey, "DA"))
        {
            mQafTaskForUser = true;
        }
        if (tUserData.bIsBum && z_QafTask("*", asKey, "EB"))
        {
            mQafTaskForUser = true;
        }
        if (tUserData.bIsFin && z_QafTask(tUserData.sManagerFin, asKey, "EA"))
        {
            mQafTaskForUser = true;
        }
        if (tUserData.bIsCmgr && z_QafTask(tUserData.sManagerCmgr, asKey, "EA"))
        {
            mQafTaskForUser = true;
        }
        if (tUserData.bIsPres && z_QafTask(tUserData.sManagerPres, asKey, "EA"))
        {
            mQafTaskForUser = true;
        }
        if (tUserData.bIsPgm && z_QafTask(sPGM_aka_CoC, asKey, "DA"))
        {
            mQafTaskForUser = true;
        }
        if (tUserData.bIsBeb && z_QafTask(tUserData.sManagerBeb, asKey, "DA"))
        {
            mQafTaskForUser = true;
        }
        if ((tUserData.bIsSDir | bUserIsOrig | (bOrigIsAMgr & tUserData.bIsAMgr)) && z_QafTask(mQafSalesActionGuy(), asKey, "DA"))
        {
            mQafTaskForUser = true;
        }
        checked
        {
            if (tUserData.bIsCost)
            {
                if (z_QafTask(sCostEstimator, asKey, "CA"))
                {
                    mQafTaskForUser = true;
                }
                if ((Operators.CompareString(sStatus, "040100FR", TextCompare: false) == 0) | (Operators.CompareString(sStatus, "040150FR", TextCompare: false) == 0))
                {
                    int num = iQAFCount - 1;
                    for (int iLoop = 0; iLoop <= num && !Information.IsNothing(tPNL[iLoop]); iLoop++)
                    {
                        clsPNL clsPNL = tPNL[iLoop];
                        if ((Conversion.Val(clsPNL.sQAFKey) == Conversion.Val(asKey)) & (Operators.CompareString(clsPNL.sQafStatus, "010100QF", TextCompare: false) <= 0))
                        {
                            mQafTaskForUser = true;
                        }
                        clsPNL = null;
                    }
                }
            }
            return mQafTaskForUser;
        }
    }

    private bool z_QafTask(string asUser, string asKey, string asRtgCode)
    {
        return tRouting.mHasCurrentTask("24", asRtgCode, asRtgCode, asUser, asKey);
    }

    public bool z_QafCostEdit()
    {
        if (bSuperUser || (tUserData.bIsCost && mHasQRCostTask()))
        {
            return true;
        }
        bool z_QafCostEdit = default(bool);
        return z_QafCostEdit;
    }

    public string z_FromSheetToStruct_Old(clsCQR tCQR, string sPath, long aiIndex = -1L, long alRow = -1L)
    {
        int try0001_dispatch = -1;
        int num2 = default(int);
        double dTotal = default(double);
        double dIRR = default(double);
        int num = default(int);
        string z_FromSheetToStruct_Old;
        while (true)
        {
            try
            {
                /*Note: ILSpy has introduced the following switch to emulate a goto from catch-block to try-block*/
                ;
                checked
                {
                    switch (try0001_dispatch)
                    {
                        default:
                            {
                                string sTitle = "";
                                object tExcel = null;
                                ProjectData.ClearProjectError();
                                num2 = 2;
                                z_FromSheetToStruct_Old = "";
                                //corePage atPage = new corePage();
                                if (tExcel == null)
                                {
                                    //if (atPage.Session["ExcelObject"] == null)
                                    //{
                                    //    atPage.Session["ExcelObject"] = RuntimeHelpers.GetObjectValue(atPage.Server.CreateObject("Excel.Application"));
                                    //}
                                    //tExcel = RuntimeHelpers.GetObjectValue(atPage.Session["ExcelObject"]);
                                    if (tExcel == null)
                                    {
                                        goto end_IL_0001;
                                    }
                                }
                                clsPNL atPNL = new clsPNL();
                                clsCQR clsCQR2 = tCQR;
                                atPNL.sQafName = sTitle;
                                object[] array;
                                bool[] array2;
                                object obj = NewLateBinding.LateGet(NewLateBinding.LateGet(tExcel, null, "Workbooks", new object[0], null, null, null), null, "Open", array = new object[7]
                                {
                            sPath,
                            Missing.Value,
                            true,
                            Missing.Value,
                            Missing.Value,
                            Missing.Value,
                            true
                                }, null, null, array2 = new bool[7] { true, false, false, false, false, false, false });
                                if (array2[0])
                                {
                                    sPath = (string)Conversions.ChangeType(RuntimeHelpers.GetObjectValue(array[0]), typeof(string));
                                }
                                Workbook tBook = (Workbook)obj;
                                Worksheet tWorkSheet = (Worksheet)NewLateBinding.LateGet(tExcel, null, "Worksheets", new object[1] { "Program Summary" }, null, null, null);
                                atPNL.sQafName = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[5, "C"], null, "Text", new object[0], null, null, null)));
                                atPNL.sQafStatus = "010100QF";
                                atPNL.sStatusDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
                                atPNL.sTargetSellPrice = "";
                                if (Conversion.Val(RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(tWorkSheet.Cells[15, "E"], null, "Value", new object[0], null, null, null))) != 0.0)
                                {
                                    atPNL.sStartingSellPrice = "---";
                                    atPNL.sTotalVolume = "---";
                                }
                                else
                                {
                                    atPNL.sStartingSellPrice = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[15, "C"], null, "Value", new object[0], null, null, null)));
                                    atPNL.sTotalVolume = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[26, "C"], null, "Value", new object[0], null, null, null)));
                                }
                                atPNL.sAmortizedTools = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[38, "W"], null, "Value", new object[0], null, null, null)));
                                atPNL.sTotalSales = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[27, "W"], null, "Value", new object[0], null, null, null)));
                                atPNL.sTRWPaidTools = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[31, "W"], null, "Value", new object[0], null, null, null)));
                                atPNL.sTRWCapital = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[32, "W"], null, "Value", new object[0], null, null, null)));
                                atPNL.sCustomerPaidTools = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[36, "W"], null, "Value", new object[0], null, null, null)));
                                atPNL.sPATPercent = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[29, "W"], null, "Value", new object[0], null, null, null)));
                                tWorkSheet = (Worksheet)NewLateBinding.LateGet(tExcel, null, "Worksheets", new object[1] { "P&L (1)" }, null, null, null);
                                atPNL.sPATDollars = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[50, "AO"], null, "Value", new object[0], null, null, null)));
                                tWorkSheet = (Worksheet)NewLateBinding.LateGet(tExcel, null, "Worksheets", new object[1] { "Program Summary" }, null, null, null);
                                atPNL.sIRRPercent = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[9, "W"], null, "Value", new object[0], null, null, null)));
                                atPNL.sFOBPoint = modTRS.mTRSGetSingleDesc("FOBPOINT", sQRFOB);
                                atPNL.tPNLLineItem = new PNLLineItem[0];
                                int iLast = 69;
                                tWorkSheet = (Worksheet)NewLateBinding.LateGet(tExcel, null, "Worksheets", new object[1] { "Input Sheet (1)" }, null, null, null);
                                int iLoop = 69;
                                do
                                {
                                    string currChar = Conversions.ToString(Strings.Chr(iLoop));
                                    string currTotal = "";
                                    if (NewLateBinding.LateGet(tWorkSheet.Cells[18, currChar], null, "Value", new object[0], null, null, null) != null)
                                    {
                                        currTotal = Strings.Trim(NewLateBinding.LateGet(tWorkSheet.Cells[18, currChar], null, "Value", new object[0], null, null, null).ToString());
                                    }
                                    double dTemp = Conversions.ToDouble(Interaction.IIf(Operators.CompareString(currTotal, "", TextCompare: false) == 0, 0, currTotal));
                                    if (dTemp != 0.0)
                                    {
                                        iLast = iLoop;
                                    }
                                    dTotal += dTemp;
                                    iLoop++;
                                }
                                while (iLoop <= 76);
                                tWorkSheet = (Worksheet)NewLateBinding.LateGet(tExcel, null, "Worksheets", new object[1] { "Summary Cash Flow" }, null, null, null);
                                iLoop = 66;
                                do
                                {
                                    dIRR += Conversions.ToDouble(Interaction.IIf(Operators.CompareString(Strings.Trim(Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[149, Strings.Chr(iLoop)], null, "Value", new object[0], null, null, null)))), "", TextCompare: false) == 0, 0, Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[149, Strings.Chr(iLoop)], null, "Value", new object[0], null, null, null)))));
                                    iLoop += 2;
                                }
                                while (iLoop <= 84);
                                atPNL.sIRRDollars = Conversions.ToString(dIRR);
                                atPNL.sAvgAnnualSPReduction = Conversions.ToString(dTotal / (double)(iLast - 69 + 1));
                                tWorkSheet = (Worksheet)NewLateBinding.LateGet(tExcel, null, "Worksheets", new object[1] { "Input Sheet (1)" }, null, null, null);
                                atPNL.sSGA = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[85, "D"], null, "Value", new object[0], null, null, null)));
                                tWorkSheet = (Worksheet)NewLateBinding.LateGet(tExcel, null, "Worksheets", new object[1] { "Summary P&L" }, null, null, null);
                                atPNL.sMaterialDollars = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[39, "AF"], null, "Value", new object[0], null, null, null)));
                                atPNL.sMaterialPercent = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[39, "AG"], null, "Value", new object[0], null, null, null)));
                                atPNL.sEngDollars = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[141, "AF"], null, "Value", new object[0], null, null, null)));
                                atPNL.sEngPercent = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[141, "AG"], null, "Value", new object[0], null, null, null)));
                                tWorkSheet = (Worksheet)NewLateBinding.LateGet(tExcel, null, "Worksheets", new object[1] { "Program Summary" }, null, null, null);
                                atPNL.sPayback = Strings.Trim(Conversions.ToString(NewLateBinding.LateGet(tWorkSheet.Cells[11, "W"], null, "Value", new object[0], null, null, null)));
                                if (aiIndex == -1)
                                {
                                    atPNL.sQAFKey = clsCQR2.tPNLAttach.tTab.tAttInfo[clsCQR2.tPNLAttach.tTab.iAttCount - 1].sQueueKey;
                                }
                                else
                                {
                                    atPNL.sOldQafKey = tCQR.tPNL[(int)aiIndex].sQAFKey;
                                    atPNL.sQAFKey = clsCQR2.tPNLAttach.tTab.tAttInfo[(int)aiIndex].sQueueKey;
                                    atPNL.bReplaced = true;
                                    atPNL.iReplacedIndex = (int)aiIndex;
                                }
                                if (aiIndex == -1)
                                {
                                    mQafItemAdd(atPNL);
                                }
                                else
                                {
                                    tCQR.tPNL[(int)aiIndex] = atPNL;
                                    tCQR.tPNL[(int)aiIndex].bDeleteOnSave = true;
                                    tCQR.tPNL[(int)aiIndex].bCreateOnSave = true;
                                }
                                tBook.Close(false, RuntimeHelpers.GetObjectValue(Missing.Value), RuntimeHelpers.GetObjectValue(Missing.Value));
                                clsCQR2 = null;
                                tWorkSheet = null;
                                tExcel = null;
                                NAR(tWorkSheet);
                                NAR(RuntimeHelpers.GetObjectValue(tExcel));
                                GC.Collect();
                                goto end_IL_0001;
                            }
                        case 2665:
                            num = -1;
                            switch (num2)
                            {
                                case 2:
                                    z_FromSheetToStruct_Old = "Error: " + Information.Err().Description;
                                    z_FromSheetToStruct_Old = Strings.Replace(z_FromSheetToStruct_Old, "\\", "/");
                                    z_FromSheetToStruct_Old = Strings.Replace(z_FromSheetToStruct_Old, "\r\n", "\\n");
                                    z_FromSheetToStruct_Old = Strings.Replace(z_FromSheetToStruct_Old, "\n", "\\n");
                                    goto end_IL_0001;
                            }
                            break;
                    }
                }
            }
            catch(Exception e) { Debug.WriteLine(e); }
            //catch (object obj2) when (obj2 is Exception && num2 != 0 && num == 0)
            //{
            //    ProjectData.SetProjectError((Exception)obj2);
            //    try0001_dispatch = 2665;
            //    continue;
            //}
            //throw ProjectData.CreateProjectError(-2146828237);
            //continue;
        end_IL_0001:
            break;
        }
        if (num != 0)
        {
            ProjectData.ClearProjectError();
        }
        //return z_FromSheetToStruct_Old;
        return "";
    }

    public string z_FromSheetToStruct(clsCQR tCQR, string sPath, long aiIndex = -1L, long alRow = -1L)
    {
        string z_FromSheetToStruct = "";
        clsPNL atPNL = new clsPNL();
        atPNL.bIsNewFormat = true;
        clsCQR clsCQR2 = tCQR;
        checked
        {
            atPNL.sQafName = Strings.Mid(sPath, Strings.InStrRev(sPath, "\\") + 1);
            atPNL.sQafName = Strings.Mid(atPNL.sQafName, Strings.InStr(atPNL.sQafName, "_") + 1);
            atPNL.sQafStatus = "010100QF";
            atPNL.sStatusDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
            atPNL.sTargetSellPrice = "";
            atPNL.sTotalVolume = "???";
            atPNL.sPATDollars = "???";
            atPNL.tPNLLineItem = new PNLLineItem[0];
            atPNL.sIRRDollars = "???";
            atPNL.sMaterialDollars = "???";
            if (aiIndex == -1)
            {
                atPNL.sQAFKey = clsCQR2.tPNLAttach.tTab.tAttInfo[clsCQR2.tPNLAttach.tTab.iAttCount - 1].sQueueKey;
            }
            else
            {
                if (tCQR.tPNL == null)
                {
                    //HttpContext.Current.Response.Write("?????" + Conversions.ToString(aiIndex));
                    //HttpContext.Current.Response.Flush();
                }
                //HttpContext.Current.Response.Write(tCQR.tPNL.ToString() + "---" + Conversions.ToString(aiIndex));
                //HttpContext.Current.Response.Flush();
                //atPNL.sOldQafKey = tCQR.tPNL[(int)aiIndex].sQAFKey;
                //HttpContext.Current.Response.Write(222);
                //HttpContext.Current.Response.Flush();
                //atPNL.sQAFKey = clsCQR2.tPNLAttach.tTab.tAttInfo[(int)aiIndex].sQueueKey;
                //HttpContext.Current.Response.Write(333);
                //HttpContext.Current.Response.Flush();
                atPNL.bReplaced = true;
                atPNL.iReplacedIndex = (int)aiIndex;
            }
            if (aiIndex == -1)
            {
                mQafItemAdd(atPNL);
            }
            else
            {
                //HttpContext.Current.Response.Write(444);
                //HttpContext.Current.Response.Flush();
                //tCQR.tPNL[(int)aiIndex] = atPNL;
                //HttpContext.Current.Response.Write(555);
                //HttpContext.Current.Response.Flush();
                tCQR.tPNL[(int)aiIndex].bDeleteOnSave = true;
                tCQR.tPNL[(int)aiIndex].bCreateOnSave = true;
            }
            clsCQR2 = null;
            GC.Collect();
            return z_FromSheetToStruct;
        }
    }

    private void mKillAllExcelProcesses()
    {
        Process[] tExcelProcesses = Process.GetProcessesByName("EXCEL");
        Process[] array = tExcelProcesses;
        foreach (Process tExcelProcess in array)
        {
            try
            {
                tExcelProcess.Kill();
            }
            catch (Exception ex2)
            {
                ProjectData.SetProjectError(ex2);
                Exception ex = ex2;
                ProjectData.ClearProjectError();
            }
        }
    }

    /// new new new new new new new '''
    public void mQafItemAdd(clsPNL tQaf, bool newCQR = true)
    {
        checked
        {
            iQAFCount++;
            ref clsPNL[] reference = ref tPNL;
            reference = (clsPNL[])Utils.CopyArray(reference, new clsPNL[iQAFCount + 1]);
            tPNL[iQAFCount - 1] = new clsPNL();
            tPNL[iQAFCount - 1] = tQaf;
            tPNL[iQAFCount - 1].bSave = true;
            tPNL[iQAFCount - 1].bCreateOnSave = newCQR;
        }
    }

    public void mQafItemUpdate(object tQaf, long aiIndex)
    {
        if (!((aiIndex < 0) | (aiIndex >= iQAFCount)))
        {
            tPNL[checked((int)aiIndex)] = (clsPNL)tQaf;
        }
    }

    private object z_DbsAddPNL(SqlDataReader drRecVals)
    {
        //corePage atPage = new corePage();
        //string sQAFKey = atPage.rstString(ref drRecVals, "PNL_AttachmentKey");
        //string sQafName = atPage.rstString(ref drRecVals, "PNL_Name");
        //string sQafStatus = atPage.rstString(ref drRecVals, "PNL_Status");
        //string sTargetSellPrice = atPage.rstString(ref drRecVals, "PNL_TargSellPrice");
        //string sStartingSellPrice = atPage.rstString(ref drRecVals, "PNL_StrtSellPrice");
        //string sTotalVolume = atPage.rstString(ref drRecVals, "PNL_TotalVolume");
        //string sTotalSales = atPage.rstString(ref drRecVals, "PNL_TotalSales");
        //string sAmortizedTools = atPage.rstString(ref drRecVals, "PNL_AmortizedTools");
        //if (Operators.CompareString(sAmortizedTools, "", TextCompare: false) == 0)
        //{
        //    sAmortizedTools = "???";
        //}
        //string sTRWPaidTools = atPage.rstString(ref drRecVals, "PNL_TRWPaidTools");
        //string sTRWCapital = atPage.rstString(ref drRecVals, "PNL_TRWCapital");
        //string sCustomerPaidTools = atPage.rstString(ref drRecVals, "PNL_CustPaidTools");
        //string sPATPercent = atPage.rstString(ref drRecVals, "PNL_PatPercent");
        //string sPATDollars = atPage.rstString(ref drRecVals, "PNL_PatDollars");
        //string sIRRPercent = atPage.rstString(ref drRecVals, "PNL_IRRPercent");
        //string sIRRDollars = atPage.rstString(ref drRecVals, "PNL_IRRDollars");
        //string sMaterialPercent = atPage.rstString(ref drRecVals, "PNL_MatPercent");
        //string sMaterialDollars = atPage.rstString(ref drRecVals, "PNL_MatDollars");
        //string sSGA = atPage.rstString(ref drRecVals, "PNL_SGA");
        //string sEngDollars = atPage.rstString(ref drRecVals, "PNL_EngDollars");
        //string sEngPercent = atPage.rstString(ref drRecVals, "PNL_EngPercent");
        //string sPayback = atPage.rstString(ref drRecVals, "PNL_Payback");
        //string sAvgAnnualSPReduction = atPage.rstString(ref drRecVals, "PNL_AvgAnnSPReduc");
        //string sEngSite = atPage.rstString(ref drRecVals, "PNL_EngSite");
        //string sFOBPoint = atPage.rstString(ref drRecVals, "PNL_FOBPoint");
        //bool bIsNewFormat = atPage.rstInt(ref drRecVals, "PNL_Format") > 0;
        //bool bAttachmentFound = true;
        bool bSave = true;
        //return RuntimeHelpers.GetObjectValue(z_AddPNL(abCanAdvance: (mQafTaskForUser(sQAFKey) & (iOpenMode != 2)) ? true : false, asQafName: sQafName, asQafStatus: sQafStatus, asTargetSellPrice: sTargetSellPrice, asStartingSellPrice: sStartingSellPrice, asTotalVolume: sTotalVolume, asTotalSales: sTotalSales, asAmortizedTools: sAmortizedTools, asTRWPaidTools: sTRWPaidTools, asTRWCapital: sTRWCapital, asCustomerPaidTools: sCustomerPaidTools, asPATPercent: sPATPercent, asPATDollars: sPATDollars, asIRRPercent: sIRRPercent, asIRRDollars: sIRRDollars, asMaterialPercent: sMaterialPercent, asMaterialDollars: sMaterialDollars, asSGA: sSGA, asEngDollars: sEngDollars, asEngPercent: sEngPercent, asPayback: sPayback, asAvgAnnualSPReduction: sAvgAnnualSPReduction, asEngSite: sEngSite, asFOBPoint: sFOBPoint, abAttachmentFound: Conversions.ToString(bAttachmentFound), abSave: bSave, asQafKey: sQAFKey, bIsNewFormat: bIsNewFormat));
        return null;
    }

    private object z_AddPNL(string asQafName, string asQafStatus, string asTargetSellPrice, string asStartingSellPrice, string asTotalVolume, string asTotalSales, string asAmortizedTools, string asTRWPaidTools, string asTRWCapital, string asCustomerPaidTools, string asPATPercent, string asPATDollars, string asIRRPercent, string asIRRDollars, string asMaterialPercent, string asMaterialDollars, string asSGA, string asEngDollars, string asEngPercent, string asPayback, string asAvgAnnualSPReduction, string asEngSite, string asFOBPoint, string abAttachmentFound, bool abSave, bool abCanAdvance, string asQafKey, bool bIsNewFormat)
    {
        int aiIndex = -1;
        bool abCreateOnSave = false;
        //corePage atPage = new corePage();
        object z_AddPNL = null;
        clsPNL atPNL = new clsPNL();
        clsPNL clsPNL = atPNL;
        clsPNL.sQafName = asQafName;
        clsPNL.sQafStatus = asQafStatus;
        clsPNL.sTargetSellPrice = asTargetSellPrice;
        clsPNL.sStartingSellPrice = asStartingSellPrice;
        clsPNL.sTotalVolume = asTotalVolume;
        clsPNL.sTotalSales = asTotalSales;
        clsPNL.sAmortizedTools = asAmortizedTools;
        if (Operators.CompareString(clsPNL.sAmortizedTools, "", TextCompare: false) == 0)
        {
            clsPNL.sAmortizedTools = "???";
        }
        clsPNL.sTRWPaidTools = asTRWPaidTools;
        clsPNL.sTRWCapital = asTRWCapital;
        clsPNL.sCustomerPaidTools = asCustomerPaidTools;
        clsPNL.sPATPercent = asPATPercent;
        clsPNL.sPATDollars = asPATDollars;
        clsPNL.sIRRPercent = asIRRPercent;
        clsPNL.sIRRDollars = asIRRDollars;
        clsPNL.sMaterialPercent = asMaterialPercent;
        clsPNL.sMaterialDollars = asMaterialDollars;
        clsPNL.sSGA = asSGA;
        clsPNL.sEngDollars = asEngDollars;
        clsPNL.sEngPercent = asEngPercent;
        clsPNL.sPayback = asPayback;
        clsPNL.sAvgAnnualSPReduction = asAvgAnnualSPReduction;
        clsPNL.sEngSite = asEngSite;
        clsPNL.sFOBPoint = asFOBPoint;
        clsPNL.bAttachmentFound = Conversions.ToBoolean(abAttachmentFound);
        clsPNL.bSave = abSave;
        clsPNL.bCreateOnSave = abCreateOnSave;
        clsPNL.bIsNewFormat = bIsNewFormat;
        clsPNL.sQAFKey = asQafKey;
        clsPNL.tPNLLineItem = new PNLLineItem[0];
        if (mQafTaskForUser(clsPNL.sQAFKey) & (iOpenMode != 2))
        {
            clsPNL.bCanAdvance = true;
        }
        else
        {
            clsPNL.bCanAdvance = false;
        }
        clsPNL = null;
        mQafItemAdd(atPNL, newCQR: false);
        return z_AddPNL;
    }

    public bool LoadPNL()
    {
        //corePage tPage = new corePage();
        //corePage atPage = new corePage();
        //SqlDataReader rst = atPage.cnExecute("SELECT * FROM CQR_PNLFields WHERE QueueKey = " + Conversions.ToString(iQueueKey) + " ORDER BY PNL_AutoNum");
        tPNLAttach.mInitialize(base.UserId, "FQ", Strings.Right("**********" + Conversions.ToString(iQueueKey), 10), "ATTACH_PNL");
        tPNLAttach.LoadData();
        checked
        {
            int index = default(int);
            //while (rst.Read())
            //{
            //    z_DbsAddPNL(rst);
            //    index++;
            //}
            if (!Information.IsNothing(tPNL))
            {
                int num = tPNL.GetUpperBound(0) - 1;
                for (int iLoop = 1; iLoop <= num && iLoop <= tPNL.GetUpperBound(0) - 1; iLoop++)
                {
                    int currAttachmentKey = Conversions.ToInteger(tPNL[iLoop].sQAFKey);
                    int num2 = iLoop - 1;
                    for (int jLoop = 0; jLoop <= num2; jLoop++)
                    {
                        if (Conversions.ToDouble(tPNL[jLoop].sQAFKey) == (double)currAttachmentKey)
                        {
                            int num3 = iLoop;
                            int num4 = tPNL.GetUpperBound(0) - 2;
                            for (int kLoop = num3; kLoop <= num4; kLoop++)
                            {
                                tPNL[kLoop] = null;
                                tPNL[kLoop] = tPNL[kLoop + 1];
                            }
                            tPNL[tPNL.GetUpperBound(0) - 1] = null;
                            ref clsPNL[] reference = ref tPNL;
                            reference = (clsPNL[])Utils.CopyArray(reference, new clsPNL[tPNL.GetUpperBound(0) - 1 + 1]);
                            iQAFCount--;
                            if (iQAFCount != 1)
                            {
                                iLoop--;
                            }
                            break;
                        }
                    }
                }
            }
            //rst.Close();
            if (tPNLAttach.tTab.iAttCount > iQAFCount)
            {
                int num5 = tPNLAttach.tTab.iAttCount - 1;
                for (int iLoop = 0; iLoop <= num5; iLoop++)
                {
                    int num6 = iQAFCount - 1;
                    int jLoop;
                    for (jLoop = 0; jLoop <= num6 && Conversion.Val(tPNLAttach.tTab.tAttInfo[iLoop].sQueueKey) != Conversion.Val(tPNL[jLoop].sQAFKey); jLoop++)
                    {
                    }
                    if (jLoop >= iQAFCount)
                    {
                        tPNLAttach.btnRemoveAttachment(iLoop, Conversions.ToString(0));
                    }
                }
            }
            //rst = atPage.cnExecute("SELECT * FROM CQR_PNLLineItem WHERE QueueKey = " + Conversions.ToString(iQueueKey) + " ORDER BY PNL_AttachmentKey, SaveOrder");
            //while (rst.Read())
            //{
            //    int num7 = iQAFCount - 1;
            //    for (int iLoop = 0; iLoop <= num7; iLoop++)
            //    {
            //        if (Conversion.Val(tPNL[iLoop].sQAFKey) == (double)atPage.rstInt(ref rst, "PNL_AttachmentKey"))
            //        {
            //            ref PNLLineItem[] tPNLLineItem = ref tPNL[iLoop].tPNLLineItem;
            //            tPNLLineItem = (PNLLineItem[])Utils.CopyArray(tPNLLineItem, new PNLLineItem[Information.UBound(tPNL[iLoop].tPNLLineItem) + 1 + 1]);
            //            ref PNLLineItem reference2 = ref tPNL[iLoop].tPNLLineItem[Information.UBound(tPNL[iLoop].tPNLLineItem)];
            //            reference2.iPNLLineItemId = atPage.rstInt(ref rst, "PNLLineItemId");
            //            reference2.sProduct = atPage.rstString(ref rst, "Product");
            //            reference2.sSite = atPage.rstString(ref rst, "Site");
            //            reference2.sAverageAnnualVolume = atPage.rstString(ref rst, "AverageAnnualVolume");
            //            reference2.sSalesPriceInQuoted = atPage.rstString(ref rst, "SalesPriceInQuoted");
            //            reference2.sQuotedCurrency = atPage.rstString(ref rst, "QuotedCurrency");
            //            reference2.sSalesBusinessUnit = atPage.rstString(ref rst, "SalesBusinessUnit");
            //            reference2.sSalesPriceSOP = atPage.rstString(ref rst, "SalesPriceSOP");
            //            reference2.sAverageAnnualSales = atPage.rstString(ref rst, "AverageAnnualSales");
            //            reference2.sAverageAnnualDVP = atPage.rstString(ref rst, "AverageAnnualDVP");
            //            reference2.sAverageAnnualMPBT = atPage.rstString(ref rst, "AverageAnnualMPBT");
            //            reference2.sEVAFullCost = atPage.rstString(ref rst, "EVAFullCost");
            //            reference2.sEVAIncremental = atPage.rstString(ref rst, "EVAIncremental");
            //            reference2.sAwardStatus = atPage.rstString(ref rst, "AwardStatus");
            //            reference2.sAwardStatusDbs = reference2.sAwardStatus;
            //            reference2.sAwardStatusDate = atPage.rstString(ref rst, "AwardStatusDate");
            //            reference2.sAwardStatusUser = atPage.rstString(ref rst, "AwardStatusUser");
            //            reference2.sCreateDate = atPage.rstString(ref rst, "CreateDate");
            //        }
            //    }
            //}
            //rst.Close();
            //atPage.cnClose();
            return true;
        }
    }

    public void ClearPNLData()
    {
        tPNL = new clsPNL[1];
        iQAFCount = 0;
    }

    public void SavePNL(string iQueueKey, corePage atPage)
    {
        string sValues = "";
        savePNLAttachments(atPage);
        string sSQL = "UPDATE CQR_PNLLineItem SET Marked='1' WHERE QueueKey = " + iQueueKey;
        atPage.cnExecuteNonQuery(sSQL);
        if (iQAFCount == 0)
        {
            return;
        }
        checked
        {
            int num = iQAFCount - 1;
            for (int iLoop = 0; iLoop <= num; iLoop++)
            {
                clsPNL clsPNL = tPNL[iLoop];
                if (clsPNL.bReplaced)
                {
                    clsPNL.sQAFKey = tPNLAttach.tTab.tAttInfo[clsPNL.iReplacedIndex].sQueueKey;
                    clsPNL.bReplaced = false;
                }
                if (false & !clsPNL.bCreateOnSave & !clsPNL.bDeleteOnSave)
                {
                    sSQL = "UPDATE CQR_PNLFields SET ";
                    sSQL += z_AddUpdateField("PNL_Name", Strings.Left(clsPNL.sQafName, 100));
                    sSQL += z_AddUpdateField("PNL_AttachmentKey", Conversions.ToString(Conversion.Val(mGetAttachmentQueueKey(atPage, iLoop))));
                    sSQL += z_AddUpdateField("PNL_Status", clsPNL.sQafStatus);
                    sSQL += z_AddUpdateField("PNL_StatusDate", clsPNL.sStatusDate);
                    sSQL += z_AddUpdateField("PNL_TargSellPrice", Strings.Left(clsPNL.sTargetSellPrice, 15));
                    sSQL += z_AddUpdateField("PNL_StrtSellPrice", Strings.Left(clsPNL.sStartingSellPrice, 15));
                    sSQL += z_AddUpdateField("PNL_TotalVolume", Strings.Left(clsPNL.sTotalVolume, 15));
                    sSQL += z_AddUpdateField("PNL_TotalSales", Strings.Left(clsPNL.sTotalSales, 15));
                    sSQL += z_AddUpdateField("PNL_AmortizedTools", Strings.Left(clsPNL.sAmortizedTools, 15));
                    sSQL += z_AddUpdateField("PNL_TRWPaidTools", Strings.Left(clsPNL.sTRWPaidTools, 15));
                    sSQL += z_AddUpdateField("PNL_TRWCapital", Strings.Left(clsPNL.sTRWCapital, 15));
                    sSQL += z_AddUpdateField("PNL_CustPaidTools", Strings.Left(clsPNL.sCustomerPaidTools, 15));
                    sSQL += z_AddUpdateField("PNL_PatPercent", Strings.Left(clsPNL.sPATPercent, 15));
                    sSQL += z_AddUpdateField("PNL_PatDollars", Strings.Left(clsPNL.sPATDollars, 15));
                    sSQL += z_AddUpdateField("PNL_IRRPercent", Strings.Left(clsPNL.sIRRPercent, 15));
                    sSQL += z_AddUpdateField("PNL_IRRDollars", Strings.Left(clsPNL.sIRRDollars, 15));
                    sSQL += z_AddUpdateField("PNL_MatPercent", Strings.Left(clsPNL.sMaterialPercent, 15));
                    sSQL += z_AddUpdateField("PNL_MatDollars", Strings.Left(clsPNL.sMaterialDollars, 15));
                    sSQL += z_AddUpdateField("PNL_SGA", Strings.Left(clsPNL.sSGA, 15));
                    sSQL += z_AddUpdateField("PNL_EngDollars", Strings.Left(clsPNL.sEngDollars, 15));
                    sSQL += z_AddUpdateField("PNL_EngPercent", Strings.Left(clsPNL.sEngPercent, 15));
                    sSQL += z_AddUpdateField("PNL_Payback", Strings.Left(clsPNL.sPayback, 15));
                    sSQL += z_AddUpdateField("PNL_AvgAnnSPReduc", Strings.Left(clsPNL.sAvgAnnualSPReduction, 15));
                    sSQL += z_AddUpdateField("PNL_EngSite", clsPNL.sEngSite);
                    sSQL += z_AddUpdateField("PNL_FOBPoint", clsPNL.sFOBPoint);
                    sSQL += z_AddUpdateField("PNL_Format", Conversions.ToString(Interaction.IIf(clsPNL.bIsNewFormat, 1, 0)));
                    sSQL = Strings.Left(sSQL, sSQL.Length - 2);
                    sSQL = sSQL + " WHERE PNL_AttachmentKey = " + clsPNL.sOldQafKey;
                    atPage.cnExecuteNonQuery(sSQL);
                    clsPNL.bCreateOnSave = false;
                    clsPNL.bDeleteOnSave = false;
                    clsPNL.sOldQafKey = clsPNL.sQAFKey;
                }
                else if (clsPNL.bDeleteOnSave)
                {
                    if (!clsPNL.bCreateOnSave)
                    {
                        sSQL = "Delete FROM CQR_PNLFields where QueueKey = " + iQueueKey + " and PNL_AttachmentKey = " + clsPNL.sQAFKey;
                        atPage.cnExecuteNonQuery(sSQL);
                    }
                }
                else if (clsPNL.bCreateOnSave)
                {
                    sSQL = "INSERT INTO CQR_PNLFields ";
                    sSQL += "(QueueKey, PNL_Name, PNL_AttachmentKey, PNL_Status,PNL_StatusDate, ";
                    sSQL += " PNL_TargSellPrice,PNL_StrtSellPrice,PNL_TotalVolume,PNL_TotalSales,";
                    sSQL += "PNL_AmortizedTools,PNL_TRWPaidTools,PNL_TRWCapital,PNL_CustPaidTools,";
                    sSQL += "PNL_PatPercent,PNL_PatDollars,PNL_IRRPercent,PNL_IRRDollars,PNL_MatPercent,";
                    sSQL += "PNL_MatDollars,PNL_SGA,PNL_EngDollars,PNL_EngPercent,PNL_Payback,";
                    sSQL += "PNL_AvgAnnSPReduc,PNL_EngSite,PNL_FOBPoint,PNL_Format)";
                    sSQL += " VALUES ";
                    sValues = iQueueKey;
                    clsPNL.sQAFKey = Conversions.ToString(Conversion.Val(tPNLAttach.tTab.tAttInfo[iLoop].sQueueKey));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sQafName, 100));
                    sValues += z_AddInsertValue(clsPNL.sQAFKey);
                    sValues += z_AddInsertValue(clsPNL.sQafStatus);
                    sValues += z_AddInsertValue(clsPNL.sStatusDate);
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sTargetSellPrice, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sStartingSellPrice, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sTotalVolume, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sTotalSales, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sAmortizedTools, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sTRWPaidTools, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sTRWCapital, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sCustomerPaidTools, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sPATPercent, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sPATDollars, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sIRRPercent, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sIRRDollars, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sMaterialPercent, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sMaterialDollars, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sSGA, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sEngDollars, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sEngPercent, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sPayback, 15));
                    sValues += z_AddInsertValue(Strings.Left(clsPNL.sAvgAnnualSPReduction, 15));
                    sValues += z_AddInsertValue(clsPNL.sEngSite);
                    sValues += z_AddInsertValue(clsPNL.sFOBPoint);
                    sValues += z_AddInsertValue(Conversions.ToString(Interaction.IIf(clsPNL.bIsNewFormat, 1, 0)));
                    atPage.cnExecuteNonQuery(sSQL + "(" + sValues + ")");
                    clsPNL.bCreateOnSave = false;
                }
                if (!clsPNL.bDeleteOnSave & !clsPNL.bCreateOnSave)
                {
                    sSQL = "UPDATE CQR_PNLFields SET PNL_Status = '" + clsPNL.sQafStatus + "' WHERE PNL_AttachmentKey = " + mGetAttachmentQueueKey(atPage, iLoop);
                    atPage.cnExecuteNonQuery(sSQL);
                }
                int num2 = Information.UBound(clsPNL.tPNLLineItem);
                for (int jLoop = 0; jLoop <= num2; jLoop++)
                {
                    if (clsPNL.tPNLLineItem[jLoop].iPNLLineItemId == 0)
                    {
                        sSQL = "INSERT INTO CQR_PNLLineItem (QueueKey, PNL_AttachmentKey) VALUES(";
                        sSQL = sSQL + iQueueKey + "," + clsPNL.sQAFKey + ")";
                        clsPNL.tPNLLineItem[jLoop].iPNLLineItemId = atPage.cnExecuteGetIdentity(sSQL);
                    }
                    sSQL = "UPDATE CQR_PNLLineItem SET ";
                    sSQL += z_AddUpdateField("SaveOrder", Conversions.ToString(jLoop + 1));
                    sSQL += z_AddUpdateField("Product", clsPNL.tPNLLineItem[jLoop].sProduct);
                    sSQL += z_AddUpdateField("Site", clsPNL.tPNLLineItem[jLoop].sSite);
                    sSQL += z_AddUpdateField("AverageAnnualVolume", clsPNL.tPNLLineItem[jLoop].sAverageAnnualVolume);
                    sSQL += z_AddUpdateField("SalesPriceInQuoted", clsPNL.tPNLLineItem[jLoop].sSalesPriceInQuoted);
                    sSQL += z_AddUpdateField("QuotedCurrency", clsPNL.tPNLLineItem[jLoop].sQuotedCurrency);
                    sSQL += z_AddUpdateField("SalesBusinessUnit", clsPNL.tPNLLineItem[jLoop].sSalesBusinessUnit);
                    sSQL += z_AddUpdateField("SalesPriceSOP", clsPNL.tPNLLineItem[jLoop].sSalesPriceSOP);
                    sSQL += z_AddUpdateField("AverageAnnualSales", clsPNL.tPNLLineItem[jLoop].sAverageAnnualSales);
                    sSQL += z_AddUpdateField("AverageAnnualDVP", clsPNL.tPNLLineItem[jLoop].sAverageAnnualDVP);
                    sSQL += z_AddUpdateField("AverageAnnualMPBT", clsPNL.tPNLLineItem[jLoop].sAverageAnnualMPBT);
                    sSQL += z_AddUpdateField("EVAFullCost", clsPNL.tPNLLineItem[jLoop].sEVAFullCost);
                    sSQL += z_AddUpdateField("EVAIncremental", clsPNL.tPNLLineItem[jLoop].sEVAIncremental);
                    sSQL += z_AddUpdateField("AwardStatus", clsPNL.tPNLLineItem[jLoop].sAwardStatus);
                    if (Operators.CompareString(clsPNL.tPNLLineItem[jLoop].sAwardStatus, clsPNL.tPNLLineItem[jLoop].sAwardStatusDbs, TextCompare: false) != 0)
                    {
                        clsPNL.tPNLLineItem[jLoop].sAwardStatusDbs = clsPNL.tPNLLineItem[jLoop].sAwardStatus;
                        clsPNL.tPNLLineItem[jLoop].sAwardStatusDate = Conversions.ToString(DateAndTime.Now);
                        clsPNL.tPNLLineItem[jLoop].sAwardStatusUser = base.UserId;
                        sSQL += z_AddUpdateField("AwardStatusDate", clsPNL.tPNLLineItem[jLoop].sAwardStatusDate);
                        sSQL += z_AddUpdateField("AwardStatusUser", clsPNL.tPNLLineItem[jLoop].sAwardStatusUser);
                    }
                    sSQL += z_AddUpdateField("CreateDate", clsPNL.tPNLLineItem[jLoop].sCreateDate);
                    sSQL = sSQL + " Marked=null WHERE PNLLineItemId = " + Conversions.ToString(clsPNL.tPNLLineItem[jLoop].iPNLLineItemId);
                    atPage.cnExecuteNonQuery(sSQL);
                }
                clsPNL = null;
            }
            sSQL = "DELETE FROM CQR_PNLLineItem WHERE Marked='1' AND QueueKey = " + iQueueKey;
            atPage.cnExecuteNonQuery(sSQL);
        }
    }

    public bool mDeletePNL(int iIndex)
    {
        tPNL[iIndex].bSave = false;
        tPNL[iIndex].bDeleteOnSave = true;
        tPNL[iIndex].bCreateOnSave = false;
        tRouting.mDeleteAllTasksForDrawing(Strings.Right("**********" + tPNL[iIndex].sOldQafKey, 10));
        tRouting.mDeleteAllTasksForDrawing(Strings.Right("**********" + tPNL[iIndex].sQAFKey, 10));
        return true;
    }

    public string z_QafFormat(string sValue)
    {
        string z_QafFormat = sValue;
        if (Strings.InStr("$#", Strings.Left(sValue, 1)) <= 0 && Strings.InStr("-?", Strings.Mid(sValue, 2, 1)) <= 0 && Strings.InStr("%", Strings.Right(sValue, 1)) <= 0)
        {
            double dValue = Conversion.Val(sValue) / 1000.0;
            z_QafFormat = ((!(Math.Abs(dValue) < 100.0)) ? Strings.Format(dValue, "###,###k") : Strings.Format(dValue, "0.0##k"));
        }
        return z_QafFormat;
    }

    public string z_Percent(string asText)
    {
        string asValue = Strings.Format(Conversion.Val(asText) * 100.0, "##0.00");
        if (Math.Abs(Conversion.Val(asValue)) > 1000000.0)
        {
            double dValue = Conversion.Val(asValue) / 1000000.0;
            return Strings.Format(dValue, "###,###M");
        }
        if (Math.Abs(Conversion.Val(asValue)) > 1000.0)
        {
            return z_QafFormat(asValue);
        }
        return asValue;
    }

    private void NAR(object o)
    {
        try
        {
            int i;
            do
            {
                i = Marshal.ReleaseComObject(RuntimeHelpers.GetObjectValue(o));
            }
            while (i > 0);
        }
        catch (Exception ex)
        {
            ProjectData.SetProjectError(ex);
            Exception excpUnexpected = ex;
            ProjectData.ClearProjectError();
        }
        finally
        {
            o = null;
        }
    }

    public int buildPNLTable(coreTable PNLTable, string sBaseId, bool canView, bool canDelete, bool canReplace, bool bReadOnly, bool bIsGateway)
    {
        for (int iCount = 0; iCount < iQAFCount; iCount = checked(iCount + 1))
        {
            int iIndex = pnl_GetAttachmentIndex(tPNL[iCount].sQAFKey);
            if (iIndex == -1)
            {
                mDeletePNL(iCount);
            }
            else if (tPNLAttach.tTab.tAttInfo[iIndex].bDeleteOnSave)
            {
                mDeletePNL(iCount);
            }
            if (Information.IsNothing(tPNL[iCount]))
            {
                break;
            }
            bool bShow = !tPNL[iCount].bDeleteOnSave | (Operators.CompareString(tPNL[iCount].sOldQafKey, "", TextCompare: false) != 0);
            if (bIsGateway)
            {
                bShow &= Operators.CompareString(tPNL[iCount].sQafStatus, "010100QF", TextCompare: false) > 0;
            }
            if (!bShow)
            {
                continue;
            }
            //TableRow tableRow = PNLTable.AddRow();
            //tableRow.ID = "PNLTableRow_" + Conversions.ToString(iCount);
            //tableRow.Attributes.Add("Released", Conversions.ToString(Interaction.IIf(Operators.CompareString(tPNL[iCount].sQafStatus, "010100QF", TextCompare: false) > 0, "1", "")));
            //tableRow = null;
            //if (!bIsGateway)
            //{
            //    System.Web.UI.WebControls.CheckBox chkBox = new System.Web.UI.WebControls.CheckBox();
            //    chkBox.ID = "chkBox_" + Conversions.ToString(iCount);
            //    chkBox.CssClass = "CCTextBox";
            //    chkBox.Checked = Conversions.ToBoolean(Interaction.IIf(Operators.CompareString(tPNL[iCount].bChecked, "on", TextCompare: false) == 0, true, false));
            //    chkBox.Enabled = mQafTaskForUser(tPNL[iCount].sQAFKey);
            //    PNLTable.AddCell("").Controls.Add(chkBox);
            //}
            //System.Web.UI.WebControls.Label tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtPNLName_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(100);
            //tLabel.Style.Add("text-align", "left");
            //tLabel.Text = tPNL[iCount].sQafName;
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtPNLStartingSellPrice_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(60);
            //tLabel.Style.Add("text-align", "right");
            //tLabel.Text = Strings.Format(Conversion.Val(tPNL[iCount].sStartingSellPrice), "####0.00");
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtPNLAmortizedTools_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(60);
            //tLabel.Style.Add("text-align", "right");
            //tLabel.Text = z_QafFormat(tPNL[iCount].sAmortizedTools);
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtPNLTotalSales_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(60);
            //tLabel.Style.Add("text-align", "right");
            //tLabel.Text = z_QafFormat(tPNL[iCount].sTotalSales);
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtPNLTRWPaidTools_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(60);
            //tLabel.Style.Add("text-align", "right");
            //tLabel.Text = z_QafFormat(tPNL[iCount].sTRWPaidTools);
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtPNLTRWCapital_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(60);
            //tLabel.Style.Add("text-align", "right");
            //tLabel.Text = z_QafFormat(tPNL[iCount].sTRWCapital);
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtCustomerPaidTools_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(60);
            //tLabel.Style.Add("text-align", "right");
            //tLabel.Text = z_QafFormat(tPNL[iCount].sCustomerPaidTools);
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtPNLPATPercent_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(60);
            //tLabel.Style.Add("text-align", "right");
            //tLabel.Text = z_Percent(tPNL[iCount].sPATPercent);
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtPNLIRRPercent_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(60);
            //tLabel.Style.Add("text-align", "right");
            //tLabel.Text = z_Percent(tPNL[iCount].sIRRPercent);
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //tLabel = new System.Web.UI.WebControls.Label();
            //tLabel.ID = "txtPNLMaterialPercent_" + Conversions.ToString(iCount);
            //tLabel.CssClass = "CCLabel";
            //tLabel.Width = Unit.Pixel(60);
            //tLabel.Style.Add("text-align", "right");
            //tLabel.Text = z_Percent(tPNL[iCount].sMaterialPercent);
            //PNLTable.AddCell("").Controls.Add(tLabel);
            //HyperLink tHyperLinkView = new HyperLink();
            //tHyperLinkView.ID = "lnkPNLView_" + sBaseId + "_" + Conversions.ToString(iCount);
            //tHyperLinkView.Text = "view";
            //tHyperLinkView.NavigateUrl = "../_coreForm/frmAttachment.aspx?QueueKey=" + Conversions.ToString(iQueueKey) + "&Index=" + Conversions.ToString(iIndex) + "&View=1&SessionTag=" + tPNLAttach.tTab.sSessionTag;
            //tHyperLinkView.Target = "_attachmentPopup";
            //TableCell tableCell = PNLTable.AddCell();
            //System.Web.UI.WebControls.Label spaceLabel = new System.Web.UI.WebControls.Label();
            //spaceLabel.Text = "&nbsp;";
            //if (canView)
            //{
            //    tableCell.Controls.Add(tHyperLinkView);
            //}
            //tableCell.HorizontalAlign = HorizontalAlign.Center;
            //tableCell = null;
            //HyperLink tHyperLinkDel = new HyperLink();
            //tHyperLinkDel.ID = "lnkPNLDel_" + sBaseId + "_" + Conversions.ToString(iCount);
            //tHyperLinkDel.Text = "del";
            //tHyperLinkDel.NavigateUrl = "javascript:fnGoDelete(Index=" + Conversions.ToString(iIndex) + ");";
            //if (!bReadOnly)
            //{
            //    TableCell tableCell2 = PNLTable.AddCell();
            //    if (canDelete && Operators.CompareString(tPNL[iCount].sQafStatus, "010100QF", TextCompare: false) <= 0)
            //    {
            //        tableCell2.Controls.Add(tHyperLinkDel);
            //    }
            //    tableCell2.Width = Unit.Pixel(20);
            //    tableCell2.HorizontalAlign = HorizontalAlign.Center;
            //    tableCell2 = null;
            //}
            //HyperLink tReplaceLink = new HyperLink();
            //tReplaceLink.ID = "lnkReAttach_" + sBaseId + "_" + Conversions.ToString(iCount);
            //tReplaceLink.Text = "replace";
            //tReplaceLink.NavigateUrl = "javascript:fnReattachPNL('../_coreForm/frmAttachment.aspx?QueueKey=" + Conversions.ToString(iQueueKey) + "|" + Conversions.ToString(iIndex) + "|" + tPNLAttach.tTab.sSessionTag + "|&Index=" + Conversions.ToString(iIndex) + "&Create=1', '" + Conversions.ToString(iIndex) + "')";
            //if (!bReadOnly)
            //{
            //    TableCell tableCell3 = PNLTable.AddCell();
            //    if (canReplace)
            //    {
            //        tableCell3.Controls.Add(tReplaceLink);
            //    }
            //    tableCell3.Width = Unit.Pixel(20);
            //    tableCell3.HorizontalAlign = HorizontalAlign.Center;
            //    tableCell3 = null;
            //}
        }
        int buildPNLTable = default(int);
        return buildPNLTable;
    }

    public bool showOldPNL()
    {
        checked
        {
            int num = tPNLAttach.tTab.iAttCount - 1;
            for (int iLoop = 0; iLoop <= num; iLoop++)
            {
                if (Operators.CompareString(tPNLAttach.tTab.tAttInfo[iLoop].sCreateDate, "20120910", TextCompare: false) > 0)
                {
                    return false;
                }
            }
            return true;
        }
    }
}

