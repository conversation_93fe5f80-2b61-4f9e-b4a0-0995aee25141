﻿using CQR.Application.Dto.CQRHeard;
using CQR.Domain.CQRHeaders;
using CQR.Domain.Interfaces.Repository;

namespace CQR.Application.Services;

public class CQRValidationService : ICQRValidationService
{
    private readonly IUnitOfWork _unitOfWork;

    private readonly ICQRHeaderRepository _repository;
    public CQRValidationService(IUnitOfWork unitOfWork, ICQRHeaderRepository repository)
    {
        _unitOfWork = unitOfWork;
        _repository = repository;
    }

    public async Task<string> ValidateCQRRequest(ValidateCQRRequest request)
    {
        //throw new NotImplementedException();
        if (request.CQRType == "New")
            return "New";

        if (request.CQRType != "Modify")
            return "NotValid";

        if (!await _repository.ExistProjectNbr(request.ModifiedProjNbr))
            return "NotValid";

        if (request.ModifiedRevNbr == -1)
            return "Modify";

        var revExists = await _repository.ExistProjectNbrAndRevNbr(request.ModifiedProjNbr, request.ModifiedRevNbr);
        return revExists ? "Modify" : "NotValid";
    }
}
