﻿using CQR.Application.Repositories;
using CQR.Domain.CQR_GDPIMPhases;
using CQR.Domain.CQR_IHSs;
using CQR.Domain.CQRHeaders;
using CQR.Domain.CQRIHSFolder;
using CQR.Domain.ROUTING_RGHeaders;
using CQR.Persistence.Command.Repositories;

namespace CQR.Application.Common;

public class RepositoryCollection: IRepositoryCollection
{
    private readonly CQRDbContext _context;
    public ICQRIHSRepository CQRIHSRepository { get; }
    public ICQRHeaderRepository CQRHeaderRepository { get; }
    public ICQRIHSFolderRepository CQRIHSFolderRepository { get; }
    public IRoutingRGHeaderRepository RoutingRGHeaderRepository { get; }
    public IAttachFileQueryRepository AttachFileQueryRepository { get; }
    public ICQRGDPIMPhasesRepository CQRGDPIMPhasesRepository { get; }
    public ICqrHeaderQueryRepository CqrHeaderQueryRepository { get; }
    public IUserRoleQueryRepository UserRoleQueryRepository { get; }
    public ITRSHeaderQueryRespository TRSHeaderQueryRepository { get; }
    public ICQRAntaresQueryRepository CQRAntaresQueryRepository { get; }

    public RepositoryCollection(
        CQRDbContext context,
        ICQRIHSRepository cqrIhsRepository,
        ICQRHeaderRepository cqrHeaderRepository,
        ICQRIHSFolderRepository cqrIhsFolderRepository,
        IRoutingRGHeaderRepository routingRGHeaderRepository,
        IAttachFileQueryRepository attachFileQueryRepository,
        ICQRGDPIMPhasesRepository cqrGdpimPhasesRepository,
        ICqrHeaderQueryRepository cqrHeaderQueryRepository,
        IUserRoleQueryRepository userRoleQueryRepository,
        ITRSHeaderQueryRespository tRSHeaderQueryRepository,
        ICQRAntaresQueryRepository cQRAntaresQueryRepository
        //CQRDbContext context
        )
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        CQRIHSRepository = cqrIhsRepository;
        CQRHeaderRepository = cqrHeaderRepository;
        CQRIHSFolderRepository = cqrIhsFolderRepository;
        RoutingRGHeaderRepository = routingRGHeaderRepository;
        AttachFileQueryRepository = attachFileQueryRepository;
        CQRGDPIMPhasesRepository = cqrGdpimPhasesRepository;
        CqrHeaderQueryRepository = cqrHeaderQueryRepository;
        UserRoleQueryRepository = userRoleQueryRepository;
        TRSHeaderQueryRepository = tRSHeaderQueryRepository;
        CQRAntaresQueryRepository = cQRAntaresQueryRepository;
        //_context = context;
    }
}

