﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CQR.Domain.Primitives;

namespace CQR.Domain.CQRIHSFolder;

[Table("CQR_IHSFolder")]
public class CQR_IHSFolder : BaseEntity<int>
{
    [Key]
    [Column("CQRIHSFolderId")] // 改成資料表實際主鍵欄位名稱
    public override int Id { get; protected set; }

    // Private fields for encapsulation
    private int? _queueKey;
    private int? _uniqueNumber;
    private string? _archivedByUser;
    private string? _coreNameplatePlantMnemonic;
    private string? _region;
    private string? _country;
    private string? _platform;
    private string? _program;
    private string? _productionNameplate;
    private string? _startOfProduction;
    private string? _endOfProduction;
    private string? _oemGroup;
    private string? _oem;
    private string? _productDescription;
    private string? _productGrouping;
    private string? _soldFrom;
    private string? _finalAssembly;

    // Constructor for EF Core
    private CQR_IHSFolder()
    {
        // EF Core requires parameterless constructor
    }

    // Constructor for creating new entity
    public CQR_IHSFolder(
        int? queueKey,
        int? uniqueNumber,
        string? coreNameplatePlantMnemonic = null,
        string? region = null,
        string? country = null,
        string? platform = null,
        string? program = null,
        string? productionNameplate = null,
        string? startOfProduction = null,
        string? endOfProduction = null,
        string? oemGroup = null,
        string? oem = null,
        string? productDescription = null,
        string? productGrouping = null,
        string? soldFrom = null,
        string? finalAssembly = null
    )
    {
        SetQueueKey(queueKey);
        SetUniqueNumber(uniqueNumber);
        SetCoreNameplatePlantMnemonic(mnemonic: coreNameplatePlantMnemonic);
        SetRegion(region);
        SetCountry(country);
        SetPlatform(platform);
        SetProgram(program);
        SetProductionNameplate(productionNameplate);
        SetProductionPeriod(startOfProduction, endOfProduction);
        SetOEMInformation(oemGroup, oem);
        SetProductInformation(productDescription, productGrouping);
        SetLocationInformation(soldFrom, finalAssembly);

        DateAdded = DateTime.UtcNow;
        DateUpdated = DateTime.UtcNow;
        Archived = false;
    }

    // Properties with proper encapsulation
    //public int CQRIHSFolderId { get; private set; }

    public int? QueueKey
    {
        get => _queueKey;
        private set => _queueKey = value;
    }

    public int? UniqueNumber
    {
        get => _uniqueNumber;
        private set => _uniqueNumber = value;
    }

    public bool? Archived { get; private set; }
    public int? ArchivedByQueueKey { get; private set; }

    public string? ArchivedByUser
    {
        get => _archivedByUser;
        private set => _archivedByUser = value;
    }

    public DateTime? ArchivedDate { get; private set; }
    public DateTime? DateAdded { get; private set; }
    public DateTime? DateUpdated { get; private set; }

    public string? CoreNameplatePlantMnemonic
    {
        get => _coreNameplatePlantMnemonic;
        private set => _coreNameplatePlantMnemonic = value;
    }

    public string? Region
    {
        get => _region;
        private set => _region = value;
    }

    public string? Country
    {
        get => _country;
        private set => _country = value;
    }

    public string? Platform
    {
        get => _platform;
        private set => _platform = value;
    }

    public string? Program
    {
        get => _program;
        private set => _program = value;
    }

    public string? ProductionNameplate
    {
        get => _productionNameplate;
        private set => _productionNameplate = value;
    }

    public string? StartOfProduction
    {
        get => _startOfProduction;
        private set => _startOfProduction = value;
    }

    public string? EndOfProduction
    {
        get => _endOfProduction;
        private set => _endOfProduction = value;
    }

    public string? OEMGroup
    {
        get => _oemGroup;
        private set => _oemGroup = value;
    }

    public string? OEM
    {
        get => _oem;
        private set => _oem = value;
    }

    public string? ProductDescription
    {
        get => _productDescription;
        private set => _productDescription = value;
    }

    public string? ProductGrouping
    {
        get => _productGrouping;
        private set => _productGrouping = value;
    }

    public string? SoldFrom
    {
        get => _soldFrom;
        private set => _soldFrom = value;
    }

    public string? FinalAssembly
    {
        get => _finalAssembly;
        private set => _finalAssembly = value;
    }

    // Domain methods with business logic validation
    public void SetQueueKey(int? queueKey)
    {
        if (queueKey.HasValue && queueKey.Value <= 0)
            throw new ArgumentException(
                "Queue key must be a positive number when provided.",
                nameof(queueKey)
            );

        _queueKey = queueKey;
        UpdateTimestamp();
    }

    public void SetUniqueNumber(int? uniqueNumber)
    {
        if (uniqueNumber.HasValue && uniqueNumber.Value <= 0)
            throw new ArgumentException(
                "Unique number must be a positive number when provided.",
                nameof(uniqueNumber)
            );

        _uniqueNumber = uniqueNumber;
        UpdateTimestamp();
    }

    public void SetCoreNameplatePlantMnemonic(string mnemonic)
    {
        ValidateStringLength(mnemonic, 50, nameof(mnemonic));
        _coreNameplatePlantMnemonic = mnemonic?.Trim();
        UpdateTimestamp();
    }

    public void SetRegion(string region)
    {
        ValidateStringLength(region, 50, nameof(region));
        _region = region?.Trim();
        UpdateTimestamp();
    }

    public void SetCountry(string country)
    {
        ValidateStringLength(country, 50, nameof(country));
        _country = country?.Trim();
        UpdateTimestamp();
    }

    public void SetPlatform(string platform)
    {
        ValidateStringLength(platform, 50, nameof(platform));
        _platform = platform?.Trim();
        UpdateTimestamp();
    }

    public void SetProgram(string program)
    {
        ValidateStringLength(program, 50, nameof(program));
        _program = program?.Trim();
        UpdateTimestamp();
    }

    public void SetProductionNameplate(string productionNameplate)
    {
        ValidateStringLength(productionNameplate, 50, nameof(productionNameplate));
        _productionNameplate = productionNameplate?.Trim();
        UpdateTimestamp();
    }

    public void SetProductionPeriod(string startOfProduction, string endOfProduction)
    {
        ValidateStringLength(startOfProduction, 50, nameof(startOfProduction));
        ValidateStringLength(endOfProduction, 50, nameof(endOfProduction));

        _startOfProduction = startOfProduction?.Trim();
        _endOfProduction = endOfProduction?.Trim();
        UpdateTimestamp();
    }

    public void SetOEMInformation(string oemGroup, string oem)
    {
        ValidateStringLength(oemGroup, 50, nameof(oemGroup));
        ValidateStringLength(oem, 50, nameof(oem));

        _oemGroup = oemGroup?.Trim();
        _oem = oem?.Trim();
        UpdateTimestamp();
    }

    public void SetProductInformation(string productDescription, string productGrouping)
    {
        ValidateStringLength(productDescription, 50, nameof(productDescription));
        ValidateStringLength(productGrouping, 50, nameof(productGrouping));

        _productDescription = productDescription?.Trim();
        _productGrouping = productGrouping?.Trim();
        UpdateTimestamp();
    }

    public void SetLocationInformation(string soldFrom, string finalAssembly)
    {
        ValidateStringLength(soldFrom, 50, nameof(soldFrom));
        ValidateStringLength(finalAssembly, 50, nameof(finalAssembly));

        _soldFrom = soldFrom?.Trim();
        _finalAssembly = finalAssembly?.Trim();
        UpdateTimestamp();
    }

    public void Archive(int archivedByQueueKey, string archivedByUser)
    {
        if (Archived == true)
            throw new InvalidOperationException("This record is already archived.");

        if (archivedByQueueKey <= 0)
            throw new ArgumentException(
                "Archived by queue key must be a positive number.",
                nameof(archivedByQueueKey)
            );

        ValidateStringLength(archivedByUser, 50, nameof(archivedByUser));

        if (string.IsNullOrWhiteSpace(archivedByUser))
            throw new ArgumentException(
                "Archived by user cannot be null or empty.",
                nameof(archivedByUser)
            );

        Archived = true;
        ArchivedByQueueKey = archivedByQueueKey;
        _archivedByUser = archivedByUser.Trim();
        ArchivedDate = DateTime.UtcNow;
        UpdateTimestamp();
    }

    //public void Unarchive()
    //{
    //    if (Archived != true)
    //        throw new InvalidOperationException("This record is not archived.");

    //    Archived = false;
    //    ArchivedByQueueKey = null;
    //    _archivedByUser = null;
    //    ArchivedDate = null;
    //    UpdateTimestamp();
    //}


    public bool IsArchived()
    {
        return Archived == true;
    }

    // Helper methods
    private void ValidateStringLength(string value, int maxLength, string parameterName)
    {
        if (!string.IsNullOrEmpty(value) && value.Length > maxLength)
            throw new ArgumentException(
                $"{parameterName} cannot exceed {maxLength} characters.",
                parameterName
            );
    }

    private void UpdateTimestamp()
    {
        DateUpdated = DateTime.UtcNow;
    }

    // Domain events could be raised here for important business operations
    // public void RaiseDomainEvent(IDomainEvent domainEvent) { ... }
}
