using Newtonsoft.Json;
using JsonException = Newtonsoft.Json.JsonException;

namespace CQRLIB.Utilities
{

    public static class JsonUtils
    {
        public static T Deserialize<T>(string jsonString)
        {
            try
            {
                T result = JsonConvert.DeserializeObject<T>(jsonString);
                return result;
            }
            catch (JsonException ex)
            {
                // 在實際應用中，可能需要更多的錯誤處理邏輯，例如記錄錯誤、拋出自定義例外等。
                throw new InvalidOperationException("JSON deserialization failed.", ex);
            }
        }

        public static string Serialize(object obj)
        {
            try
            {
                string jsonString = JsonConvert.SerializeObject(obj);
                return jsonString;
            }
            catch (JsonException ex)
            {
                // 在實際應用中，可能需要更多的錯誤處理邏輯，例如記錄錯誤、拋出自定義例外等。
                throw new InvalidOperationException("JSON serialization failed.", ex);
            }
        }
    }

}
