using CQR.Application.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace CQRAPI.Controllers
{
    [ApiController]
    //[AllowAnonymous]
    [Route("api/[controller]")]
    //[Route("[controller]")]
    public class ERPMasterController : ControllerBase
    {
        // private UserGPL user;
        //private readonly IHttpContextAccessor _contextAccessor;
        //private readonly ILogger<SysConfigurController> _logger;
        // private IBusServicesColleciton busServicesColleciton;
        private IERPMasterQueryRepository erpMasterQueryRepository;
        public ERPMasterController(IERPMasterQueryRepository oERPMasterQueryRepository
            //IHttpContextAccessor httpContextAccessor
            )
        {
            //busServicesColleciton = _BusServicesColleciton;
            //_contextAccessor = httpContextAccessor;
            erpMasterQueryRepository = oERPMasterQueryRepository;
            //user = HelperUtil.GetHttpContextUserInfo(_contextAccessor.HttpContext);
        }

        // [HttpPost]
        // [Route("LookupCUPNByCustomerCode")]
        // //[Route("LookupCUPNByCustomerCode/salesOrg/{vkorg}/customer/{customer}")]
        // public async Task<IActionResult> GetSalesPlanPivotSearchWithCustomerPN([FromBody] LookkupCriteriaCustomerPN searchCriteria)
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.LookupCUPNByCustomerCodeOrName(searchCriteria.VKORG, searchCriteria.CUSTOMER);
        //     return Ok(result);
        // }

        // // '/materialPrice/salesOrg/:salesOrg?/oem/:oem/customer/:customer?/part/:part/dchl/:dchl?',
        // //      materialPrice/salesOrg/535A/oem/502172/customer//part/17040189-02/dchl/10
        [HttpGet]
        [Route("OEMHiearchy")]
        public async Task<ActionResult> getOEMHiearchy()
        {
            var result = await erpMasterQueryRepository.getOEMHiearchy();
            return Ok(result);
        }
        // [HttpGet]
        // [Route("masterSapSalesOrgAll")]
        // public async Task<IActionResult> GetAllERPSalesOrgPlant()
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.GetAllERPSalesOrgPlant();
        //     return Ok(result);
        // }
        // [HttpPost]
        // [Route("PriceSearchInSAP/multipleEntities")]
        // public async Task<IActionResult> SearchPriceSapPriceByInputEntities(
        //     [FromBody] IList<PriceSAPSearchCriteria> oSearchCriterias)
        // {
        //     string today = "";
        //     var busSAPService = busServicesColleciton.busSAPService;

        //     string? vKORG = oSearchCriterias.First().VKORG;
        //     //busSAPService.Initialize(vkorg: vKORG);
        //     var result = await busServicesColleciton.busSAPService
        //         .SearchBundlePriceSapPriceInEntitiesBySearchS(user, today, oSearchCriterias, GPL_Core.Enum.EnumPriceType.BasePrice);
        //     return Ok(result);
        // }
        // [HttpPost]
        // [Route("GetTotalSAPPriceFromSAPRFCByWorkflowId/WorkflowId/{WorkflowId}")]
        // public async Task<IActionResult> GetTotalSAPPriceFromSAPRFCByWorkflowId(int WorkflowId)
        // {
        //     var busSAPService = busServicesColleciton.busSAPService;
        //     var vKORG = busServicesColleciton.repositoryCollection.pOHeaderDao.GetGPLHeaderEntityByWorkflowId(WorkflowId).SALES_ORG;

        //     //busSAPService.Initialize(vkorg: vKORG);
        //     var searchItems = await busServicesColleciton.busSAPService.GetTotalSAPPriceFromSAPRFCByWorkflowId(user, WorkflowId);
        //     //todo call prodcedure ,reportDao -
        //     var rsult2 = await busServicesColleciton.repositoryCollection.reportDao.sp_105_sync_previous_po_for_validFromTo_by_workflow_prices_calculate(user.name, searchItems);

        //     return Ok(rsult2);
        // }
        // [HttpPost]
        // [Route("PriceSearchInSAP/QueueKey/{queueKey}")]
        // public async Task<IActionResult> searchPriceSapPrice([FromBody] SearchCriteriaInPO searchCriterias)
        // {
        //     var today = "";
        //     //::Note must finished signed
        //     var dataCostRecordsNoneWorkflow = await busServicesColleciton.repositoryCollection.reportDao.GetGPLSoldToNoneWorkflowByQueueKeyAsync(user.name
        //         , searchCriterias.queueKey);

        //     var dataCostRecords = busServicesColleciton.repositoryCollection.mapper.Map<IEnumerable<view_gpl_report_node_PO_Tree_SoldTo>>(dataCostRecordsNoneWorkflow);
        //     //var lstdataCostRecordsNoneWorkflow = dataCostRecords.ToList();
        //     foreach (var item in dataCostRecords)
        //     {
        //         item.ValidFrom = "";
        //         item.ValidTo = "";
        //     }
        //     var oVkorg = dataCostRecords.FirstOrDefault()!.SALES_ORG;
        //     //busServicesColleciton.busSAPService.Initialize(vkorg: oVkorg);
        //     var result = await busServicesColleciton.busSAPService.SearchPriceSapPrice(user, today, dataCostRecords.ToList());
        //     return Ok(result);
        // }
        // [HttpPost]
        // [Route("PriceSearchInSAPApproved/QueueKey/{queueKey}")]
        // public async Task<IActionResult> searchPriceSapPriceApproved([FromBody] SearchCriteriaInPO searchCriterias)
        // {
        //     var today = "";
        //     //::Note must finished signed
        //     var dataCostRecords = busServicesColleciton.repositoryCollection.reportDao.GetMatrixTreeNodesByQueukeyAndNodeIds(user.name, searchCriterias.queueKey);
        //     if (!dataCostRecords.Any())
        //     {
        //         throw new Exception("should not be draft PO Status!");
        //     }
        //     var oVkorg = dataCostRecords.FirstOrDefault()!.SALES_ORG;
        //     //busServicesColleciton.busSAPService.Initialize(vkorg: oVkorg);
        //     var result = await busServicesColleciton.busSAPService.SearchPriceSapPrice(user, today, dataCostRecords.ToList());
        //     return Ok(result);
        // }
        // [HttpGet]
        // [Route("masterSapIncoterm")]
        // public async Task<IActionResult> GetSapIncoterm()
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.GetSapIncoterm();
        //     return Ok(result);
        // }
        // [HttpGet]
        // [Route("masterSapPlantAndOEMGroupAndOEMroupInAuth")]
        // public async Task<IActionResult> GetERPMasterCodeWithByAuth()
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.GetERPMasterCodeWithByAuth(user.name);
        //     return Ok(result);
        // }
        // [HttpGet]
        // [Route("masterSapOEM")]
        // public async Task<IActionResult> GetERPOEMs()
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.GetSAPOEMByOrg("535A");
        //     return Ok(result);
        // }
        // [HttpGet]
        // [Route("masterSapOEMAuth/salesOrg/{salesOrg}")]
        // public async Task<IActionResult> getERPOEMAuthBySaleOrg(string salesOrg)
        // {
        //     if (string.IsNullOrEmpty(salesOrg)) throw new ArgumentException("No salesOrg found");

        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.GetERPOEMAuth(salesOrg, user.name);
        //     return Ok(result);
        // }


        // [HttpPost]
        // [Route("checkMasterCustomerPNExistedByPOTree/salesOrg/{salesOrg}/includeMATNR/{includeMATNR}")]
        // public async Task<IActionResult> CheckMasterCustomerPNExistedByPOTree(string salesOrg, int includeMATNR, IList<CheckExistCustomerPNInERPCriteria> oCriterias)
        // {
        //     if (string.IsNullOrEmpty(salesOrg))
        //         return BadRequest("SalesOrg is undefined.");

        //     var result = await busServicesColleciton.busSAPService.CheckMasterCustomerPNExistedByPOTree(user, salesOrg, (EnumrCheckPNLink)includeMATNR, oCriterias);
        //     return Ok(result);
        // }
        // [HttpGet]
        // [Route("masterSapProduct")]
        // public async Task<IActionResult> getERPProductInChild()
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.GetERPProductInChild();
        //     return Ok(result);
        // }
        // [HttpGet]
        // [Route("masterSapSalesOrg")]
        // public async Task<IActionResult> GetERPSalesOrgPlant()
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.GetERPSalesOrgPlant();
        //     return Ok(result);
        // }
        // [HttpGet]
        // [Route("CustomerDayLimit/salesOrg/{salesOrg}")]
        // public async Task<IActionResult> GetPlantDayLimitBySalesOrg(string salesOrg)
        // {
        //     if (string.IsNullOrEmpty(salesOrg)) throw new ArgumentNullException(nameof(salesOrg));
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.GetPlantDayLimitBySalesOrg(salesOrg);
        //     return Ok(result);
        // }
        // [HttpGet]
        // [Route("masterSapSupplierCodes")]
        // public async Task<IActionResult> SearchERPSupplierCodeInfos()
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.SearchERPCustomerInfos();
        //     var mapResults = result
        //   .Where(s => s.EIKTO != "")
        //   .Select(s => new { s.EIKTO, s.VKORG, s.HKUNNR }).Distinct().ToList();
        //     return Ok(result);
        // }
        // [HttpGet]
        // [Route("masterSapSupplierCode/salesOrg/{salesOrg}/oem/{oem}")]
        // public async Task<IActionResult> SearchERPSupplierCodeByOrgAndOEM()
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.SearchERPCustomerInfos();
        //     var mapResults = result.Where(s => s.EIKTO != "")
        //   .Select(s => new { s.EIKTO, s.VKORG, s.HKUNNR }).Distinct().ToList();
        //     return Ok(result);
        // }

        // [HttpGet]
        // [Route("masterDistributePanels/salesOrg/{salesOrg}")]
        // public async Task<IActionResult> GetERPDistributePanelsBySalesOrg(string salesOrg)
        // {
        //     if (string.IsNullOrEmpty(salesOrg)) throw new ArgumentNullException(nameof(salesOrg));
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.GetERPDistributePanelsBySalesOrg(salesOrg);
        //     return Ok(result);
        // }
        // [HttpPost]
        // [Route("PriceSearchInSAP/manuallyCriteria")]
        // public async Task<IActionResult> SearchPriceSapPriceByManuallyCriteria([FromBody] ManuallySearchCriteria oCriteria)
        // {
        //     if (oCriteria == null)
        //     {
        //         // If the provided criteria is null, return a 400 Bad Request
        //         return BadRequest("Invalid search criteria");
        //     }
        //     //busServicesColleciton.busSAPService.Initialize(oCriteria.SALES_ORG);

        //     var lstCriterias = new List<ManuallySearchCriteria>() { oCriteria };
        //     var result = await busServicesColleciton.busSAPService.SearchPriceSapPriceInEntities(user, oCriteria.valdOn,
        //        lstCriterias);
        //     return Ok(result);
        // }

        // [HttpPost]
        // [Route("analysisMateiralPivotSourceByCriteria")]
        // public IActionResult AnalysisMateiralPivotSourceByCriteria([FromBody] SearchMatnrERPCriteria req)
        // {
        //     var searchMaterial = req.MATNR;
        //     if (string.IsNullOrEmpty(searchMaterial))
        //     {
        //         throw new ArgumentException("Parameter SearchMaterial is required");
        //     }
        //     var oResultSapMaterial = busServicesColleciton.repositoryCollection.erpmasterDao.GetAnalysisSapMateiral(
        //         searchMaterial);
        //     var oResultSapPlantMaterial = busServicesColleciton.repositoryCollection.erpmasterDao.GetAnalysisSapPlantMateiral(
        //         searchMaterial);
        //     var oResultMateriaColleciton = new
        //     {
        //         BW4PZMaterial = oResultSapMaterial,
        //         BW4PZMatPlant = oResultSapPlantMaterial
        //     };
        //     return Ok(oResultMateriaColleciton);
        // }

        // [HttpPost]
        // [Route("analysisCustomerPartByCriteria")]
        // public IActionResult AnalysisCustomerPartByCriteria([FromBody] SearchCustomerERPCriteria oCriteria)
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.SearchERPMasterCustomerPNByDynamicCriterias(
        //        oCriteria);
        //     return Ok(result);
        // }

        // [HttpPost]
        // [Route("analysisCustomerHiearchyByCriteria")]
        // public IActionResult AnalysisCustomerHiearchyByCriteria([FromBody] SearchCustomerHiearchyERPCriteria oCriteria)
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.SearchERPCustomerHiearchyByDynamicCriterias(
        //       oCriteria);
        //     return Ok(result);
        // }

        // [HttpPost]
        // [Route("FilterCustomerBySalesOrgOEM")]
        // public IActionResult FilterCustomerBySalesOrgOEM([FromBody] SearchCustomerSoldTo oCriteria)
        // {
        //     var result = busServicesColleciton.repositoryCollection.erpmasterDao.FilterCustomerBySalesOrgOEM(
        //       oCriteria.VKORG, oCriteria.OEM);
        //     return Ok(result);
        // }
    }
}
