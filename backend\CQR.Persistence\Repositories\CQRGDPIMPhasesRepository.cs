﻿

using CQR.Domain.Constants;
using CQR.Domain.CQR_GDPIMPhases;
using Microsoft.EntityFrameworkCore;

namespace CQR.Persistence.Command.Repositories;

public class CQRGDPIMPhasesRepository : EntityRepository<CQR_GDPIMPhase, int>, ICQRGDPIMPhasesRepository
{
    public CQRGDPIMPhasesRepository(CQRDbContext context) : base(context) { }

    public async Task<CQR_GDPIMPhase> GetByQueueAndPhase(int queueKey, int phase)
    {
        return await _context.Set<CQR_GDPIMPhase>().Where(x => x.QueueKey == queueKey && x.GDPIMPhase == phase).FirstOrDefaultAsync();
    }

    public  async Task<bool> SaveGDPEPAsync(int iPhase, int iQueueKey, CQR_GDPIMPhase phaseData)
    {
        //throw new NotImplementedException();
        // 嘗試從 DB 取得現有資料
        var entity = await _context.CQR_GDPIMPhase
       .FirstOrDefaultAsync(p => p.QueueKey == iQueueKey && p.GDPIMPhase == iPhase);

        if (entity == null)
        {
            entity = new CQR_GDPIMPhase
            {
                QueueKey = iQueueKey,
                GDPIMPhase = iPhase,
                // 若要複製 phaseData 的其他欄位，也可以手動設
                Comment = phaseData.Comment
            };


            await _context.CQR_GDPIMPhase.AddAsync(entity);
        }

        // UPDATE 欄位
        //entity.co = phaseData.Comment;

        for (int i = 1; i <= 7; i++)
        {
            var propertyName = $"Volume{i}";
            var value = phaseData.GetType().GetProperty(propertyName)?.GetValue(phaseData);
            entity.GetType().GetProperty(propertyName)?.SetValue(entity, value);
            //entity.GetType().GetProperty($"Volume{i}")?.SetValue(entity, phaseData.Volume1[i]);
        }

        for (int i = 1; i <= 10; i++)
        {
            //var value = phaseData[i];
            var propertyName = $"SellPrice{i}";
            var value = phaseData.GetType().GetProperty(propertyName)?.GetValue(phaseData);

            entity.GetType().GetProperty($"SellPrice{i}")?.SetValue(entity, value);
            //entity.GetType().GetProperty($"SellPrice{i}")?.SetValue(entity, phaseData.SellPrice1[i]);
            //entity.GetType().GetProperty($"SellPrice{i}")?.SetValue(entity, phaseData.sSellPrice[i]);
        }

        for (int i = 1; i <= CqrConstants.GDPEP_ROLE_COUNT; i++)
        {
            var propertyName1 = $"ApprovalUser{i}";
            var propertyName2 = $"ApprovalDate{i}";
            var propertyName3 = $"ApprovalState{i}";
            var propertyName4 = $"RequiredState{i}";

            var value1 = phaseData.GetType().GetProperty(propertyName1)?.GetValue(phaseData);
            var value2 = phaseData.GetType().GetProperty(propertyName2)?.GetValue(phaseData);
            var value3 = phaseData.GetType().GetProperty(propertyName3)?.GetValue(phaseData);
            var value4 = phaseData.GetType().GetProperty(propertyName4)?.GetValue(phaseData);

            entity.GetType().GetProperty($"ApprovalUser{i}")?.SetValue(entity, value1);
            entity.GetType().GetProperty($"ApprovalDate{i}")?.SetValue(entity, value2);
            entity.GetType().GetProperty($"ApprovalState{i}")?.SetValue(entity, value3);
            entity.GetType().GetProperty($"RequiredState{i}")?.SetValue(entity, value4);

            //phaseDatasApprovalStateDbs[i] = phaseData.sApprovalState[i]; // 同步狀態
        }

        //entity.ReleaseDate = phaseData.ReleaseDate);
        //entity.Category = phaseData.Category;

        // 儲存資料
        await _context.SaveChangesAsync();
        return true;
    }

    // 工具方法：轉日期
    //private DateTime? ParseNullableDate(string dateStr)
    //{
    //    return DateTime.TryParse(dateStr, out var result) ? result : (DateTime?)null;
    //}
}
