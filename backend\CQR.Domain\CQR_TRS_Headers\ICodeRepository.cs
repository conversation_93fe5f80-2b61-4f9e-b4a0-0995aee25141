﻿using CQR.Domain.CQR_TRS_Headers;

namespace CQR.Domain.TRS_Headers;

public interface ICodeRepository
{
    Task<Dictionary<string, List<CQR_TRS_Header>>> GetDictCode();
    Task<IEnumerable<CQR_TRS_Header>> GetGPLCodesAsync();
    Task<IList<string>> GetGPLCodesByTableNameInAsync(string table_name);

    Task<bool> updateById(string userLogin, CQR_TRS_Header parament);
    Task<bool> createCode(string userLogin, string table_name, string desc);
}
