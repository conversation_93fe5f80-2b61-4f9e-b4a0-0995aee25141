﻿
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CQR.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ValuesController : ControllerBase
    {
        [HttpGet("secure")]
        [Authorize]
        public IActionResult GetSecureValue()
        {
            var user = User.Identity?.Name;
            return Ok($"Hello, authenticated user: {user}");
        }
        [HttpGet("secure-group")]
        [Authorize(Roles = "Your-Group-ID-or-Name")]
        public IActionResult AdminOnly() => Ok("Hello Admin");
    }
}
