using System.ComponentModel.DataAnnotations;

namespace CQR.Application.Dto
{
    public class CQRSearchResultDto
    {
        public int QueueKey { get; set; }

        [Display(Name = "CQR #")]
        public string CQRNumber { get; set; } = string.Empty;

        [Display(Name = "Project Number")]
        public string ProjectNbr { get; set; } = string.Empty;

        [Display(Name = "Revision")]
        public string RevNbr { get; set; } = string.Empty;

        [Display(Name = "OEM Group")]
        public string? OEMGroup { get; set; }

        [Display(Name = "OEM Customer")]
        public string? OEMCustomer { get; set; }

        [Display(Name = "Account Manager")]
        public string? AccountManagerName { get; set; }

        [Display(Name = "Estimator")]
        public string? EstimatorName { get; set; }

        [Display(Name = "Engineering Director")]
        public string? PGMName { get; set; }

        [Display(Name = "Program Manager")]
        public string? PDMName { get; set; }

        [Display(Name = "Engineering Manager")]
        public string? EngMgrName { get; set; }

        [Display(Name = "Responsible Design Eng.")]
        public string? PRDName { get; set; }

        [Display(Name = "Product Description")]
        public string? ProductDesc { get; set; }

        [Display(Name = "Gate Exit")]
        public string? GateExitTRS { get; set; }

        [Display(Name = "Status")]
        public string? StatusTRS { get; set; }

        [Display(Name = "Release Date")]
        public DateTime? ReleaseDate { get; set; }

        [Display(Name = "Issue Date")]
        public DateTime? FRANIssueDate { get; set; }

        [Display(Name = "Due Date from Engineering")]
        public DateTime? DueDateFromEng { get; set; }

        [Display(Name = "Due Date to B&E")]
        public DateTime? DueDateToBnE { get; set; }

        [Display(Name = "Quote Response Due Date")]
        public DateTime? CustQuoteDueDate { get; set; }

        [Display(Name = "Opening Meeting Date")]
        public DateTime? OpeningMeetingDate { get; set; }

        [Display(Name = "CQR Description")]
        public string? FRANDesc { get; set; }

        [Display(Name = "Vehicle")]
        public string? PlatformName { get; set; }

        [Display(Name = "Model Year")]
        public string? ModelYearTRS { get; set; }

        [Display(Name = "Volume Per Annum")]
        public string? VolumePerAnnum { get; set; }

        [Display(Name = "Approx. Annual Value $")]
        public string? ApproxAnnualValue { get; set; }

        [Display(Name = "Background Info")]
        public string? BkRndInfComments { get; set; }

        [Display(Name = "Antares Unique Number")]
        public string? UniqueNumber { get; set; }

        [Display(Name = "Eng. Pkg. Comment")]
        public string? EngPkgComments { get; set; }

        [Display(Name = "Manufacturing Site")]
        public string? MfgSiteTRS { get; set; }

        // Quote Report 額外欄位
        [Display(Name = "Customer Response")]
        public string? QSCustomerResp { get; set; }

        [Display(Name = "Comments")]
        public string? QSComments { get; set; }

        // 內部使用
        public bool IsSelected { get; set; }
        public DateTime? LastUpdatedDate { get; set; }
    }

    public class CQRSearchCriteriaDto
    {
        public string? Field { get; set; }
        public string? Operator { get; set; }
        public object? Value { get; set; }
        public string? LogicalOperator { get; set; } = "AND";
    }

    public class CQRSearchFilterDto
    {
        public List<CQRSearchCriteriaDto> Criteria { get; set; } = new();
        public string? QuickSearch { get; set; }
        public List<string> SelectedColumns { get; set; } = new();
        public bool SaveAsPreset { get; set; }
        public string? PresetName { get; set; }
    }
}