﻿using System.Text;


// decisionCore, Version=1.0.9056.27004, Culture=neutral, PublicKeyToken=null
// decisionCore.frmAttachmentView
using ICSharpCode.SharpZipLib.Zip;
using Microsoft.AspNetCore.Http;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using Microsoft.Extensions.Configuration;
using CQR.Domain.DecisionCore;

namespace CQR.Core.DecisionCore;
public class frmAttachmentView : corePage
{
    private object designerPlaceholderDeclaration;

    public frmAttachmentView(IConfiguration configuration, IHttpContextAccessor httpContextAccessor) : base(configuration, httpContextAccessor)
    {
    }

    //public frmAttachmentView()
    //{
    //    base.Init += Page_Init;
    //    base.Load += Page_Load;
    //}

    private void InitializeComponent()
    {
    }

    private void Page_Init(object sender, EventArgs e)
    {
        InitializeComponent();
    }

    private void Page_Load(object sender, EventArgs e)
    {
        //_ = base.Request.ServerVariables["APPL_PHYSICAL_PATH"] + "Zip\\";
        //unZipThatFile(base.Request["FileToView"]);
    }

    public string mStripPath(string asPath)
    {
        string result = asPath;
        checked
        {
            for (int i = Strings.Len(asPath); i >= 1; i += -1)
            {
                if ((Operators.CompareString(Strings.Mid(asPath, i, 1), "\\", TextCompare: false) == 0) | (Operators.CompareString(Strings.Mid(asPath, i, 1), "/", TextCompare: false) == 0) | (Operators.CompareString(Strings.Mid(asPath, i, 1), ":", TextCompare: false) == 0))
                {
                    result = Strings.Mid(asPath, i + 1, Strings.Len(asPath) - i);
                    break;
                }
            }
            return result;
        }
    }

    public string Get_AttachDirectory()
    {
        //string @string = Strings.LCase(HttpContext.Current.Request.ServerVariables["PATH_INFO"]);
        string @string = HttpContext.Request.Path.Value.ToLower();

        string text = "DIR";
        if (Strings.InStr(@string, "_staging") != 0)
        {
            text += "STAGE";
        }
        string text2 = "";//cnExecuteForSingleValue("SELECT _desc FROM TRS_Header WHERE table_name = 'ATTACH' and table_entry = '" + text + "'");
        //string text2 = cnExecuteForSingleValue("SELECT _desc FROM TRS_Header WHERE table_name = 'ATTACH' and table_entry = '" + text + "'");
        if (Operators.CompareString(Strings.LCase(modTools.APP_NAME), "concerns", TextCompare: false) == 0)
        {
            text2 += "Concerns\\";
        }
        if (Operators.CompareString(Strings.LCase(modTools.APP_NAME), "ncr-eng", TextCompare: false) == 0)
        {
            text2 += "CTLNCR\\";
        }
        if (Operators.CompareString(Strings.LCase(modTools.APP_NAME), "ncrmfg", TextCompare: false) == 0)
        {
            text2 += "NCRMFG\\";
        }
        return text2;
    }

    public string unZipThatFileEx(string asFile)
    {
        ZipInputStream zipInputStream = new ZipInputStream(System.IO.File.OpenRead(Get_AttachDirectory() + asFile));
        new StringBuilder();
        ZipEntry nextEntry = zipInputStream.GetNextEntry();
        if (Information.IsNothing(nextEntry))
        {
            zipInputStream.Close();
            return "Error";
        }
        int num = 2048;
        byte[] array = new byte[2049];
        string text = Strings.Replace(nextEntry.Name.ToString(), "?", "_");
        //if (nextEntry.IsFile)
        //{
        //    string text2 = HttpContext.Current.Request.ServerVariables["APPL_PHYSICAL_PATH"] + "Zip\\";
        //    FileStream fileStream = new FileStream(text2 + text, FileMode.Create);
        //    while (num != 0)
        //    {
        //        num = zipInputStream.Read(array, 0, array.Length);
        //        if (num > 0)
        //        {
        //            fileStream.Write(array, 0, num);
        //        }
        //    }
        //    fileStream.Close();
        //}
        zipInputStream.Close();
        return text;
    }

    private void unZipThatFile(string asFile)
    {
        string text = "";// HttpContext.Current.Request.ServerVariables["APPL_PHYSICAL_PATH"] + "Zip\\";
        //string text = HttpContext.Current.Request.ServerVariables["APPL_PHYSICAL_PATH"] + "Zip\\";
        string text2 = "";
        string text3 = text2;
        checked
        {
            if (Operators.CompareString(text3, "", TextCompare: false) != 0)
            {
                text3 = Strings.Replace(text + mStripPath(text3), "\\", "/");
                string text4 = text3;
                text3 = Strings.LCase(text3);
                if (false & (Operators.CompareString(Strings.LCase(Strings.Right(text3, 4)), ".zip", TextCompare: false) != 0))
                {
                    text3 = Strings.Replace(text3, "c:/inetpub/wwwroot/", "/");
                    text3 = Strings.Replace(text3, "d:/inetpub/wwwroot/", "/");
                    text3 = Strings.Replace(text3, "d:/projects/trw/web/", "/");
                    base.Response.Redirect(text3);
                }
                else
                {
                    text3 = decodeFileName(text3);
                    string value = new FileInfo(text3).Length.ToString();
                    //base.Response.ClearHeaders();
                    base.Response.Clear();
                    string value2 = Strings.Mid(text3, Strings.InStrRev(text3, ".") + 1);
                    switch (Strings.LCase(value2))
                    {
                        case "docm":
                            base.Response.ContentType = "application/vnd.ms-word.document.macroEnabled.12";
                            break;
                        case "docx":
                            base.Response.ContentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                            break;
                        case "dotm":
                            base.Response.ContentType = "application/vnd.ms-word.template.macroEnabled.12";
                            break;
                        case "dotx":
                            base.Response.ContentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.template";
                            break;
                        case "potm":
                            base.Response.ContentType = "application/vnd.ms-powerpoint.template.macroEnabled.12";
                            break;
                        case "potx":
                            base.Response.ContentType = "application/vnd.openxmlformats-officedocument.presentationml.template";
                            break;
                        case "ppam":
                            base.Response.ContentType = "application/vnd.ms-powerpoint.addin.macroEnabled.12";
                            break;
                        case "ppsm":
                            base.Response.ContentType = "application/vnd.ms-powerpoint.slideshow.macroEnabled.12";
                            break;
                        case "ppsx":
                            base.Response.ContentType = "application/vnd.openxmlformats-officedocument.presentationml.slideshow";
                            break;
                        case "pptm":
                            base.Response.ContentType = "application/vnd.ms-powerpoint.presentation.macroEnabled.12";
                            break;
                        case "pptx":
                            base.Response.ContentType = "application/vnd.openxmlformats-officedocument.presentationml.presentation";
                            break;
                        case "xlam":
                            base.Response.ContentType = "application/vnd.ms-excel.addin.macroEnabled.12";
                            break;
                        case "xlsb":
                            base.Response.ContentType = "application/vnd.ms-excel.sheet.binary.macroEnabled.12";
                            break;
                        case "xlsm":
                            base.Response.ContentType = "application/vnd.ms-excel.sheet.macroEnabled.12";
                            break;
                        case "xlsx":
                            base.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                            break;
                        case "xltm":
                            base.Response.ContentType = "application/vnd.ms-excel.template.macroEnabled.12";
                            break;
                        case "xltx":
                            base.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.template";
                            break;
                        default:
                            base.Response.ContentType = "text/plain";
                            break;
                    }
                    //base.Response.Charset = "";
                    if (Strings.LCase(text2).Contains("_rev"))
                    {
                        string text5 = "SELECT ";
                        text5 += " CASE WHEN PATINDEX('%_Rev%', AttachTitle) = 0 THEN";
                        text5 += "   attachTitle";
                        text5 += " ELSE";
                        text5 += "   LEFT(AttachTitle,PATINDEX('%_Rev%', AttachTitle)-1)+SUBSTRING(AttachTitle,LEN(AttachTitle)-PATINDEX('%.%', REVERSE(AttachTitle))+1,999)";
                        text5 += " End";
                        text5 += " FROM ATTDIR_AttachFileDirHeader AFD";
                        text5 += " LEFT JOIN ATTDIR_CheckoutLog ACL ON ACL.QueueKey=AFD.QueueKey";
                        text5 = text5 + " WHERE FileLocation='" + asFile + "'";
                        string text6 = "";//cnExecuteForSingleValue(text5);
                        int num = Strings.InStr(text6, "_");
                        text4 = ((num <= 0) ? text6 : Strings.Replace(Strings.Mid(text6, num + 1), ",", "%2c"));
                        if (!Strings.UCase(text4).EndsWith(Strings.UCase(value2)))
                        {
                            text4 = text4 + "." + Strings.LCase(value2);
                        }
                    }
                    else
                    {
                        if (text2.Contains("_"))
                        {
                            text2 = Strings.Mid(text2, Strings.InStr(text2, "_") + 1);
                        }
                        text4 = text2;
                    }
                    //base.Response.Clear();
                    //base.Response.AddHeader("Content-Disposition", "attachment;filename=" + frmAttachment.encodeFileName_Custom(text4));
                    //base.Response.AddHeader("Content-Length", value);
                    //base.Response.WriteFile(text3);
                    //base.Response.Flush();
                    //base.Response.Close();
                }
            }
            else
            {
                //base.Response.Write("Error During Unzip.");
            }
            //base.Response.End();
        }
    }

    public string decodeFileName(string strFile)
    {
        strFile = Strings.UCase(strFile);
        strFile = Strings.Replace(strFile, "%C2%A0", " ");
        strFile = Strings.Replace(strFile, "%20", " ");
        strFile = Strings.Replace(strFile, "%25", "%");
        strFile = Strings.Replace(strFile, "%21", "!");
        strFile = Strings.Replace(strFile, "%23", "#");
        strFile = Strings.Replace(strFile, "%3F", "=");
        strFile = Strings.Replace(strFile, "%40", "@");
        strFile = Strings.Replace(strFile, "%24", "$");
        strFile = Strings.Replace(strFile, "%2B", "+");
        strFile = Strings.Replace(strFile, "%2F", "/");
        strFile = Strings.Replace(strFile, "%3A", ":");
        strFile = Strings.Replace(strFile, "%3B", ";");
        strFile = Strings.Replace(strFile, "%3F", "?");
        strFile = Strings.Replace(strFile, "%2C", ",");
        strFile = Strings.Replace(strFile, "%26", "&");
        strFile = Strings.Replace(strFile, "%5B", "[");
        strFile = Strings.Replace(strFile, "%5D", "]");
        strFile = Strings.Replace(strFile, "%7E", "~");
        return strFile;
    }
}
