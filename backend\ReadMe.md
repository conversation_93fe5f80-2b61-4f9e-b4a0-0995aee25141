﻿✅ Azure 端設定（快速說明）
登入 Azure Portal

前往「Azure Active Directory」→「App registrations」

建立 Web API：

Client ID → 用來填入 appsettings.json

「Expose an API」 → 設定 Application ID URI（格式：api://<client-id>）

新增 Scope：access_as_user

建立一個前端 App，授權它可以存取這個 API scope。

✅ 測試：如何取得 JWT Token 呼叫 API？
用 Postman：
bash
Copy
Edit
POST https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/token

Body (x-www-form-urlencoded)
- client_id=...        // 前端 App Client ID
- client_secret=...    // 前端 App Secret
- scope=api://{api-client-id}/access_as_user
- grant_type=client_credentials
回傳的 access_token 加入 Header 測試 API：

pgsql
Copy
Edit
GET /api/values/secure
Authorization: Bearer {access_token}


✅ 前置作業
登入 Azure Portal

建立 App registration：

Redirect URI：不需要（Web API 不需要登入 UI）

Expose an API → Application ID URI（例如：api://{client-id}）

建立 scope，例如：access_as_user

記下以下資訊（稍後會用）：

Tenant ID

Client ID（Application ID）

Application ID URI（例：api://xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx）

✅ 專案設定
1. 安裝 NuGet 套件
bash
Copy
Edit
dotnet add package Microsoft.Identity.Web
dotnet add package Microsoft.AspNetCore.Authentication.JwtBearer

2. appsettings.json 設定
json
Copy
Edit
{
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "your-tenant-id",
    "ClientId": "your-api-app-client-id",
    "Audience": "api://your-api-app-client-id"
  }
}

✅ Postman 測試方式（取得 JWT Token）
從你註冊的前端 App（或用 Postman）呼叫：

bash
Copy
Edit
POST https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/token

Body (x-www-form-urlencoded):
- client_id
- scope: api://{your-api-client-id}/access_as_user
- grant_type: client_credentials 或 authorization_code
- client_secret
使用回傳的 access_token 呼叫你的 Web API，加入 Header：

css
Copy
Edit
Authorization: Bearer {access_token}



# CQR System Development

## 開發文件
- 程式規格書: `docs/specs/cqr-system-specs.md`
- API 文件: `docs/api/`

## 使用 Claude Code 協助開發

### 常用指令範例：
```bash
# 根據規格生成程式碼
claude "implement the Antares dropdown cascade functionality from specs"

# 程式碼審查
claude "review this code against the security requirements in specs"

# 生成測試案例
claude "generate unit tests for the IHS data validation based on specs"

6.2 持續更新規格書
當系統需求變更時，先更新規格書，然後使用 Claude Code 來協助更新相關程式碼：
bashclaude "規格書已更新，請幫我找出需要修改的程式碼檔案"