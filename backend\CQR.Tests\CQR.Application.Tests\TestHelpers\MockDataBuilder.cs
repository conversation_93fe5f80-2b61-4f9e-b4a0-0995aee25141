using CQR.Application.Dto;
using CQR.Application.Dto.CQRHead;
using CQRLIB.CQR_Headers;
using CQRLIB.CQR_IHSFolders;

namespace CQR.Application.Tests.TestHelpers;

public static class MockDataBuilder
{
    public static CQR_Header CreateTestCQRHeader(int queueKey = 12345, string status = "Active")
    {
        return new CQR_Header
        {
            QueueKey = queueKey,
            CQRStatus = status,
            ActionComments = "Test comments for unit testing",
            CreatedDate = DateTime.Now.AddDays(-7),
            ModifiedDate = DateTime.Now
        };
    }

    public static CQRHeaderCollection CreateTestCQRHeaderCollection(int queueKey = 12345)
    {
        return new CQRHeaderCollection
        {
            iCQRHeader = CreateTestCQRHeader(queueKey),
            cUserRoles = CreateTestUserRoles(),
            tGateway = CreateTestGatewayArray(),
            ihsFolderRecords = CreateTestIHSFolderList(),
            sDbsCommentsAction = "Test action comments",
            sTotalCommentsAction = "Total test comments",
            routingTasks = CreateTestRoutingTaskList()
        };
    }

    public static UserRoles CreateTestUserRoles()
    {
        return new UserRoles
        {
            bIsAMgr = true,
            bIsCost = false,
            bIsPgm = false,
            bIsPetmOnly = false,
            sManagerFin = "TestFinManager",
            sManagerPres = "TestPresManager",
            sManagerFran = "TestFranManager",
            sManagerBids = "TestBidsManager",
            sManagerBeb = "TestBebManager",
            sManagerCmgr = "TestCmgrManager"
        };
    }

    public static GDPEP_APPROVAL[] CreateTestGatewayArray()
    {
        return new GDPEP_APPROVAL[]
        {
            new GDPEP_APPROVAL(),
            new GDPEP_APPROVAL(),
            new GDPEP_APPROVAL()
        };
    }

    public static List<CQR_IHSFolder> CreateTestIHSFolderList()
    {
        return new List<CQR_IHSFolder>
        {
            new CQR_IHSFolder 
            { 
                QueueKey = 12345, 
                FolderName = "Test Folder 1" 
            },
            new CQR_IHSFolder 
            { 
                QueueKey = 12345, 
                FolderName = "Test Folder 2" 
            }
        };
    }

    public static List<RoutingTaskDto> CreateTestRoutingTaskList()
    {
        return new List<RoutingTaskDto>
        {
            new RoutingTaskDto 
            { 
                TaskId = 1, 
                TaskName = "Test Task 1",
                Status = "Pending"
            },
            new RoutingTaskDto 
            { 
                TaskId = 2, 
                TaskName = "Test Task 2",
                Status = "Completed"
            }
        };
    }

    public static List<string> CreateTestUserRolesList()
    {
        return new List<string>
        {
            "AMGR",
            "CQRHRCOST",
            "FRAN",
            "PSR"
        };
    }

    public static CQRHeaderQueryRequest CreateTestCQRHeaderQueryRequest()
    {
        return new CQRHeaderQueryRequest
        {
            PageIndex = 1,
            PageSize = 10,
            OrderByProperty = "QueueKey",
            Ascending = true
        };
    }
}