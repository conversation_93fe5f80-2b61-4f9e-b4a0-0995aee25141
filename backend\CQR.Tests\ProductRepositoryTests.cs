using CQR.Persistence.Command.Repositories;
using Microsoft.EntityFrameworkCore;

namespace CQRLIB.Tests;
public class ProductRepositoryTests
{
    private DbContextOptions<CQRDbContext> GetInMemoryDbOptions()
    {
        return new DbContextOptionsBuilder<CQRDbContext>()
            .UseInMemoryDatabase(databaseName: "TestDb") // 使用 In-Memory Database
            .Options;
    }
    [Fact]
    public async Task AddAsync_AddsProductToDatabase()
    {
        //    var options = GetInMemoryDbOptions();
        //    using (var context = new CQrDbContext(options))
        //    {
        //        var repository = new ProductRepository(context);
        //        var product = new Product { Name = "Test Product", Price = 2, Stock = 3 };
        //        await repository.AddAsync(product);
        //        await context.SaveChangesAsync();
        //        // 然后查询数据库
        //        var count = await context.Product.CountAsync();
        //        Console.WriteLine(count); // 应该输出 1
        //    }

        //    using (var context = new CQrDbContext(options))
        //    {
        //        Assert.Equal(1, await context.Product.CountAsync());
        //        Assert.Equal("Test Product", (await context.Product.FirstAsync()).Name);
        //    }
    }
    [Fact]
    public async Task UpdateAsync_UpdatesProductInDatabase()
    {
        var options = GetInMemoryDbOptions();
        //using (var context = new CQrDbContext(options))
        //{
        //    var product = new Product { Name = "Test Update Product", Price = 20, Stock = 30 };
        //    context.Product.Add(product);
        //    await context.SaveChangesAsync();
        //}
        //using (var context = new CQrDbContext(options))
        //{
        //    var repository = new ProductRepository(context);
        //    var productToUpdate = await repository.GetByIdAsync(1);
        //    productToUpdate.Name = "New Name";

        //    repository.Update(productToUpdate);
        //    await context.SaveChangesAsync();
        //    // await context.Product(productToUpdate);
        //}
        //using (var context = new CQrDbContext(options))
        //{
        //    var product = await context.Product.FindAsync(1);
        //    Assert.Equal("New Name", product.Name);
        //}
    }

    [Fact]
    public async Task DeleteAsync_RemovesProductFromDatabase()
    {
        //var options = GetInMemoryDbOptions();
        //Product product1 = new Product { Name = "Product A", Price = 30, Stock = 50 };
        //using (var context = new CQrDbContext(options))
        //{
        //    context.Product.Add(product1);
        //    await context.SaveChangesAsync();
        //}
        //using (var context = new CQrDbContext(options))
        //{
        //    var repository = new ProductRepository(context);
        //    repository.Delete(product1);
        //    await context.SaveChangesAsync();
        //}

        //using (var context = new CQrDbContext(options))
        //{
        //    var product = await context.Product.FindAsync(product1.Id);
        //    Assert.Null(product);
        //}
    }
    [Fact]
    public void Test1()
    {

    }
}