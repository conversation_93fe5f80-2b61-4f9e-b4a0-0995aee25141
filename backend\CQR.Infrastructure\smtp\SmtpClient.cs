﻿namespace CQR.Infrastructure.smtp
{


    public static class SmtpClientService
    {
        //private IOptions<SmtpSettings> smtpSettings;
        //public static bool SendMail(IOptions<SmtpSettings> smtpSettings, MailMessage mailMessage)
        //{
        //    // Set up SMTP client
        //    var smtpClient = new SmtpClient
        //    {
        //        Host = smtpSettings.Value.SMTP_HOST, // Specify your SMTP server here
        //        Port = smtpSettings.Value.SMTP_PORT, // Specify the appropriate port for your SMTP server
        //        //Credentials = new NetworkCredential("<EMAIL>", "your-password"), // Specify your email credentials
        //        //EnableSsl = true // Enable SSL if required by your SMTP server
        //    };
        //    try
        //    {
        //        smtpClient.Send(mailMessage);
        //        return true; // Assuming 1 for success, you can modify the return value based on your requirements
        //    }
        //    catch (Exception ex)
        //    {
        //        return false; // Assuming 0 for failure, you can modify the return value based on your requirements
        //    }
        //}
    }
}
