// using System.Data.SqlClient;
// using System.Threading.Tasks;
// using System.Data.SqlClient;
using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace CQR.Infrastructure;

public class DbService
{
    private readonly string _connectionString;

    public DbService(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection");
    }

    public async Task<int> ExecuteQueryAsync(string sql)
    {
        using (var connection = new SqlConnection(_connectionString))
        {
            await connection.OpenAsync();
            return await connection.ExecuteAsync(sql); // 使用 Dapper 或 ADO.NET 執行 SQL 查詢
        }
    }
}
