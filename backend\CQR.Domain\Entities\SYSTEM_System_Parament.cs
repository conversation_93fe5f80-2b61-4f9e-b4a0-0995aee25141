using System.ComponentModel.DataAnnotations;

namespace CQR.Core.Entities
{
    // [Table("[SYSTEM.System_Parament]")]
    public partial class SYSTEM_System_Parament
    {
        [Key]
        public string Configure_Name { get; set; } = null!;

        public string Configure_Value { get; set; } = null!;

        public bool IsEnableEdit { get; set; }

        public string? Note { get; set; }

        public DateTimeOffset? Modified_At { get; set; }

        public string? Modified_By { get; set; }
    }

}
