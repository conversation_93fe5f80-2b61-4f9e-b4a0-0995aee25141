﻿using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.VisualBasic;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using Microsoft.VisualBasic.CompilerServices;
using ICSharpCode.SharpZipLib.Zip;
using Microsoft.Extensions.Configuration;

namespace CQR.Domain.DecisionCore;
public class clsAttachment
{

    private readonly IConfiguration _configuration;
    private readonly IHttpContextAccessor _httpContextAccessor;


    public ATT_DATA sATT_DATA;

    public  T_TAB sT_TAB;


    private const int ZIP_ADD = 4;

    public const string T_ATTACH = "t:\\attach\\";

    private string sAttachmentDir;

    public string sHeaderText;

    public string sSubDir;

    public string sAddLinkText;

    public string sSourceFileText;

    public string sReAttachText;

    public bool bShowTypeAndTitle;

    public bool bShowTitle;

    public bool bShowCheckout;

    public bool bAceOffixMode;

    public string sAceOffixPath;

    public T_TAB tTab;

    //public object sessionAttachment
    //{
    //    get
    //    {
    //        object result = null;
    //        object objectValue = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(HttpContext.Current.Session, null, "Item", new object[1] { Operators.ConcatenateObject(Operators.ConcatenateObject(Interaction.IIf(Operators.CompareString(tTab.sSessionTag, "", TextCompare: false) == 0, HttpContext.Current.Request["SessionTag"], tTab.sSessionTag), "."), valFolderNum()) }, null, null, null));
    //        if (!Information.IsNothing(RuntimeHelpers.GetObjectValue(objectValue)))
    //        {
    //            result = ((!(objectValue is clsAttachment)) ? RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue, null, "tAttachment", new object[0], null, null, null)) : RuntimeHelpers.GetObjectValue(objectValue));
    //        }
    //        return result;
    //    }
    //    set
    //    {
    //        HttpContext.Current.Session[tTab.sSessionTag + "." + valFolderNum()] = RuntimeHelpers.GetObjectValue(value);
    //    }
    //}
    public string mGetTempPath => "C:\\";

    public string Get_AttachDirectory
    {
        get
        {
            if (true | (Operators.CompareString(sAttachmentDir, "", TextCompare: false) == 0))
            {
                sAttachmentDir = getAttachDir() + sSubDir;
                ref string reference = ref sAttachmentDir;
                reference = Conversions.ToString(Operators.ConcatenateObject(reference, Interaction.IIf(sAttachmentDir.EndsWith("\\"), "", "\\")));
            }
            return sAttachmentDir;
        }
    }

    public bool pbViewOnly
    {
        set
        {
            tTab.bViewOnly = value;
        }
    }

    [DllImport("kernel32", CharSet = CharSet.Ansi, EntryPoint = "GetTempPathA", ExactSpelling = true, SetLastError = true)]
    public static extern long GetTempPath(long nBufferLength, [MarshalAs(UnmanagedType.VBByRefStr)] ref string lpBuffer);

    public string valFolderNum()
    {
        if (Versioned.IsNumeric(tTab.sFolderNum))
        {
            return Conversions.ToString(Conversion.Val(tTab.sFolderNum));
        }
        return tTab.sFolderNum;
    }

    public int getAttachCount()
    {
        checked
        {
            int num = tTab.iAttCount - 1;
            int num2 = default(int);
            for (int i = 0; i <= num; i++)
            {
                if (!tTab.tAttInfo[i].bDeleteOnSave)
                {
                    num2++;
                }
            }
            return num2;
        }
    }

    public ATT_DATA getAttachment(int aiIndex)
    {
        throw new NotImplementedException();
        //checked
        //{
        //    int num = tTab.iAttCount - 1;
        //    int num2 = default(int);
        //    for (int i = 0; i <= num; i++)
        //    {
        //        if (!tTab.tAttInfo[i].bDeleteOnSave)
        //        {
        //            if (num2 == aiIndex)
        //            {
        //                return tTab.tAttInfo[i];
        //            }
        //            num2++;
        //        }
        //    }
        //    return new ATT_DATA();
        //}
    }

    public int getCreateCount()
    {
        checked
        {
            int num = tTab.iAttCount - 1;
            int num2 = default(int);
            for (int i = 0; i <= num; i++)
            {
                if (tTab.tAttInfo[i].bCreateOnSave)
                {
                    num2++;
                }
            }
            return num2;
        }
    }

    public bool mCheckFormAttached(string keyWord, string checkExt)
    {
        bool result = false;
        checked
        {
            int num = tTab.iAttCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (Strings.InStr(Strings.LCase(tTab.tAttInfo[i].sAttachTitle), Strings.LCase(keyWord)) > 0 && ((Operators.CompareString(checkExt, "", TextCompare: false) == 0) | (Strings.InStr(Strings.LCase(tTab.tAttInfo[i].sAttachTitle), Strings.LCase(checkExt)) > 0)))
                {
                    result = true;
                    break;
                }
            }
            return result;
        }
    }

    public  string getAttachDir()
    {
        var path = _httpContextAccessor.HttpContext?.Request?.Path.Value?.ToLower();
        if (!string.IsNullOrEmpty(path) && path.Contains("_staging"))
        //if (Strings.InStr(Strings.LCase(HttpContext.Current.Request.ServerVariables["PATH_INFO"]), "_staging") != 0)
        {
            return modTRS.mTRSGetSingleDesc("ATTACH", "DIRSTAGE");
        }
        return modTRS.mTRSGetSingleDesc("ATTACH", "DIR");
    }

    public string Get_AttachDirectoryAceOffix()
    {
        string text = Get_AttachDirectory + sAceOffixPath + tTab.sFolderNum;
        if (!Directory.Exists(text))
        {
            try
            {
                Directory.CreateDirectory(text);
            }
            catch (Exception ex)
            {
                ProjectData.SetProjectError(ex);
                Exception ex2 = ex;
                ProjectData.ClearProjectError();
            }
        }
        if (!text.EndsWith("\\"))
        {
            text += "\\";
        }
        return text;
    }

    //private void AddLogRecord(string msg, int attIndex)
    //{
    //    try
    //    {
    //        modTRS.logAddRecord(new corePage(), "CQR", tTab.sUserID, msg, tTab.sFolderNum, bIsSuperUser: false, tTab.tAttInfo[attIndex].sAttachTitle, tTab.tAttInfo[attIndex].sCheckoutUser, tTab.tAttInfo[attIndex].sCheckoutDate);
    //    }
    //    catch (Exception ex)
    //    {
    //        ProjectData.SetProjectError(ex);
    //        Exception ex2 = ex;
    //        modTRS.logAddRecord(new corePage(), "CQR", tTab.sUserID, msg, tTab.sFolderNum, bIsSuperUser: false, "First Revision: " + Conversions.ToString(attIndex), ex2.ToString(), "");
    //        ProjectData.ClearProjectError();
    //    }
    //}

    //public void AttachmentCheckout(int attIndex)
    //{
    //    AddLogRecord("CHECKOUT", attIndex);
    //    tTab.tAttInfo[checked((int)Math.Round(Conversion.Val(attIndex)))].sCheckoutUser = tTab.sUserID;
    //}

    public void AttachmentCheckin(bool isUndo = false)
    {
        checked
        {
            int num = tTab.iAttCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (Operators.CompareString(tTab.tAttInfo[i].sCheckoutUser, tTab.sUserID, TextCompare: false) == 0)
                {
                    //AddLogRecord(Conversions.ToString(Interaction.IIf(isUndo, "UNDO CHECKOUT", "CHECKIN")), i);
                    tTab.tAttInfo[i].sCheckoutUser = "";
                    tTab.tAttInfo[i].sRevLevel = Conversions.ToString(Interaction.IIf(isUndo, "", "X"));
                }
            }
        }
    }

    public int CurrentCheckout()
    {
        checked
        {
            int num = tTab.iAttCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (Operators.CompareString(tTab.tAttInfo[i].sCheckoutUser, tTab.sUserID, TextCompare: false) == 0)
                {
                    return i;
                }
            }
            return -1;
        }
    }

    public bool AttachmentCreate(ref corePage tPage, string asFile, int aiReplaceRow = -1)
    {
        checked
        {
            if (bShowTypeAndTitle)
            {
                int num = tTab.iAttCount - 1;
                int i;
                //for (i = 0; i <= num && Operators.CompareString(tPage.Request["txtAttachmentTitle"], tTab.tAttInfo[i].sAttachTitle, TextCompare: false) != 0; i++)
                //{
                //}
                //if (i < tTab.iAttCount)
                //{
                //    //((Label)tPage.FindControl("lblError")).Text = "All attachment descriptions must be unique.";
                //    //goto IL_01b4;
                //}
            }
            int num2 = Strings.InStr(asFile, ".");
            num2 = Conversions.ToInteger(Interaction.IIf(num2 == 0, Strings.Len(asFile), num2));
            string text = UpdateRevisionIfNeeded(asFile);
            //string asAttachTitle = Conversions.ToString(Interaction.IIf(Operators.CompareString(tPage.Request["txtAttachmentTitle"], "", TextCompare: false) == 0, text, tPage.Request["txtAttachmentTitle"]));
            //z_AddAttachment(tPage.Request["cmbAttachmentType"], asAttachTitle, text, Strings.UCase(Strings.Mid(asFile, num2 + 1)), asFile, "", "", abCreateOnSave: true, aiReplaceRow, tTab.sUserID);
           
            HttpResponse response = tPage.Response;
            //response.Write("\r\n<script>");
            //response.Write("\r\n  if (window.opener != null)");
            //response.Write("\r\n     {");
            //response.Write("\r\n     if (window.opener.goAttachmentDone != null)");
            //response.Write("\r\n        window.opener.goAttachmentDone();");
            //response.Write("\r\n     else");
            //response.Write("\r\n        window.opener.document.getElementById('Form1').submit();");
            //response.Write("\r\n     }");
            //response.Write("\r\n  window.close();");
            //response.Write("\r\n</script>");
            //_ = null;
            goto IL_01b4;
        }
    IL_01b4:
        bool result = default(bool);
        return result;
    }

    private void z_DeleteAttachmentFile(string asFile, bool abCreateOnSave)
    {
        try
        {
            if (abCreateOnSave)
            {
                File.Delete(asFile);
            }
            else
            {
                File.Delete(Get_AttachDirectory + asFile);
            }
        }
        catch (Exception ex)
        {
            ProjectData.SetProjectError(ex);
            Exception ex2 = ex;
            ProjectData.ClearProjectError();
        }
    }

    public bool btnRemoveAttachment(int iIndex, string asZipPath)
    {
        tTab.tAttInfo[iIndex].bDeleteOnSave = true;
        if (tTab.tAttInfo[iIndex].bCreateOnSave)
        {
            z_DeleteAttachmentFile(asZipPath + tTab.tAttInfo[iIndex].sDstFile, abCreateOnSave: true);
        }
        return true;
    }

    public void btnViewCurrentRow(int iRow)
    {
        if (!((iRow < 1) | (iRow > tTab.iAttCount)))
        {
            ATT_DATA aTT_DATA = tTab.tAttInfo[checked(iRow - 1)];
            if (Operators.CompareString(aTT_DATA.sQueueKey, "", TextCompare: false) != 0)
            {
                mViewFile(aTT_DATA.sSrcFile);
            }
            aTT_DATA = null;
        }
    }

    public void ClearData()
    {
        tTab.tAttInfo = new ATT_DATA[1];
        tTab.iAttCount = 0;
    }

    public bool LoadData(bool bNoFix = false)
    {
        //corePage corePage2 = new corePage();
        bool result = default(bool);
        try
        {
            ClearData();
            string text = "SELECT AH.* ";
            text += " , (SELECT TOP 1 CheckoutUser FROM ATTDIR_CheckoutLog CL WHERE CL.QueueKey=AH.QueueKey AND CL.CheckinDate IS NULL) AS CheckoutUser";
            text += " , (SELECT TOP 1 CheckoutDate FROM ATTDIR_CheckoutLog CD WHERE CD.QueueKey=AH.QueueKey AND CD.CheckinDate IS NULL) AS CheckoutDate";
            text += " FROM ATTDIR_AttachFileDirHeader AH";
            text = text + " WHERE FolderTypeCode = '" + tTab.sFolderType + "'";
            text = Conversions.ToString(Operators.ConcatenateObject(text, Operators.ConcatenateObject(Operators.ConcatenateObject("   AND FolderNbr = '", Interaction.IIf(bNoFix, tTab.sFolderNum, Strings.Right("0000000000" + tTab.sFolderNum, 10))), "'")));
            text += " ORDER BY QueueKey";
            //SqlDataReader sqlDataReader = corePage2.cnExecute(text);
            //while (sqlDataReader.Read())
            //{
            //    z_DbsAddAttachment(sqlDataReader);
            //}
            //sqlDataReader.Close();
            result = true;
        }
        catch (Exception projectError)
        {
            //ProjectData.SetProjectError(projectError);
            //ProjectData.ClearProjectError();
        }
        //corePage2.cnClose();
        return result;
    }

    private void z_SetDisplay(ATT_DATA tAttInfo)
    {
        ATT_DATA aTT_DATA = tAttInfo;
        checked
        {
            string text;
            if (Operators.CompareString(aTT_DATA.sExtension, "", TextCompare: false) == 0)
            {
                text = aTT_DATA.sAttachTitle;
                int num = Strings.InStrRev(text, ".");
                if (num > 0)
                {
                    string text2 = modTRS.mTRSGetSingleDesc("AFMEXT", Strings.Mid(text, num + 1));
                    if (Operators.CompareString(text2, "", TextCompare: false) == 0)
                    {
                        text2 = Strings.Mid(text, num + 1);
                    }
                    text = "(" + Strings.Mid(text, num) + ") " + text2;
                }
            }
            else
            {
                string text2 = modTRS.mTRSGetSingleDesc("AFMEXT", aTT_DATA.sExtension);
                text = "(" + aTT_DATA.sExtension + ") " + text2;
            }
            string text3 = Strings.Trim(modTRS.mTRSGetSingleDesc("ATTYPE", aTT_DATA.sAttachType)) + "\t";
            text3 = text3 + Strings.Trim(aTT_DATA.sAttachTitle) + "\t";
            text3 = text3 + Strings.Trim(text) + "\t";
            text3 += Strings.Trim(aTT_DATA.sSrcFile);
            aTT_DATA.sDisplay = text3;
            aTT_DATA = null;
        }
    }

    public bool DZAddFileToZIP(string szZIP, string szFile)
    {
        ZipOutputStream zipOutputStream = new ZipOutputStream(File.Create(szZIP));
        ZipEntry zipEntry = new ZipEntry(Path.GetFileName(szFile));
        FileStream fileStream = File.OpenRead(szFile);
        byte[] array = new byte[checked((int)(fileStream.Length - 1) + 1)];
        fileStream.Read(array, 0, array.Length);
        zipEntry.DateTime = DateTime.Now;
        zipEntry.Size = fileStream.Length;
        fileStream.Close();
        zipOutputStream.PutNextEntry(zipEntry);
        zipOutputStream.Write(array, 0, array.Length);
        zipOutputStream.Finish();
        zipOutputStream.Close();
        return true;
    }

    [MethodImpl(MethodImplOptions.NoInlining | MethodImplOptions.NoOptimization)]
    public bool SaveData(corePage tPage, string folderStatus = "")
    {
        checked
        {
            int num = tTab.iAttCount - 1;
            int i;
            for (i = 0; i <= num && tTab.tAttInfo[i].bCreateOnSave; i++)
            {
            }
            if (i == tTab.iAttCount)
            {
                string text = "DELETE FROM ATTDIR_AttachFileDirHeader";
                text = text + " WHERE FolderTypeCode = '" + Strings.Replace(tTab.sFolderType, "'", "''") + "'";
                text = text + "   AND FolderNbr = '" + tTab.sFolderNum + "'";
                tPage.cnExecuteNonQuery(text);
                tPage.cnClose2();
            }
            if (tTab.iAttCount != 0)
            {
                int num2 = tTab.iAttCount - 1;
                for (i = 0; i <= num2; i++)
                {
                    ATT_DATA aTT_DATA = tTab.tAttInfo[i];
                    if (aTT_DATA.bDeleteOnSave)
                    {
                        try
                        {
                            File.Delete(getAttachDir() + aTT_DATA.sSrcFile);
                            if (!aTT_DATA.bCreateOnSave & (Conversion.Val(aTT_DATA.sQueueKey) > 0.0))
                            {
                                string text = "DELETE FROM ATTDIR_AttachFileDirHeader WHERE QueueKey = " + Conversions.ToString(Conversion.Val(aTT_DATA.sQueueKey));
                                tPage.cnExecuteNonQuery(text);
                            }
                            z_DeleteAttachmentFile(aTT_DATA.sDstFile, aTT_DATA.bCreateOnSave);
                            aTT_DATA.bDeleteOnSave = true;
                            aTT_DATA.sRevLevel = "x";
                        }
                        catch (Exception projectError)
                        {
                            ProjectData.SetProjectError(projectError);
                            ProjectData.ClearProjectError();
                        }
                    }
                    else if (aTT_DATA.bCreateOnSave)
                    {
                        string text;
                        if (Conversion.Val(aTT_DATA.sQueueKey) > 0.0)
                        {
                            text = "DELETE FROM ATTDIR_AttachFileDirHeader WHERE QueueKey = " + Conversions.ToString(Conversion.Val(aTT_DATA.sQueueKey));
                            tPage.cnExecuteNonQuery(text);
                            z_DeleteAttachmentFile(aTT_DATA.sDstFile, aTT_DATA.bCreateOnSave);
                        }
                        string text2 = Strings.Replace(modTools.mStripPath(aTT_DATA.sSrcFile), "'", "''");
                        text = "INSERT INTO ATTDIR_AttachFileDirHeader";
                        text += " (FolderTypeCode,FolderNbr,FolderStatus,CustomData,CreatedByName,CreatedDate,";
                        text += "  CreatedTime,AttachTypeCode, AttachTitle, FileLocation) VALUES (";
                        text = text + " '" + Strings.Replace(tTab.sFolderType, "'", "''") + "','" + tTab.sFolderNum + "','" + folderStatus + "',";
                        text = text + " '" + Strings.Replace(aTT_DATA.sCustomData, "'", "''") + "',";
                        text = text + " '" + tTab.sUserID + "','" + Strings.Format(DateAndTime.Now, "yyyyMMdd") + "',";
                        text = text + " '" + Strings.Format(DateAndTime.Now, "HHmm") + "','" + aTT_DATA.sAttachType + "',";
                        text = text + " '" + Strings.Replace(Conversions.ToString(Interaction.IIf(bShowTitle | (Operators.CompareString(aTT_DATA.sAttachTitle, "", TextCompare: false) != 0), aTT_DATA.sAttachTitle, text2)), "'", "''") + "','" + text2 + "');";
                        tPage.cnExecuteNonQuery(text);
                        aTT_DATA.bCreateOnSave = false;
                        text = "SELECT QueueKey FROM ATTDIR_AttachFileDirHeader ";
                        text = text + " WHERE FileLocation = '" + text2 + "'";
                        //string text3 = tPage.cnExecuteForSingleValue(text);
                        //string text4 = HttpContext.Current.Request.ServerVariables["APPL_PHYSICAL_PATH"] + "Zip\\";
                        //string text5 = Strings.Format(Conversion.Val(text3), "00000000") + ".zip";


                        string text3 = "";
                        string text4= "";
                        string text5 = "";

                        if (!bAceOffixMode & File.Exists(text4 + aTT_DATA.sSrcFile))
                        {
                            string text6 = tTab.sFolderType + "\\";
                            if (!Directory.Exists(Get_AttachDirectory + text6))
                            {
                                Directory.CreateDirectory(Get_AttachDirectory + text6);
                            }
                            text6 = text6 + Strings.Format(Conversion.Val(tTab.sFolderNum) % 1000.0, "000") + "\\";
                            if (!Directory.Exists(Get_AttachDirectory + text6))
                            {
                                Directory.CreateDirectory(Get_AttachDirectory + text6);
                            }
                            if (DZAddFileToZIP(text4 + text5, text4 + aTT_DATA.sSrcFile))
                            {
                                text = "UPDATE ATTDIR_AttachFileDirHeader ";
                                text = text + " SET FileLocation = '" + text6 + text5 + "'";
                                text = text + " WHERE FileLocation = '" + text2 + "'";
                                tPage.cnExecuteNonQuery(text);
                                FileSystem.Kill(text4 + aTT_DATA.sSrcFile);
                                aTT_DATA.sSrcFile = modTools.mStripPath(text6 + text5);
                            }
                            else
                            {
                                aTT_DATA.sSrcFile = modTools.mStripPath(text6 + aTT_DATA.sSrcFile);
                            }
                            FileSystem.FileCopy(text4 + aTT_DATA.sSrcFile, Get_AttachDirectory + text6 + aTT_DATA.sSrcFile);
                            tTab.tAttInfo[i].sSrcFile = text6 + text5;
                            FileSystem.Kill(text4 + text5);
                        }
                        aTT_DATA.sQueueKey = text3;
                    }
                    else
                    {
                        if (Operators.CompareString(aTT_DATA.sCheckoutUser, aTT_DATA.sCheckoutUserDbs, TextCompare: false) != 0)
                        {
                            string text;
                            if (Operators.CompareString(aTT_DATA.sCheckoutUser, "", TextCompare: false) == 0)
                            {
                                text = "UPDATE ATTDIR_AttachFileDirHeader SET RevLevel='" + aTT_DATA.sRevLevel + "' WHERE QueueKey=" + aTT_DATA.sQueueKey;
                                tPage.cnExecuteNonQuery(text);
                                text = "UPDATE ATTDIR_CheckoutLog SET CheckinDate=getdate(), FolderStatus='" + folderStatus + "' WHERE QueueKey=" + aTT_DATA.sQueueKey + " AND CheckoutUser='" + aTT_DATA.sCheckoutUserDbs + "' AND CheckinDate IS NULL";
                            }
                            else
                            {
                                text = "INSERT INTO ATTDIR_CheckoutLog (QueueKey, CheckoutDate, CheckoutUser) VALUES(" + aTT_DATA.sQueueKey + ",getdate(),'" + aTT_DATA.sCheckoutUser + "')";
                            }
                            aTT_DATA.sCheckoutUserDbs = aTT_DATA.sCheckoutUser;
                            tPage.cnExecuteNonQuery(text);
                        }
                        if (Operators.CompareString(aTT_DATA.sCustomData, aTT_DATA.sCustomDataDbs, TextCompare: false) != 0)
                        {
                            string text = "UPDATE ATTDIR_AttachFileDirHeader SET CustomData='" + Strings.Replace(aTT_DATA.sCustomData, "'", "''") + "' WHERE QueueKey=" + aTT_DATA.sQueueKey;
                            aTT_DATA.sCustomDataDbs = aTT_DATA.sCustomData;
                            tPage.cnExecuteNonQuery(text);
                        }
                    }
                    aTT_DATA = null;
                }
                return true;
            }
            bool result = default(bool);
            return result;
        }
    }

    public bool HasChangesToSave()
    {
        checked
        {
            int num = tTab.iAttCount - 1;
            for (int i = 0; i <= num; i++)
            {
                ATT_DATA aTT_DATA = tTab.tAttInfo[i];
                if (aTT_DATA.bCreateOnSave)
                {
                    return true;
                }
                if (aTT_DATA.bDeleteOnSave & (Operators.CompareString(aTT_DATA.sRevLevel, "x", TextCompare: false) != 0))
                {
                    return true;
                }
                if (Operators.CompareString(aTT_DATA.sCheckoutUser, aTT_DATA.sCheckoutUserDbs, TextCompare: false) != 0)
                {
                    return true;
                }
                aTT_DATA = null;
            }
            return false;
        }
    }

    public void mInitializeAceOffix(string subPath)
    {
        bAceOffixMode = true;
        sAceOffixPath = Conversions.ToString(Operators.ConcatenateObject(subPath, Interaction.IIf(subPath.EndsWith("\\"), "", "\\")));
        bShowCheckout = false;
    }

    public void mInitialize(string asUserID, string asFolderType, string asFolderNum, string sSessionTag)
    {
        if (tTab == null)
        {
            tTab = new T_TAB();
        }
        tTab.bInitialized = true;
        tTab.sUserID = asUserID;
        tTab.sFolderType = asFolderType;
        tTab.sFolderNum = asFolderNum;
        modUserProf.UserNameFromId(asUserID);
        tTab.sUserLoc = modUserProf.UserLocationFromId(asUserID);
        tTab.sSessionTag = sSessionTag;
    }

    private string z_URL()
    {
        return "../_coreForm/frmAttachment.aspx?QueueKey=" + tTab.sFolderNum;
    }

    //public void ScreenAddCreate(corePage tPage, Panel tPanel)
    //{
    //    HyperLink hyperLink = new HyperLink();
    //    hyperLink.ID = "lnkAttachCreate" + tPanel.ID;
    //    hyperLink.Text = sAddLinkText;
    //    hyperLink.NavigateUrl = "javascript:createAttachment('" + valFolderNum() + "|-1|" + tTab.sSessionTag + "|');";
    //    tPanel.Controls.Add(hyperLink);
    //}

    //public HyperLink ScreenAddCheckin(corePage tPage, Panel tPanel, long iRow)
    //{
    //    HyperLink hyperLink = new HyperLink();
    //    hyperLink.ID = "lnkCheckIn" + tPanel.ID + Conversions.ToString(iRow);
    //    hyperLink.Text = "check in";
    //    hyperLink.NavigateUrl = "javascript:createAttachment('" + valFolderNum() + "|-1|" + tTab.sSessionTag + "|checkin');";
    //    return hyperLink;
    //}

    //public HyperLink ScreenAddUndoCheckOut(corePage tPage, Panel tPanel, int rowIndex, string rowId)
    //{
    //    string text = z_URL() + "|" + Conversions.ToString(rowIndex) + "|" + tTab.sSessionTag + "|undo";
    //    HyperLink hyperLink = new HyperLink();
    //    hyperLink.ID = "lnkUndoCheckout" + tPanel.ID + Conversions.ToString(rowIndex);
    //    hyperLink.Text = "undo check out";
    //    hyperLink.NavigateUrl = "javascript:undoCheckoutAttachment('" + text + "', '" + rowId + "');";
    //    return hyperLink;
    //}

    //public HyperLink ScreenAddReAttach(corePage tPage, Panel tPanel, long iRow)
    //{
    //    HyperLink hyperLink = new HyperLink();
    //    hyperLink.ID = "lnkReAttach" + tPanel.ID + Conversions.ToString(iRow);
    //    hyperLink.Text = sReAttachText;
    //    hyperLink.NavigateUrl = "javascript:createAttachment('" + valFolderNum() + "|" + Conversions.ToString(iRow) + "|" + tTab.sSessionTag + "|');";
    //    return hyperLink;
    //}

    //public HyperLink ScreenAddCheckout(corePage tPage, Panel tPanel, int iRow, string rowId)
    //{
    //    string text = z_URL() + "&Index=" + Conversions.ToString(iRow) + "&Checkout=1&SessionTag=" + tTab.sSessionTag;
    //    string text2 = z_URL() + "&Index=" + Conversions.ToString(iRow) + "&View=1&SessionTag=" + tTab.sSessionTag;
    //    HyperLink hyperLink = new HyperLink();
    //    hyperLink.Text = "check out";
    //    hyperLink.NavigateUrl = "javascript:checkoutAttachment('" + text + "','" + text2 + "','" + rowId + "','" + tTab.sFolderNum + "');";
    //    return hyperLink;
    //}

    //public void ScreenAddWriteJS(corePage tPage, bool bPromptSave = true, bool bAddFunction = true)
    //{
    //    StringBuilder stringBuilder = new StringBuilder();
    //    if (tPage.ClientScript.IsClientScriptBlockRegistered("attachmentAdd"))
    //    {
    //        return;
    //    }
    //    StringBuilder stringBuilder2 = stringBuilder;
    //    stringBuilder2.AppendLine("<script>");
    //    if (bAddFunction)
    //    {
    //        stringBuilder2.AppendLine("function createAttachment(iQueueKey) {");
    //        if (bPromptSave)
    //        {
    //            stringBuilder2.AppendLine("  if(confirm('The folder must be saved for this operation.  Would you like to save and continue?') == false)");
    //            stringBuilder2.AppendLine("     return;");
    //        }
    //        stringBuilder2.AppendLine("  fnLink('saveAttach' + iQueueKey);");
    //        stringBuilder2.AppendLine("  }");
    //    }
    //    stringBuilder2.AppendLine("function goAttachmentDelete(url) {");
    //    stringBuilder2.AppendLine("   $.ajax({");
    //    stringBuilder2.AppendLine("           url: url,");
    //    stringBuilder2.AppendLine("           dataType: 'html',");
    //    stringBuilder2.AppendLine("           async: false");
    //    stringBuilder2.AppendLine("          });");
    //    stringBuilder2.AppendLine("  }");
    //    stringBuilder2.AppendLine("var lastCommentBoxId = '';");
    //    stringBuilder2.AppendLine("function goAttachmentComments(commentBoxId) {");
    //    stringBuilder2.AppendLine("  lastCommentBoxId = commentBoxId;");
    //    stringBuilder2.AppendLine("  Tip(document.getElementById(\"hoverComments\").innerHTML, FOLLOWMOUSE, false, BGCOLOR, \"#4CB550\",HEIGHT, 125);");
    //    stringBuilder2.AppendLine("  $('#WzBoDy textarea').val($('#'+commentBoxId).val());");
    //    stringBuilder2.AppendLine("  }");
    //    stringBuilder2.AppendLine("function goAttachmentCommentsDone(commentBoxId) {");
    //    stringBuilder2.AppendLine("  $('#'+lastCommentBoxId).val($('#WzBoDyI textarea').val());");
    //    stringBuilder2.AppendLine("  UnTip();");
    //    stringBuilder2.AppendLine("  }");
    //    stringBuilder2.AppendLine("</script>");
    //    tPage.ClientScript.RegisterClientScriptBlock(tPage.GetType(), "attachmentAdd", stringBuilder.ToString());
    //    stringBuilder2 = null;
    //}

    //public void ScreenDelWriteJS(corePage tPage)
    //{
    //    StringBuilder stringBuilder = new StringBuilder();
    //    ClientScriptManager clientScript = tPage.ClientScript;
    //    if (!clientScript.IsClientScriptBlockRegistered("attachmentDelete"))
    //    {
    //        stringBuilder.AppendLine("<script>");
    //        stringBuilder.AppendLine("function deleteAttachment(szUrl) {");
    //        stringBuilder.AppendLine("   $.ajax({");
    //        stringBuilder.AppendLine("           url: szUrl,");
    //        stringBuilder.AppendLine("           dataType: 'html',");
    //        stringBuilder.AppendLine("           async: false");
    //        stringBuilder.AppendLine("          });");
    //        stringBuilder.AppendLine("   if (self.goAttachmentDone != null)");
    //        stringBuilder.AppendLine("         self.goAttachmentDone();");
    //        stringBuilder.AppendLine("   else");
    //        stringBuilder.AppendLine("         self.document.getElementById('Form1').document.forms[0].submit();");
    //        stringBuilder.AppendLine("  }");
    //        stringBuilder.AppendLine("</script>");
    //        clientScript.RegisterClientScriptBlock(tPage.GetType(), "attachmentDelete", stringBuilder.ToString());
    //        _ = null;
    //    }
    //}

    //public void ScreenCheckoutWriteJS(corePage tPage)
    //{
    //    StringBuilder stringBuilder = new StringBuilder();
    //    ClientScriptManager clientScript = tPage.ClientScript;
    //    if (!clientScript.IsClientScriptBlockRegistered("attachmentCheckout"))
    //    {
    //        StringBuilder stringBuilder2 = stringBuilder;
    //        stringBuilder2.AppendLine("<style>");
    //        stringBuilder2.AppendLine("  .oldrev { display:none; }");
    //        stringBuilder2.AppendLine("</style>");
    //        stringBuilder2.AppendLine("<script>");
    //        stringBuilder2.AppendLine("function goShowOldRevs() {");
    //        stringBuilder2.AppendLine("  if(event.srcElement.checked)");
    //        stringBuilder2.AppendLine("    $(\"*[oldrev]\").show();");
    //        stringBuilder2.AppendLine("  else");
    //        stringBuilder2.AppendLine("    $(\"*[oldrev]\").hide();");
    //        stringBuilder2.AppendLine("  }");
    //        stringBuilder2.AppendLine("function checkoutAttachment(szUrl, szView, rowId, queueKey) {");
    //        stringBuilder2.AppendLine("   var tbl = document.getElementById(rowId).parentElement;");
    //        stringBuilder2.AppendLine("   if(tbl.innerHTML.indexOf('check out on save') > 0) {");
    //        stringBuilder2.AppendLine("     alert('Only one file may be checked out at a time.');");
    //        stringBuilder2.AppendLine("     return;");
    //        stringBuilder2.AppendLine("   }");
    //        stringBuilder2.AppendLine("   $.ajax({");
    //        stringBuilder2.AppendLine("           url: szUrl,");
    //        stringBuilder2.AppendLine("           dataType: 'html',");
    //        stringBuilder2.AppendLine("           async: false");
    //        stringBuilder2.AppendLine("          });");
    //        stringBuilder2.AppendLine("   document.getElementById(rowId).cells[5].innerHTML = 'check out on save';");
    //        stringBuilder2.AppendLine("   document.getElementById(rowId).cells[5].innerHTML += '<br><a href=\"javascript:createAttachment(\\''+queueKey+'|-1|ATTACH_TASKLIST|checkin\\');\">check in</a>';");
    //        stringBuilder2.AppendLine("   document.getElementById(rowId).cells[5].innerHTML += '<br><a href=\"javascript:undoCheckoutAttachment(\\'../_coreForm/frmAttachment.aspx?QueueKey='+queueKey+'|0|ATTACH_TASKLIST|undo\\','+rowId+');\">undo check out</a>';");
    //        if (!modTools.isLocalhost_())
    //        {
    //            stringBuilder2.AppendLine("   window.open(szView);");
    //        }
    //        stringBuilder2.AppendLine("  }");
    //        stringBuilder2.AppendLine();
    //        stringBuilder2.AppendLine("function undoCheckoutAttachment(szUrl, rowId) {");
    //        stringBuilder2.AppendLine("   $.ajax({");
    //        stringBuilder2.AppendLine("           url: szUrl,");
    //        stringBuilder2.AppendLine("           dataType: 'html',");
    //        stringBuilder2.AppendLine("           async: false");
    //        stringBuilder2.AppendLine("          });");
    //        stringBuilder2.AppendLine("   location=document.URL;");
    //        stringBuilder2.AppendLine("  }");
    //        stringBuilder2.AppendLine("</script>");
    //        clientScript.RegisterClientScriptBlock(tPage.GetType(), "attachmentCheckout", stringBuilder.ToString());
    //        stringBuilder2 = null;
    //    }
    //}
    public void ControlsFromPreviousComments()
    {
        var form = _httpContextAccessor.HttpContext?.Request.Form;

        if (form == null)
            return;

        foreach (var key in form.Keys)
        {
            if (key.StartsWith("cmt"))
            {
                var parts = key.Split('_');
                if (int.TryParse(parts.Last(), out int index) && index < tTab.tAttInfo.Length)
                {
                    tTab.tAttInfo[index].sCustomData = form[key];
                }
            }
        }

        for (int j = 0; j < tTab.iAttCount; j++)
        {
            CustomColumnsFrom(j);
        }
    }


    //public void ControlsFromPreviousComments()
    //{
    //    string[] allKeys = HttpContext.Current.Request.Form.AllKeys;
    //    checked
    //    {
    //        foreach (string text in allKeys)
    //        {
    //            if (text.StartsWith("cmt"))
    //            {
    //                string[] array = Strings.Split(text, "_");
    //                tTab.tAttInfo[(int)Math.Round(Conversion.Val(array[Information.UBound(array)]))].sCustomData = HttpContext.Current.Request[text];
    //            }
    //        }
    //        int num = tTab.iAttCount - 1;
    //        for (int j = 0; j <= num; j++)
    //        {
    //            CustomColumnsFrom(j);
    //        }
    //    }
    //}

    public virtual bool CustomColumnsHeader(coreTable tbl)
    {
        return false;
    }

    public virtual bool CustomColumnsInto(int attIndex, int rowNumber, coreTable tbl, bool bReadOnly)
    {
        return false;
    }

    public virtual bool CustomColumnsFrom(int attIndex)
    {
        return false;
    }

    //public coreTable ScreenAddAttachments(corePage tPage, PlaceHolder phAttach, bool bReadOnly, string asDivId = "", bool abViewLink = true, bool abAddLink = true, bool abShowUserAndDate = false, string creatorID = "", bool abReattachLink = true, bool abDeleteLink = true, bool abShowComments = false)
    //{
    //    Table atTable = new Table();
    //    coreTable coreTable2 = new coreTable(ref atTable);
    //    Label label = new Label();
    //    Panel panel = new Panel();
    //    panel.ID = asDivId;
    //    if (((Operators.CompareString(asDivId, "", TextCompare: false) != 0) & (Operators.CompareString(Strings.Left(asDivId, 3), "xxx", TextCompare: false) != 0)) && abViewLink)
    //    {
    //        panel.Style.Add("visibility", "hidden");
    //        panel.Style.Add("position", "absolute");
    //    }
    //    if (Operators.CompareString(sHeaderText, "", TextCompare: false) != 0)
    //    {
    //        label.Text = sHeaderText;
    //        label.ID = "lblAttachmentHeader_" + asDivId;
    //        panel.Controls.Add(label);
    //    }
    //    if (!bReadOnly && abAddLink)
    //    {
    //        ScreenAddCreate(tPage, panel);
    //    }
    //    Label label2 = new Label();
    //    label2.Text = "<BR><BR>";
    //    panel.Controls.Add(label2);
    //    this.set_sessionAttachment((object)tPage, (object)this);
    //    Table tTable = coreTable2.tTable;
    //    tTable.Width = Unit.Percentage(100.0);
    //    tTable.BorderColor = Color.Black;
    //    tTable.BorderStyle = BorderStyle.Solid;
    //    tTable.BorderWidth = Unit.Pixel(1);
    //    tTable.CellPadding = 3;
    //    tTable.CellSpacing = 0;
    //    tTable.GridLines = GridLines.Both;
    //    tTable.ID = "tblAttach" + asDivId + "_" + tTab.sFolderNum;
    //    _ = null;
    //    coreTable2.AddRow(-1, abHeader: true);
    //    if (bShowTypeAndTitle)
    //    {
    //        coreTable2.AddCell("Attachment Type");
    //        coreTable2.AddCell("Title");
    //        coreTable2.AddCell("File Type");
    //    }
    //    else if (bShowTitle)
    //    {
    //        coreTable2.AddCell("Title");
    //    }
    //    coreTable2.AddCell("Source File");
    //    if (abShowUserAndDate)
    //    {
    //        coreTable2.AddCell("Creator");
    //        coreTable2.AddCell("Last Updated");
    //    }
    //    if (abShowComments)
    //    {
    //        coreTable2.AddCell("Comments");
    //    }
    //    if (abViewLink)
    //    {
    //        coreTable2.AddCell();
    //    }
    //    if (abReattachLink)
    //    {
    //        coreTable2.AddCell();
    //    }
    //    if (bShowCheckout)
    //    {
    //        coreTable2.AddCell("<nobr><input type='checkbox' onclick='goShowOldRevs()'>Show Old Revisions</nobr>").Width = Unit.Percentage(15.0);
    //    }
    //    if (!bReadOnly)
    //    {
    //        coreTable2.AddCell();
    //    }
    //    if (bShowCheckout & bAceOffixMode)
    //    {
    //        TableCell tableCell = coreTable2.AddCell("Old Revisions");
    //        tableCell.CssClass = "oldrev";
    //        tableCell.Attributes.Add("oldrev", "1");
    //        _ = null;
    //    }
    //    CustomColumnsHeader(coreTable2);
    //    Random random = new Random();
    //    checked
    //    {
    //        int num = tTab.iAttCount - 1;
    //        for (int i = 0; i <= num; i++)
    //        {
    //            if (tTab.tAttInfo[i].bDeleteOnSave)
    //            {
    //                continue;
    //            }
    //            TableRow tableRow = coreTable2.AddRow();
    //            tableRow.ID = "row" + Conversions.ToString(random.Next(1, Math.Abs(Environment.TickCount))) + "_" + asDivId;
    //            tableRow.Attributes.Add("index", Conversions.ToString(i));
    //            if (Operators.CompareString(tTab.tAttInfo[i].sRevLevel, "X", TextCompare: false) == 0)
    //            {
    //                tableRow.CssClass = "oldrev";
    //                tableRow.Attributes.Add("oldrev", "1");
    //            }
    //            string[] array = Strings.Split(tTab.tAttInfo[i].sDisplay, "\t");
    //            int num2 = Conversions.ToInteger(Interaction.IIf(bShowTypeAndTitle | bShowTitle, 0, 3));
    //            int num3 = Information.UBound(array);
    //            for (int j = num2; j <= num3; j++)
    //            {
    //                string text;
    //                unchecked
    //                {
    //                    if (!(!bShowTitle || j == 1 || j >= 3))
    //                    {
    //                        continue;
    //                    }
    //                    text = array[Conversions.ToInteger(Interaction.IIf((!bShowTypeAndTitle & !bShowTitle) && j == 3, 1, j))];
    //                }
    //                if (j == 3)
    //                {
    //                    int num4 = Strings.InStr(text, "_");
    //                    if (num4 > 0)
    //                    {
    //                        text = Strings.Mid(text, num4 + 1);
    //                    }
    //                }
    //                TableCell tableCell2 = coreTable2.AddCell(text);
    //                if (j <= 2)
    //                {
    //                    tableCell2.Style.Add("white-space", "nowrap");
    //                }
    //                tableCell2 = null;
    //            }
    //            if (abShowUserAndDate)
    //            {
    //                string text2 = "";
    //                _ = tTab.tAttInfo[i].sQueueKey;
    //                string text3 = modUserProf.UserNameFromId(Conversions.ToString(Interaction.IIf(Operators.CompareString(tTab.tAttInfo[i].sCreator, "", TextCompare: false) == 0, creatorID, tTab.tAttInfo[i].sCreator)));
    //                coreTable2.AddCell("<nobr>" + text3 + "</nobr>");
    //                text2 = Conversions.ToString(Operators.ConcatenateObject(modTools.mDateCygnetToScreen(tTab.tAttInfo[i].sCreateDate) + "&nbsp;", Interaction.IIf(Operators.CompareString(tTab.tAttInfo[i].sCreateTime, "", TextCompare: false) == 0, "", tTab.tAttInfo[i].sCreateTime.Substring(0, 2) + ":" + tTab.tAttInfo[i].sCreateTime.Substring(2, 2))));
    //                coreTable2.AddCell("<nobr>" + text2 + "</nobr>");
    //            }
    //            if (abShowComments)
    //            {
    //                TextBox textBox = new TextBox();
    //                textBox.TextMode = TextBoxMode.MultiLine;
    //                textBox.Style.Add("display", "none");
    //                textBox.Text = tTab.tAttInfo[i].sCustomData;
    //                textBox.ID = Strings.Replace(tableRow.ID, "row", "cmt") + "_" + Conversions.ToString(i);
    //                Button button = new Button();
    //                button.Text = "Comments";
    //                button.CssClass = "CCTextBox";
    //                button.Attributes.Add("onclick", "goAttachmentComments('" + textBox.ID + "'); return false;");
    //                TableCell tableCell3 = coreTable2.AddCell();
    //                tableCell3.Controls.Add(button);
    //                tableCell3.Controls.Add(textBox);
    //                _ = null;
    //            }
    //            if (abViewLink)
    //            {
    //                HyperLink hyperLink = new HyperLink();
    //                hyperLink.ID = "lnkAttachView_" + panel.ID + "_" + Conversions.ToString(i) + "_" + tTab.sFolderNum;
    //                if (bAceOffixMode)
    //                {
    //                    hyperLink.Text = Conversions.ToString(Interaction.IIf(bReadOnly, "view", "edit"));
    //                    hyperLink.NavigateUrl = Conversions.ToString(Operators.ConcatenateObject("../aceoffix/AceOffix.aspx?QueueKey=" + Conversions.ToString(Conversion.Val(tTab.sFolderNum)) + "&Index=" + Conversions.ToString(i) + "&ro=", Interaction.IIf(bReadOnly, "1", "0")));
    //                    hyperLink.Target = "_blank";
    //                }
    //                else
    //                {
    //                    hyperLink.Text = "view";
    //                    hyperLink.NavigateUrl = z_URL() + "&Index=" + Conversions.ToString(i) + "&View=1&SessionTag=" + tTab.sSessionTag;
    //                    hyperLink.Target = "_attachmentPopup";
    //                }
    //                coreTable2.AddCell().Controls.Add(hyperLink);
    //            }
    //            if (abReattachLink)
    //            {
    //                coreTable2.AddCell().Controls.Add(ScreenAddReAttach(tPage, panel, i));
    //            }
    //            bool flag = false;
    //            unchecked
    //            {
    //                if (bShowCheckout)
    //                {
    //                    if (tTab.tAttInfo[i].bCreateOnSave)
    //                    {
    //                        coreTable2.AddCell("not saved");
    //                    }
    //                    else if (Operators.CompareString(tTab.tAttInfo[i].sCheckoutUser, "", TextCompare: false) == 0 && !bReadOnly)
    //                    {
    //                        if (HasCurrentCheckout(creatorID) | (Operators.CompareString(tTab.tAttInfo[i].sRevLevel, "X", TextCompare: false) == 0))
    //                        {
    //                            coreTable2.AddCell();
    //                        }
    //                        else
    //                        {
    //                            coreTable2.AddCell().Controls.Add(ScreenAddCheckout(tPage, panel, i, tableRow.ID));
    //                            flag = true;
    //                        }
    //                    }
    //                    else if (Operators.CompareString(tTab.tAttInfo[i].sCheckoutUser, creatorID, TextCompare: false) == 0 && !bReadOnly)
    //                    {
    //                        TableCell tableCell4 = coreTable2.AddCell();
    //                        if ((Operators.CompareString(tTab.tAttInfo[i].sCheckoutUser, creatorID, TextCompare: false) == 0) & (Operators.CompareString(tTab.tAttInfo[i].sCheckoutUserDbs, "", TextCompare: false) == 0))
    //                        {
    //                            tableCell4.Controls.Add(new LiteralControl("checkout on save<br />"));
    //                        }
    //                        flag = true;
    //                        tableCell4.Controls.Add(ScreenAddCheckin(tPage, panel, i));
    //                        tableCell4.Controls.Add(new LiteralControl("<br />"));
    //                        tableCell4.Controls.Add(ScreenAddUndoCheckOut(tPage, panel, i, tableRow.ID));
    //                        tableCell4 = null;
    //                    }
    //                    else if (Operators.CompareString(tTab.tAttInfo[i].sCheckoutUser, "", TextCompare: false) != 0)
    //                    {
    //                        coreTable2.AddCell("checked out by:<br />" + modUserProf.UserNameFromId(tTab.tAttInfo[i].sCheckoutUser));
    //                        flag = true;
    //                    }
    //                    else
    //                    {
    //                        coreTable2.AddCell();
    //                    }
    //                }
    //                if (!bReadOnly)
    //                {
    //                    HyperLink hyperLink2 = new HyperLink();
    //                    hyperLink2.ID = "lnkAttachDel_" + panel.ID + "_" + Conversions.ToString(i) + "_" + tTab.sFolderNum;
    //                    hyperLink2.Text = "del";
    //                    if ((abDeleteLink & (Operators.CompareString(creatorID, "", TextCompare: false) == 0)) | (Operators.CompareString(creatorID, "SUPER", TextCompare: false) == 0) | (Operators.CompareString(Strings.UCase(creatorID), Strings.UCase(tTab.tAttInfo[i].sCreator), TextCompare: false) == 0) | (Operators.CompareString(tTab.tAttInfo[i].sCreator, "", TextCompare: false) == 0))
    //                    {
    //                        hyperLink2.Attributes.Add("href", "javascript:goAttachmentDelete('" + z_URL() + "&Index=" + Conversions.ToString(i) + "&Delete=1&SessionTag=" + tTab.sSessionTag + "'); $('#" + tableRow.ID + "').remove();");
    //                    }
    //                    else
    //                    {
    //                        hyperLink2.NavigateUrl = "javascript:alert(\"To delete an attachment you must be the user who added it.  This attachment was added by: " + modUserProf.UserNameFromId(tTab.tAttInfo[i].sCreator) + "\");";
    //                    }
    //                    if (flag & (Operators.CompareString(Strings.UCase(creatorID), "SUPER", TextCompare: false) != 0))
    //                    {
    //                        coreTable2.AddCell();
    //                    }
    //                    else
    //                    {
    //                        coreTable2.AddCell().Controls.Add(hyperLink2);
    //                    }
    //                }
    //            }
    //            if (bShowCheckout & bAceOffixMode)
    //            {
    //                string attachDirectoryAceOffix = Get_AttachDirectoryAceOffix();
    //                DirectoryInfo directoryInfo = new DirectoryInfo(attachDirectoryAceOffix);
    //                string text4 = "";
    //                FileInfo[] array2 = null;
    //                try
    //                {
    //                    directoryInfo.GetFiles("*.*");
    //                    FileInfo[] array3 = array2;
    //                    foreach (FileInfo fileInfo in array3)
    //                    {
    //                        if (Operators.CompareString(Strings.LCase(fileInfo.Name), Strings.LCase(tTab.tAttInfo[i].sSrcFile), TextCompare: false) != 0)
    //                        {
    //                            string[] array4 = Strings.Split(fileInfo.Name, "_");
    //                            string text5 = "";
    //                            int num5 = array4.Length - 2;
    //                            for (int l = 0; l <= num5; l++)
    //                            {
    //                                text5 = Conversions.ToString(Operators.ConcatenateObject(text5, Operators.ConcatenateObject(Interaction.IIf(l > 0, "_", ""), array4[l])));
    //                            }
    //                            text4 = text4 + "<a href='../aceoffix/AceOffix.aspx?QueueKey=9437&Index=" + fileInfo.FullName + "&ro=0' target='_blank'>" + text5 + "<br>" + array4[array4.Length - 2] + " - " + array4[array4.Length - 1] + "</a><br><br>";
    //                        }
    //                    }
    //                }
    //                catch (Exception ex)
    //                {
    //                    ProjectData.SetProjectError(ex);
    //                    Exception ex2 = ex;
    //                    ProjectData.ClearProjectError();
    //                }
    //                TableCell tableCell5 = coreTable2.AddCell(text4);
    //                tableCell5.CssClass = "oldrev";
    //                tableCell5.Attributes.Add("oldrev", "1");
    //                _ = null;
    //            }
    //            if (Operators.CompareString(tTab.tAttInfo[i].sCustomData, "", TextCompare: false) != 0)
    //            {
    //                TableCell tableCell6 = coreTable2.AddCell();
    //                tableCell6.Style.Add("display", "none");
    //                TextBox textBox2 = new TextBox();
    //                textBox2.TextMode = TextBoxMode.MultiLine;
    //                textBox2.Text = tTab.tAttInfo[i].sCustomData;
    //                textBox2.Style.Add("display", "none");
    //                textBox2.ID = "txtCustom_" + asDivId + "_" + Conversions.ToString(i);
    //                tableCell6.Controls.Add(textBox2);
    //                _ = null;
    //            }
    //            CustomColumnsInto(i, coreTable2.tTable.Rows.Count, coreTable2, bReadOnly);
    //        }
    //        panel.Controls.Add(coreTable2.tTable);
    //        phAttach.Controls.Add(panel);
    //        return coreTable2;
    //    }
    //}

    public bool mViewFile(string asFile)
    {
        bool result = default(bool);
        return result;
    }

    public void z_AddAttachment(string asAttachType, string asAttachTitle, string asDstFile, string asExtension = "", string asSrcFile = "", string asUserLoc = "", string asKey = "", bool abCreateOnSave = false, int aiIndex = -1, string asCreator = "", string asCheckoutUser = "", string asCheckoutDate = "", string asCustomData = "")
    {
        checked
        {
            int num;
            if (aiIndex == -1)
            {
                tTab.iAttCount++;
                ref ATT_DATA[] tAttInfo = ref tTab.tAttInfo;
                tAttInfo = (ATT_DATA[])Utils.CopyArray(tAttInfo, new ATT_DATA[tTab.iAttCount + 1]);
                num = tTab.iAttCount - 1;
            }
            else
            {
                num = aiIndex;
                asKey = tTab.tAttInfo[num].sQueueKey;
            }
            //tTab.tAttInfo[num] = new ATT_DATA();
            ATT_DATA aTT_DATA = tTab.tAttInfo[num];
            aTT_DATA.sQueueKey = asKey;
            aTT_DATA.sQueueKeyForCompare = Conversions.ToString(Interaction.IIf(Operators.CompareString(asKey, "", TextCompare: false) == 0, Environment.TickCount, asKey));
            aTT_DATA.sAttachTitle = Conversions.ToString(Interaction.IIf(Operators.CompareString(asAttachTitle, "", TextCompare: false) == 0, modTools.mStripPath(asSrcFile), asAttachTitle));
            aTT_DATA.sAttachType = asAttachType;
            aTT_DATA.sDstFile = asDstFile;
            aTT_DATA.sExtension = asExtension;
            aTT_DATA.sSrcFile = asSrcFile;
            aTT_DATA.sUserLoc = asUserLoc;
            aTT_DATA.sCreator = asCreator;
            aTT_DATA.sCreateDate = Strings.Format(DateAndTime.Now, "yyyyMMdd");
            aTT_DATA.sCreateTime = Strings.Format(DateAndTime.Now, "HHmm");
            aTT_DATA.sCheckoutDate = asCheckoutDate;
            aTT_DATA.sCheckoutUser = asCheckoutUser;
            aTT_DATA.sCheckoutUserDbs = asCheckoutUser;
            aTT_DATA.sCustomData = asCustomData;
            aTT_DATA.sCustomDataDbs = asCustomData;
            if (Operators.CompareString(aTT_DATA.sCheckoutUser, "", TextCompare: false) != 0 && DateAndTime.DateDiff(DateInterval.Hour, Conversions.ToDate(aTT_DATA.sCheckoutDate), DateAndTime.Now) > 36)
            {
                //new corePage().cnExecuteNonQuery("DELETE FROM ATTDIR_CheckoutLog WHERE QueueKey=" + aTT_DATA.sQueueKey + " AND CheckoutUser='" + aTT_DATA.sCheckoutUserDbs + "' AND CheckinDate IS NULL");
                aTT_DATA.sCheckoutUser = "";
                aTT_DATA.sCheckoutUserDbs = "";
            }
            aTT_DATA.bCreateOnSave = abCreateOnSave;
            aTT_DATA.bRequiresProcessing = abCreateOnSave;
            aTT_DATA = null;
            z_SetDisplay(tTab.tAttInfo[num]);
        }
    }

    private bool z_DbsAddAttachment(SqlDataReader acHdr)
    {
        //corePage corePage2 = new corePage();
        //string asKey = corePage2.rstString(ref acHdr, "QueueKey");
        //string asAttachType = corePage2.rstString(ref acHdr, "AttachTypeCode");
        //string asAttachTitle = corePage2.rstString(ref acHdr, "AttachTitle");
        //string asExtension = corePage2.rstString(ref acHdr, "ViewPgmCode");
        //string text = corePage2.rstString(ref acHdr, "FileLocation");
        //string asCreator = corePage2.rstString(ref acHdr, "CreatedByName");
        //string asCheckoutDate = corePage2.rstString(ref acHdr, "CheckoutDate");
        //string asCheckoutUser = corePage2.rstString(ref acHdr, "CheckoutUser");
        //string asCustomData = corePage2.rstString(ref acHdr, "CustomData");
        checked
        {
            //if (Operators.CompareString(Strings.Left(Strings.LCase(text), Strings.Len("t:\\attach\\")), "t:\\attach\\", TextCompare: false) == 0)
            //{
            //    //text = Strings.Mid(text, Strings.Len("t:\\attach\\") + 1);
            //}
            //corePage2.cnClose();
            //z_AddAttachment(asAttachType, asAttachTitle, text, asExtension, text, "", asKey, abCreateOnSave: false, -1, asCreator, asCheckoutUser, asCheckoutDate, asCustomData);
            //tTab.tAttInfo[tTab.iAttCount - 1].sRevLevel = corePage2.rstString(ref acHdr, "RevLevel");
            //tTab.tAttInfo[tTab.iAttCount - 1].sCreateDate = corePage2.rstString(ref acHdr, "CreatedDate");
            //tTab.tAttInfo[tTab.iAttCount - 1].sCreateTime = corePage2.rstString(ref acHdr, "CreatedTime");
            return true;
        }
    }

    //~clsAttachment()
    //{
    //    pbViewOnly = true;
    //    base.Finalize();
    //}

    public clsAttachment()
    {
        sHeaderText = "Attachments:&nbsp;&nbsp;&nbsp;";
        sSourceFileText = "Source File";
        tTab = new T_TAB();
        sAddLinkText = "add";
        sReAttachText = "re-attach";
    }

    public bool checkDuplicateName(string asFile)
    {
        checked
        {
            string right = Strings.Mid(asFile, Strings.InStrRev(asFile, "\\") + 1);
            int num = tTab.iAttCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (!tTab.tAttInfo[i].bDeleteOnSave && Operators.CompareString(tTab.tAttInfo[i].sAttachTitle, right, TextCompare: false) == 0)
                {
                    return true;
                }
            }
            bool result = default(bool);
            return result;
        }
    }

    public bool hasNewAttachments()
    {
        checked
        {
            int num = tTab.iAttCount - 1;
            bool result = default(bool);
            for (int i = 0; i <= num; i++)
            {
                if (!tTab.tAttInfo[i].bDeleteOnSave & (Operators.CompareString(tTab.tAttInfo[i].sQueueKey, "", TextCompare: false) == 0))
                {
                    result = true;
                }
            }
            return result;
        }
    }

    public bool hasAttachments()
    {
        checked
        {
            int num = tTab.iAttCount - 1;
            bool result = default(bool);
            for (int i = 0; i <= num; i++)
            {
                if (!tTab.tAttInfo[i].bDeleteOnSave)
                {
                    result = true;
                }
            }
            return result;
        }
    }

    public void MarkAllAsCreate()
    {
        //frmAttachmentView frmAttachmentView2 = new frmAttachmentView();
        checked
        {
            int num = tTab.iAttCount - 1;
            for (int i = 0; i <= num; i++)
            {
                try
                {
                    //tTab.tAttInfo[i].sSrcFile = frmAttachmentView2.unZipThatFileEx(tTab.tAttInfo[i].sSrcFile);
                }
                catch (Exception ex)
                {
                    ProjectData.SetProjectError(ex);
                    Exception ex2 = ex;
                    ProjectData.ClearProjectError();
                }
                tTab.tAttInfo[i].bCreateOnSave = true;
                tTab.tAttInfo[i].sQueueKey = "";
            }
        }
    }

    public bool HasPendingCheckIn(string checkInUser)
    {
        bool result = false;
        checked
        {
            int num = tTab.iAttCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if ((Operators.CompareString(Strings.UCase(tTab.tAttInfo[i].sCheckoutUserDbs), Strings.UCase(checkInUser), TextCompare: false) == 0) & (Operators.CompareString(tTab.tAttInfo[i].sCheckoutUser, "", TextCompare: false) == 0) & (Operators.CompareString(tTab.tAttInfo[i].sRevLevel, "X", TextCompare: false) == 0))
                {
                    result = true;
                }
            }
            return result;
        }
    }

    public bool HasCurrentCheckout(string checkInUser)
    {
        bool result = false;
        checked
        {
            int num = tTab.iAttCount - 1;
            for (int i = 0; i <= num; i++)
            {
                if (Operators.CompareString(Strings.UCase(tTab.tAttInfo[i].sCheckoutUser), Strings.UCase(checkInUser), TextCompare: false) == 0)
                {
                    result = true;
                }
            }
            return result;
        }
    }

    public bool HasCheckInFor(string checkInStatus, string checkInUser, string checkedInAfter = "")
    {
        corePage obj = new corePage(_configuration,_httpContextAccessor);
        string text = "SELECT COUNT(1) FROM ATTDIR_CheckoutLog CL ";
        text += " INNER JOIN ATTDIR_AttachFileDirHeader ADH ON ADH.QueueKey=CL.QueueKey ";
        text = text + "                                          AND ADH.FolderTypeCode='" + tTab.sFolderType + "'";
        text = text + "                                          AND ADH.FolderNbr='" + tTab.sFolderNum + "'";
        text = text + " WHERE CL.CheckoutUser='" + checkInUser + "'";
        text = text + "   AND CL.FolderStatus='" + checkInStatus + "'";
        if (Operators.CompareString(checkedInAfter, "", TextCompare: false) != 0)
        {
            text = text + "   AND CL.CheckInDate > '" + checkedInAfter + "'";
        }
        int num = checked((int)Math.Round(Conversion.Val(obj.cnExecuteForSingleValue(text))));
        if (num == 0 && HasPendingCheckIn(checkInUser))
        {
            return true;
        }
        return num > 0;
    }

    public bool HasCheckOut(string checkInUser)
    {
        string sql = "SELECT COUNT(1) FROM ATTDIR_CheckoutLog WHERE CheckinDate IS NULL AND CheckoutUser = @CheckinUser";

        var result = new corePage(_configuration, _httpContextAccessor).cnExecuteForSingleValue(
            sql,
            new Dictionary<string, object> {
            { "@CheckinUser", checkInUser }
            }
        );

        return Convert.ToInt32(result) > 0;
    }

    //public bool HasCheckOut(string checkInUser)
    //{
    //    return checked((int)Math.Round(Conversion.Val(new corePage(null).cnExecuteForSingleValue("SELECT COUNT(1) FROM ATTDIR_CheckoutLog WHERE CheckinDate IS NULL AND CheckoutUser='" + checkInUser + "'")))) > 0;
    //}

    private string UpdateRevisionIfNeeded(string fileName)
    {
        checked
        {
            if (bShowCheckout)
            {
                int num = 1;
                int num2 = CurrentCheckout();
                if (num2 >= 0 &&
    _httpContextAccessor.HttpContext != null &&
    _httpContextAccessor.HttpContext.Request != null &&
    _httpContextAccessor.HttpContext.Request.Query.ContainsKey("QueueKey") &&
    _httpContextAccessor.HttpContext.Request.Query["QueueKey"].ToString().Contains("checkin"))
                {
                    int num3 = Strings.InStrRev(tTab.tAttInfo[num2].sAttachTitle, "_Rev ");
                    //AddLogRecord("REMOVING REVISION (" + Conversions.ToString(num3) + ")", num2);
                    num = Conversions.ToInteger(Interaction.IIf(num3 <= 0, 1, Conversion.Val(Strings.Mid(tTab.tAttInfo[num2].sAttachTitle, num3 + 4)) + 1.0));
                    //AddLogRecord("NEW REVISION (" + Conversions.ToString(num) + ")", num2);
                }
                int num4 = Strings.InStrRev(fileName, ".");
                //AddLogRecord("ADDING REVISION", num2);
                fileName = Strings.Left(fileName, num4 - 1) + "_Rev " + Conversions.ToString(num) + Strings.Mid(fileName, num4);
            }
            return fileName;
        }
    }
}
