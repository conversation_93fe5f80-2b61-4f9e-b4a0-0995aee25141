import { ref, watch } from 'vue'
import { storageLocal } from '@pureadmin/utils'

export interface UserPreferences {
  // 搜尋偏好設定
  search: {
    showEmptySearchConfirm: boolean
    defaultPageSize: number
    autoSearchOnLoad: boolean
  }
  // 界面偏好設定
  ui: {
    showSearchTips: boolean
    compactMode: boolean
    defaultSortField: string
    defaultSortDirection: 'asc' | 'desc'
  }
  // 通知偏好設定
  notifications: {
    accountPassword: boolean
    systemMessages: boolean
    todoTasks: boolean
  }
}

const DEFAULT_PREFERENCES: UserPreferences = {
  search: {
    showEmptySearchConfirm: false, // 預設不顯示空搜尋確認
    defaultPageSize: 50,
    autoSearchOnLoad: false
  },
  ui: {
    showSearchTips: true,
    compactMode: false,
    defaultSortField: 'cqrnumber',
    defaultSortDirection: 'desc'
  },
  notifications: {
    accountPassword: true,
    systemMessages: true,
    todoTasks: true
  }
}

const STORAGE_KEY = 'user-preferences'

// 全域的用戶偏好設定
const preferences = ref<UserPreferences>(
  storageLocal().getItem(STORAGE_KEY) || DEFAULT_PREFERENCES
)

// 監聽變化並自動保存到 localStorage
watch(
  preferences,
  (newPrefs) => {
    storageLocal().setItem(STORAGE_KEY, newPrefs)
  },
  { deep: true }
)

export function useUserPreferences() {
  // 取得特定偏好設定
  const getPreference = <T extends keyof UserPreferences>(
    section: T
  ): UserPreferences[T] => {
    return preferences.value[section]
  }

  // 更新特定偏好設定
  const updatePreference = <T extends keyof UserPreferences>(
    section: T,
    updates: Partial<UserPreferences[T]>
  ) => {
    preferences.value[section] = {
      ...preferences.value[section],
      ...updates
    }
  }

  // 重置偏好設定
  const resetPreferences = () => {
    preferences.value = { ...DEFAULT_PREFERENCES }
  }

  // 取得完整偏好設定
  const getAllPreferences = () => preferences.value

  // 檢查是否顯示空搜尋確認
  const shouldShowEmptySearchConfirm = () => {
    return preferences.value.search.showEmptySearchConfirm
  }

  return {
    preferences,
    getPreference,
    updatePreference,
    resetPreferences,
    getAllPreferences,
    shouldShowEmptySearchConfirm
  }
}